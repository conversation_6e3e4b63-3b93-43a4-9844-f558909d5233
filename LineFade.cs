﻿using System;
using UnityEngine;

// Token: 0x0200000F RID: 15
public class LineFade : MonoBehaviour
{
	// Token: 0x06000067 RID: 103 RVA: 0x000040C1 File Offset: 0x000022C1
	private void Start()
	{
		this.lr = base.GetComponent<LineRenderer>();
	}

	// Token: 0x06000068 RID: 104 RVA: 0x000040D0 File Offset: 0x000022D0
	private void Update()
	{
		this.color.a = Mathf.Lerp(this.color.a, 0f, Time.deltaTime * this.speed);
		this.lr.startColor = this.color;
		this.lr.endColor = this.color;
		if (this.decreaseInSize)
		{
			this.lr.SetPosition(this.lr.positionCount - 1, Vector3.Lerp(this.lr.GetPosition(this.lr.positionCount - 1), this.lr.GetPosition(0), Time.deltaTime * this.decreaseInSizeSpeed));
		}
	}

	// Token: 0x04000074 RID: 116
	public Color color;

	// Token: 0x04000075 RID: 117
	public bool decreaseInSize;

	// Token: 0x04000076 RID: 118
	public float decreaseInSizeSpeed;

	// Token: 0x04000077 RID: 119
	public float speed = 10f;

	// Token: 0x04000078 RID: 120
	private LineRenderer lr;
}

﻿using System;
using System.Collections;
using DG.Tweening;
using DG.Tweening.Core;
using DG.Tweening.Plugins.Options;
using FishNet;
using FishNet.Connection;
using FishNet.Managing;
using FishNet.Object;
using FishNet.Object.Delegating;
using FishNet.Serializing;
using FishNet.Transporting;
using TMPro;
using UnityEngine;

// Token: 0x020000FE RID: 254
public class RoundManager : NetworkBehaviour
{
	// Token: 0x06000CDF RID: 3295 RVA: 0x00057504 File Offset: 0x00055704
	public virtual void Awake()
	{
		this.NetworkInitialize___Early();
		this.Awake___UserLogic();
		this.NetworkInitialize__Late();
	}

	// Token: 0x06000CE0 RID: 3296 RVA: 0x00057523 File Offset: 0x00055723
	private void Start()
	{
		this.container.transform.localScale = Vector3.zero;
		this.background.transform.localScale = Vector3.zero;
	}

	// Token: 0x06000CE1 RID: 3297 RVA: 0x00057550 File Offset: 0x00055750
	public void StopCoroutine()
	{
		if (this.InterfaceSetupCoroutine != null)
		{
			base.StopCoroutine(this.InterfaceSetupCoroutine);
			this.selfPlusOne.transform.localScale = Vector3.zero;
			this.enemyPlusOne[0].transform.localScale = Vector3.zero;
			this.enemyPlusOne[1].transform.localScale = Vector3.zero;
			this.enemyPlusOne[2].transform.localScale = Vector3.zero;
			this.nextRoundImageOne.DOMove(this.nextRoundImageOneRestPos.position, 0f, false).SetEase(Ease.OutSine);
			this.nextRoundImageTwo.DOMove(this.nextRoundImageTwoRestPos.position, 0f, false).SetEase(Ease.OutSine);
			this.nextRoundImageThree.DOMove(this.nextRoundImageThreeRestPos.position, 0f, false).SetEase(Ease.OutSine);
			this.nextRoundImageFour.DOMove(this.nextRoundImageFourRestPos.position, 0f, false).SetEase(Ease.OutSine);
			this.nextRoundImageFive.DOMove(this.nextRoundImageFourRestPos.position, 0f, false).SetEase(Ease.OutSine);
			this.nextRoundImageSix.DOMove(this.nextRoundImageFourRestPos.position, 0f, false).SetEase(Ease.OutSine);
			this.container.transform.localScale = Vector3.zero;
			this.background.transform.localScale = Vector3.zero;
			this.InterfaceSetupCoroutine = null;
		}
	}

	// Token: 0x06000CE2 RID: 3298 RVA: 0x000576D1 File Offset: 0x000558D1
	[ServerRpc(RequireOwnership = false)]
	public void CmdEndRound(int winningTeamId)
	{
		this.RpcWriter___Server_CmdEndRound_3316948804(winningTeamId);
	}

	// Token: 0x06000CE3 RID: 3299 RVA: 0x000576DD File Offset: 0x000558DD
	[ObserversRpc]
	public void EndRoundObservers(int winningTeamId)
	{
		this.RpcWriter___Observers_EndRoundObservers_3316948804(winningTeamId);
	}

	// Token: 0x06000CE4 RID: 3300 RVA: 0x000576EC File Offset: 0x000558EC
	public void NextRoundCall(int playerId, bool won, int winningTeamId)
	{
		this.names = new string[4];
		this.scores = new int[4];
		this.players = new ClientInstance[SteamLobby.Instance.players.Count];
		if (won)
		{
			Settings.Instance.IncreaseRoundsWon();
		}
		else
		{
			Settings.Instance.IncreaseRoundsLost();
		}
		for (int i = 0; i < this.players.Length; i++)
		{
			this.players[i] = SteamLobby.Instance.players[i].GetComponent<ClientInstance>();
		}
		for (int j = 0; j < this.names.Length; j++)
		{
			ClientInstance clientInstance;
			if (ClientInstance.playerInstances.TryGetValue(j, out clientInstance))
			{
				this.names[j] = clientInstance.PlayerName;
			}
			else
			{
				this.names[j] = "";
			}
		}
		for (int k = 0; k < this.scores.Length; k++)
		{
			this.scores[k] = ScoreManager.Instance.GetPoints(ScoreManager.Instance.GetTeamId(k));
		}
		this.InterfaceSetupCoroutine = this.InterfaceSetup(playerId, won, winningTeamId);
		base.StartCoroutine(this.InterfaceSetupCoroutine);
	}

	// Token: 0x06000CE5 RID: 3301 RVA: 0x000577FF File Offset: 0x000559FF
	private IEnumerator InterfaceSetup(int playerId, bool won, int winningTeamId)
	{
		PauseManager.Instance.onEndRoundScreen = true;
		yield return new WaitForSeconds(0f);
		this.DynamicLoading(playerId);
		this.selfPlusOne.transform.localScale = Vector3.zero;
		this.enemyPlusOne[0].transform.localScale = Vector3.zero;
		this.enemyPlusOne[1].transform.localScale = Vector3.zero;
		this.enemyPlusOne[2].transform.localScale = Vector3.zero;
		SoundManager.Instance.PlaySound(won ? this.winClip : this.loseClip);
		this.container.transform.localScale = Vector3.one;
		this.background.transform.localScale = Vector3.one;
		this.playerName.text = this.names[playerId].ToLower();
		this.playerScore.text = ((won && this.scores[playerId] - 1 >= 0) ? (this.scores[playerId] - 1) : this.scores[playerId]).ToString();
		if (this.names.Length >= 2)
		{
			for (int i = 0; i < 4; i++)
			{
				if (ClientInstance.playerInstances.ContainsKey(i) && playerId != i)
				{
					this.enemyName[(playerId < i) ? (i - 1) : i].text = this.names[i];
				}
			}
		}
		if (this.scores.Length >= 2)
		{
			for (int j = 0; j < 4; j++)
			{
				if (ClientInstance.playerInstances.ContainsKey(j) && playerId != j)
				{
					bool flag = ScoreManager.Instance.GetTeamId(j) == winningTeamId;
					this.enemyScore[(playerId < j) ? (j - 1) : j].text = (flag ? (this.scores[j] - 1) : this.scores[j]).ToString();
				}
			}
		}
		this.winText.color = (won ? this.winColor : this.loseColor);
		this.winText.text = (won ? "victory" : "defeat");
		this.roundText.text = "";
		SoundManager.Instance.PlaySound(this.swooshClip[0]);
		this.nextRoundImageOne.DOMove(this.nextRoundImageOneActivePos.position, this.tweenTime, false).SetEase(Ease.OutSine);
		this.nextRoundImageTwo.DOMove(this.nextRoundImageTwoActivePos.position, this.tweenTime, false).SetEase(Ease.OutSine);
		this.nextRoundImageThree.DOMove(this.nextRoundImageThreeActivePos.position, this.tweenTime, false).SetEase(Ease.OutSine);
		this.nextRoundImageFour.DOMove(this.nextRoundImageFourActivePos.position, this.tweenTime, false).SetEase(Ease.OutSine);
		this.nextRoundImageFive.DOMove(this.nextRoundImageFiveActivePos.position, this.tweenTime, false).SetEase(Ease.OutSine);
		this.nextRoundImageSix.DOMove(this.nextRoundImageSixActivePos.position, this.tweenTime, false).SetEase(Ease.OutSine);
		yield return new WaitForSeconds(0.75f);
		SoundManager.Instance.PlaySound(this.plusOneClip);
		if (won)
		{
			this.selfPlusOne.transform.localScale = Vector3.one;
		}
		for (int k = 0; k < 4; k++)
		{
			if (ClientInstance.playerInstances.ContainsKey(k) && playerId != k && ScoreManager.Instance.GetTeamId(k) == winningTeamId)
			{
				this.enemyPlusOne[(playerId < k) ? (k - 1) : k].transform.localScale = Vector3.one;
			}
		}
		yield return new WaitForSeconds(0.8f);
		if (won)
		{
			this.selfPlusOne.transform.DOScale(Vector3.zero, 0.2f);
		}
		for (int l = 0; l < 4; l++)
		{
			if (ClientInstance.playerInstances.ContainsKey(l) && playerId != l && ScoreManager.Instance.GetTeamId(l) == winningTeamId)
			{
				this.enemyPlusOne[(playerId < l) ? (l - 1) : l].transform.DOScale(Vector3.zero, 0.2f);
			}
		}
		yield return new WaitForSeconds(0.2f);
		this.playerScore.text = this.scores[playerId].ToString();
		if (this.scores.Length >= 2)
		{
			for (int m = 0; m < 4; m++)
			{
				if (ClientInstance.playerInstances.ContainsKey(m) && playerId != m)
				{
					this.enemyScore[(playerId < m) ? (m - 1) : m].text = this.scores[m].ToString();
				}
			}
		}
		this.selfPlusOne.transform.localScale = Vector3.zero;
		this.enemyPlusOne[0].transform.localScale = Vector3.zero;
		this.enemyPlusOne[1].transform.localScale = Vector3.zero;
		this.enemyPlusOne[2].transform.localScale = Vector3.zero;
		yield return new WaitForSeconds(1.25f);
		SoundManager.Instance.PlaySound(this.swooshClip[1]);
		this.nextRoundImageOne.DOMove(this.nextRoundImageOneRestPos.position, this.tweenTime, false).SetEase(Ease.OutSine);
		this.nextRoundImageTwo.DOMove(this.nextRoundImageTwoRestPos.position, this.tweenTime, false).SetEase(Ease.OutSine);
		this.nextRoundImageThree.DOMove(this.nextRoundImageThreeRestPos.position, this.tweenTime, false).SetEase(Ease.OutSine);
		this.nextRoundImageFour.DOMove(this.nextRoundImageFourRestPos.position, this.tweenTime, false).SetEase(Ease.OutSine);
		this.nextRoundImageFive.DOMove(this.nextRoundImageFiveRestPos.position, this.tweenTime, false).SetEase(Ease.OutSine);
		this.nextRoundImageSix.DOMove(this.nextRoundImageSixRestPos.position, this.tweenTime, false).SetEase(Ease.OutSine);
		yield return new WaitForSeconds(1f);
		this.container.transform.localScale = Vector3.zero;
		this.background.transform.localScale = Vector3.zero;
		SceneMotor.Instance.ShowLoadingScreen();
		PauseManager.Instance.onEndRoundScreen = false;
		yield break;
	}

	// Token: 0x06000CE6 RID: 3302 RVA: 0x00057824 File Offset: 0x00055A24
	private void DynamicLoading(int playerId)
	{
		switch (playerId)
		{
		case 0:
			if (!ClientInstance.playerInstances.ContainsKey(1))
			{
				this.nextRoundImageFour.transform.localScale = Vector3.zero;
			}
			if (!ClientInstance.playerInstances.ContainsKey(2))
			{
				this.nextRoundImageFive.transform.localScale = Vector3.zero;
			}
			if (!ClientInstance.playerInstances.ContainsKey(3))
			{
				this.nextRoundImageSix.transform.localScale = Vector3.zero;
			}
			if (ClientInstance.playerInstances.ContainsKey(1))
			{
				this.nextRoundImageFour.transform.localScale = Vector3.one;
			}
			if (ClientInstance.playerInstances.ContainsKey(2))
			{
				this.nextRoundImageFive.transform.localScale = Vector3.one;
			}
			if (ClientInstance.playerInstances.ContainsKey(3))
			{
				this.nextRoundImageSix.transform.localScale = Vector3.one;
				return;
			}
			break;
		case 1:
			if (!ClientInstance.playerInstances.ContainsKey(0))
			{
				this.nextRoundImageFour.transform.localScale = Vector3.zero;
			}
			if (!ClientInstance.playerInstances.ContainsKey(2))
			{
				this.nextRoundImageFive.transform.localScale = Vector3.zero;
			}
			if (!ClientInstance.playerInstances.ContainsKey(3))
			{
				this.nextRoundImageSix.transform.localScale = Vector3.zero;
			}
			if (ClientInstance.playerInstances.ContainsKey(0))
			{
				this.nextRoundImageFour.transform.localScale = Vector3.one;
			}
			if (ClientInstance.playerInstances.ContainsKey(2))
			{
				this.nextRoundImageFive.transform.localScale = Vector3.one;
			}
			if (ClientInstance.playerInstances.ContainsKey(3))
			{
				this.nextRoundImageSix.transform.localScale = Vector3.one;
				return;
			}
			break;
		case 2:
			if (!ClientInstance.playerInstances.ContainsKey(0))
			{
				this.nextRoundImageFour.transform.localScale = Vector3.zero;
			}
			if (!ClientInstance.playerInstances.ContainsKey(1))
			{
				this.nextRoundImageFive.transform.localScale = Vector3.zero;
			}
			if (!ClientInstance.playerInstances.ContainsKey(3))
			{
				this.nextRoundImageSix.transform.localScale = Vector3.zero;
			}
			if (ClientInstance.playerInstances.ContainsKey(0))
			{
				this.nextRoundImageFour.transform.localScale = Vector3.one;
			}
			if (ClientInstance.playerInstances.ContainsKey(1))
			{
				this.nextRoundImageFive.transform.localScale = Vector3.one;
			}
			if (ClientInstance.playerInstances.ContainsKey(3))
			{
				this.nextRoundImageSix.transform.localScale = Vector3.one;
				return;
			}
			break;
		case 3:
			if (!ClientInstance.playerInstances.ContainsKey(0))
			{
				this.nextRoundImageFour.transform.localScale = Vector3.zero;
			}
			if (!ClientInstance.playerInstances.ContainsKey(1))
			{
				this.nextRoundImageFive.transform.localScale = Vector3.zero;
			}
			if (!ClientInstance.playerInstances.ContainsKey(2))
			{
				this.nextRoundImageSix.transform.localScale = Vector3.zero;
			}
			if (ClientInstance.playerInstances.ContainsKey(0))
			{
				this.nextRoundImageFour.transform.localScale = Vector3.one;
			}
			if (ClientInstance.playerInstances.ContainsKey(1))
			{
				this.nextRoundImageFive.transform.localScale = Vector3.one;
			}
			if (ClientInstance.playerInstances.ContainsKey(2))
			{
				this.nextRoundImageSix.transform.localScale = Vector3.one;
			}
			break;
		default:
			return;
		}
	}

	// Token: 0x06000CE8 RID: 3304 RVA: 0x00057BBC File Offset: 0x00055DBC
	public virtual void NetworkInitialize___Early()
	{
		if (this.NetworkInitializeEarly_RoundManager_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeEarly_RoundManager_Assembly-CSharp.dll = true;
		base.RegisterServerRpc(0U, new ServerRpcDelegate(this.RpcReader___Server_CmdEndRound_3316948804));
		base.RegisterObserversRpc(1U, new ClientRpcDelegate(this.RpcReader___Observers_EndRoundObservers_3316948804));
	}

	// Token: 0x06000CE9 RID: 3305 RVA: 0x00057C08 File Offset: 0x00055E08
	public virtual void NetworkInitialize__Late()
	{
		if (this.NetworkInitializeLate_RoundManager_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeLate_RoundManager_Assembly-CSharp.dll = true;
	}

	// Token: 0x06000CEA RID: 3306 RVA: 0x00057C1B File Offset: 0x00055E1B
	public override void NetworkInitializeIfDisabled()
	{
		this.NetworkInitialize___Early();
		this.NetworkInitialize__Late();
	}

	// Token: 0x06000CEB RID: 3307 RVA: 0x00057C2C File Offset: 0x00055E2C
	private void RpcWriter___Server_CmdEndRound_3316948804(int winningTeamId)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteInt32(winningTeamId, AutoPackType.Packed);
		base.SendServerRpc(0U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000CEC RID: 3308 RVA: 0x00057CD8 File Offset: 0x00055ED8
	public void RpcLogic___CmdEndRound_3316948804(int winningTeamId)
	{
		this.EndRoundObservers(winningTeamId);
	}

	// Token: 0x06000CED RID: 3309 RVA: 0x00057CE4 File Offset: 0x00055EE4
	private void RpcReader___Server_CmdEndRound_3316948804(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		int num = PooledReader0.ReadInt32(AutoPackType.Packed);
		if (!base.IsServer)
		{
			return;
		}
		this.RpcLogic___CmdEndRound_3316948804(num);
	}

	// Token: 0x06000CEE RID: 3310 RVA: 0x00057D1C File Offset: 0x00055F1C
	private void RpcWriter___Observers_EndRoundObservers_3316948804(int winningTeamId)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteInt32(winningTeamId, AutoPackType.Packed);
		base.SendObserversRpc(1U, writer, channel, DataOrderType.Default, false, false, false);
		writer.Store();
	}

	// Token: 0x06000CEF RID: 3311 RVA: 0x00057DD7 File Offset: 0x00055FD7
	public void RpcLogic___EndRoundObservers_3316948804(int winningTeamId)
	{
		this.NextRoundCall(LobbyController.Instance.LocalPlayerController.PlayerId, ScoreManager.Instance.GetTeamId(LobbyController.Instance.LocalPlayerController.PlayerId) == winningTeamId, winningTeamId);
	}

	// Token: 0x06000CF0 RID: 3312 RVA: 0x00057E0C File Offset: 0x0005600C
	private void RpcReader___Observers_EndRoundObservers_3316948804(PooledReader PooledReader0, Channel channel)
	{
		int num = PooledReader0.ReadInt32(AutoPackType.Packed);
		if (!base.IsClient)
		{
			return;
		}
		this.RpcLogic___EndRoundObservers_3316948804(num);
	}

	// Token: 0x06000CF1 RID: 3313 RVA: 0x00057E44 File Offset: 0x00056044
	public virtual void Awake___UserLogic()
	{
		RoundManager.Instance = this;
		this.selfPlusOne = GameObject.Find("SelfPlusOne");
		this.enemyPlusOne[0] = GameObject.Find("Enemy1PlusOne");
		this.enemyPlusOne[1] = GameObject.Find("Enemy2PlusOne");
		this.enemyPlusOne[2] = GameObject.Find("Enemy3PlusOne");
		this.container = GameObject.Find("--END ROUND SCREEN--");
		this.background = GameObject.Find("RoundBg");
		this.nextRoundImageOne = GameObject.Find("Score_One").transform;
		this.nextRoundImageTwo = GameObject.Find("Score_Two").transform;
		this.nextRoundImageThree = GameObject.Find("Score_Three").transform;
		this.nextRoundImageFour = GameObject.Find("Score_Four").transform;
		this.nextRoundImageFive = GameObject.Find("Score_Five").transform;
		this.nextRoundImageSix = GameObject.Find("Score_Six").transform;
		this.nextRoundImageOneRestPos = GameObject.Find("PosOneRest").transform;
		this.nextRoundImageTwoRestPos = GameObject.Find("PosTwoRest").transform;
		this.nextRoundImageThreeRestPos = GameObject.Find("PosThreeRest").transform;
		this.nextRoundImageFourRestPos = GameObject.Find("PosFourRest").transform;
		this.nextRoundImageFiveRestPos = GameObject.Find("PosFiveRest").transform;
		this.nextRoundImageSixRestPos = GameObject.Find("PosSixRest").transform;
		this.nextRoundImageOneActivePos = GameObject.Find("PosOneActive").transform;
		this.nextRoundImageTwoActivePos = GameObject.Find("PosTwoActive").transform;
		this.nextRoundImageThreeActivePos = GameObject.Find("PosThreeActive").transform;
		this.nextRoundImageFourActivePos = GameObject.Find("PosFourActive").transform;
		this.nextRoundImageFiveActivePos = GameObject.Find("PosFiveActive").transform;
		this.nextRoundImageSixActivePos = GameObject.Find("PosSixActive").transform;
		this.playerName = this.nextRoundImageThree.GetChild(0).GetComponent<TextMeshProUGUI>();
		this.enemyName[0] = this.nextRoundImageFour.GetChild(0).GetComponent<TextMeshProUGUI>();
		this.enemyName[1] = this.nextRoundImageFive.GetChild(0).GetComponent<TextMeshProUGUI>();
		this.enemyName[2] = this.nextRoundImageSix.GetChild(0).GetComponent<TextMeshProUGUI>();
		this.playerScore = GameObject.Find("LocalPlayerScore").GetComponent<TextMeshProUGUI>();
		this.enemyScore[0] = GameObject.Find("Enemy1PlayerScore").GetComponent<TextMeshProUGUI>();
		this.enemyScore[1] = GameObject.Find("Enemy2PlayerScore").GetComponent<TextMeshProUGUI>();
		this.enemyScore[2] = GameObject.Find("Enemy3PlayerScore").GetComponent<TextMeshProUGUI>();
		this.winText = this.nextRoundImageOne.GetChild(0).GetComponent<TextMeshProUGUI>();
		this.roundText = this.nextRoundImageTwo.GetChild(0).GetComponent<TextMeshProUGUI>();
	}

	// Token: 0x04000B13 RID: 2835
	public static RoundManager Instance;

	// Token: 0x04000B14 RID: 2836
	public string[] names;

	// Token: 0x04000B15 RID: 2837
	public int[] scores;

	// Token: 0x04000B16 RID: 2838
	public ClientInstance[] players;

	// Token: 0x04000B17 RID: 2839
	[SerializeField]
	private float tweenTime = 0.75f;

	// Token: 0x04000B18 RID: 2840
	[SerializeField]
	public AudioClip[] swooshClip;

	// Token: 0x04000B19 RID: 2841
	[SerializeField]
	private AudioClip winClip;

	// Token: 0x04000B1A RID: 2842
	[SerializeField]
	private AudioClip loseClip;

	// Token: 0x04000B1B RID: 2843
	[SerializeField]
	private AudioClip plusOneClip;

	// Token: 0x04000B1C RID: 2844
	[SerializeField]
	private Color winColor;

	// Token: 0x04000B1D RID: 2845
	[SerializeField]
	private Color loseColor;

	// Token: 0x04000B1E RID: 2846
	private GameObject container;

	// Token: 0x04000B1F RID: 2847
	private GameObject background;

	// Token: 0x04000B20 RID: 2848
	private GameObject selfPlusOne;

	// Token: 0x04000B21 RID: 2849
	private GameObject[] enemyPlusOne = new GameObject[3];

	// Token: 0x04000B22 RID: 2850
	private TextMeshProUGUI playerName;

	// Token: 0x04000B23 RID: 2851
	private TextMeshProUGUI[] enemyName = new TextMeshProUGUI[3];

	// Token: 0x04000B24 RID: 2852
	private TextMeshProUGUI playerScore;

	// Token: 0x04000B25 RID: 2853
	private TextMeshProUGUI[] enemyScore = new TextMeshProUGUI[3];

	// Token: 0x04000B26 RID: 2854
	private TextMeshProUGUI winText;

	// Token: 0x04000B27 RID: 2855
	private TextMeshProUGUI roundText;

	// Token: 0x04000B28 RID: 2856
	private Transform nextRoundImageOne;

	// Token: 0x04000B29 RID: 2857
	private Transform nextRoundImageTwo;

	// Token: 0x04000B2A RID: 2858
	private Transform nextRoundImageThree;

	// Token: 0x04000B2B RID: 2859
	private Transform nextRoundImageFour;

	// Token: 0x04000B2C RID: 2860
	private Transform nextRoundImageFive;

	// Token: 0x04000B2D RID: 2861
	private Transform nextRoundImageSix;

	// Token: 0x04000B2E RID: 2862
	private Transform nextRoundImageOneRestPos;

	// Token: 0x04000B2F RID: 2863
	private Transform nextRoundImageOneActivePos;

	// Token: 0x04000B30 RID: 2864
	private Transform nextRoundImageTwoRestPos;

	// Token: 0x04000B31 RID: 2865
	private Transform nextRoundImageTwoActivePos;

	// Token: 0x04000B32 RID: 2866
	private Transform nextRoundImageThreeRestPos;

	// Token: 0x04000B33 RID: 2867
	private Transform nextRoundImageThreeActivePos;

	// Token: 0x04000B34 RID: 2868
	private Transform nextRoundImageFourRestPos;

	// Token: 0x04000B35 RID: 2869
	private Transform nextRoundImageFourActivePos;

	// Token: 0x04000B36 RID: 2870
	private Transform nextRoundImageFiveRestPos;

	// Token: 0x04000B37 RID: 2871
	private Transform nextRoundImageFiveActivePos;

	// Token: 0x04000B38 RID: 2872
	private Transform nextRoundImageSixRestPos;

	// Token: 0x04000B39 RID: 2873
	private Transform nextRoundImageSixActivePos;

	// Token: 0x04000B3A RID: 2874
	public IEnumerator InterfaceSetupCoroutine;

	// Token: 0x04000B3B RID: 2875
	private bool NetworkInitializeEarly_RoundManager_Assembly-CSharp.dll;

	// Token: 0x04000B3C RID: 2876
	private bool NetworkInitializeLate_RoundManager_Assembly-CSharp.dll;
}

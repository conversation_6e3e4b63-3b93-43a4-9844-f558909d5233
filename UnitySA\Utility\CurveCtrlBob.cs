﻿using System;
using UnityEngine;

namespace UnitySA.Utility
{
	// Token: 0x02000178 RID: 376
	[Serializable]
	public class CurveCtrlBob
	{
		// Token: 0x06000F99 RID: 3993 RVA: 0x00066A94 File Offset: 0x00064C94
		public void Setup(Camera camera, float bobBaseInterval)
		{
			this.m_BobBaseInterval = bobBaseInterval;
			this.m_OriginalCameraPosition = camera.transform.localPosition;
			this.m_Time = this.Bobcurve[this.Bobcurve.length - 1].time;
		}

		// Token: 0x06000F9A RID: 3994 RVA: 0x00066AE0 File Offset: 0x00064CE0
		public Vector3 DoHeadBob(float speed)
		{
			float num = this.m_OriginalCameraPosition.x + this.Bobcurve.Evaluate(this.m_CyclePositionX) * this.HorizontalBobRange;
			float num2 = this.m_OriginalCameraPosition.y + this.Bobcurve.Evaluate(this.m_CyclePositionY) * this.VerticalBobRange;
			this.m_CyclePositionX += speed * Time.deltaTime / this.m_BobBaseInterval;
			this.m_CyclePositionY += speed * Time.deltaTime / this.m_BobBaseInterval * this.VerticaltoHorizontalRatio;
			if (this.m_CyclePositionX > this.m_Time)
			{
				this.m_CyclePositionX -= this.m_Time;
			}
			if (this.m_CyclePositionY > this.m_Time)
			{
				this.m_CyclePositionY -= this.m_Time;
			}
			return new Vector3(num, num2, 0f);
		}

		// Token: 0x04000E7E RID: 3710
		public float HorizontalBobRange = 0.33f;

		// Token: 0x04000E7F RID: 3711
		public float VerticalBobRange = 0.33f;

		// Token: 0x04000E80 RID: 3712
		public AnimationCurve Bobcurve = new AnimationCurve(new Keyframe[]
		{
			new Keyframe(0f, 0f),
			new Keyframe(0.5f, 1f),
			new Keyframe(1f, 0f),
			new Keyframe(1.5f, -1f),
			new Keyframe(2f, 0f)
		});

		// Token: 0x04000E81 RID: 3713
		public float VerticaltoHorizontalRatio = 1f;

		// Token: 0x04000E82 RID: 3714
		private float m_CyclePositionX;

		// Token: 0x04000E83 RID: 3715
		private float m_CyclePositionY;

		// Token: 0x04000E84 RID: 3716
		private float m_BobBaseInterval;

		// Token: 0x04000E85 RID: 3717
		private Vector3 m_OriginalCameraPosition;

		// Token: 0x04000E86 RID: 3718
		private float m_Time;
	}
}

﻿using System;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

// Token: 0x02000142 RID: 322
public class WeaponPresetManager : MonoBehaviour
{
	// Token: 0x170000DD RID: 221
	// (get) Token: 0x06000EA3 RID: 3747 RVA: 0x000608A9 File Offset: 0x0005EAA9
	// (set) Token: 0x06000EA4 RID: 3748 RVA: 0x000608B0 File Offset: 0x0005EAB0
	public static WeaponPresetManager Instance { get; private set; }

	// Token: 0x06000EA5 RID: 3749 RVA: 0x000608B8 File Offset: 0x0005EAB8
	private void Awake()
	{
		if (WeaponPresetManager.Instance != null)
		{
			return;
		}
		WeaponPresetManager.Instance = this;
	}

	// Token: 0x06000EA6 RID: 3750 RVA: 0x000608CE File Offset: 0x0005EACE
	private void Start()
	{
		this.PopulatePresetList();
	}

	// Token: 0x06000EA7 RID: 3751 RVA: 0x000608D8 File Offset: 0x0005EAD8
	public void PopulatePresetList()
	{
		foreach (object obj in this.buttonContainer)
		{
			global::UnityEngine.Object.Destroy(((Transform)obj).gameObject);
		}
		using (Dictionary<string, WeaponPreset>.KeyCollection.Enumerator enumerator2 = this.weaponPresetUtil.Presets.Keys.GetEnumerator())
		{
			while (enumerator2.MoveNext())
			{
				string presetName = enumerator2.Current;
				Button button = global::UnityEngine.Object.Instantiate<Button>(this.buttonPrefab, this.buttonContainer);
				button.GetComponentInChildren<TMP_Text>().text = presetName;
				button.onClick.AddListener(delegate
				{
					this.weaponPresetUtil.LoadPreset(presetName);
				});
				button.transform.Find("DeleteButton").GetComponent<Button>().onClick.AddListener(delegate
				{
					if (this.weaponPresetUtil.Presets.Remove(presetName))
					{
						this.PopulatePresetList();
					}
				});
			}
		}
	}

	// Token: 0x04000D40 RID: 3392
	[SerializeField]
	private Transform buttonContainer;

	// Token: 0x04000D41 RID: 3393
	[SerializeField]
	private Button buttonPrefab;

	// Token: 0x04000D42 RID: 3394
	[SerializeField]
	public TMP_InputField presetNameField;

	// Token: 0x04000D43 RID: 3395
	[SerializeField]
	public WeaponPresetUtility weaponPresetUtil;
}

﻿using System;
using UnityEngine;

// Token: 0x0200014C RID: 332
public class UseArea : MonoBehaviour
{
	// Token: 0x06000ED6 RID: 3798 RVA: 0x00061883 File Offset: 0x0005FA83
	private void OnTriggerEnter(Collider Intruder)
	{
		if (Intruder.tag == "Player")
		{
			this.PlayerIsHere = true;
		}
	}

	// Token: 0x06000ED7 RID: 3799 RVA: 0x0006189E File Offset: 0x0005FA9E
	private void OnTriggerExit(Collider Intruder)
	{
		if (Intruder.tag == "Player")
		{
			this.PlayerIsHere = false;
		}
	}

	// Token: 0x06000ED8 RID: 3800 RVA: 0x000618B9 File Offset: 0x0005FAB9
	public void Use()
	{
		if (this.Glass && this.PlayerIsHere)
		{
			this.Glass.Shatter(Vector2.zero, this.Glass.transform.forward);
		}
	}

	// Token: 0x04000D6F RID: 3439
	private bool PlayerIsHere;

	// Token: 0x04000D70 RID: 3440
	public ShatterableGlass Glass;
}

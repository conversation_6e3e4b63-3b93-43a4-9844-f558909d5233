﻿using System;
using System.Collections;
using System.Threading.Tasks;
using FishNet;
using FishNet.Managing.Scened;
using FishNet.Object;
using TMPro;
using UnityEngine;
using UnityEngine.SceneManagement;

// Token: 0x02000104 RID: 260
public class SceneLoader : MonoBehaviour
{
	// Token: 0x06000D11 RID: 3345 RVA: 0x00058F81 File Offset: 0x00057181
	private void Awake()
	{
		if (SceneLoader.Instance == null)
		{
			SceneLoader.Instance = this;
		}
	}

	// Token: 0x06000D12 RID: 3346 RVA: 0x00058F96 File Offset: 0x00057196
	private void Start()
	{
		this.Shuffle(this.SCENE_NAME);
	}

	// Token: 0x06000D13 RID: 3347 RVA: 0x00058FA4 File Offset: 0x000571A4
	private void Update()
	{
		if (Input.GetKeyDown(KeyCode.V))
		{
			this.LoadScene();
		}
		this.currentScene = global::UnityEngine.SceneManagement.SceneManager.GetActiveScene().name;
	}

	// Token: 0x06000D14 RID: 3348 RVA: 0x00058FD3 File Offset: 0x000571D3
	public void LoadScene()
	{
		if (this.canSwitch)
		{
			this.LoadSceneInternal();
		}
	}

	// Token: 0x06000D15 RID: 3349 RVA: 0x00058FE4 File Offset: 0x000571E4
	public void LoadSceneInternal()
	{
		base.StartCoroutine(this.CanStartAgain());
		GameObject.FindGameObjectsWithTag("ClientInstance");
		SceneLoadData sceneLoadData = new SceneLoadData(this.SCENE_NAME[this.sceneIndex]);
		sceneLoadData.ReplaceScenes = ReplaceOption.All;
		InstanceFinder.SceneManager.LoadGlobalScenes(sceneLoadData);
		if (this.sceneIndex < this.SCENE_NAME.Length - 1)
		{
			this.sceneIndex++;
			return;
		}
		this.sceneIndex = 0;
	}

	// Token: 0x06000D16 RID: 3350 RVA: 0x00059056 File Offset: 0x00057256
	private IEnumerator CanStartAgain()
	{
		this.canSwitch = false;
		yield return new WaitForSeconds(0.5f);
		this.canSwitch = true;
		yield break;
	}

	// Token: 0x06000D17 RID: 3351 RVA: 0x00059068 File Offset: 0x00057268
	public void LoadSceneSteam()
	{
		GameObject[] array = GameObject.FindGameObjectsWithTag("ClientInstance");
		SceneLoadData sceneLoadData = new SceneLoadData(this.SCENE_NAME[this.sceneIndex]);
		sceneLoadData.MovedNetworkObjects = new NetworkObject[array.Length];
		LoadOptions loadOptions = new LoadOptions
		{
			AllowStacking = true
		};
		sceneLoadData.Options = loadOptions;
		for (int i = 0; i < array.Length; i++)
		{
			sceneLoadData.MovedNetworkObjects[i] = array[i].GetComponent<NetworkObject>();
		}
		sceneLoadData.ReplaceScenes = ReplaceOption.All;
		InstanceFinder.SceneManager.LoadConnectionScenes(sceneLoadData);
	}

	// Token: 0x06000D18 RID: 3352 RVA: 0x000590E8 File Offset: 0x000572E8
	public void LoadSceneFromMenu()
	{
		SceneLoadData sceneLoadData = new SceneLoadData(this.SCENE_NAME[this.sceneIndex]);
		sceneLoadData.ReplaceScenes = ReplaceOption.All;
		InstanceFinder.SceneManager.LoadGlobalScenes(sceneLoadData);
		if (this.sceneIndex < this.SCENE_NAME.Length - 1)
		{
			this.sceneIndex++;
			return;
		}
		this.sceneIndex = 0;
	}

	// Token: 0x06000D19 RID: 3353 RVA: 0x0004BE32 File Offset: 0x0004A032
	public void ReturnToMenu()
	{
		global::UnityEngine.SceneManagement.SceneManager.LoadScene("MainMenu");
	}

	// Token: 0x06000D1A RID: 3354 RVA: 0x00059144 File Offset: 0x00057344
	public void Shuffle(string[] texts)
	{
		for (int i = 0; i < texts.Length; i++)
		{
			string text = texts[i];
			int num = global::UnityEngine.Random.Range(i, texts.Length);
			texts[i] = texts[num];
			texts[num] = text;
		}
	}

	// Token: 0x06000D1B RID: 3355 RVA: 0x00059178 File Offset: 0x00057378
	public async void LoadScene(string sceneName)
	{
		AsyncOperation scene = global::UnityEngine.SceneManagement.SceneManager.LoadSceneAsync(sceneName);
		this._loaderCanvas.SetActive(true);
		do
		{
			await Task.Delay(100);
		}
		while (scene.progress < 0.9f);
		this._loaderCanvas.SetActive(false);
	}

	// Token: 0x04000B48 RID: 2888
	public string[] SCENE_NAME;

	// Token: 0x04000B49 RID: 2889
	public int sceneIndex;

	// Token: 0x04000B4A RID: 2890
	private float respawnTimer;

	// Token: 0x04000B4B RID: 2891
	public string currentScene;

	// Token: 0x04000B4C RID: 2892
	private float timer;

	// Token: 0x04000B4D RID: 2893
	public bool canSwitch = true;

	// Token: 0x04000B4E RID: 2894
	public PlayerTracker manager;

	// Token: 0x04000B4F RID: 2895
	[SerializeField]
	public GameObject _loaderCanvas;

	// Token: 0x04000B50 RID: 2896
	[SerializeField]
	public TextMeshProUGUI sceneText;

	// Token: 0x04000B51 RID: 2897
	public static SceneLoader Instance;
}

﻿using System;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

// Token: 0x020000B3 RID: 179
public class AudioPositionSlider : <PERSON>o<PERSON><PERSON><PERSON>our, IPointerDownHandler, IEventSystemHandler
{
	// Token: 0x06000A37 RID: 2615 RVA: 0x0004B0CD File Offset: 0x000492CD
	private void Start()
	{
		this.slider = base.GetComponent<Slider>();
	}

	// Token: 0x06000A38 RID: 2616 RVA: 0x0004B0DB File Offset: 0x000492DB
	private void Update()
	{
		this.slider.value = this.musicController.audio.time / this.musicController.menuTracks[this.musicController.currentTrackId].length;
	}

	// Token: 0x06000A39 RID: 2617 RVA: 0x0004B115 File Offset: 0x00049315
	public void OnPointerDown(PointerEventData eventData)
	{
		this.musicController.SetAudioPosition(this.slider.value);
	}

	// Token: 0x04000909 RID: 2313
	private Slider slider;

	// Token: 0x0400090A RID: 2314
	[SerializeField]
	private SetMenuMusicVolume musicController;
}

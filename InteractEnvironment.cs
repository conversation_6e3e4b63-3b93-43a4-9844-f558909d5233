﻿using System;
using FishNet.Object;
using UnityEngine;

// Token: 0x0200007C RID: 124
public abstract class InteractEnvironment : NetworkBehaviour
{
	// Token: 0x0600053A RID: 1338 RVA: 0x00021D33 File Offset: 0x0001FF33
	public virtual void Awake()
	{
		this.NetworkInitialize___Early();
		this.Awake___UserLogic();
		this.NetworkInitialize__Late();
	}

	// Token: 0x0600053B RID: 1339
	public abstract void OnInteract(Transform player);

	// Token: 0x0600053C RID: 1340
	public abstract void OnFocus();

	// Token: 0x0600053D RID: 1341
	public abstract void OnLoseFocus();

	// Token: 0x0600053F RID: 1343 RVA: 0x00021D47 File Offset: 0x0001FF47
	public virtual void NetworkInitialize___Early()
	{
		if (this.NetworkInitializeEarly_InteractEnvironment_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeEarly_InteractEnvironment_Assembly-CSharp.dll = true;
	}

	// Token: 0x06000540 RID: 1344 RVA: 0x00021D5A File Offset: 0x0001FF5A
	public virtual void NetworkInitialize__Late()
	{
		if (this.NetworkInitializeLate_InteractEnvironment_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeLate_InteractEnvironment_Assembly-CSharp.dll = true;
	}

	// Token: 0x06000541 RID: 1345 RVA: 0x00021D6D File Offset: 0x0001FF6D
	public override void NetworkInitializeIfDisabled()
	{
		this.NetworkInitialize___Early();
		this.NetworkInitialize__Late();
	}

	// Token: 0x06000542 RID: 1346 RVA: 0x00021D7B File Offset: 0x0001FF7B
	public virtual void Awake___UserLogic()
	{
		base.gameObject.layer = 19;
	}

	// Token: 0x04000518 RID: 1304
	public string popupText;

	// Token: 0x04000519 RID: 1305
	private bool NetworkInitializeEarly_InteractEnvironment_Assembly-CSharp.dll;

	// Token: 0x0400051A RID: 1306
	private bool NetworkInitializeLate_InteractEnvironment_Assembly-CSharp.dll;
}

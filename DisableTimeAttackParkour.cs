﻿using System;
using TMPro;
using UnityEngine;
using UnityEngine.InputSystem;

// Token: 0x0200004B RID: 75
public class DisableTimeAttackParkour : InteractEnvironment
{
	// Token: 0x06000375 RID: 885 RVA: 0x0001A9F8 File Offset: 0x00018BF8
	private void Start()
	{
		this.restPos = base.transform.localPosition;
		this.timerManager = global::UnityEngine.Object.FindObjectOfType<TimerManager>();
		this.text.text = (this.timerManager.enabled ? "Disable Parkour Races" : "Enable Parkour Races");
	}

	// Token: 0x06000376 RID: 886 RVA: 0x0001AA45 File Offset: 0x00018C45
	public override void OnFocus()
	{
		PauseManager.Instance.interactPopup.gameObject.SetActive(true);
		PauseManager.Instance.interactPopup.text = this.popupText.ToLower();
		this.focused = true;
	}

	// Token: 0x06000377 RID: 887 RVA: 0x0001AA7D File Offset: 0x00018C7D
	public override void OnInteract(Transform player)
	{
		this.timerManager.Enable(!this.timerManager.enabled);
		this.text.text = (this.timerManager.enabled ? "Disable time attack course" : "Enable time attack course");
	}

	// Token: 0x06000378 RID: 888 RVA: 0x0001AABC File Offset: 0x00018CBC
	public override void OnLoseFocus()
	{
		this.focused = false;
	}

	// Token: 0x06000379 RID: 889 RVA: 0x0001AAC8 File Offset: 0x00018CC8
	private void Update()
	{
		Vector3 vector = (this.focused ? (this.restPos + new Vector3(this.focusOffset, 0f, 0f)) : (this.act ? (this.restPos + new Vector3(this.pressOffset, 0f, 0f)) : this.restPos));
		base.transform.localPosition = Vector3.Lerp(base.transform.localPosition, vector, this.moveSpeed * Time.deltaTime);
	}

	// Token: 0x0600037B RID: 891 RVA: 0x0001AB88 File Offset: 0x00018D88
	public override void NetworkInitialize___Early()
	{
		if (this.NetworkInitializeEarly_DisableTimeAttackParkour_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeEarly_DisableTimeAttackParkour_Assembly-CSharp.dll = true;
		base.NetworkInitialize___Early();
	}

	// Token: 0x0600037C RID: 892 RVA: 0x0001ABA1 File Offset: 0x00018DA1
	public override void NetworkInitialize__Late()
	{
		if (this.NetworkInitializeLate_DisableTimeAttackParkour_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeLate_DisableTimeAttackParkour_Assembly-CSharp.dll = true;
		base.NetworkInitialize__Late();
	}

	// Token: 0x0600037D RID: 893 RVA: 0x0001ABBA File Offset: 0x00018DBA
	public override void NetworkInitializeIfDisabled()
	{
		this.NetworkInitialize___Early();
		this.NetworkInitialize__Late();
	}

	// Token: 0x0600037E RID: 894 RVA: 0x0001ABC8 File Offset: 0x00018DC8
	public override void Awake()
	{
		this.NetworkInitialize___Early();
		base.Awake();
		this.NetworkInitialize__Late();
	}

	// Token: 0x0600037F RID: 895 RVA: 0x000023D6 File Offset: 0x000005D6
	public override void Awake___UserLogic()
	{
	}

	// Token: 0x040003E7 RID: 999
	[SerializeField]
	private InputAction menu;

	// Token: 0x040003E8 RID: 1000
	[SerializeField]
	private InputAction menu2;

	// Token: 0x040003E9 RID: 1001
	private Vector3 restPos;

	// Token: 0x040003EA RID: 1002
	[SerializeField]
	private float focusOffset = 0.15f;

	// Token: 0x040003EB RID: 1003
	[SerializeField]
	private float pressOffset = 0.33f;

	// Token: 0x040003EC RID: 1004
	[SerializeField]
	private float moveSpeed = 12f;

	// Token: 0x040003ED RID: 1005
	[Space]
	[SerializeField]
	private TMP_Text text;

	// Token: 0x040003EE RID: 1006
	private TimerManager timerManager;

	// Token: 0x040003EF RID: 1007
	private bool act;

	// Token: 0x040003F0 RID: 1008
	private bool focused;

	// Token: 0x040003F1 RID: 1009
	private bool rematch = true;

	// Token: 0x040003F2 RID: 1010
	private bool NetworkInitializeEarly_DisableTimeAttackParkour_Assembly-CSharp.dll;

	// Token: 0x040003F3 RID: 1011
	private bool NetworkInitializeLate_DisableTimeAttackParkour_Assembly-CSharp.dll;
}

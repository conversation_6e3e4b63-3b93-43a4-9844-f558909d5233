﻿using System;
using System.Collections.Generic;
using Steamworks;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

// Token: 0x0200013C RID: 316
public class VictoryMenuUI : MonoBehaviour
{
	// Token: 0x06000E8F RID: 3727 RVA: 0x0005FF68 File Offset: 0x0005E168
	private void Start()
	{
		List<int> list = new List<int>(ScoreManager.Instance.TeamIdToPlayerIds.Keys);
		list.Sort(delegate(int a, int b)
		{
			int points2 = ScoreManager.Instance.GetPoints(a);
			return ScoreManager.Instance.GetPoints(b).CompareTo(points2);
		});
		int num = 0;
		for (int i = 0; i < list.Count; i++)
		{
			int num2 = list[i];
			int points = ScoreManager.Instance.GetPoints(num2);
			foreach (int num3 in ScoreManager.Instance.GetPlayerIdsForTeam(num2))
			{
				ClientInstance clientInstance;
				if (ClientInstance.playerInstances.TryGetValue(num3, out clientInstance))
				{
					GameObject gameObject = global::UnityEngine.Object.Instantiate<GameObject>(this.positionCell, this.cellParent.transform);
					gameObject.SetActive(true);
					gameObject.transform.localPosition = new Vector3(0f, (float)(num * -3), 0f);
					num++;
					bool flag = i == 0;
					gameObject.transform.GetChild(0).gameObject.SetActive(flag);
					gameObject.transform.GetChild(1).GetComponent<TMP_Text>().text = VictoryMenuUI.ToOrdinal(num);
					gameObject.transform.GetChild(2).GetComponent<TMP_Text>().text = clientInstance.PlayerName;
					gameObject.transform.GetChild(3).GetComponent<TMP_Text>().text = "Won " + points.ToString() + " rounds!";
					int largeFriendAvatar = SteamFriends.GetLargeFriendAvatar((CSteamID)clientInstance.PlayerSteamID);
					gameObject.transform.GetChild(4).GetChild(0).GetComponent<RawImage>()
						.texture = this.GetSteamImageAsTexture(largeFriendAvatar);
				}
			}
		}
		Settings.Instance.IncreaseGamesPlayed();
		int num4 = list[0];
		if (ScoreManager.Instance.GetTeamId(LobbyController.Instance.LocalPlayerController.PlayerId) == num4)
		{
			Settings.Instance.IncreaseGamesWon();
			this.victoryDefeatText.color = this.victoryColor;
			this.victoryDefeatText.text = "Victory";
			return;
		}
		Settings.Instance.IncreaseGamesLost();
		this.victoryDefeatText.color = this.defeatColor;
		this.victoryDefeatText.text = "Defeat";
	}

	// Token: 0x06000E90 RID: 3728 RVA: 0x000601CC File Offset: 0x0005E3CC
	private Texture2D GetSteamImageAsTexture(int iImage)
	{
		Texture2D texture2D = null;
		uint num;
		uint num2;
		if (SteamUtils.GetImageSize(iImage, out num, out num2))
		{
			byte[] array = new byte[num * num2 * 4U];
			if (SteamUtils.GetImageRGBA(iImage, array, (int)(num * num2 * 4U)))
			{
				texture2D = new Texture2D((int)num, (int)num2, TextureFormat.RGBA32, false, true);
				texture2D.LoadRawTextureData(array);
				texture2D.Apply();
			}
		}
		return texture2D;
	}

	// Token: 0x06000E91 RID: 3729 RVA: 0x0006021C File Offset: 0x0005E41C
	public static string ToOrdinal(int n)
	{
		return n.ToString() + ((n % 100 >= 11 && n % 100 <= 13) ? "th" : ((n % 10 == 1) ? "st" : ((n % 10 == 2) ? "nd" : ((n % 10 == 3) ? "rd" : "th"))));
	}

	// Token: 0x04000D1C RID: 3356
	[SerializeField]
	private GameObject positionCell;

	// Token: 0x04000D1D RID: 3357
	[SerializeField]
	private GameObject cellParent;

	// Token: 0x04000D1E RID: 3358
	[Space]
	[SerializeField]
	private TMP_Text victoryDefeatText;

	// Token: 0x04000D1F RID: 3359
	[SerializeField]
	private Color victoryColor;

	// Token: 0x04000D20 RID: 3360
	[SerializeField]
	private Color defeatColor;
}

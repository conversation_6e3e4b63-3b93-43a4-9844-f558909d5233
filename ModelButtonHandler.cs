﻿using System;
using UnityEngine;
using UnityEngine.EventSystems;

// Token: 0x0200012D RID: 301
public class ModelButtonHandler : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IPointerEnterHandler, IEventSystemHandler, IPointerExitHandler, IPointerDownHandler, IPointerUpHandler, ISelectHandler, IDeselectHandler
{
	// Token: 0x06000E59 RID: 3673 RVA: 0x0005F673 File Offset: 0x0005D873
	public void OnPointerEnter(PointerEventData eventData)
	{
		this.button.Hover();
	}

	// Token: 0x06000E5A RID: 3674 RVA: 0x0005F680 File Offset: 0x0005D880
	public void OnPointerDown(PointerEventData eventData)
	{
		this.button.Press();
	}

	// Token: 0x06000E5B RID: 3675 RVA: 0x0005F68D File Offset: 0x0005D88D
	public void OnPointerExit(PointerEventData eventData)
	{
		this.button.Leave();
	}

	// Token: 0x06000E5C RID: 3676 RVA: 0x0005F69A File Offset: 0x0005D89A
	public void OnPointerUp(PointerEventData eventData)
	{
		this.button.Release();
	}

	// Token: 0x06000E5D RID: 3677 RVA: 0x0005F6A7 File Offset: 0x0005D8A7
	public void OnSelect(BaseEventData eventData)
	{
		if (PauseManager.Instance.gamepad)
		{
			this.button.Hover();
		}
	}

	// Token: 0x06000E5E RID: 3678 RVA: 0x0005F6C0 File Offset: 0x0005D8C0
	public void OnDeselect(BaseEventData eventData)
	{
		if (PauseManager.Instance.gamepad)
		{
			this.button.Leave();
		}
	}

	// Token: 0x04000CF0 RID: 3312
	[SerializeField]
	private ModelButtonTween button;
}

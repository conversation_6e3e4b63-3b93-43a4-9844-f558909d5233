﻿using System;
using UnityEngine;

// Token: 0x0200006E RID: 110
public class Rotate : MonoBehaviour
{
	// Token: 0x060004C4 RID: 1220 RVA: 0x00020523 File Offset: 0x0001E723
	private void Update()
	{
		base.transform.Rotate(this.rotationAxis * this.rotationSpeed * Time.deltaTime);
	}

	// Token: 0x040004D8 RID: 1240
	[SerializeField]
	private Vector3 rotationAxis = new Vector3(1f, 0f, 0f);

	// Token: 0x040004D9 RID: 1241
	[SerializeField]
	private float rotationSpeed = 50f;
}

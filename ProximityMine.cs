﻿using System;
using System.Collections;
using System.Collections.Generic;
using DG.Tweening;
using FishNet;
using FishNet.Connection;
using FishNet.Managing;
using FishNet.Object;
using FishNet.Object.Delegating;
using FishNet.Object.Synchronizing;
using FishNet.Serializing;
using FishNet.Serializing.Generated;
using FishNet.Transporting;
using UnityEngine;
using UnityEngine.UI;

// Token: 0x020000A4 RID: 164
public class ProximityMine : NetworkBehaviour
{
	// Token: 0x060008F7 RID: 2295 RVA: 0x00041C86 File Offset: 0x0003FE86
	public virtual void Awake()
	{
		this.NetworkInitialize___Early();
		this.Awake___UserLogic();
		this.NetworkInitialize__Late();
	}

	// Token: 0x060008F8 RID: 2296 RVA: 0x00041C9A File Offset: 0x0003FE9A
	private void Start()
	{
		base.StartCoroutine(this.ActivateTrap());
	}

	// Token: 0x060008F9 RID: 2297 RVA: 0x00041CA9 File Offset: 0x0003FEA9
	private void OnEnable()
	{
		PauseManager.OnBeforeSpawn += this.StartNewRound;
	}

	// Token: 0x060008FA RID: 2298 RVA: 0x00041CBC File Offset: 0x0003FEBC
	private void OnDisable()
	{
		PauseManager.OnBeforeSpawn -= this.StartNewRound;
	}

	// Token: 0x060008FB RID: 2299 RVA: 0x00041CD0 File Offset: 0x0003FED0
	[ServerRpc(RequireOwnership = false, RunLocally = true)]
	private void StartNewRound()
	{
		this.RpcWriter___Server_StartNewRound_2166136261();
		this.RpcLogic___StartNewRound_2166136261();
	}

	// Token: 0x060008FC RID: 2300 RVA: 0x00041CE9 File Offset: 0x0003FEE9
	private IEnumerator ActivateTrap()
	{
		yield return new WaitForSeconds((float)(this.instantExplode ? 0 : 1));
		this.canExplode = true;
		this.audio.PlayOneShot(this.activationSound);
		if (!this.instantExplode)
		{
			this.graph.transform.DOScale(new Vector3(1.858382f, 1.858382f, 1.858382f), 0.2f);
		}
		yield return new WaitForSeconds(0.2f);
		Material[] array = new Material[] { this.realMat };
		if (!this.instantExplode)
		{
			this.graph.GetComponent<MeshRenderer>().materials = array;
		}
		if (!this.instantExplode)
		{
			this.indicator.SetActive(true);
		}
		yield break;
	}

	// Token: 0x060008FD RID: 2301 RVA: 0x00041CF8 File Offset: 0x0003FEF8
	private void Update()
	{
		if (this.SyncAccessor_detonated && this.activated)
		{
			this.HandleExplosion();
			this.sync___set_value_detonated(false, true);
			this.activated = false;
		}
	}

	// Token: 0x060008FE RID: 2302 RVA: 0x00041D20 File Offset: 0x0003FF20
	public void HandleExplosion()
	{
		if (!base.IsOwner)
		{
			return;
		}
		Collider[] array = Physics.OverlapSphere(base.transform.position, this.explosionRadius, this.bodyLayer);
		if (array.Length != 0)
		{
			List<PlayerHealth> list = new List<PlayerHealth>();
			for (int i = 0; i < array.Length; i++)
			{
				if (array[i].GetComponentInParent<PlayerHealth>() != null && !list.Contains(array[i].GetComponentInParent<PlayerHealth>()))
				{
					list.Add(array[i].GetComponentInParent<PlayerHealth>());
				}
			}
			this.ph2 = list.ToArray();
			for (int j = 0; j < this.ph2.Length; j++)
			{
				if (this.ph2[j] != null && base.IsOwner && !this.ph2[j].SyncAccessor_isKilled)
				{
					if (this.ph2[j].transform.gameObject == this.SyncAccessor__rootObject)
					{
						if (this.ph2[j].SyncAccessor_health - this.damage <= 0f)
						{
							this.ph2[j].ChangeKilledState(true);
							this.ph2[j].suicide = true;
							this.IncreaseSuicidesAmount();
							this.ph2[j].Explode(false, true, this.ph2[j].gameObject.name, this.ph2[j].transform.position - base.transform.position, this.ragdollEjectForce, base.transform.position);
						}
						this.HitMarker(false);
						this.ph2[j].RemoveHealth(this.damage);
						this.ph2[j].SetKiller(this.SyncAccessor__rootObject.transform);
						this.touched = true;
						if (this.stunMine)
						{
							this.ph2[j].TaserEnemy(this.ph2[j], this.stunTime);
						}
					}
					else if (this.ph2[j].transform.gameObject != this.SyncAccessor__rootObject)
					{
						if (this.ph2[j].SyncAccessor_health - this.damage <= 0f)
						{
							this.ph2[j].ChangeKilledState(true);
							this.KillShockWave();
							this.SendKillLog(this.ph2[j]);
							this.ph2[j].Explode(false, true, this.ph2[j].gameObject.name, this.ph2[j].transform.position - base.transform.position, this.ragdollEjectForce, base.transform.position);
						}
						this.HitMarker(false);
						this.ph2[j].RemoveHealth(this.damage);
						this.ph2[j].SetKiller(this.SyncAccessor__rootObject.transform);
						this.touched2 = true;
						if (this.stunMine)
						{
							this.ph2[j].TaserEnemy(this.ph2[j], this.stunTime);
						}
					}
				}
			}
		}
		this.ExplodeServer();
	}

	// Token: 0x060008FF RID: 2303 RVA: 0x00042022 File Offset: 0x00040222
	[ServerRpc(RunLocally = true)]
	private void ExplodeServer()
	{
		this.RpcWriter___Server_ExplodeServer_2166136261();
		this.RpcLogic___ExplodeServer_2166136261();
	}

	// Token: 0x06000900 RID: 2304 RVA: 0x00042030 File Offset: 0x00040230
	[ObserversRpc]
	private void ExplodeObservers()
	{
		this.RpcWriter___Observers_ExplodeObservers_2166136261();
	}

	// Token: 0x06000901 RID: 2305 RVA: 0x00042044 File Offset: 0x00040244
	private void SendKillLog(PlayerHealth enemyHealth)
	{
		if (this.sendKillLog)
		{
			return;
		}
		this.sendKillLog = true;
		PauseManager.Instance.WriteLog(string.Concat(new string[]
		{
			"<b><color=#",
			PauseManager.Instance.selfNameLogColor,
			">",
			enemyHealth.SyncAccessor_playerValues.SyncAccessor_playerClient.PlayerName,
			"</color></b> was killed with a <b><color=white>",
			this.weaponName,
			"</color></b> by <b><color=#",
			PauseManager.Instance.enemyNameLogColor,
			">",
			ClientInstance.Instance.PlayerName,
			"</color></b>"
		}));
	}

	// Token: 0x06000902 RID: 2306 RVA: 0x000420EC File Offset: 0x000402EC
	private void IncreaseSuicidesAmount()
	{
		if (this.suicide)
		{
			this.suicide = false;
			Settings.Instance.IncreaseSuicidesAmount();
		}
	}

	// Token: 0x06000903 RID: 2307 RVA: 0x00042108 File Offset: 0x00040308
	public void KillShockWave()
	{
		if (!this.increaseKillAmount)
		{
			Settings.Instance.IncreaseKillsAmount();
			this.increaseKillAmount = true;
		}
		this.SyncAccessor__rootObject.GetComponent<FirstPersonController>().lensDistortion.intensity.value = this.SyncAccessor__rootObject.GetComponent<FirstPersonController>().killShockWaveStrength;
		this.SyncAccessor__rootObject.GetComponent<FirstPersonController>().colorGrading.saturation.value = -100f;
	}

	// Token: 0x06000904 RID: 2308 RVA: 0x00042177 File Offset: 0x00040377
	private void OnTriggerEnter(Collider col)
	{
		if (this.instantExplode && this.canActivate)
		{
			Debug.Log("chiasse");
			this.canActivate = false;
			this.ChangeState();
		}
	}

	// Token: 0x06000905 RID: 2309 RVA: 0x000421A0 File Offset: 0x000403A0
	private void OnTriggerStay(Collider col)
	{
		if (!this.canExplode || this.instantExplode)
		{
			return;
		}
		if (col.CompareTag("Player"))
		{
			this.bipTimer -= Time.deltaTime;
			if (this.bipTimer < 0f)
			{
				this.bipTimer = 0.006f;
				SoundManager.Instance.PlaySound(this.bipClip);
			}
		}
	}

	// Token: 0x06000906 RID: 2310 RVA: 0x00042205 File Offset: 0x00040405
	private void OnTriggerExit(Collider col)
	{
		if (!this.canExplode || this.instantExplode)
		{
			return;
		}
		if (col.CompareTag("Player") && this.canActivate)
		{
			this.canActivate = false;
			this.ChangeState();
		}
	}

	// Token: 0x06000907 RID: 2311 RVA: 0x0004223A File Offset: 0x0004043A
	[ServerRpc(RequireOwnership = false)]
	public void ChangeState()
	{
		this.RpcWriter___Server_ChangeState_2166136261();
	}

	// Token: 0x06000908 RID: 2312 RVA: 0x00042244 File Offset: 0x00040444
	private void HitMarker(bool head)
	{
		this.audio.PlayOneShot(this.hitSfx);
		if (head)
		{
			this.audio.PlayOneShot(Crosshair.Instance.headshotHitClip);
		}
		if (this.marker == null)
		{
			this.marker = global::UnityEngine.Object.Instantiate<GameObject>(this.hitMarker, Crosshair.Instance.transform.position, Quaternion.identity, PauseManager.Instance.transform);
			this.marker.transform.DOPunchScale(head ? new Vector3(2.5f, 2.5f, 2.5f) : Vector3.one, 0.3f, 8, 2f);
			if (head)
			{
				this.marker.GetComponent<Image>().color = Color.red;
			}
			global::UnityEngine.Object.Destroy(this.marker, 0.3f);
			return;
		}
		global::UnityEngine.Object.Destroy(this.marker);
		this.marker = global::UnityEngine.Object.Instantiate<GameObject>(this.hitMarker, Crosshair.Instance.transform.position, Quaternion.identity, PauseManager.Instance.transform);
		this.marker.transform.DOPunchScale(head ? new Vector3(2.5f, 2.5f, 2.5f) : Vector3.one, 0.3f, 8, 2f);
		if (head)
		{
			this.marker.GetComponent<Image>().color = Color.red;
		}
		global::UnityEngine.Object.Destroy(this.marker, 0.3f);
	}

	// Token: 0x0600090A RID: 2314 RVA: 0x000423EC File Offset: 0x000405EC
	public virtual void NetworkInitialize___Early()
	{
		if (this.NetworkInitializeEarly_ProximityMine_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeEarly_ProximityMine_Assembly-CSharp.dll = true;
		this.syncVar___weapon = new SyncVar<Weapon>(this, 2U, WritePermission.ServerOnly, ReadPermission.Observers, -1f, Channel.Reliable, this.weapon);
		this.syncVar____rootObject = new SyncVar<GameObject>(this, 1U, WritePermission.ServerOnly, ReadPermission.Observers, -1f, Channel.Reliable, this._rootObject);
		this.syncVar___detonated = new SyncVar<bool>(this, 0U, WritePermission.ServerOnly, ReadPermission.Observers, -1f, Channel.Reliable, this.detonated);
		base.RegisterServerRpc(0U, new ServerRpcDelegate(this.RpcReader___Server_StartNewRound_2166136261));
		base.RegisterServerRpc(1U, new ServerRpcDelegate(this.RpcReader___Server_ExplodeServer_2166136261));
		base.RegisterObserversRpc(2U, new ClientRpcDelegate(this.RpcReader___Observers_ExplodeObservers_2166136261));
		base.RegisterServerRpc(3U, new ServerRpcDelegate(this.RpcReader___Server_ChangeState_2166136261));
		base.RegisterSyncVarRead(new SyncVarReadDelegate(this.ReadSyncVar___ProximityMine));
	}

	// Token: 0x0600090B RID: 2315 RVA: 0x000424F9 File Offset: 0x000406F9
	public virtual void NetworkInitialize__Late()
	{
		if (this.NetworkInitializeLate_ProximityMine_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeLate_ProximityMine_Assembly-CSharp.dll = true;
		this.syncVar___weapon.SetRegistered();
		this.syncVar____rootObject.SetRegistered();
		this.syncVar___detonated.SetRegistered();
	}

	// Token: 0x0600090C RID: 2316 RVA: 0x0004252D File Offset: 0x0004072D
	public override void NetworkInitializeIfDisabled()
	{
		this.NetworkInitialize___Early();
		this.NetworkInitialize__Late();
	}

	// Token: 0x0600090D RID: 2317 RVA: 0x0004253C File Offset: 0x0004073C
	private void RpcWriter___Server_StartNewRound_2166136261()
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendServerRpc(0U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x0600090E RID: 2318 RVA: 0x000425D8 File Offset: 0x000407D8
	private void RpcLogic___StartNewRound_2166136261()
	{
		base.Despawn(null);
	}

	// Token: 0x0600090F RID: 2319 RVA: 0x000425F4 File Offset: 0x000407F4
	private void RpcReader___Server_StartNewRound_2166136261(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		if (!base.IsServer)
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___StartNewRound_2166136261();
	}

	// Token: 0x06000910 RID: 2320 RVA: 0x00042624 File Offset: 0x00040824
	private void RpcWriter___Server_ExplodeServer_2166136261()
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendServerRpc(1U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000911 RID: 2321 RVA: 0x00042718 File Offset: 0x00040918
	private void RpcLogic___ExplodeServer_2166136261()
	{
		this.ExplodeObservers();
	}

	// Token: 0x06000912 RID: 2322 RVA: 0x00042720 File Offset: 0x00040920
	private void RpcReader___Server_ExplodeServer_2166136261(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___ExplodeServer_2166136261();
	}

	// Token: 0x06000913 RID: 2323 RVA: 0x00042760 File Offset: 0x00040960
	private void RpcWriter___Observers_ExplodeObservers_2166136261()
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendObserversRpc(2U, writer, channel, DataOrderType.Default, false, false, false);
		writer.Store();
	}

	// Token: 0x06000914 RID: 2324 RVA: 0x0004280C File Offset: 0x00040A0C
	private void RpcLogic___ExplodeObservers_2166136261()
	{
		GameObject[] array = GameObject.FindGameObjectsWithTag("Player");
		for (int i = 0; i < array.Length; i++)
		{
			if (array[i] != null)
			{
				float num = Vector3.Distance(base.transform.position, array[i].transform.position);
				array[i].GetComponent<PlayerHealth>().LocalScreenshake(this.duration, Mathf.Lerp(this.maxStrength, this.minStrength, Mathf.Clamp(num / this.maxDistance, 0f, 1f)), this.vibrato, this.randomness, this.shakeEase);
			}
		}
		global::UnityEngine.Object.Destroy(base.gameObject);
		global::UnityEngine.Object.Instantiate<GameObject>(this.explosionVfx, base.transform.position, Quaternion.identity);
		SoundManager.Instance.PlaySound(this.explosionClip);
	}

	// Token: 0x06000915 RID: 2325 RVA: 0x000428E0 File Offset: 0x00040AE0
	private void RpcReader___Observers_ExplodeObservers_2166136261(PooledReader PooledReader0, Channel channel)
	{
		if (!base.IsClient)
		{
			return;
		}
		this.RpcLogic___ExplodeObservers_2166136261();
	}

	// Token: 0x06000916 RID: 2326 RVA: 0x00042900 File Offset: 0x00040B00
	private void RpcWriter___Server_ChangeState_2166136261()
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendServerRpc(3U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000917 RID: 2327 RVA: 0x0004299A File Offset: 0x00040B9A
	public void RpcLogic___ChangeState_2166136261()
	{
		this.sync___set_value_detonated(true, true);
	}

	// Token: 0x06000918 RID: 2328 RVA: 0x000429A4 File Offset: 0x00040BA4
	private void RpcReader___Server_ChangeState_2166136261(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		if (!base.IsServer)
		{
			return;
		}
		this.RpcLogic___ChangeState_2166136261();
	}

	// Token: 0x1700008C RID: 140
	// (get) Token: 0x06000919 RID: 2329 RVA: 0x000429C4 File Offset: 0x00040BC4
	// (set) Token: 0x0600091A RID: 2330 RVA: 0x000429CC File Offset: 0x00040BCC
	public bool SyncAccessor_detonated
	{
		get
		{
			return this.detonated;
		}
		set
		{
			if (value || !base.IsServer)
			{
				this.detonated = value;
			}
			this.syncVar___detonated.SetValue(value, value);
		}
	}

	// Token: 0x0600091B RID: 2331 RVA: 0x00042A04 File Offset: 0x00040C04
	public virtual bool ReadSyncVar___ProximityMine(PooledReader PooledReader0, uint UInt321, bool Boolean2)
	{
		if (UInt321 == 2U)
		{
			if (PooledReader0 == null)
			{
				this.sync___set_value_weapon(this.syncVar___weapon.GetValue(true), true);
				return true;
			}
			Weapon weapon = global::FishNet.Serializing.Generated.GeneratedReaders___Internal.Read___WeaponFishNet.Serializing.Generateds(PooledReader0);
			this.sync___set_value_weapon(weapon, Boolean2);
			return true;
		}
		else if (UInt321 == 1U)
		{
			if (PooledReader0 == null)
			{
				this.sync___set_value__rootObject(this.syncVar____rootObject.GetValue(true), true);
				return true;
			}
			GameObject gameObject = PooledReader0.ReadGameObject();
			this.sync___set_value__rootObject(gameObject, Boolean2);
			return true;
		}
		else
		{
			if (UInt321 != 0U)
			{
				return false;
			}
			if (PooledReader0 == null)
			{
				this.sync___set_value_detonated(this.syncVar___detonated.GetValue(true), true);
				return true;
			}
			bool flag = PooledReader0.ReadBoolean();
			this.sync___set_value_detonated(flag, Boolean2);
			return true;
		}
	}

	// Token: 0x1700008D RID: 141
	// (get) Token: 0x0600091C RID: 2332 RVA: 0x00042ADE File Offset: 0x00040CDE
	// (set) Token: 0x0600091D RID: 2333 RVA: 0x00042AE6 File Offset: 0x00040CE6
	public GameObject SyncAccessor__rootObject
	{
		get
		{
			return this._rootObject;
		}
		set
		{
			if (value || !base.IsServer)
			{
				this._rootObject = value;
			}
			this.syncVar____rootObject.SetValue(value, value);
		}
	}

	// Token: 0x1700008E RID: 142
	// (get) Token: 0x0600091E RID: 2334 RVA: 0x00042B1B File Offset: 0x00040D1B
	// (set) Token: 0x0600091F RID: 2335 RVA: 0x00042B23 File Offset: 0x00040D23
	public Weapon SyncAccessor_weapon
	{
		get
		{
			return this.weapon;
		}
		set
		{
			if (value || !base.IsServer)
			{
				this.weapon = value;
			}
			this.syncVar___weapon.SetValue(value, value);
		}
	}

	// Token: 0x06000920 RID: 2336 RVA: 0x00042B58 File Offset: 0x00040D58
	public virtual void Awake___UserLogic()
	{
		this.audio = base.GetComponent<AudioSource>();
	}

	// Token: 0x04000835 RID: 2101
	[SerializeField]
	private string weaponName;

	// Token: 0x04000836 RID: 2102
	[SerializeField]
	private float explosionRadius;

	// Token: 0x04000837 RID: 2103
	[SerializeField]
	private LayerMask playerLayer;

	// Token: 0x04000838 RID: 2104
	[SerializeField]
	private LayerMask bodyLayer;

	// Token: 0x04000839 RID: 2105
	[SerializeField]
	private bool instantExplode;

	// Token: 0x0400083A RID: 2106
	[SerializeField]
	private float damage = 5f;

	// Token: 0x0400083B RID: 2107
	[SyncVar]
	public bool detonated;

	// Token: 0x0400083C RID: 2108
	[SerializeField]
	private float ragdollEjectForce = 3f;

	// Token: 0x0400083D RID: 2109
	[SerializeField]
	private GameObject explosionVfx;

	// Token: 0x0400083E RID: 2110
	[SerializeField]
	private AudioClip explosionClip;

	// Token: 0x0400083F RID: 2111
	[SerializeField]
	private AudioClip bipClip;

	// Token: 0x04000840 RID: 2112
	[SyncVar]
	public GameObject _rootObject;

	// Token: 0x04000841 RID: 2113
	[SerializeField]
	private GameObject graph;

	// Token: 0x04000842 RID: 2114
	[SerializeField]
	private AudioClip activationSound;

	// Token: 0x04000843 RID: 2115
	public bool canExplode;

	// Token: 0x04000844 RID: 2116
	private AudioSource audio;

	// Token: 0x04000845 RID: 2117
	private float bipTimer;

	// Token: 0x04000846 RID: 2118
	private PlayerHealth[] ph2;

	// Token: 0x04000847 RID: 2119
	[Header("Screenshake values")]
	[SerializeField]
	private float duration;

	// Token: 0x04000848 RID: 2120
	[SerializeField]
	private float minStrength;

	// Token: 0x04000849 RID: 2121
	[SerializeField]
	private float maxStrength;

	// Token: 0x0400084A RID: 2122
	[SerializeField]
	private int vibrato;

	// Token: 0x0400084B RID: 2123
	[SerializeField]
	private float randomness;

	// Token: 0x0400084C RID: 2124
	[SerializeField]
	private Ease shakeEase;

	// Token: 0x0400084D RID: 2125
	[SerializeField]
	private float maxDistance;

	// Token: 0x0400084E RID: 2126
	[Space]
	[SerializeField]
	private Material realMat;

	// Token: 0x0400084F RID: 2127
	[SerializeField]
	private GameObject indicator;

	// Token: 0x04000850 RID: 2128
	[SyncVar]
	public Weapon weapon;

	// Token: 0x04000851 RID: 2129
	[Space]
	[SerializeField]
	private bool stunMine;

	// Token: 0x04000852 RID: 2130
	[SerializeField]
	private float stunTime;

	// Token: 0x04000853 RID: 2131
	public bool isOwner;

	// Token: 0x04000854 RID: 2132
	private bool touched;

	// Token: 0x04000855 RID: 2133
	private bool touched2;

	// Token: 0x04000856 RID: 2134
	private bool activated = true;

	// Token: 0x04000857 RID: 2135
	private bool sendKillLog;

	// Token: 0x04000858 RID: 2136
	private bool suicide = true;

	// Token: 0x04000859 RID: 2137
	private bool increaseKillAmount;

	// Token: 0x0400085A RID: 2138
	private bool canActivate = true;

	// Token: 0x0400085B RID: 2139
	[SerializeField]
	private GameObject hitMarker;

	// Token: 0x0400085C RID: 2140
	[SerializeField]
	private AudioClip hitSfx;

	// Token: 0x0400085D RID: 2141
	private GameObject marker;

	// Token: 0x0400085E RID: 2142
	public SyncVar<bool> syncVar___detonated;

	// Token: 0x0400085F RID: 2143
	public SyncVar<GameObject> syncVar____rootObject;

	// Token: 0x04000860 RID: 2144
	public SyncVar<Weapon> syncVar___weapon;

	// Token: 0x04000861 RID: 2145
	private bool NetworkInitializeEarly_ProximityMine_Assembly-CSharp.dll;

	// Token: 0x04000862 RID: 2146
	private bool NetworkInitializeLate_ProximityMine_Assembly-CSharp.dll;
}

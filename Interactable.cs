﻿using System;
using FishNet.Object;

// Token: 0x0200007B RID: 123
public abstract class Interactable : NetworkBehaviour
{
	// Token: 0x06000531 RID: 1329 RVA: 0x00021CD6 File Offset: 0x0001FED6
	public virtual void Awake()
	{
		this.NetworkInitialize___Early();
		this.Awake___UserLogic();
		this.NetworkInitialize__Late();
	}

	// Token: 0x06000532 RID: 1330
	public abstract void OnInteract();

	// Token: 0x06000533 RID: 1331
	public abstract void OnFocus();

	// Token: 0x06000534 RID: 1332
	public abstract void OnLoseFocus();

	// Token: 0x06000536 RID: 1334 RVA: 0x00021CEA File Offset: 0x0001FEEA
	public virtual void NetworkInitialize___Early()
	{
		if (this.NetworkInitializeEarly_Interactable_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeEarly_Interactable_Assembly-CSharp.dll = true;
	}

	// Token: 0x06000537 RID: 1335 RVA: 0x00021CFD File Offset: 0x0001FEFD
	public virtual void NetworkInitialize__Late()
	{
		if (this.NetworkInitializeLate_Interactable_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeLate_Interactable_Assembly-CSharp.dll = true;
	}

	// Token: 0x06000538 RID: 1336 RVA: 0x00021D10 File Offset: 0x0001FF10
	public override void NetworkInitializeIfDisabled()
	{
		this.NetworkInitialize___Early();
		this.NetworkInitialize__Late();
	}

	// Token: 0x06000539 RID: 1337 RVA: 0x00021D1E File Offset: 0x0001FF1E
	public virtual void Awake___UserLogic()
	{
		base.gameObject.layer = 7;
		this.canTake = true;
	}

	// Token: 0x04000515 RID: 1301
	public bool canTake;

	// Token: 0x04000516 RID: 1302
	private bool NetworkInitializeEarly_Interactable_Assembly-CSharp.dll;

	// Token: 0x04000517 RID: 1303
	private bool NetworkInitializeLate_Interactable_Assembly-CSharp.dll;
}

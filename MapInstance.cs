﻿using System;
using TMPro;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

// Token: 0x020000D5 RID: 213
public class MapInstance : MonoBehaviour, IPointerEnterHandler, IEventSystemHandler, IPointerExitHandler
{
	// Token: 0x06000B9E RID: 2974 RVA: 0x00051A31 File Offset: 0x0004FC31
	private void Awake()
	{
		base.transform.GetComponentInChildren<TextMeshProUGUI>().text = this.name;
	}

	// Token: 0x06000B9F RID: 2975 RVA: 0x00051A4C File Offset: 0x0004FC4C
	private void Start()
	{
		this.sprite = (Texture2D)Resources.Load("MapSprites/" + this.name, typeof(Texture2D));
		this.img.texture = this.sprite;
		this.UpdateUI();
	}

	// Token: 0x06000BA0 RID: 2976 RVA: 0x00051A9C File Offset: 0x0004FC9C
	public void UpdateUI()
	{
		base.transform.GetComponentInChildren<TextMeshProUGUI>().text = this.name;
		this.addText.text = ((!this.selected) ? "+" : "-");
		ColorBlock colors = this.button.colors;
		colors.normalColor = (this.selected ? this.selectedColor : this.deselectedColor);
		colors.selectedColor = (this.selected ? this.selectedColor : this.deselectedColor);
		colors.disabledColor = (this.selected ? this.selectedColor : this.deselectedColor);
		this.button.colors = colors;
	}

	// Token: 0x06000BA1 RID: 2977 RVA: 0x00051B50 File Offset: 0x0004FD50
	public void ChangeState()
	{
		this.selected = !this.selected;
		this.addText.text = ((!this.selected) ? "+" : "-");
		ColorBlock colors = this.button.colors;
		colors.normalColor = (this.selected ? this.selectedColor : this.deselectedColor);
		colors.selectedColor = (this.selected ? this.selectedColor : this.deselectedColor);
		colors.disabledColor = (this.selected ? this.selectedColor : this.deselectedColor);
		this.button.colors = colors;
		MapsManager.Instance.ChangeMapsState(this.name);
	}

	// Token: 0x06000BA2 RID: 2978 RVA: 0x00051C0A File Offset: 0x0004FE0A
	public void OpenMap()
	{
		SteamLobby.Instance.EnterExplorationMap(this.name);
	}

	// Token: 0x06000BA3 RID: 2979 RVA: 0x00051C1C File Offset: 0x0004FE1C
	public void OnPointerEnter(PointerEventData eventData)
	{
		MapsManager.Instance.ChangePicture(this.sprite, this.name);
	}

	// Token: 0x06000BA4 RID: 2980 RVA: 0x000023D6 File Offset: 0x000005D6
	public void OnPointerExit(PointerEventData eventData)
	{
	}

	// Token: 0x06000BA5 RID: 2981 RVA: 0x00051C34 File Offset: 0x0004FE34
	private void OnDisable()
	{
		MapsManager.Instance.ChangePicture(null, "");
	}

	// Token: 0x040009C2 RID: 2498
	public new string name;

	// Token: 0x040009C3 RID: 2499
	public bool selected;

	// Token: 0x040009C4 RID: 2500
	[Space]
	public Transform mapsViewport;

	// Token: 0x040009C5 RID: 2501
	public Transform mapsAddedViewport;

	// Token: 0x040009C6 RID: 2502
	[Space]
	[SerializeField]
	private Color selectedColor;

	// Token: 0x040009C7 RID: 2503
	[SerializeField]
	private Color deselectedColor;

	// Token: 0x040009C8 RID: 2504
	[SerializeField]
	private TextMeshProUGUI addText;

	// Token: 0x040009C9 RID: 2505
	[SerializeField]
	private Texture2D sprite;

	// Token: 0x040009CA RID: 2506
	[SerializeField]
	private RawImage img;

	// Token: 0x040009CB RID: 2507
	[SerializeField]
	private Button button;
}

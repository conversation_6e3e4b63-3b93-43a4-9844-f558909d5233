﻿using System;
using DG.Tweening;
using DG.Tweening.Core;
using DG.Tweening.Plugins.Options;
using FishNet;
using FishNet.Connection;
using FishNet.Managing;
using FishNet.Object;
using FishNet.Object.Delegating;
using FishNet.Object.Synchronizing;
using FishNet.Serializing;
using FishNet.Transporting;
using UnityEngine;

// Token: 0x0200004D RID: 77
public class Door : InteractEnvironment
{
	// Token: 0x0600039B RID: 923 RVA: 0x0001B07C File Offset: 0x0001927C
	private void Start()
	{
		if (base.GetComponent<AudioSource>() != null)
		{
			this.audio = base.GetComponent<AudioSource>();
		}
		if (this.audio != null)
		{
			this.audio.maxDistance = 34f;
			this.audio.spatialBlend = 1f;
		}
	}

	// Token: 0x0600039C RID: 924 RVA: 0x0001B0D4 File Offset: 0x000192D4
	public override void OnFocus()
	{
		PauseManager.Instance.interactPopup.gameObject.SetActive(true);
		PauseManager.Instance.interactPopup.text = (this.SyncAccessor_isOpen ? this.closeDoor.ToLower() : this.popupText.ToLower()) + " [" + PauseManager.Instance.InteractPromptLetter.ToLower() + "]";
	}

	// Token: 0x0600039D RID: 925 RVA: 0x0001B143 File Offset: 0x00019343
	public override void OnInteract(Transform player)
	{
		if (this.timerdoor > 0f)
		{
			return;
		}
		this.CmdInteract(player);
	}

	// Token: 0x0600039E RID: 926 RVA: 0x000023D6 File Offset: 0x000005D6
	public override void OnLoseFocus()
	{
	}

	// Token: 0x0600039F RID: 927 RVA: 0x0001B15A File Offset: 0x0001935A
	[ServerRpc(RunLocally = true, RequireOwnership = false)]
	private void CmdInteract(Transform player)
	{
		this.RpcWriter___Server_CmdInteract_3068987916(player);
		this.RpcLogic___CmdInteract_3068987916(player);
	}

	// Token: 0x060003A0 RID: 928 RVA: 0x0001B170 File Offset: 0x00019370
	[ObserversRpc(RunLocally = true, ExcludeOwner = true)]
	private void TriggerDoor()
	{
		this.RpcWriter___Observers_TriggerDoor_2166136261();
		this.RpcLogic___TriggerDoor_2166136261();
	}

	// Token: 0x060003A1 RID: 929 RVA: 0x0001B180 File Offset: 0x00019380
	private void Update()
	{
		if (this.audio != null && this.source != null && this.audio.volume != this.source.volume)
		{
			this.audio.volume = this.source.volume;
		}
		this.timerdoor -= Time.deltaTime;
		if (this.trigger)
		{
			this.trigger = false;
			this.tween.Kill(false);
			Vector3 vector = base.transform.TransformDirection(Vector3.forward);
			Vector3 vector2 = this.SyncAccessor_localplayer.position - base.transform.position;
			float num = Vector3.Dot(vector, vector2);
			if (this.SyncAccessor_isOpen)
			{
				this.tween = base.transform.DOLocalRotate(new Vector3(0f, (num > 0f) ? this.maxRotation : (-this.maxRotation), 0f), this.doorOpeningTime, RotateMode.Fast).SetAutoKill(true);
				if (this.timerdoor < 0f)
				{
					if (this.audio != null)
					{
						this.audio.PlayOneShot(this.openClip);
					}
					this.firstSfx = false;
					this.timerdoor = 0.1f;
					return;
				}
			}
			else
			{
				this.tween = base.transform.DOLocalRotate(new Vector3(0f, 0f, 0f), this.doorOpeningTime, RotateMode.Fast).SetAutoKill(true);
				if (this.timerdoor < 0f)
				{
					if (this.audio != null)
					{
						this.audio.PlayOneShot(this.openClip);
					}
					this.firstSfx = false;
					this.timerdoor = 0.1f;
				}
			}
		}
	}

	// Token: 0x060003A2 RID: 930 RVA: 0x0001B33B File Offset: 0x0001953B
	public override void Awake()
	{
		this.NetworkInitialize___Early();
		this.Awake___UserLogic();
		this.NetworkInitialize__Late();
	}

	// Token: 0x060003A4 RID: 932 RVA: 0x0001B388 File Offset: 0x00019588
	public override void NetworkInitialize___Early()
	{
		if (this.NetworkInitializeEarly_Door_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeEarly_Door_Assembly-CSharp.dll = true;
		base.NetworkInitialize___Early();
		this.syncVar___localplayer = new SyncVar<Transform>(this, 2U, WritePermission.ServerOnly, ReadPermission.Observers, -1f, Channel.Reliable, this.localplayer);
		this.syncVar___previousIsOpen = new SyncVar<bool>(this, 1U, WritePermission.ServerOnly, ReadPermission.Observers, -1f, Channel.Reliable, this.previousIsOpen);
		this.syncVar___isOpen = new SyncVar<bool>(this, 0U, WritePermission.ServerOnly, ReadPermission.Observers, -1f, Channel.Reliable, this.isOpen);
		base.RegisterServerRpc(0U, new ServerRpcDelegate(this.RpcReader___Server_CmdInteract_3068987916));
		base.RegisterObserversRpc(1U, new ClientRpcDelegate(this.RpcReader___Observers_TriggerDoor_2166136261));
		base.RegisterSyncVarRead(new SyncVarReadDelegate(this.ReadSyncVar___Door));
	}

	// Token: 0x060003A5 RID: 933 RVA: 0x0001B46D File Offset: 0x0001966D
	public override void NetworkInitialize__Late()
	{
		if (this.NetworkInitializeLate_Door_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeLate_Door_Assembly-CSharp.dll = true;
		base.NetworkInitialize__Late();
		this.syncVar___localplayer.SetRegistered();
		this.syncVar___previousIsOpen.SetRegistered();
		this.syncVar___isOpen.SetRegistered();
	}

	// Token: 0x060003A6 RID: 934 RVA: 0x0001B4A7 File Offset: 0x000196A7
	public override void NetworkInitializeIfDisabled()
	{
		this.NetworkInitialize___Early();
		this.NetworkInitialize__Late();
	}

	// Token: 0x060003A7 RID: 935 RVA: 0x0001B4B8 File Offset: 0x000196B8
	private void RpcWriter___Server_CmdInteract_3068987916(Transform player)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteTransform(player);
		base.SendServerRpc(0U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x060003A8 RID: 936 RVA: 0x0001B55F File Offset: 0x0001975F
	private void RpcLogic___CmdInteract_3068987916(Transform player)
	{
		this.TriggerDoor();
		this.sync___set_value_previousIsOpen(this.SyncAccessor_isOpen, true);
		this.sync___set_value_isOpen(!this.SyncAccessor_isOpen, true);
		this.sync___set_value_localplayer(player, true);
	}

	// Token: 0x060003A9 RID: 937 RVA: 0x0001B58C File Offset: 0x0001978C
	private void RpcReader___Server_CmdInteract_3068987916(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		Transform transform = PooledReader0.ReadTransform();
		if (!base.IsServer)
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___CmdInteract_3068987916(transform);
	}

	// Token: 0x060003AA RID: 938 RVA: 0x0001B5CC File Offset: 0x000197CC
	private void RpcWriter___Observers_TriggerDoor_2166136261()
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendObserversRpc(1U, writer, channel, DataOrderType.Default, false, false, true);
		writer.Store();
	}

	// Token: 0x060003AB RID: 939 RVA: 0x0001B675 File Offset: 0x00019875
	private void RpcLogic___TriggerDoor_2166136261()
	{
		if (this.timerdoor > 0f)
		{
			return;
		}
		this.trigger = true;
	}

	// Token: 0x060003AC RID: 940 RVA: 0x0001B68C File Offset: 0x0001988C
	private void RpcReader___Observers_TriggerDoor_2166136261(PooledReader PooledReader0, Channel channel)
	{
		if (!base.IsClient)
		{
			return;
		}
		if (base.IsHost)
		{
			return;
		}
		this.RpcLogic___TriggerDoor_2166136261();
	}

	// Token: 0x17000061 RID: 97
	// (get) Token: 0x060003AD RID: 941 RVA: 0x0001B6B6 File Offset: 0x000198B6
	// (set) Token: 0x060003AE RID: 942 RVA: 0x0001B6BE File Offset: 0x000198BE
	public bool SyncAccessor_isOpen
	{
		get
		{
			return this.isOpen;
		}
		set
		{
			if (value || !base.IsServer)
			{
				this.isOpen = value;
			}
			this.syncVar___isOpen.SetValue(value, value);
		}
	}

	// Token: 0x060003AF RID: 943 RVA: 0x0001B6F4 File Offset: 0x000198F4
	public virtual bool ReadSyncVar___Door(PooledReader PooledReader0, uint UInt321, bool Boolean2)
	{
		if (UInt321 == 2U)
		{
			if (PooledReader0 == null)
			{
				this.sync___set_value_localplayer(this.syncVar___localplayer.GetValue(true), true);
				return true;
			}
			Transform transform = PooledReader0.ReadTransform();
			this.sync___set_value_localplayer(transform, Boolean2);
			return true;
		}
		else if (UInt321 == 1U)
		{
			if (PooledReader0 == null)
			{
				this.sync___set_value_previousIsOpen(this.syncVar___previousIsOpen.GetValue(true), true);
				return true;
			}
			bool flag = PooledReader0.ReadBoolean();
			this.sync___set_value_previousIsOpen(flag, Boolean2);
			return true;
		}
		else
		{
			if (UInt321 != 0U)
			{
				return false;
			}
			if (PooledReader0 == null)
			{
				this.sync___set_value_isOpen(this.syncVar___isOpen.GetValue(true), true);
				return true;
			}
			bool flag2 = PooledReader0.ReadBoolean();
			this.sync___set_value_isOpen(flag2, Boolean2);
			return true;
		}
	}

	// Token: 0x17000062 RID: 98
	// (get) Token: 0x060003B0 RID: 944 RVA: 0x0001B7CE File Offset: 0x000199CE
	// (set) Token: 0x060003B1 RID: 945 RVA: 0x0001B7D6 File Offset: 0x000199D6
	public bool SyncAccessor_previousIsOpen
	{
		get
		{
			return this.previousIsOpen;
		}
		set
		{
			if (value || !base.IsServer)
			{
				this.previousIsOpen = value;
			}
			this.syncVar___previousIsOpen.SetValue(value, value);
		}
	}

	// Token: 0x17000063 RID: 99
	// (get) Token: 0x060003B2 RID: 946 RVA: 0x0001B80B File Offset: 0x00019A0B
	// (set) Token: 0x060003B3 RID: 947 RVA: 0x0001B813 File Offset: 0x00019A13
	public Transform SyncAccessor_localplayer
	{
		get
		{
			return this.localplayer;
		}
		set
		{
			if (value || !base.IsServer)
			{
				this.localplayer = value;
			}
			this.syncVar___localplayer.SetValue(value, value);
		}
	}

	// Token: 0x060003B4 RID: 948 RVA: 0x0001B848 File Offset: 0x00019A48
	public override void Awake___UserLogic()
	{
		if (SoundManager.Instance._effectsSource != null)
		{
			this.source = SoundManager.Instance._effectsSource;
		}
	}

	// Token: 0x040003F9 RID: 1017
	[SyncVar]
	public bool isOpen;

	// Token: 0x040003FA RID: 1018
	[SyncVar]
	public bool previousIsOpen = true;

	// Token: 0x040003FB RID: 1019
	private bool trigger;

	// Token: 0x040003FC RID: 1020
	private bool firstSfx = true;

	// Token: 0x040003FD RID: 1021
	[SerializeField]
	private AudioClip openClip;

	// Token: 0x040003FE RID: 1022
	[SerializeField]
	private AudioClip closeClip;

	// Token: 0x040003FF RID: 1023
	[SerializeField]
	private float maxRotation = 105f;

	// Token: 0x04000400 RID: 1024
	[SerializeField]
	private float doorOpeningTime = 0.33f;

	// Token: 0x04000401 RID: 1025
	[SerializeField]
	private string closeDoor = "close door";

	// Token: 0x04000402 RID: 1026
	private AudioSource audio;

	// Token: 0x04000403 RID: 1027
	private Tween tween;

	// Token: 0x04000404 RID: 1028
	[SyncVar]
	public Transform localplayer;

	// Token: 0x04000405 RID: 1029
	private float timerdoor;

	// Token: 0x04000406 RID: 1030
	private AudioSource source;

	// Token: 0x04000407 RID: 1031
	public SyncVar<bool> syncVar___isOpen;

	// Token: 0x04000408 RID: 1032
	public SyncVar<bool> syncVar___previousIsOpen;

	// Token: 0x04000409 RID: 1033
	public SyncVar<Transform> syncVar___localplayer;

	// Token: 0x0400040A RID: 1034
	private bool NetworkInitializeEarly_Door_Assembly-CSharp.dll;

	// Token: 0x0400040B RID: 1035
	private bool NetworkInitializeLate_Door_Assembly-CSharp.dll;
}

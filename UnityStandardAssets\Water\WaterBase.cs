﻿using System;
using UnityEngine;

namespace UnityStandardAssets.Water
{
	// Token: 0x020001A1 RID: 417
	[ExecuteInEditMode]
	public class WaterBase : MonoBehaviour
	{
		// Token: 0x0600105F RID: 4191 RVA: 0x0006AC78 File Offset: 0x00068E78
		public void UpdateShader()
		{
			if (this.waterQuality > WaterQuality.Medium)
			{
				this.sharedMaterial.shader.maximumLOD = 501;
			}
			else if (this.waterQuality > WaterQuality.Low)
			{
				this.sharedMaterial.shader.maximumLOD = 301;
			}
			else
			{
				this.sharedMaterial.shader.maximumLOD = 201;
			}
			if (!SystemInfo.SupportsRenderTextureFormat(RenderTextureFormat.Depth))
			{
				this.edgeBlend = false;
			}
			if (this.edgeBlend)
			{
				Shader.EnableKeyword("WATER_EDGEBLEND_ON");
				Shader.DisableKeyword("WATER_EDGEBLEND_OFF");
				if (Camera.main)
				{
					Camera.main.depthTextureMode |= DepthTextureMode.Depth;
					return;
				}
			}
			else
			{
				Shader.EnableKeyword("WATER_EDGEBLEND_OFF");
				Shader.DisableKeyword("WATER_EDGEBLEND_ON");
			}
		}

		// Token: 0x06001060 RID: 4192 RVA: 0x0006AD38 File Offset: 0x00068F38
		public void WaterTileBeingRendered(Transform tr, Camera currentCam)
		{
			if (currentCam && this.edgeBlend)
			{
				currentCam.depthTextureMode |= DepthTextureMode.Depth;
			}
		}

		// Token: 0x06001061 RID: 4193 RVA: 0x0006AD58 File Offset: 0x00068F58
		public void Update()
		{
			if (this.sharedMaterial)
			{
				this.UpdateShader();
			}
		}

		// Token: 0x04000F28 RID: 3880
		public Material sharedMaterial;

		// Token: 0x04000F29 RID: 3881
		public WaterQuality waterQuality = WaterQuality.High;

		// Token: 0x04000F2A RID: 3882
		public bool edgeBlend = true;
	}
}

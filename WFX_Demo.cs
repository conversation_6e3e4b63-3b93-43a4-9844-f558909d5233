﻿using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

// Token: 0x02000162 RID: 354
public class WFX_Demo : MonoBehaviour
{
	// Token: 0x06000F17 RID: 3863 RVA: 0x00063514 File Offset: 0x00061714
	private void OnMouseDown()
	{
		RaycastHit raycastHit = default(RaycastHit);
		if (base.GetComponent<Collider>().Raycast(Camera.main.ScreenPointToRay(Input.mousePosition), out raycastHit, 9999f))
		{
			GameObject gameObject = this.spawnParticle();
			if (!gameObject.name.StartsWith("WFX_MF"))
			{
				gameObject.transform.position = raycastHit.point + gameObject.transform.position;
			}
		}
	}

	// Token: 0x06000F18 RID: 3864 RVA: 0x00063588 File Offset: 0x00061788
	public GameObject spawnParticle()
	{
		GameObject gameObject = global::UnityEngine.Object.Instantiate<GameObject>(this.ParticleExamples[this.exampleIndex]);
		if (gameObject.name.StartsWith("WFX_MF"))
		{
			gameObject.transform.parent = this.ParticleExamples[this.exampleIndex].transform.parent;
			gameObject.transform.localPosition = this.ParticleExamples[this.exampleIndex].transform.localPosition;
			gameObject.transform.localRotation = this.ParticleExamples[this.exampleIndex].transform.localRotation;
		}
		else if (gameObject.name.Contains("Hole"))
		{
			gameObject.transform.parent = this.bulletholes.transform;
		}
		this.SetActiveCrossVersions(gameObject, true);
		return gameObject;
	}

	// Token: 0x06000F19 RID: 3865 RVA: 0x00063654 File Offset: 0x00061854
	private void SetActiveCrossVersions(GameObject obj, bool active)
	{
		obj.SetActive(active);
		for (int i = 0; i < obj.transform.childCount; i++)
		{
			obj.transform.GetChild(i).gameObject.SetActive(active);
		}
	}

	// Token: 0x06000F1A RID: 3866 RVA: 0x00063698 File Offset: 0x00061898
	private void OnGUI()
	{
		GUILayout.BeginArea(new Rect(5f, 20f, (float)(Screen.width - 10), 60f));
		GUILayout.BeginHorizontal(Array.Empty<GUILayoutOption>());
		GUILayout.Label("Effect: " + this.ParticleExamples[this.exampleIndex].name, new GUILayoutOption[] { GUILayout.Width(280f) });
		if (GUILayout.Button("<", new GUILayoutOption[] { GUILayout.Width(30f) }))
		{
			this.prevParticle();
		}
		if (GUILayout.Button(">", new GUILayoutOption[] { GUILayout.Width(30f) }))
		{
			this.nextParticle();
		}
		GUILayout.FlexibleSpace();
		GUILayout.Label("Click on the ground to spawn the selected effect", Array.Empty<GUILayoutOption>());
		GUILayout.FlexibleSpace();
		if (GUILayout.Button(this.rotateCam ? "Pause Camera" : "Rotate Camera", new GUILayoutOption[] { GUILayout.Width(110f) }))
		{
			this.rotateCam = !this.rotateCam;
		}
		if (GUILayout.Button(base.GetComponent<Renderer>().enabled ? "Hide Ground" : "Show Ground", new GUILayoutOption[] { GUILayout.Width(90f) }))
		{
			base.GetComponent<Renderer>().enabled = !base.GetComponent<Renderer>().enabled;
		}
		if (GUILayout.Button(this.slowMo ? "Normal Speed" : "Slow Motion", new GUILayoutOption[] { GUILayout.Width(100f) }))
		{
			this.slowMo = !this.slowMo;
			if (this.slowMo)
			{
				Time.timeScale = 0.33f;
			}
			else
			{
				Time.timeScale = 1f;
			}
		}
		GUILayout.EndHorizontal();
		GUILayout.BeginHorizontal(Array.Empty<GUILayoutOption>());
		GUILayout.Label("Ground texture: " + this.groundTextureStr, new GUILayoutOption[] { GUILayout.Width(160f) });
		if (GUILayout.Button("<", new GUILayoutOption[] { GUILayout.Width(30f) }))
		{
			this.prevTexture();
		}
		if (GUILayout.Button(">", new GUILayoutOption[] { GUILayout.Width(30f) }))
		{
			this.nextTexture();
		}
		GUILayout.EndHorizontal();
		GUILayout.EndArea();
		if (this.m4.GetComponent<Renderer>().enabled)
		{
			GUILayout.BeginArea(new Rect(5f, (float)(Screen.height - 100), (float)(Screen.width - 10), 90f));
			this.rotate_m4 = GUILayout.Toggle(this.rotate_m4, "AutoRotate Weapon", new GUILayoutOption[] { GUILayout.Width(250f) });
			GUI.enabled = !this.rotate_m4;
			float num = this.m4.transform.localEulerAngles.x;
			num = ((num > 90f) ? (num - 180f) : num);
			float num2 = this.m4.transform.localEulerAngles.y;
			float num3 = this.m4.transform.localEulerAngles.z;
			num = GUILayout.HorizontalSlider(num, 0f, 179f, new GUILayoutOption[] { GUILayout.Width(256f) });
			num2 = GUILayout.HorizontalSlider(num2, 0f, 359f, new GUILayoutOption[] { GUILayout.Width(256f) });
			num3 = GUILayout.HorizontalSlider(num3, 0f, 359f, new GUILayoutOption[] { GUILayout.Width(256f) });
			if (GUI.changed)
			{
				if (num > 90f)
				{
					num += 180f;
				}
				this.m4.transform.localEulerAngles = new Vector3(num, num2, num3);
				Debug.Log(num);
			}
			GUILayout.EndArea();
		}
	}

	// Token: 0x06000F1B RID: 3867 RVA: 0x00063A49 File Offset: 0x00061C49
	private IEnumerator RandomSpawnsCoroutine()
	{
		for (;;)
		{
			GameObject gameObject = this.spawnParticle();
			if (this.orderedSpawns)
			{
				gameObject.transform.position = base.transform.position + new Vector3(this.order, gameObject.transform.position.y, 0f);
				this.order -= this.step;
				if (this.order < -this.range)
				{
					this.order = this.range;
				}
			}
			else
			{
				gameObject.transform.position = base.transform.position + new Vector3(global::UnityEngine.Random.Range(-this.range, this.range), 0f, global::UnityEngine.Random.Range(-this.range, this.range)) + new Vector3(0f, gameObject.transform.position.y, 0f);
			}
			yield return new WaitForSeconds(float.Parse(this.randomSpawnsDelay));
		}
		yield break;
	}

	// Token: 0x06000F1C RID: 3868 RVA: 0x00063A58 File Offset: 0x00061C58
	private void Update()
	{
		if (Input.GetKeyDown(KeyCode.LeftArrow))
		{
			this.prevParticle();
		}
		else if (Input.GetKeyDown(KeyCode.RightArrow))
		{
			this.nextParticle();
		}
		if (this.rotateCam)
		{
			Camera.main.transform.RotateAround(Vector3.zero, Vector3.up, this.cameraSpeed * Time.deltaTime);
		}
		if (this.rotate_m4)
		{
			this.m4.transform.Rotate(new Vector3(0f, 40f, 0f) * Time.deltaTime, Space.World);
		}
	}

	// Token: 0x06000F1D RID: 3869 RVA: 0x00063AF0 File Offset: 0x00061CF0
	private void prevTexture()
	{
		int num = this.groundTextures.IndexOf(this.groundTextureStr);
		num--;
		if (num < 0)
		{
			num = this.groundTextures.Count - 1;
		}
		this.groundTextureStr = this.groundTextures[num];
		this.selectMaterial();
	}

	// Token: 0x06000F1E RID: 3870 RVA: 0x00063B40 File Offset: 0x00061D40
	private void nextTexture()
	{
		int num = this.groundTextures.IndexOf(this.groundTextureStr);
		num++;
		if (num >= this.groundTextures.Count)
		{
			num = 0;
		}
		this.groundTextureStr = this.groundTextures[num];
		this.selectMaterial();
	}

	// Token: 0x06000F1F RID: 3871 RVA: 0x00063B8C File Offset: 0x00061D8C
	private void selectMaterial()
	{
		string text = this.groundTextureStr;
		if (text == "Concrete")
		{
			base.GetComponent<Renderer>().material = this.concrete;
			this.walls.transform.GetChild(0).GetComponent<Renderer>().material = this.concreteWall;
			this.walls.transform.GetChild(1).GetComponent<Renderer>().material = this.concreteWall;
			return;
		}
		if (text == "Wood")
		{
			base.GetComponent<Renderer>().material = this.wood;
			this.walls.transform.GetChild(0).GetComponent<Renderer>().material = this.woodWall;
			this.walls.transform.GetChild(1).GetComponent<Renderer>().material = this.woodWall;
			return;
		}
		if (text == "Metal")
		{
			base.GetComponent<Renderer>().material = this.metal;
			this.walls.transform.GetChild(0).GetComponent<Renderer>().material = this.metalWall;
			this.walls.transform.GetChild(1).GetComponent<Renderer>().material = this.metalWall;
			return;
		}
		if (!(text == "Checker"))
		{
			return;
		}
		base.GetComponent<Renderer>().material = this.checker;
		this.walls.transform.GetChild(0).GetComponent<Renderer>().material = this.checkerWall;
		this.walls.transform.GetChild(1).GetComponent<Renderer>().material = this.checkerWall;
	}

	// Token: 0x06000F20 RID: 3872 RVA: 0x00063D2A File Offset: 0x00061F2A
	private void prevParticle()
	{
		this.exampleIndex--;
		if (this.exampleIndex < 0)
		{
			this.exampleIndex = this.ParticleExamples.Length - 1;
		}
		this.showHideStuff();
	}

	// Token: 0x06000F21 RID: 3873 RVA: 0x00063D59 File Offset: 0x00061F59
	private void nextParticle()
	{
		this.exampleIndex++;
		if (this.exampleIndex >= this.ParticleExamples.Length)
		{
			this.exampleIndex = 0;
		}
		this.showHideStuff();
	}

	// Token: 0x06000F22 RID: 3874 RVA: 0x00063D88 File Offset: 0x00061F88
	private void showHideStuff()
	{
		if (this.ParticleExamples[this.exampleIndex].name.StartsWith("WFX_MF Spr"))
		{
			this.m4.GetComponent<Renderer>().enabled = true;
		}
		else
		{
			this.m4.GetComponent<Renderer>().enabled = false;
		}
		if (this.ParticleExamples[this.exampleIndex].name.StartsWith("WFX_MF FPS"))
		{
			this.m4fps.GetComponent<Renderer>().enabled = true;
		}
		else
		{
			this.m4fps.GetComponent<Renderer>().enabled = false;
		}
		if (this.ParticleExamples[this.exampleIndex].name.StartsWith("WFX_BImpact"))
		{
			this.SetActiveCrossVersions(this.walls, true);
			Renderer[] array = this.bulletholes.GetComponentsInChildren<Renderer>();
			for (int i = 0; i < array.Length; i++)
			{
				array[i].enabled = true;
			}
		}
		else
		{
			this.SetActiveCrossVersions(this.walls, false);
			Renderer[] array = this.bulletholes.GetComponentsInChildren<Renderer>();
			for (int i = 0; i < array.Length; i++)
			{
				array[i].enabled = false;
			}
		}
		if (this.ParticleExamples[this.exampleIndex].name.Contains("Wood"))
		{
			this.groundTextureStr = "Wood";
			this.selectMaterial();
			return;
		}
		if (this.ParticleExamples[this.exampleIndex].name.Contains("Concrete"))
		{
			this.groundTextureStr = "Concrete";
			this.selectMaterial();
			return;
		}
		if (this.ParticleExamples[this.exampleIndex].name.Contains("Metal"))
		{
			this.groundTextureStr = "Metal";
			this.selectMaterial();
			return;
		}
		if (this.ParticleExamples[this.exampleIndex].name.Contains("Dirt") || this.ParticleExamples[this.exampleIndex].name.Contains("Sand") || this.ParticleExamples[this.exampleIndex].name.Contains("SoftBody"))
		{
			this.groundTextureStr = "Checker";
			this.selectMaterial();
			return;
		}
		if (this.ParticleExamples[this.exampleIndex].name == "WFX_Explosion")
		{
			this.groundTextureStr = "Checker";
			this.selectMaterial();
		}
	}

	// Token: 0x04000DD2 RID: 3538
	public float cameraSpeed = 10f;

	// Token: 0x04000DD3 RID: 3539
	public bool orderedSpawns = true;

	// Token: 0x04000DD4 RID: 3540
	public float step = 1f;

	// Token: 0x04000DD5 RID: 3541
	public float range = 5f;

	// Token: 0x04000DD6 RID: 3542
	private float order = -5f;

	// Token: 0x04000DD7 RID: 3543
	public GameObject walls;

	// Token: 0x04000DD8 RID: 3544
	public GameObject bulletholes;

	// Token: 0x04000DD9 RID: 3545
	public GameObject[] ParticleExamples;

	// Token: 0x04000DDA RID: 3546
	private int exampleIndex;

	// Token: 0x04000DDB RID: 3547
	private string randomSpawnsDelay = "0.5";

	// Token: 0x04000DDC RID: 3548
	private bool randomSpawns;

	// Token: 0x04000DDD RID: 3549
	private bool slowMo;

	// Token: 0x04000DDE RID: 3550
	private bool rotateCam = true;

	// Token: 0x04000DDF RID: 3551
	public Material wood;

	// Token: 0x04000DE0 RID: 3552
	public Material concrete;

	// Token: 0x04000DE1 RID: 3553
	public Material metal;

	// Token: 0x04000DE2 RID: 3554
	public Material checker;

	// Token: 0x04000DE3 RID: 3555
	public Material woodWall;

	// Token: 0x04000DE4 RID: 3556
	public Material concreteWall;

	// Token: 0x04000DE5 RID: 3557
	public Material metalWall;

	// Token: 0x04000DE6 RID: 3558
	public Material checkerWall;

	// Token: 0x04000DE7 RID: 3559
	private string groundTextureStr = "Checker";

	// Token: 0x04000DE8 RID: 3560
	private List<string> groundTextures = new List<string>(new string[] { "Concrete", "Wood", "Metal", "Checker" });

	// Token: 0x04000DE9 RID: 3561
	public GameObject m4;

	// Token: 0x04000DEA RID: 3562
	public GameObject m4fps;

	// Token: 0x04000DEB RID: 3563
	private bool rotate_m4 = true;
}

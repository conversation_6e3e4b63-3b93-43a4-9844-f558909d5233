﻿using System;
using System.Collections;
using System.Collections.Generic;
using FishNet;
using FishNet.Connection;
using FishNet.Managing;
using FishNet.Managing.Scened;
using FishNet.Object;
using FishNet.Object.Delegating;
using FishNet.Object.Synchronizing;
using FishNet.Serializing;
using FishNet.Transporting;
using TMPro;
using UnityEngine;
using UnityEngine.SceneManagement;

// Token: 0x020000BB RID: 187
public class SceneMotor : NetworkBehaviour
{
	// Token: 0x06000A5A RID: 2650 RVA: 0x0004B6E7 File Offset: 0x000498E7
	public virtual void Awake()
	{
		this.NetworkInitialize___Early();
		this.Awake___UserLogic();
		this.NetworkInitialize__Late();
	}

	// Token: 0x06000A5B RID: 2651 RVA: 0x0004B6FB File Offset: 0x000498FB
	private void OnEnable()
	{
		InstanceFinder.SceneManager.OnLoadStart += this.OnLoadSceneStart;
		InstanceFinder.SceneManager.OnLoadEnd += this.OnLoadSceneEnd;
	}

	// Token: 0x06000A5C RID: 2652 RVA: 0x0004B72C File Offset: 0x0004992C
	private void Start()
	{
		base.StartCoroutine(this.CanLoadIntoSceneEnable());
		if (SteamLobby.Instance.isInExplorationMap)
		{
			return;
		}
		MapSelection.Instance.InitiateMaps();
		this.roundAmountDropdown = GameObject.Find("RoundAmount").GetComponent<TMP_Dropdown>();
		this.OnRoundAmountChange();
		this.roundAmountDropdown.onValueChanged.AddListener(delegate
		{
			this.OnRoundAmountChange();
		});
	}

	// Token: 0x06000A5D RID: 2653 RVA: 0x0004B794 File Offset: 0x00049994
	private IEnumerator CanLoadIntoSceneEnable()
	{
		yield return new WaitForSeconds(0.1f);
		this.canLoadIntoScene = true;
		yield break;
	}

	// Token: 0x06000A5E RID: 2654 RVA: 0x0004B7A3 File Offset: 0x000499A3
	public void OnRoundAmountChange()
	{
		this.roundAmountDropdown = GameObject.Find("RoundAmount").GetComponent<TMP_Dropdown>();
		if (InstanceFinder.NetworkManager.IsServer)
		{
			this.CmdChangeRoundAmount();
		}
		this.RoundsAlgorithm();
	}

	// Token: 0x06000A5F RID: 2655 RVA: 0x0004B7D4 File Offset: 0x000499D4
	public void RoundsAlgorithm()
	{
		foreach (string text in this.scenes)
		{
			if (this.playedMaps.Contains(text))
			{
				this.remainingMaps.Remove(text);
			}
		}
		if (this.shuffle)
		{
			this.Shuffle(this.scenes);
		}
		if (this.remainingMaps.Count < this.scenes.Count)
		{
			foreach (string text2 in this.scenes)
			{
				if (!this.remainingMaps.Contains(text2))
				{
					this.remainingMaps.Add(text2);
				}
			}
		}
		this.tempScenes = new string[Mathf.Clamp(this.remainingMaps.Count * this.loops, 0, 400)];
		if (this.remainingMaps.Count == 0)
		{
			this.tempScenes = new string[Mathf.Clamp(this.firstToXWins ? (this.SyncAccessor_roundAmount * ClientInstance.playerInstances.Count) : this.SyncAccessor_roundAmount, 0, 400)];
		}
		int num = 0;
		for (int i = 0; i < this.loops; i++)
		{
			int num2 = 0;
			while (num2 < this.scenes.Count && num <= 399)
			{
				this.tempScenes[num2 + i * this.scenes.Count] = this.remainingMaps[num2];
				num++;
				num2++;
			}
			if (num > 399)
			{
				break;
			}
		}
		this.gameMaps = new string[this.firstToXWins ? (this.SyncAccessor_roundAmount * ClientInstance.playerInstances.Count) : this.SyncAccessor_roundAmount];
		for (int j = 0; j < (this.firstToXWins ? (this.SyncAccessor_roundAmount * ClientInstance.playerInstances.Count) : this.SyncAccessor_roundAmount); j++)
		{
			this.gameMaps[j] = this.tempScenes[j];
		}
	}

	// Token: 0x06000A60 RID: 2656 RVA: 0x0004BA00 File Offset: 0x00049C00
	[ServerRpc(RunLocally = true, RequireOwnership = false)]
	public void CmdChangeRoundAmount()
	{
		this.RpcWriter___Server_CmdChangeRoundAmount_2166136261();
		this.RpcLogic___CmdChangeRoundAmount_2166136261();
	}

	// Token: 0x06000A61 RID: 2657 RVA: 0x0004BA10 File Offset: 0x00049C10
	private void Update()
	{
		if (Input.GetKeyDown(KeyCode.M) && Input.GetKey(KeyCode.LeftControl) && Application.isEditor)
		{
			this.ChangeNetworkScene();
		}
		if (SceneMotor.Instance._loaderCanvas.activeSelf && Camera.main == null && !this.inLoadingScreen)
		{
			SceneMotor.Instance._loaderCanvas.SetActive(false);
		}
		if (this.explorationText.activeSelf != this.testMap)
		{
			this.explorationText.SetActive(this.testMap);
		}
	}

	// Token: 0x06000A62 RID: 2658 RVA: 0x0004BA9C File Offset: 0x00049C9C
	public void ChangeNetworkScene()
	{
		MapsManager.Instance.inExplorationMap = false;
		if (this.firstToXWins)
		{
			using (List<NetworkObject>.Enumerator enumerator = SteamLobby.Instance.players.GetEnumerator())
			{
				while (enumerator.MoveNext())
				{
					NetworkObject networkObject = enumerator.Current;
					int playerId = networkObject.GetComponent<ClientInstance>().PlayerId;
					int teamId = ScoreManager.Instance.GetTeamId(playerId);
					if (ScoreManager.Instance.GetPoints(teamId) >= this.SyncAccessor_roundAmount)
					{
						this.ServerEndGameScene();
						return;
					}
				}
				goto IL_008B;
			}
		}
		if (this.SyncAccessor_sceneIndex == 0)
		{
			this.ServerEndGameScene();
			return;
		}
		IL_008B:
		bool flag = false;
		using (List<string>.Enumerator enumerator2 = this.playedMaps.GetEnumerator())
		{
			while (enumerator2.MoveNext())
			{
				if (enumerator2.Current == SceneMotor.Instance.gameMaps[this.SyncAccessor_sceneIndex])
				{
					flag = true;
					break;
				}
			}
		}
		if (!flag)
		{
			this.playedMaps.Add(SceneMotor.Instance.gameMaps[this.SyncAccessor_sceneIndex]);
		}
		if (this.SyncAccessor_sceneIndex >= SceneMotor.Instance.gameMaps.Length)
		{
			return;
		}
		if (SceneMotor.Instance.gameMaps[this.SyncAccessor_sceneIndex] == global::UnityEngine.SceneManagement.SceneManager.GetActiveScene().name)
		{
			SceneLoadData sceneLoadData = new SceneLoadData("EmptyScene");
			sceneLoadData.ReplaceScenes = ReplaceOption.All;
			InstanceFinder.SceneManager.LoadGlobalScenes(sceneLoadData);
		}
		SceneLoadData sceneLoadData2 = new SceneLoadData(SceneMotor.Instance.gameMaps[this.SyncAccessor_sceneIndex]);
		sceneLoadData2.ReplaceScenes = ReplaceOption.All;
		InstanceFinder.SceneManager.LoadGlobalScenes(sceneLoadData2);
		SceneMotor.Instance.ChangeSceneId();
	}

	// Token: 0x06000A63 RID: 2659 RVA: 0x0004BC4C File Offset: 0x00049E4C
	public void ServerStartGameScene()
	{
		bool flag = false;
		int teamId = ScoreManager.Instance.GetTeamId(ClientInstance.playerInstances[0].PlayerId);
		for (int i = 0; i < 4; i++)
		{
			if (ClientInstance.playerInstances.ContainsKey(i) && ScoreManager.Instance.GetTeamId(ClientInstance.playerInstances[i].PlayerId) != teamId)
			{
				flag = true;
				break;
			}
		}
		if (SceneMotor.Instance.scenes.Count == 0)
		{
			PauseManager.Instance.WriteOfflineLog("No Maps selected !");
			return;
		}
		if (!flag && GameManager.Instance.SyncAccessor_playingTeams && ClientInstance.playerInstances.Count > 1)
		{
			PauseManager.Instance.WriteLog("All Players are in the same Team !");
			return;
		}
		this.RoundsAlgorithm();
		this.playedMaps.Clear();
		this.playedMaps.Add(SceneMotor.Instance.gameMaps[0]);
		SceneLoadData sceneLoadData = new SceneLoadData(SceneMotor.Instance.gameMaps[0]);
		sceneLoadData.ReplaceScenes = ReplaceOption.All;
		InstanceFinder.SceneManager.LoadGlobalScenes(sceneLoadData);
		SceneMotor.Instance.ChangeSceneId();
		GameManager.Instance.StartGame();
	}

	// Token: 0x06000A64 RID: 2660 RVA: 0x0004BD60 File Offset: 0x00049F60
	public void ServerRestartGameScene()
	{
		this.RoundsAlgorithm();
		this.playedMaps.Clear();
		this.playedMaps.Add(SceneMotor.Instance.gameMaps[0]);
		SceneLoadData sceneLoadData = new SceneLoadData(SceneMotor.Instance.gameMaps[0]);
		sceneLoadData.ReplaceScenes = ReplaceOption.All;
		InstanceFinder.SceneManager.LoadGlobalScenes(sceneLoadData);
		SceneMotor.Instance.ResetSceneId();
		GameManager.Instance.StartGame();
	}

	// Token: 0x06000A65 RID: 2661 RVA: 0x0004BDD0 File Offset: 0x00049FD0
	public void ServerEndGameScene()
	{
		SceneLoadData sceneLoadData = new SceneLoadData(this.victoryScene);
		sceneLoadData.ReplaceScenes = ReplaceOption.All;
		InstanceFinder.SceneManager.LoadGlobalScenes(sceneLoadData);
		SceneMotor.Instance.ChangeSceneId();
	}

	// Token: 0x06000A66 RID: 2662 RVA: 0x0004BE08 File Offset: 0x0004A008
	public void ServerLeaveGameScene()
	{
		SceneLoadData sceneLoadData = new SceneLoadData("MainMenu");
		sceneLoadData.ReplaceScenes = ReplaceOption.All;
		InstanceFinder.SceneManager.LoadGlobalScenes(sceneLoadData);
	}

	// Token: 0x06000A67 RID: 2663 RVA: 0x0004BE32 File Offset: 0x0004A032
	public void ClientLeaveGameScene()
	{
		global::UnityEngine.SceneManagement.SceneManager.LoadScene("MainMenu");
	}

	// Token: 0x06000A68 RID: 2664 RVA: 0x0004BE3E File Offset: 0x0004A03E
	[ServerRpc(RequireOwnership = false)]
	private void ChangeSceneId()
	{
		this.RpcWriter___Server_ChangeSceneId_2166136261();
	}

	// Token: 0x06000A69 RID: 2665 RVA: 0x0004BE46 File Offset: 0x0004A046
	[ServerRpc(RequireOwnership = false)]
	public void ResetSceneId()
	{
		this.RpcWriter___Server_ResetSceneId_2166136261();
	}

	// Token: 0x06000A6A RID: 2666 RVA: 0x0004BE4E File Offset: 0x0004A04E
	[ServerRpc(RequireOwnership = false)]
	public void SetSceneIdToZero()
	{
		this.RpcWriter___Server_SetSceneIdToZero_2166136261();
	}

	// Token: 0x06000A6B RID: 2667 RVA: 0x0004BE56 File Offset: 0x0004A056
	[ServerRpc(RequireOwnership = false)]
	private void CloseScene(string sceneToClose)
	{
		this.RpcWriter___Server_CloseScene_3615296227(sceneToClose);
	}

	// Token: 0x06000A6C RID: 2668 RVA: 0x0004BE62 File Offset: 0x0004A062
	[ObserversRpc]
	private void CloseScenesObserver(string sceneToClose)
	{
		this.RpcWriter___Observers_CloseScenesObserver_3615296227(sceneToClose);
	}

	// Token: 0x06000A6D RID: 2669 RVA: 0x0004BE70 File Offset: 0x0004A070
	public void Shuffle(List<string> texts)
	{
		for (int i = 0; i < texts.Count; i++)
		{
			string text = texts[i];
			int num = global::UnityEngine.Random.Range(i, texts.Count);
			texts[i] = texts[num];
			texts[num] = text;
		}
	}

	// Token: 0x06000A6E RID: 2670 RVA: 0x0004BEB9 File Offset: 0x0004A0B9
	[ServerRpc(RunLocally = true)]
	public void ReturnMenuServer()
	{
		this.RpcWriter___Server_ReturnMenuServer_2166136261();
		this.RpcLogic___ReturnMenuServer_2166136261();
	}

	// Token: 0x06000A6F RID: 2671 RVA: 0x0004BEC7 File Offset: 0x0004A0C7
	[ObserversRpc(RunLocally = true, ExcludeOwner = true)]
	private void ReturnMenuObservers()
	{
		this.RpcWriter___Observers_ReturnMenuObservers_2166136261();
		this.RpcLogic___ReturnMenuObservers_2166136261();
	}

	// Token: 0x06000A70 RID: 2672 RVA: 0x0004BED8 File Offset: 0x0004A0D8
	[ServerRpc(RequireOwnership = false)]
	public void LeaveMatchForAll()
	{
		this.RpcWriter___Server_LeaveMatchForAll_2166136261();
	}

	// Token: 0x06000A71 RID: 2673 RVA: 0x0004BEEB File Offset: 0x0004A0EB
	public void OnLoadSceneStart(SceneLoadStartEventArgs args)
	{
		if (!this.canLoadIntoScene)
		{
			return;
		}
		this.inLoadingScreen = true;
		SceneMotor.Instance._loaderCanvas.SetActive(true);
	}

	// Token: 0x06000A72 RID: 2674 RVA: 0x0004BF0D File Offset: 0x0004A10D
	public void ShowLoadingScreen()
	{
		SceneMotor.Instance._loaderCanvas.SetActive(true);
	}

	// Token: 0x06000A73 RID: 2675 RVA: 0x0004BF1F File Offset: 0x0004A11F
	public void OnLoadSceneEnd(SceneLoadEndEventArgs args)
	{
		SceneMotor.Instance.StartCoroutine(SceneMotor.Instance.DelayLoad());
	}

	// Token: 0x06000A74 RID: 2676 RVA: 0x0004BF36 File Offset: 0x0004A136
	private IEnumerator DelayLoad()
	{
		yield return new WaitForSeconds(0.25f);
		this.inLoadingScreen = false;
		SceneMotor.Instance._loaderCanvas.SetActive(false);
		yield break;
	}

	// Token: 0x06000A75 RID: 2677 RVA: 0x0004BF45 File Offset: 0x0004A145
	public IEnumerator StartGameClients()
	{
		yield return new WaitForSeconds(1.2f);
		SceneMotor.Instance._loaderCanvas.SetActive(false);
		yield break;
	}

	// Token: 0x06000A76 RID: 2678 RVA: 0x0004BF50 File Offset: 0x0004A150
	[ServerRpc(RequireOwnership = false)]
	public void EnterScene(string sceneName)
	{
		this.RpcWriter___Server_EnterScene_3615296227(sceneName);
	}

	// Token: 0x06000A77 RID: 2679 RVA: 0x0004BF67 File Offset: 0x0004A167
	[ObserversRpc]
	private void EnterSceneForAll()
	{
		this.RpcWriter___Observers_EnterSceneForAll_2166136261();
	}

	// Token: 0x06000A7A RID: 2682 RVA: 0x0004BF98 File Offset: 0x0004A198
	public virtual void NetworkInitialize___Early()
	{
		if (this.NetworkInitializeEarly_SceneMotor_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeEarly_SceneMotor_Assembly-CSharp.dll = true;
		this.syncVar___roundAmount = new SyncVar<int>(this, 1U, WritePermission.ServerOnly, ReadPermission.Observers, -1f, Channel.Reliable, this.roundAmount);
		this.syncVar___sceneIndex = new SyncVar<int>(this, 0U, WritePermission.ServerOnly, ReadPermission.Observers, -1f, Channel.Reliable, this.sceneIndex);
		base.RegisterServerRpc(0U, new ServerRpcDelegate(this.RpcReader___Server_CmdChangeRoundAmount_2166136261));
		base.RegisterServerRpc(1U, new ServerRpcDelegate(this.RpcReader___Server_ChangeSceneId_2166136261));
		base.RegisterServerRpc(2U, new ServerRpcDelegate(this.RpcReader___Server_ResetSceneId_2166136261));
		base.RegisterServerRpc(3U, new ServerRpcDelegate(this.RpcReader___Server_SetSceneIdToZero_2166136261));
		base.RegisterServerRpc(4U, new ServerRpcDelegate(this.RpcReader___Server_CloseScene_3615296227));
		base.RegisterObserversRpc(5U, new ClientRpcDelegate(this.RpcReader___Observers_CloseScenesObserver_3615296227));
		base.RegisterServerRpc(6U, new ServerRpcDelegate(this.RpcReader___Server_ReturnMenuServer_2166136261));
		base.RegisterObserversRpc(7U, new ClientRpcDelegate(this.RpcReader___Observers_ReturnMenuObservers_2166136261));
		base.RegisterServerRpc(8U, new ServerRpcDelegate(this.RpcReader___Server_LeaveMatchForAll_2166136261));
		base.RegisterServerRpc(9U, new ServerRpcDelegate(this.RpcReader___Server_EnterScene_3615296227));
		base.RegisterObserversRpc(10U, new ClientRpcDelegate(this.RpcReader___Observers_EnterSceneForAll_2166136261));
		base.RegisterSyncVarRead(new SyncVarReadDelegate(this.ReadSyncVar___SceneMotor));
	}

	// Token: 0x06000A7B RID: 2683 RVA: 0x0004C11B File Offset: 0x0004A31B
	public virtual void NetworkInitialize__Late()
	{
		if (this.NetworkInitializeLate_SceneMotor_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeLate_SceneMotor_Assembly-CSharp.dll = true;
		this.syncVar___roundAmount.SetRegistered();
		this.syncVar___sceneIndex.SetRegistered();
	}

	// Token: 0x06000A7C RID: 2684 RVA: 0x0004C144 File Offset: 0x0004A344
	public override void NetworkInitializeIfDisabled()
	{
		this.NetworkInitialize___Early();
		this.NetworkInitialize__Late();
	}

	// Token: 0x06000A7D RID: 2685 RVA: 0x0004C154 File Offset: 0x0004A354
	private void RpcWriter___Server_CmdChangeRoundAmount_2166136261()
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendServerRpc(0U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000A7E RID: 2686 RVA: 0x0004C1EE File Offset: 0x0004A3EE
	public void RpcLogic___CmdChangeRoundAmount_2166136261()
	{
		this.sync___set_value_roundAmount(this.roundAmountDropdown.value + 2, true);
	}

	// Token: 0x06000A7F RID: 2687 RVA: 0x0004C204 File Offset: 0x0004A404
	private void RpcReader___Server_CmdChangeRoundAmount_2166136261(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		if (!base.IsServer)
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___CmdChangeRoundAmount_2166136261();
	}

	// Token: 0x06000A80 RID: 2688 RVA: 0x0004C234 File Offset: 0x0004A434
	private void RpcWriter___Server_ChangeSceneId_2166136261()
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendServerRpc(1U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000A81 RID: 2689 RVA: 0x0004C2CE File Offset: 0x0004A4CE
	private void RpcLogic___ChangeSceneId_2166136261()
	{
		if (SceneMotor.Instance.SyncAccessor_sceneIndex < SceneMotor.Instance.gameMaps.Length - 1)
		{
			SceneMotor instance = SceneMotor.Instance;
			instance.sync___set_value_sceneIndex(instance.SyncAccessor_sceneIndex + 1, true);
			return;
		}
		SceneMotor.Instance.sync___set_value_sceneIndex(0, true);
	}

	// Token: 0x06000A82 RID: 2690 RVA: 0x0004C30C File Offset: 0x0004A50C
	private void RpcReader___Server_ChangeSceneId_2166136261(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		if (!base.IsServer)
		{
			return;
		}
		this.RpcLogic___ChangeSceneId_2166136261();
	}

	// Token: 0x06000A83 RID: 2691 RVA: 0x0004C32C File Offset: 0x0004A52C
	private void RpcWriter___Server_ResetSceneId_2166136261()
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendServerRpc(2U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000A84 RID: 2692 RVA: 0x0004C3C6 File Offset: 0x0004A5C6
	public void RpcLogic___ResetSceneId_2166136261()
	{
		SceneMotor.Instance.sync___set_value_sceneIndex(1, true);
	}

	// Token: 0x06000A85 RID: 2693 RVA: 0x0004C3D4 File Offset: 0x0004A5D4
	private void RpcReader___Server_ResetSceneId_2166136261(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		if (!base.IsServer)
		{
			return;
		}
		this.RpcLogic___ResetSceneId_2166136261();
	}

	// Token: 0x06000A86 RID: 2694 RVA: 0x0004C3F4 File Offset: 0x0004A5F4
	private void RpcWriter___Server_SetSceneIdToZero_2166136261()
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendServerRpc(3U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000A87 RID: 2695 RVA: 0x0004C48E File Offset: 0x0004A68E
	public void RpcLogic___SetSceneIdToZero_2166136261()
	{
		SceneMotor.Instance.sync___set_value_sceneIndex(0, true);
	}

	// Token: 0x06000A88 RID: 2696 RVA: 0x0004C49C File Offset: 0x0004A69C
	private void RpcReader___Server_SetSceneIdToZero_2166136261(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		if (!base.IsServer)
		{
			return;
		}
		this.RpcLogic___SetSceneIdToZero_2166136261();
	}

	// Token: 0x06000A89 RID: 2697 RVA: 0x0004C4BC File Offset: 0x0004A6BC
	private void RpcWriter___Server_CloseScene_3615296227(string sceneToClose)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteString(sceneToClose);
		base.SendServerRpc(4U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000A8A RID: 2698 RVA: 0x0004C563 File Offset: 0x0004A763
	private void RpcLogic___CloseScene_3615296227(string sceneToClose)
	{
		this.CloseScenesObserver(sceneToClose);
	}

	// Token: 0x06000A8B RID: 2699 RVA: 0x0004C56C File Offset: 0x0004A76C
	private void RpcReader___Server_CloseScene_3615296227(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		string text = PooledReader0.ReadString();
		if (!base.IsServer)
		{
			return;
		}
		this.RpcLogic___CloseScene_3615296227(text);
	}

	// Token: 0x06000A8C RID: 2700 RVA: 0x0004C5A0 File Offset: 0x0004A7A0
	private void RpcWriter___Observers_CloseScenesObserver_3615296227(string sceneToClose)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteString(sceneToClose);
		base.SendObserversRpc(5U, writer, channel, DataOrderType.Default, false, false, false);
		writer.Store();
	}

	// Token: 0x06000A8D RID: 2701 RVA: 0x0004C656 File Offset: 0x0004A856
	private void RpcLogic___CloseScenesObserver_3615296227(string sceneToClose)
	{
		global::UnityEngine.SceneManagement.SceneManager.UnloadSceneAsync(sceneToClose);
	}

	// Token: 0x06000A8E RID: 2702 RVA: 0x0004C660 File Offset: 0x0004A860
	private void RpcReader___Observers_CloseScenesObserver_3615296227(PooledReader PooledReader0, Channel channel)
	{
		string text = PooledReader0.ReadString();
		if (!base.IsClient)
		{
			return;
		}
		this.RpcLogic___CloseScenesObserver_3615296227(text);
	}

	// Token: 0x06000A8F RID: 2703 RVA: 0x0004C694 File Offset: 0x0004A894
	private void RpcWriter___Server_ReturnMenuServer_2166136261()
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendServerRpc(6U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000A90 RID: 2704 RVA: 0x0004C788 File Offset: 0x0004A988
	public void RpcLogic___ReturnMenuServer_2166136261()
	{
		this.ReturnMenuObservers();
	}

	// Token: 0x06000A91 RID: 2705 RVA: 0x0004C790 File Offset: 0x0004A990
	private void RpcReader___Server_ReturnMenuServer_2166136261(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___ReturnMenuServer_2166136261();
	}

	// Token: 0x06000A92 RID: 2706 RVA: 0x0004C7D0 File Offset: 0x0004A9D0
	private void RpcWriter___Observers_ReturnMenuObservers_2166136261()
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendObserversRpc(7U, writer, channel, DataOrderType.Default, false, false, true);
		writer.Store();
	}

	// Token: 0x06000A93 RID: 2707 RVA: 0x0004BE32 File Offset: 0x0004A032
	private void RpcLogic___ReturnMenuObservers_2166136261()
	{
		global::UnityEngine.SceneManagement.SceneManager.LoadScene("MainMenu");
	}

	// Token: 0x06000A94 RID: 2708 RVA: 0x0004C87C File Offset: 0x0004AA7C
	private void RpcReader___Observers_ReturnMenuObservers_2166136261(PooledReader PooledReader0, Channel channel)
	{
		if (!base.IsClient)
		{
			return;
		}
		if (base.IsHost)
		{
			return;
		}
		this.RpcLogic___ReturnMenuObservers_2166136261();
	}

	// Token: 0x06000A95 RID: 2709 RVA: 0x0004C8A8 File Offset: 0x0004AAA8
	private void RpcWriter___Server_LeaveMatchForAll_2166136261()
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendServerRpc(8U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000A96 RID: 2710 RVA: 0x0004C944 File Offset: 0x0004AB44
	public void RpcLogic___LeaveMatchForAll_2166136261()
	{
		SceneLoadData sceneLoadData = new SceneLoadData("MainMenu");
		sceneLoadData.ReplaceScenes = ReplaceOption.All;
		InstanceFinder.SceneManager.LoadGlobalScenes(sceneLoadData);
		this.SetSceneIdToZero();
	}

	// Token: 0x06000A97 RID: 2711 RVA: 0x0004C974 File Offset: 0x0004AB74
	private void RpcReader___Server_LeaveMatchForAll_2166136261(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		if (!base.IsServer)
		{
			return;
		}
		this.RpcLogic___LeaveMatchForAll_2166136261();
	}

	// Token: 0x06000A98 RID: 2712 RVA: 0x0004C994 File Offset: 0x0004AB94
	private void RpcWriter___Server_EnterScene_3615296227(string sceneName)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteString(sceneName);
		base.SendServerRpc(9U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000A99 RID: 2713 RVA: 0x0004CA3C File Offset: 0x0004AC3C
	public void RpcLogic___EnterScene_3615296227(string sceneName)
	{
		this.EnterSceneForAll();
		if (SteamLobby.Instance.players.Count > 1)
		{
			SceneLoadData sceneLoadData = new SceneLoadData(sceneName);
			sceneLoadData.ReplaceScenes = ReplaceOption.All;
			InstanceFinder.SceneManager.LoadGlobalScenes(sceneLoadData);
			return;
		}
		global::UnityEngine.SceneManagement.SceneManager.UnloadSceneAsync("MainMenu");
		SceneLookupData sceneLookupData = new SceneLookupData
		{
			Name = sceneName
		};
		SceneLoadData sceneLoadData2 = new SceneLoadData(sceneName);
		sceneLoadData2.PreferredActiveScene = sceneLookupData;
		sceneLoadData2.ReplaceScenes = ReplaceOption.All;
		InstanceFinder.SceneManager.LoadConnectionScenes(LobbyController.Instance.LocalPlayerController.Owner, sceneLoadData2);
	}

	// Token: 0x06000A9A RID: 2714 RVA: 0x0004CAC4 File Offset: 0x0004ACC4
	private void RpcReader___Server_EnterScene_3615296227(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		string text = PooledReader0.ReadString();
		if (!base.IsServer)
		{
			return;
		}
		this.RpcLogic___EnterScene_3615296227(text);
	}

	// Token: 0x06000A9B RID: 2715 RVA: 0x0004CAF8 File Offset: 0x0004ACF8
	private void RpcWriter___Observers_EnterSceneForAll_2166136261()
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendObserversRpc(10U, writer, channel, DataOrderType.Default, false, false, false);
		writer.Store();
	}

	// Token: 0x06000A9C RID: 2716 RVA: 0x0004CBA1 File Offset: 0x0004ADA1
	private void RpcLogic___EnterSceneForAll_2166136261()
	{
		this.testMap = true;
	}

	// Token: 0x06000A9D RID: 2717 RVA: 0x0004CBAC File Offset: 0x0004ADAC
	private void RpcReader___Observers_EnterSceneForAll_2166136261(PooledReader PooledReader0, Channel channel)
	{
		if (!base.IsClient)
		{
			return;
		}
		this.RpcLogic___EnterSceneForAll_2166136261();
	}

	// Token: 0x1700009B RID: 155
	// (get) Token: 0x06000A9E RID: 2718 RVA: 0x0004CBCC File Offset: 0x0004ADCC
	// (set) Token: 0x06000A9F RID: 2719 RVA: 0x0004CBD4 File Offset: 0x0004ADD4
	public int SyncAccessor_sceneIndex
	{
		get
		{
			return this.sceneIndex;
		}
		set
		{
			if (value || !base.IsServer)
			{
				this.sceneIndex = value;
			}
			this.syncVar___sceneIndex.SetValue(value, value);
		}
	}

	// Token: 0x06000AA0 RID: 2720 RVA: 0x0004CC0C File Offset: 0x0004AE0C
	public virtual bool ReadSyncVar___SceneMotor(PooledReader PooledReader0, uint UInt321, bool Boolean2)
	{
		if (UInt321 == 1U)
		{
			if (PooledReader0 == null)
			{
				this.sync___set_value_roundAmount(this.syncVar___roundAmount.GetValue(true), true);
				return true;
			}
			int num = PooledReader0.ReadInt32(AutoPackType.Packed);
			this.sync___set_value_roundAmount(num, Boolean2);
			return true;
		}
		else
		{
			if (UInt321 != 0U)
			{
				return false;
			}
			if (PooledReader0 == null)
			{
				this.sync___set_value_sceneIndex(this.syncVar___sceneIndex.GetValue(true), true);
				return true;
			}
			int num2 = PooledReader0.ReadInt32(AutoPackType.Packed);
			this.sync___set_value_sceneIndex(num2, Boolean2);
			return true;
		}
	}

	// Token: 0x1700009C RID: 156
	// (get) Token: 0x06000AA1 RID: 2721 RVA: 0x0004CCAC File Offset: 0x0004AEAC
	// (set) Token: 0x06000AA2 RID: 2722 RVA: 0x0004CCB4 File Offset: 0x0004AEB4
	public int SyncAccessor_roundAmount
	{
		get
		{
			return this.roundAmount;
		}
		set
		{
			if (value || !base.IsServer)
			{
				this.roundAmount = value;
			}
			this.syncVar___roundAmount.SetValue(value, value);
		}
	}

	// Token: 0x06000AA3 RID: 2723 RVA: 0x0004CCE9 File Offset: 0x0004AEE9
	public virtual void Awake___UserLogic()
	{
		SceneMotor.Instance = this;
	}

	// Token: 0x0400091A RID: 2330
	public static SceneMotor Instance;

	// Token: 0x0400091B RID: 2331
	public bool firstToXWins;

	// Token: 0x0400091C RID: 2332
	[Space]
	[SerializeField]
	private bool shuffle = true;

	// Token: 0x0400091D RID: 2333
	public List<string> scenes;

	// Token: 0x0400091E RID: 2334
	public List<string> playedMaps;

	// Token: 0x0400091F RID: 2335
	public List<string> remainingMaps;

	// Token: 0x04000920 RID: 2336
	[SerializeField]
	private string victoryScene = "VictoryScene";

	// Token: 0x04000921 RID: 2337
	[SerializeField]
	private int loops = 5;

	// Token: 0x04000922 RID: 2338
	public string[] gameMaps;

	// Token: 0x04000923 RID: 2339
	[SyncVar]
	public int sceneIndex;

	// Token: 0x04000924 RID: 2340
	[SerializeField]
	public GameObject _loaderCanvas;

	// Token: 0x04000925 RID: 2341
	[SerializeField]
	public TextMeshProUGUI sceneText;

	// Token: 0x04000926 RID: 2342
	private TMP_Dropdown roundAmountDropdown;

	// Token: 0x04000927 RID: 2343
	[SyncVar]
	public int roundAmount;

	// Token: 0x04000928 RID: 2344
	private bool canLoadIntoScene;

	// Token: 0x04000929 RID: 2345
	[Space]
	[SerializeField]
	private GameObject explorationText;

	// Token: 0x0400092A RID: 2346
	private string[] tempScenes;

	// Token: 0x0400092B RID: 2347
	public bool inLoadingScreen;

	// Token: 0x0400092C RID: 2348
	public bool testMap;

	// Token: 0x0400092D RID: 2349
	public SyncVar<int> syncVar___sceneIndex;

	// Token: 0x0400092E RID: 2350
	public SyncVar<int> syncVar___roundAmount;

	// Token: 0x0400092F RID: 2351
	private bool NetworkInitializeEarly_SceneMotor_Assembly-CSharp.dll;

	// Token: 0x04000930 RID: 2352
	private bool NetworkInitializeLate_SceneMotor_Assembly-CSharp.dll;
}

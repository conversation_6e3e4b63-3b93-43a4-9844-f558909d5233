﻿using System;
using DG.Tweening;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace Goodgulf.Graphics
{
	// Token: 0x02000187 RID: 391
	public class MatchChatLine : MonoBehaviour
	{
		// Token: 0x06000FD1 RID: 4049 RVA: 0x00067AC8 File Offset: 0x00065CC8
		private void Start()
		{
			this.ialpha = this.matchImg.color.a;
			if (this.deleteMe)
			{
				this.StartDuration();
			}
			if (this.chatMsg)
			{
				SoundManager.Instance.PlaySound(PauseManager.Instance.matchChatClip);
			}
			base.transform.DOPunchScale(new Vector3(this.tweenSize, this.tweenSize, this.tweenSize), 0.3f, 10, 1f).SetEase(this.easeType);
		}

		// Token: 0x06000FD2 RID: 4050 RVA: 0x00067B4F File Offset: 0x00065D4F
		public void StartDuration()
		{
			this.startCounting = true;
		}

		// Token: 0x06000FD3 RID: 4051 RVA: 0x00067B58 File Offset: 0x00065D58
		private void Update()
		{
			if (!this.startCounting)
			{
				return;
			}
			this.currentTime += Time.deltaTime;
			if (this.currentTime > this.duration)
			{
				this.startFading = true;
			}
			if (this.startFading)
			{
				if (this.currentFadeTime < this.fadeDuration)
				{
					this.alpha = Mathf.Lerp(this.ialpha, 0f, this.currentFadeTime / this.fadeDuration);
					this.matchLine.color = new Color(this.matchLine.color.r, this.matchLine.color.g, this.matchLine.color.b, this.alpha);
					if (this.nameLine != null)
					{
						this.nameLine.color = new Color(this.nameLine.color.r, this.nameLine.color.g, this.nameLine.color.b, this.alpha);
					}
					this.currentFadeTime += Time.deltaTime;
					if (this.matchImg)
					{
						this.matchImg.color = new Color(this.matchImg.color.r, this.matchImg.color.g, this.matchImg.color.b, this.alpha);
					}
				}
				else
				{
					this.deleteMe = true;
				}
			}
			if (this.currentTime > this.duration + this.fadeDuration)
			{
				global::UnityEngine.Object.Destroy(base.gameObject);
			}
		}

		// Token: 0x04000EB9 RID: 3769
		public float duration;

		// Token: 0x04000EBA RID: 3770
		public float fadeDuration;

		// Token: 0x04000EBB RID: 3771
		public bool deleteMe;

		// Token: 0x04000EBC RID: 3772
		public bool chatMsg;

		// Token: 0x04000EBD RID: 3773
		private bool startCounting;

		// Token: 0x04000EBE RID: 3774
		private bool startFading;

		// Token: 0x04000EBF RID: 3775
		private float currentTime;

		// Token: 0x04000EC0 RID: 3776
		private float currentFadeTime;

		// Token: 0x04000EC1 RID: 3777
		private float alpha;

		// Token: 0x04000EC2 RID: 3778
		private float ialpha;

		// Token: 0x04000EC3 RID: 3779
		public float tweenSize = 1.01f;

		// Token: 0x04000EC4 RID: 3780
		public Ease easeType;

		// Token: 0x04000EC5 RID: 3781
		[SerializeField]
		private TMP_Text matchLine;

		// Token: 0x04000EC6 RID: 3782
		[SerializeField]
		private TMP_Text nameLine;

		// Token: 0x04000EC7 RID: 3783
		[SerializeField]
		private RawImage matchImg;
	}
}

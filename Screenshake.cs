﻿using System;
using System.Collections;
using UnityEngine;

// Token: 0x02000034 RID: 52
public class Screenshake : MonoBehaviour
{
	// Token: 0x060002D9 RID: 729 RVA: 0x00018339 File Offset: 0x00016539
	private void Start()
	{
		this.startingPos = Vector3.zero;
	}

	// Token: 0x060002DA RID: 730 RVA: 0x00018348 File Offset: 0x00016548
	private void Update()
	{
		this.timer -= Time.deltaTime;
		if (this.start)
		{
			this.timer = this.duration;
			this.start = false;
			base.StartCoroutine(this.Shaking());
		}
		if (this.timer < 0f)
		{
			base.transform.localPosition = Vector3.Lerp(base.transform.localPosition, this.startingPos, 4f * Time.deltaTime);
		}
	}

	// Token: 0x060002DB RID: 731 RVA: 0x000183C8 File Offset: 0x000165C8
	private IEnumerator Shaking()
	{
		float elapsedTime = 0f;
		while (elapsedTime < this.duration)
		{
			elapsedTime += Time.deltaTime;
			float num = this.strengthCurve.Evaluate(elapsedTime / this.duration);
			base.transform.localPosition = this.startingPos + global::UnityEngine.Random.insideUnitSphere * num;
			yield return null;
		}
		base.transform.localPosition = this.startingPos;
		yield break;
	}

	// Token: 0x04000326 RID: 806
	public bool start;

	// Token: 0x04000327 RID: 807
	public float smallFallDuration = 1f;

	// Token: 0x04000328 RID: 808
	public float bigFallDuration = 1f;

	// Token: 0x04000329 RID: 809
	[HideInInspector]
	public float duration = 1f;

	// Token: 0x0400032A RID: 810
	public AnimationCurve smallCurve;

	// Token: 0x0400032B RID: 811
	public AnimationCurve bigCurve;

	// Token: 0x0400032C RID: 812
	[HideInInspector]
	public AnimationCurve strengthCurve;

	// Token: 0x0400032D RID: 813
	private float timer;

	// Token: 0x0400032E RID: 814
	private Vector3 startingPos;
}

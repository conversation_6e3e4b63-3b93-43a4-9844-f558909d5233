﻿using System;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using Steamworks;
using UnityEngine;

// Token: 0x0200011B RID: 283
public class LobbiesListManager : MonoBehaviour
{
	// Token: 0x06000E0B RID: 3595 RVA: 0x0005E25E File Offset: 0x0005C45E
	private void Awake()
	{
		if (LobbiesListManager.Instance == null)
		{
			LobbiesListManager.Instance = this;
		}
	}

	// Token: 0x06000E0C RID: 3596 RVA: 0x0005E273 File Offset: 0x0005C473
	public void GetListOfLobbies()
	{
		if (!this.lobbyListContent.activeInHierarchy)
		{
			return;
		}
		SteamLobby.Instance.GetLobbiesList();
	}

	// Token: 0x06000E0D RID: 3597 RVA: 0x0005E290 File Offset: 0x0005C490
	public void DestroyLobbies()
	{
		foreach (GameObject gameObject in this.listOfLobbies)
		{
			global::UnityEngine.Object.Destroy(gameObject);
		}
		this.listOfLobbies.Clear();
	}

	// Token: 0x06000E0E RID: 3598 RVA: 0x0005E2EC File Offset: 0x0005C4EC
	public void DisplayLobbyWithData(CSteamID lobbyID)
	{
		int lobbyMemberLimit = SteamMatchmaking.GetLobbyMemberLimit(lobbyID);
		int numLobbyMembers = SteamMatchmaking.GetNumLobbyMembers(lobbyID);
		if (lobbyMemberLimit > 4)
		{
			return;
		}
		if (numLobbyMembers < 1)
		{
			return;
		}
		GameObject gameObject = global::UnityEngine.Object.Instantiate<GameObject>(this.lobbyDataItemPrefab, this.lobbyListContent.transform, true);
		LobbyDataEntry component = gameObject.GetComponent<LobbyDataEntry>();
		component.lobbyID = lobbyID;
		component.lobbyName = SteamMatchmaking.GetLobbyData(lobbyID, "name");
		component.lobbyName = ProfanityFilterProvider.Filter.CensorString(component.lobbyName);
		component.lobbyName = Regex.Replace(component.lobbyName, "<size=\\d+>", "");
		component.lobbyName = Regex.Replace(component.lobbyName, "</size>", "");
		component.ownDlc0 = SteamMatchmaking.GetLobbyData(lobbyID, "ownDlc0");
		component.gamemode = SteamMatchmaking.GetLobbyData(lobbyID, "gamemode");
		component.playerCount = numLobbyMembers;
		component.maxPlayers = lobbyMemberLimit;
		component.RenderLobby();
		gameObject.transform.localScale = Vector3.one;
		this.listOfLobbies.Add(gameObject);
	}

	// Token: 0x04000C70 RID: 3184
	public static LobbiesListManager Instance;

	// Token: 0x04000C71 RID: 3185
	public GameObject lobbyDataItemPrefab;

	// Token: 0x04000C72 RID: 3186
	public GameObject lobbyListContent;

	// Token: 0x04000C73 RID: 3187
	public GameObject lobbiesButton;

	// Token: 0x04000C74 RID: 3188
	public GameObject hostButton;

	// Token: 0x04000C75 RID: 3189
	public List<GameObject> listOfLobbies = new List<GameObject>();
}

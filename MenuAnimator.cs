﻿using System;
using UnityEngine;

// Token: 0x020000B7 RID: 183
public class MenuAnimator : MonoBehaviour
{
	// Token: 0x06000A46 RID: 2630 RVA: 0x000023D6 File Offset: 0x000005D6
	private void Start()
	{
	}

	// Token: 0x06000A47 RID: 2631 RVA: 0x0004B1D0 File Offset: 0x000493D0
	private void Update()
	{
		if (Input.GetKeyDown(KeyCode.Alpha1))
		{
			this.anim.SetBool("Crouch", false);
			this.anim.SetBool("Slide", false);
			this.anim.SetFloat("MovementSpeed", 0f);
			this.anim.SetFloat("crouchMove", 0f);
		}
		if (Input.GetKeyDown(KeyCode.Alpha2))
		{
			this.anim.SetBool("Crouch", true);
			this.anim.SetBool("Slide", false);
			this.anim.SetFloat("MovementSpeed", 0f);
			this.anim.SetFloat("crouchMove", 0f);
		}
		if (Input.GetKeyDown(KeyCode.Alpha3))
		{
			this.anim.SetBool("Crouch", false);
			this.anim.SetBool("Slide", true);
			this.anim.SetFloat("MovementSpeed", 0f);
			this.anim.SetFloat("crouchMove", 0f);
		}
		if (Input.GetKeyDown(KeyCode.Alpha4))
		{
			this.anim.SetBool("Crouch", false);
			this.anim.SetBool("Slide", false);
			this.anim.SetFloat("MovementSpeed", 0.5f);
			this.anim.SetFloat("crouchMove", 0f);
		}
		if (Input.GetKeyDown(KeyCode.Alpha5))
		{
			this.anim.SetBool("Crouch", false);
			this.anim.SetBool("Slide", false);
			this.anim.SetFloat("MovementSpeed", 1f);
			this.anim.SetFloat("crouchMove", 0f);
		}
		if (Input.GetKeyDown(KeyCode.Alpha6))
		{
			this.anim.SetBool("Crouch", true);
			this.anim.SetBool("Slide", false);
			this.anim.SetFloat("MovementSpeed", 0f);
			this.anim.SetFloat("crouchMove", 1f);
		}
	}

	// Token: 0x0400090F RID: 2319
	[SerializeField]
	private Animator anim;
}

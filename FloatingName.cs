﻿using System;
using TMPro;
using UnityEngine;

// Token: 0x02000051 RID: 81
public class FloatingName : MonoBehaviour
{
	// Token: 0x060003C6 RID: 966 RVA: 0x0001BBB6 File Offset: 0x00019DB6
	private void Awake()
	{
		if (FloatingName.Instance == null)
		{
			FloatingName.Instance = this;
		}
	}

	// Token: 0x060003C7 RID: 967 RVA: 0x0001BBCB File Offset: 0x00019DCB
	private void Start()
	{
		this.pauseManager = PauseManager.Instance;
	}

	// Token: 0x060003C8 RID: 968 RVA: 0x0001BBD8 File Offset: 0x00019DD8
	private void Update()
	{
		if (!this.pauseManager.inMainMenu)
		{
			this.nameToShow = "";
		}
		if (this.nameToShow == "")
		{
			this.display.SetActive(false);
		}
		else
		{
			this.display.SetActive(true);
		}
		this.text.text = this.nameToShow;
		base.transform.position = Input.mousePosition;
	}

	// Token: 0x0400041C RID: 1052
	public string nameToShow;

	// Token: 0x0400041D RID: 1053
	[SerializeField]
	private TextMeshProUGUI text;

	// Token: 0x0400041E RID: 1054
	public static FloatingName Instance;

	// Token: 0x0400041F RID: 1055
	public GameObject display;

	// Token: 0x04000420 RID: 1056
	private PauseManager pauseManager;
}

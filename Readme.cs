﻿using System;
using UnityEngine;

// Token: 0x0200015C RID: 348
public class Readme : ScriptableObject
{
	// Token: 0x04000DBE RID: 3518
	public Texture2D icon;

	// Token: 0x04000DBF RID: 3519
	public string title;

	// Token: 0x04000DC0 RID: 3520
	public Readme.Section[] sections;

	// Token: 0x04000DC1 RID: 3521
	public bool loadedLayout;

	// Token: 0x0200015D RID: 349
	[Serializable]
	public class Section
	{
		// Token: 0x04000DC2 RID: 3522
		public string heading;

		// Token: 0x04000DC3 RID: 3523
		public string text;

		// Token: 0x04000DC4 RID: 3524
		public string linkText;

		// Token: 0x04000DC5 RID: 3525
		public string url;
	}
}

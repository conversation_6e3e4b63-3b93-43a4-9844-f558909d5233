﻿using System;
using UnityEngine;

// Token: 0x0200016B RID: 363
[RequireComponent(typeof(Light))]
public class CFX_LightIntensityFade : MonoBehaviour
{
	// Token: 0x06000F51 RID: 3921 RVA: 0x00064EF7 File Offset: 0x000630F7
	private void Start()
	{
		this.baseIntensity = base.GetComponent<Light>().intensity;
	}

	// Token: 0x06000F52 RID: 3922 RVA: 0x00064F0A File Offset: 0x0006310A
	private void OnEnable()
	{
		this.p_lifetime = 0f;
		this.p_delay = this.delay;
		if (this.delay > 0f)
		{
			base.GetComponent<Light>().enabled = false;
		}
	}

	// Token: 0x06000F53 RID: 3923 RVA: 0x00064F3C File Offset: 0x0006313C
	private void Update()
	{
		if (this.p_delay > 0f)
		{
			this.p_delay -= Time.deltaTime;
			if (this.p_delay <= 0f)
			{
				base.GetComponent<Light>().enabled = true;
			}
			return;
		}
		if (this.p_lifetime / this.duration < 1f)
		{
			base.GetComponent<Light>().intensity = Mathf.Lerp(this.baseIntensity, this.finalIntensity, this.p_lifetime / this.duration);
			this.p_lifetime += Time.deltaTime;
			return;
		}
		if (this.autodestruct)
		{
			global::UnityEngine.Object.Destroy(base.gameObject);
		}
	}

	// Token: 0x04000E1A RID: 3610
	public float duration = 1f;

	// Token: 0x04000E1B RID: 3611
	public float delay;

	// Token: 0x04000E1C RID: 3612
	public float finalIntensity;

	// Token: 0x04000E1D RID: 3613
	private float baseIntensity;

	// Token: 0x04000E1E RID: 3614
	public bool autodestruct;

	// Token: 0x04000E1F RID: 3615
	private float p_lifetime;

	// Token: 0x04000E20 RID: 3616
	private float p_delay;
}

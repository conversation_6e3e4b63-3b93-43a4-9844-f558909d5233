﻿using System;
using System.IO;
using System.IO.Compression;
using System.Text;
using UnityEngine;

// Token: 0x020000B8 RID: 184
public class PresetSharing : MonoBehaviour
{
	// Token: 0x06000A49 RID: 2633 RVA: 0x0004B3DB File Offset: 0x000495DB
	public static string EncodePreset(string json)
	{
		return Convert.ToBase64String(PresetSharing.Compress(Encoding.UTF8.GetBytes(json))).Replace('+', '-').Replace('/', '_')
			.TrimEnd('=');
	}

	// Token: 0x06000A4A RID: 2634 RVA: 0x0004B40C File Offset: 0x0004960C
	public static string DecodePreset(string encoded)
	{
		string text = encoded.Replace('-', '+').Replace('_', '/');
		while (text.Length % 4 != 0)
		{
			text += "=";
		}
		byte[] array = PresetSharing.Decompress(Convert.FromBase64String(text));
		return JsonUtility.ToJson(Encoding.UTF8.GetString(array));
	}

	// Token: 0x06000A4B RID: 2635 RVA: 0x0004B464 File Offset: 0x00049664
	private static byte[] Compress(byte[] data)
	{
		byte[] array;
		using (MemoryStream memoryStream = new MemoryStream())
		{
			using (GZipStream gzipStream = new GZipStream(memoryStream, CompressionMode.Compress))
			{
				gzipStream.Write(data, 0, data.Length);
			}
			array = memoryStream.ToArray();
		}
		return array;
	}

	// Token: 0x06000A4C RID: 2636 RVA: 0x0004B4C8 File Offset: 0x000496C8
	private static byte[] Decompress(byte[] data)
	{
		byte[] array;
		using (MemoryStream memoryStream = new MemoryStream(data))
		{
			using (GZipStream gzipStream = new GZipStream(memoryStream, CompressionMode.Decompress))
			{
				using (MemoryStream memoryStream2 = new MemoryStream())
				{
					gzipStream.CopyTo(memoryStream2);
					array = memoryStream2.ToArray();
				}
			}
		}
		return array;
	}
}

﻿using System;
using System.Collections;
using DG.Tweening;
using FishNet;
using FishNet.Connection;
using FishNet.Managing;
using FishNet.Object;
using FishNet.Object.Delegating;
using FishNet.Object.Synchronizing;
using FishNet.Serializing;
using FishNet.Serializing.Generated;
using FishNet.Transporting;
using UnityEngine;

// Token: 0x0200008B RID: 139
public class Claymore : NetworkBehaviour
{
	// Token: 0x060006CD RID: 1741 RVA: 0x0002DCE7 File Offset: 0x0002BEE7
	private void OnEnable()
	{
		PauseManager.OnBeforeSpawn += this.StartNewRound;
	}

	// Token: 0x060006CE RID: 1742 RVA: 0x0002DCFA File Offset: 0x0002BEFA
	private void OnDisable()
	{
		PauseManager.OnBeforeSpawn -= this.StartNewRound;
		if (this.lineObject != null)
		{
			global::UnityEngine.Object.Destroy(this.lineObject);
		}
	}

	// Token: 0x060006CF RID: 1743 RVA: 0x0002DD28 File Offset: 0x0002BF28
	[ServerRpc(RequireOwnership = false, RunLocally = true)]
	private void StartNewRound()
	{
		this.RpcWriter___Server_StartNewRound_2166136261();
		this.RpcLogic___StartNewRound_2166136261();
	}

	// Token: 0x060006D0 RID: 1744 RVA: 0x0002DD41 File Offset: 0x0002BF41
	public virtual void Awake()
	{
		this.NetworkInitialize___Early();
		this.Awake___UserLogic();
		this.NetworkInitialize__Late();
	}

	// Token: 0x060006D1 RID: 1745 RVA: 0x0002DD55 File Offset: 0x0002BF55
	private void Start()
	{
		base.StartCoroutine(this.ActivateTrap());
	}

	// Token: 0x060006D2 RID: 1746 RVA: 0x0002DD64 File Offset: 0x0002BF64
	private IEnumerator ActivateTrap()
	{
		yield return new WaitForSeconds(1f);
		this.canExplode = true;
		this.audio.PlayOneShot(this.activationSound);
		RaycastHit raycastHit;
		if (Physics.Raycast(base.transform.position + base.transform.forward * 0.2f, base.transform.forward, out raycastHit, this.boxdimensions.z, this.playerLayer))
		{
			this.RenderObject(base.transform.forward, Vector3.Distance(raycastHit.point, base.transform.position));
			this.rayLength = Vector3.Distance(raycastHit.point, base.transform.position);
		}
		else
		{
			this.RenderObject(base.transform.forward, this.boxdimensions.z);
			this.rayLength = this.boxdimensions.z;
		}
		yield break;
	}

	// Token: 0x060006D3 RID: 1747 RVA: 0x0002DD74 File Offset: 0x0002BF74
	private void Update()
	{
		RaycastHit raycastHit;
		if (this.canExplode && Physics.Raycast(base.transform.position, base.transform.forward, out raycastHit, this.rayLength, this.bodyLayer))
		{
			this.sync___set_value_detonated(true, true);
			this.canExplode = false;
		}
		if (this.SyncAccessor_detonated && this.activated)
		{
			this.HandleExplosion();
			this.sync___set_value_detonated(false, true);
			this.activated = false;
		}
	}

	// Token: 0x060006D4 RID: 1748 RVA: 0x0002DDF0 File Offset: 0x0002BFF0
	public void HandleExplosion()
	{
		if (!base.IsOwner)
		{
			return;
		}
		this.explosionColliders = Physics.OverlapBox(base.transform.position + base.transform.forward * (this.rayLength / 2f), new Vector3(this.boxdimensions.x / 2f, this.boxdimensions.y / 2f, this.rayLength / 2f), Quaternion.LookRotation(base.transform.forward), this.bodyLayer);
		if (this.explosionColliders.Length != 0)
		{
			this.ph2 = new PlayerHealth[this.explosionColliders.Length];
			for (int i = 0; i < this.explosionColliders.Length; i++)
			{
				if (this.explosionColliders[i].GetComponentInParent<PlayerHealth>() != null)
				{
					this.ph2[i] = this.explosionColliders[i].GetComponentInParent<PlayerHealth>();
				}
			}
			for (int j = 0; j < this.ph2.Length; j++)
			{
				if (this.ph2[j] != null && base.IsOwner)
				{
					if (this.ph2[j].transform.gameObject == this.SyncAccessor__rootObject && !this.touched)
					{
						this.ph2[j].ChangeKilledState(true);
						this.ph2[j].RemoveHealth(10f);
						this.ph2[j].suicide = true;
						this.IncreaseSuicidesAmount();
						this.ph2[j].Explode(false, true, this.ph2[j].gameObject.name, this.ph2[j].transform.position - base.transform.position, this.ragdollEjectForce, base.transform.position);
						this.ph2[j].SetKiller(this.SyncAccessor__rootObject.transform);
						this.touched = true;
					}
					else if (this.ph2[j].transform.gameObject != this.SyncAccessor__rootObject && !this.touched2)
					{
						this.ph2[j].ChangeKilledState(true);
						this.ph2[j].RemoveHealth(10f);
						this.KillShockWave();
						this.SendKillLog(this.ph2[j]);
						this.ph2[j].Explode(false, true, this.ph2[j].gameObject.name, this.ph2[j].transform.position - base.transform.position, this.ragdollEjectForce, base.transform.position);
						this.ph2[j].SetKiller(this.SyncAccessor__rootObject.transform);
						this.touched2 = true;
					}
				}
			}
		}
		this.ExplodeServer();
	}

	// Token: 0x060006D5 RID: 1749 RVA: 0x0002E0D1 File Offset: 0x0002C2D1
	[ServerRpc(RunLocally = true)]
	private void ExplodeServer()
	{
		this.RpcWriter___Server_ExplodeServer_2166136261();
		this.RpcLogic___ExplodeServer_2166136261();
	}

	// Token: 0x060006D6 RID: 1750 RVA: 0x0002E0E0 File Offset: 0x0002C2E0
	[ObserversRpc]
	private void ExplodeObservers()
	{
		this.RpcWriter___Observers_ExplodeObservers_2166136261();
	}

	// Token: 0x060006D7 RID: 1751 RVA: 0x0002E0F4 File Offset: 0x0002C2F4
	private void SendKillLog(PlayerHealth enemyHealth)
	{
		if (this.sendKillLog)
		{
			return;
		}
		this.sendKillLog = true;
		PauseManager.Instance.WriteLog(string.Concat(new string[]
		{
			"<b><color=#",
			PauseManager.Instance.selfNameLogColor,
			">",
			enemyHealth.SyncAccessor_playerValues.SyncAccessor_playerClient.PlayerName,
			"</color></b> was killed with a <b><color=white>",
			this.weaponName,
			"</color></b> by <b><color=#",
			PauseManager.Instance.enemyNameLogColor,
			">",
			ClientInstance.Instance.PlayerName,
			"</color></b>"
		}));
	}

	// Token: 0x060006D8 RID: 1752 RVA: 0x0002E19C File Offset: 0x0002C39C
	private void IncreaseSuicidesAmount()
	{
		if (this.suicide)
		{
			this.suicide = false;
			Settings.Instance.IncreaseSuicidesAmount();
		}
	}

	// Token: 0x060006D9 RID: 1753 RVA: 0x0002E1B8 File Offset: 0x0002C3B8
	public void KillShockWave()
	{
		if (!this.increaseKillAmount)
		{
			Settings.Instance.IncreaseKillsAmount();
			this.increaseKillAmount = true;
		}
		this.SyncAccessor__rootObject.GetComponent<FirstPersonController>().lensDistortion.intensity.value = this.SyncAccessor__rootObject.GetComponent<FirstPersonController>().killShockWaveStrength;
		this.SyncAccessor__rootObject.GetComponent<FirstPersonController>().colorGrading.saturation.value = -100f;
	}

	// Token: 0x060006DA RID: 1754 RVA: 0x0002E228 File Offset: 0x0002C428
	private void RenderObject(Vector3 direction, float maxDistance)
	{
		this.lineObject = new GameObject("RaycastLine");
		LineRenderer lineRenderer = this.lineObject.AddComponent<LineRenderer>();
		lineRenderer.startWidth = 0.02f;
		lineRenderer.endWidth = 0.02f;
		lineRenderer.material = new Material(Shader.Find("Sprites/Default"));
		lineRenderer.startColor = new Color(Color.red.r, Color.red.g, Color.red.b, 0.025f);
		lineRenderer.endColor = new Color(Color.red.r, Color.red.g, Color.red.b, 0.025f);
		Vector3 position = base.transform.position;
		lineRenderer.SetPosition(0, position);
		lineRenderer.SetPosition(1, position + direction * maxDistance);
	}

	// Token: 0x060006DB RID: 1755 RVA: 0x0002E300 File Offset: 0x0002C500
	[ServerRpc(RequireOwnership = false)]
	public void ChangeState()
	{
		this.RpcWriter___Server_ChangeState_2166136261();
	}

	// Token: 0x060006DD RID: 1757 RVA: 0x0002E330 File Offset: 0x0002C530
	public virtual void NetworkInitialize___Early()
	{
		if (this.NetworkInitializeEarly_Claymore_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeEarly_Claymore_Assembly-CSharp.dll = true;
		this.syncVar___weapon = new SyncVar<Weapon>(this, 2U, WritePermission.ServerOnly, ReadPermission.Observers, -1f, Channel.Reliable, this.weapon);
		this.syncVar____rootObject = new SyncVar<GameObject>(this, 1U, WritePermission.ServerOnly, ReadPermission.Observers, -1f, Channel.Reliable, this._rootObject);
		this.syncVar___detonated = new SyncVar<bool>(this, 0U, WritePermission.ServerOnly, ReadPermission.Observers, -1f, Channel.Reliable, this.detonated);
		base.RegisterServerRpc(0U, new ServerRpcDelegate(this.RpcReader___Server_StartNewRound_2166136261));
		base.RegisterServerRpc(1U, new ServerRpcDelegate(this.RpcReader___Server_ExplodeServer_2166136261));
		base.RegisterObserversRpc(2U, new ClientRpcDelegate(this.RpcReader___Observers_ExplodeObservers_2166136261));
		base.RegisterServerRpc(3U, new ServerRpcDelegate(this.RpcReader___Server_ChangeState_2166136261));
		base.RegisterSyncVarRead(new SyncVarReadDelegate(this.ReadSyncVar___Claymore));
	}

	// Token: 0x060006DE RID: 1758 RVA: 0x0002E43D File Offset: 0x0002C63D
	public virtual void NetworkInitialize__Late()
	{
		if (this.NetworkInitializeLate_Claymore_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeLate_Claymore_Assembly-CSharp.dll = true;
		this.syncVar___weapon.SetRegistered();
		this.syncVar____rootObject.SetRegistered();
		this.syncVar___detonated.SetRegistered();
	}

	// Token: 0x060006DF RID: 1759 RVA: 0x0002E471 File Offset: 0x0002C671
	public override void NetworkInitializeIfDisabled()
	{
		this.NetworkInitialize___Early();
		this.NetworkInitialize__Late();
	}

	// Token: 0x060006E0 RID: 1760 RVA: 0x0002E480 File Offset: 0x0002C680
	private void RpcWriter___Server_StartNewRound_2166136261()
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendServerRpc(0U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x060006E1 RID: 1761 RVA: 0x0002E51C File Offset: 0x0002C71C
	private void RpcLogic___StartNewRound_2166136261()
	{
		base.Despawn(null);
	}

	// Token: 0x060006E2 RID: 1762 RVA: 0x0002E538 File Offset: 0x0002C738
	private void RpcReader___Server_StartNewRound_2166136261(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		if (!base.IsServer)
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___StartNewRound_2166136261();
	}

	// Token: 0x060006E3 RID: 1763 RVA: 0x0002E568 File Offset: 0x0002C768
	private void RpcWriter___Server_ExplodeServer_2166136261()
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendServerRpc(1U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x060006E4 RID: 1764 RVA: 0x0002E65C File Offset: 0x0002C85C
	private void RpcLogic___ExplodeServer_2166136261()
	{
		this.ExplodeObservers();
	}

	// Token: 0x060006E5 RID: 1765 RVA: 0x0002E664 File Offset: 0x0002C864
	private void RpcReader___Server_ExplodeServer_2166136261(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___ExplodeServer_2166136261();
	}

	// Token: 0x060006E6 RID: 1766 RVA: 0x0002E6A4 File Offset: 0x0002C8A4
	private void RpcWriter___Observers_ExplodeObservers_2166136261()
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendObserversRpc(2U, writer, channel, DataOrderType.Default, false, false, false);
		writer.Store();
	}

	// Token: 0x060006E7 RID: 1767 RVA: 0x0002E750 File Offset: 0x0002C950
	private void RpcLogic___ExplodeObservers_2166136261()
	{
		GameObject[] array = GameObject.FindGameObjectsWithTag("Player");
		for (int i = 0; i < array.Length; i++)
		{
			if (array[i] != null)
			{
				float num = Vector3.Distance(base.transform.position, array[i].transform.position);
				array[i].GetComponent<PlayerHealth>().LocalScreenshake(this.duration, Mathf.Lerp(this.maxStrength, this.minStrength, Mathf.Clamp(num / this.maxDistance, 0f, 1f)), this.vibrato, this.randomness, this.shakeEase);
			}
		}
		global::UnityEngine.Object.Destroy(base.gameObject);
		global::UnityEngine.Object.Instantiate<GameObject>(this.explosionVfx, base.transform.position, Quaternion.identity);
		SoundManager.Instance.PlaySound(this.explosionClip);
	}

	// Token: 0x060006E8 RID: 1768 RVA: 0x0002E824 File Offset: 0x0002CA24
	private void RpcReader___Observers_ExplodeObservers_2166136261(PooledReader PooledReader0, Channel channel)
	{
		if (!base.IsClient)
		{
			return;
		}
		this.RpcLogic___ExplodeObservers_2166136261();
	}

	// Token: 0x060006E9 RID: 1769 RVA: 0x0002E844 File Offset: 0x0002CA44
	private void RpcWriter___Server_ChangeState_2166136261()
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendServerRpc(3U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x060006EA RID: 1770 RVA: 0x0002E8DE File Offset: 0x0002CADE
	public void RpcLogic___ChangeState_2166136261()
	{
		this.sync___set_value_detonated(true, true);
	}

	// Token: 0x060006EB RID: 1771 RVA: 0x0002E8E8 File Offset: 0x0002CAE8
	private void RpcReader___Server_ChangeState_2166136261(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		if (!base.IsServer)
		{
			return;
		}
		this.RpcLogic___ChangeState_2166136261();
	}

	// Token: 0x17000076 RID: 118
	// (get) Token: 0x060006EC RID: 1772 RVA: 0x0002E908 File Offset: 0x0002CB08
	// (set) Token: 0x060006ED RID: 1773 RVA: 0x0002E910 File Offset: 0x0002CB10
	public bool SyncAccessor_detonated
	{
		get
		{
			return this.detonated;
		}
		set
		{
			if (value || !base.IsServer)
			{
				this.detonated = value;
			}
			this.syncVar___detonated.SetValue(value, value);
		}
	}

	// Token: 0x060006EE RID: 1774 RVA: 0x0002E948 File Offset: 0x0002CB48
	public virtual bool ReadSyncVar___Claymore(PooledReader PooledReader0, uint UInt321, bool Boolean2)
	{
		if (UInt321 == 2U)
		{
			if (PooledReader0 == null)
			{
				this.sync___set_value_weapon(this.syncVar___weapon.GetValue(true), true);
				return true;
			}
			Weapon weapon = global::FishNet.Serializing.Generated.GeneratedReaders___Internal.Read___WeaponFishNet.Serializing.Generateds(PooledReader0);
			this.sync___set_value_weapon(weapon, Boolean2);
			return true;
		}
		else if (UInt321 == 1U)
		{
			if (PooledReader0 == null)
			{
				this.sync___set_value__rootObject(this.syncVar____rootObject.GetValue(true), true);
				return true;
			}
			GameObject gameObject = PooledReader0.ReadGameObject();
			this.sync___set_value__rootObject(gameObject, Boolean2);
			return true;
		}
		else
		{
			if (UInt321 != 0U)
			{
				return false;
			}
			if (PooledReader0 == null)
			{
				this.sync___set_value_detonated(this.syncVar___detonated.GetValue(true), true);
				return true;
			}
			bool flag = PooledReader0.ReadBoolean();
			this.sync___set_value_detonated(flag, Boolean2);
			return true;
		}
	}

	// Token: 0x17000077 RID: 119
	// (get) Token: 0x060006EF RID: 1775 RVA: 0x0002EA22 File Offset: 0x0002CC22
	// (set) Token: 0x060006F0 RID: 1776 RVA: 0x0002EA2A File Offset: 0x0002CC2A
	public GameObject SyncAccessor__rootObject
	{
		get
		{
			return this._rootObject;
		}
		set
		{
			if (value || !base.IsServer)
			{
				this._rootObject = value;
			}
			this.syncVar____rootObject.SetValue(value, value);
		}
	}

	// Token: 0x17000078 RID: 120
	// (get) Token: 0x060006F1 RID: 1777 RVA: 0x0002EA5F File Offset: 0x0002CC5F
	// (set) Token: 0x060006F2 RID: 1778 RVA: 0x0002EA67 File Offset: 0x0002CC67
	public Weapon SyncAccessor_weapon
	{
		get
		{
			return this.weapon;
		}
		set
		{
			if (value || !base.IsServer)
			{
				this.weapon = value;
			}
			this.syncVar___weapon.SetValue(value, value);
		}
	}

	// Token: 0x060006F3 RID: 1779 RVA: 0x0002EA9C File Offset: 0x0002CC9C
	public virtual void Awake___UserLogic()
	{
		this.audio = base.GetComponent<AudioSource>();
	}

	// Token: 0x0400065F RID: 1631
	[SerializeField]
	private Vector3 boxdimensions;

	// Token: 0x04000660 RID: 1632
	[SerializeField]
	private string weaponName;

	// Token: 0x04000661 RID: 1633
	private float rayLength;

	// Token: 0x04000662 RID: 1634
	[SerializeField]
	private LayerMask playerLayer;

	// Token: 0x04000663 RID: 1635
	[SerializeField]
	private LayerMask bodyLayer;

	// Token: 0x04000664 RID: 1636
	[SyncVar]
	public bool detonated;

	// Token: 0x04000665 RID: 1637
	[SerializeField]
	private float ragdollEjectForce = 3f;

	// Token: 0x04000666 RID: 1638
	[SerializeField]
	private GameObject explosionVfx;

	// Token: 0x04000667 RID: 1639
	[SerializeField]
	private AudioClip explosionClip;

	// Token: 0x04000668 RID: 1640
	[SerializeField]
	private AudioClip bipClip;

	// Token: 0x04000669 RID: 1641
	[SyncVar]
	public GameObject _rootObject;

	// Token: 0x0400066A RID: 1642
	[SerializeField]
	private GameObject graph;

	// Token: 0x0400066B RID: 1643
	[SerializeField]
	private AudioClip activationSound;

	// Token: 0x0400066C RID: 1644
	public bool canExplode;

	// Token: 0x0400066D RID: 1645
	private AudioSource audio;

	// Token: 0x0400066E RID: 1646
	private float bipTimer;

	// Token: 0x0400066F RID: 1647
	private PlayerHealth[] ph2;

	// Token: 0x04000670 RID: 1648
	[Header("Screenshake values")]
	[SerializeField]
	private float duration;

	// Token: 0x04000671 RID: 1649
	[SerializeField]
	private float minStrength;

	// Token: 0x04000672 RID: 1650
	[SerializeField]
	private float maxStrength;

	// Token: 0x04000673 RID: 1651
	[SerializeField]
	private int vibrato;

	// Token: 0x04000674 RID: 1652
	[SerializeField]
	private float randomness;

	// Token: 0x04000675 RID: 1653
	[SerializeField]
	private Ease shakeEase;

	// Token: 0x04000676 RID: 1654
	[SerializeField]
	private float maxDistance;

	// Token: 0x04000677 RID: 1655
	[SyncVar]
	public Weapon weapon;

	// Token: 0x04000678 RID: 1656
	public bool isOwner;

	// Token: 0x04000679 RID: 1657
	private bool touched;

	// Token: 0x0400067A RID: 1658
	private bool touched2;

	// Token: 0x0400067B RID: 1659
	private bool activated = true;

	// Token: 0x0400067C RID: 1660
	private Collider[] explosionColliders;

	// Token: 0x0400067D RID: 1661
	private bool sendKillLog;

	// Token: 0x0400067E RID: 1662
	private bool suicide = true;

	// Token: 0x0400067F RID: 1663
	private bool increaseKillAmount;

	// Token: 0x04000680 RID: 1664
	private GameObject lineObject;

	// Token: 0x04000681 RID: 1665
	private bool canActivate = true;

	// Token: 0x04000682 RID: 1666
	public SyncVar<bool> syncVar___detonated;

	// Token: 0x04000683 RID: 1667
	public SyncVar<GameObject> syncVar____rootObject;

	// Token: 0x04000684 RID: 1668
	public SyncVar<Weapon> syncVar___weapon;

	// Token: 0x04000685 RID: 1669
	private bool NetworkInitializeEarly_Claymore_Assembly-CSharp.dll;

	// Token: 0x04000686 RID: 1670
	private bool NetworkInitializeLate_Claymore_Assembly-CSharp.dll;
}

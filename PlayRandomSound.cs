﻿using System;
using UnityEngine;

// Token: 0x020000EE RID: 238
public class PlayRandomSound : MonoBehaviour
{
	// Token: 0x06000C7F RID: 3199 RVA: 0x00055D27 File Offset: 0x00053F27
	private void Awake()
	{
		this.audio = base.GetComponent<AudioSource>();
	}

	// Token: 0x06000C80 RID: 3200 RVA: 0x00055D38 File Offset: 0x00053F38
	private void Start()
	{
		int num = Mathf.RoundToInt((float)global::UnityEngine.Random.Range(0, this.menuTracks.Length));
		this.audio.clip = this.menuTracks[num];
		this.audio.Play();
	}

	// Token: 0x04000ABE RID: 2750
	[SerializeField]
	private AudioClip[] menuTracks;

	// Token: 0x04000ABF RID: 2751
	private AudioSource audio;
}

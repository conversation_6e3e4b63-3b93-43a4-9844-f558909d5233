﻿using System;
using System.Collections;
using System.Collections.Generic;
using FishNet;
using FishNet.Connection;
using FishNet.Managing;
using FishNet.Object;
using FishNet.Object.Delegating;
using FishNet.Object.Synchronizing;
using FishNet.Serializing;
using FishNet.Transporting;
using HeathenEngineering.PhysKit;
using UnityEngine;

// Token: 0x0200008D RID: 141
public class DualLauncher : Weapon
{
	// Token: 0x060006FA RID: 1786 RVA: 0x0002EBEB File Offset: 0x0002CDEB
	private void Start()
	{
		this.grenadeTimer = this.timeBeforeExplosion;
	}

	// Token: 0x060006FB RID: 1787 RVA: 0x0002EBF9 File Offset: 0x0002CDF9
	[ServerRpc(RunLocally = true)]
	private void SetBool(bool value)
	{
		this.RpcWriter___Server_SetBool_1140765316(value);
		this.RpcLogic___SetBool_1140765316(value);
	}

	// Token: 0x060006FC RID: 1788 RVA: 0x0002EC10 File Offset: 0x0002CE10
	private void Update()
	{
		base.WeaponUpdate();
		if (this.fireTimer > 0f)
		{
			this.fireTimer -= Time.deltaTime;
		}
		if (this.SyncAccessor_grenadeOpen && this.grenadeTimer == this.timeBeforeExplosion && this.grenade)
		{
			ParticleSystem particleSystem = global::UnityEngine.Object.Instantiate<ParticleSystem>(this.grenadeSmoke, base.transform.position + new Vector3(-0.007f, -0.121f, -0.031f), Quaternion.identity, this.shootPoint);
			particleSystem.transform.localScale = new Vector3(0.1f, 0.1f, 0.1f);
			particleSystem.transform.forward = base.transform.up;
			this.GrenadeSFXServer(0);
			this.GrenadeSFXServer(1);
			this.douilleGrenade.transform.SetParent(null);
			this.douilleGrenade.AddComponent<Rigidbody>();
			Rigidbody component = this.douilleGrenade.GetComponent<Rigidbody>();
			component.interpolation = RigidbodyInterpolation.Interpolate;
			component.AddForce(base.transform.right * 0.4f + Vector3.up * 2f, ForceMode.Impulse);
			global::UnityEngine.Random.Range(-1, 1);
			component.AddTorque(base.transform.forward * 4f + base.transform.right * 4f, ForceMode.Impulse);
		}
		if (this.SyncAccessor_grenadeOpen)
		{
			this.grenadeTimer -= Time.deltaTime;
		}
		if (this.grenadeTimer <= 0.06f && this.grenade && this.SyncAccessor_grenadeOpen && base.IsOwner)
		{
			this.Fire();
		}
		if (base.gameObject.layer == 7)
		{
			return;
		}
		if (this.SyncAccessor_grenadeOpen && this.grenade && Input.GetKeyDown(KeyCode.F) && base.IsOwner && this.behaviour.playerPickup.currentEnvironmentInteractable == null)
		{
			this.Fire();
		}
		if (!base.IsOwner)
		{
			return;
		}
		if (this.grenade && this.grenadeTimer == this.timeBeforeExplosion)
		{
			if (!this.onePressShoot)
			{
				if ((this.invertFire ? this.fire2 : this.fire1).ReadValue<float>() > 0.1f && this.inRightHand)
				{
					this.SetBool(true);
				}
				if ((this.invertFire ? this.fire1 : this.fire2).ReadValue<float>() > 0.1f && this.inLeftHand)
				{
					this.SetBool(true);
				}
			}
			else
			{
				if ((this.invertFire ? this.fire2 : this.fire1).ReadValue<float>() < 0.1f && this.inRightHand)
				{
					this.isClicked = true;
				}
				if ((this.invertFire ? this.fire1 : this.fire2).ReadValue<float>() < 0.1f && this.inLeftHand)
				{
					this.isClicked = true;
				}
			}
			if (this.isClicked && (this.onePressShoot || (!this.reloadWeapon && base.SyncAccessor_currentAmmo <= 0)))
			{
				if ((this.invertFire ? this.fire2 : this.fire1).ReadValue<float>() > 0.1f && this.inRightHand)
				{
					this.SetBool(true);
					this.isClicked = false;
				}
				if ((this.invertFire ? this.fire1 : this.fire2).ReadValue<float>() > 0.1f && this.inLeftHand)
				{
					this.SetBool(true);
					this.isClicked = false;
					return;
				}
			}
		}
		else
		{
			if (!this.onePressShoot)
			{
				if ((this.invertFire ? this.fire2 : this.fire1).ReadValue<float>() > 0.1f && this.inRightHand)
				{
					this.Fire();
				}
				if ((this.invertFire ? this.fire1 : this.fire2).ReadValue<float>() > 0.1f && this.inLeftHand)
				{
					this.Fire();
				}
			}
			else
			{
				if ((this.invertFire ? this.fire2 : this.fire1).ReadValue<float>() < 0.1f && this.inRightHand)
				{
					this.isClicked = true;
				}
				if ((this.invertFire ? this.fire1 : this.fire2).ReadValue<float>() < 0.1f && this.inLeftHand)
				{
					this.isClicked = true;
				}
			}
			if (this.isClicked && (this.onePressShoot || (!this.reloadWeapon && base.SyncAccessor_currentAmmo <= 0)))
			{
				if ((this.invertFire ? this.fire2 : this.fire1).ReadValue<float>() > 0.1f && this.inRightHand)
				{
					this.Fire();
					this.isClicked = false;
				}
				if ((this.invertFire ? this.fire1 : this.fire2).ReadValue<float>() > 0.1f && this.inLeftHand)
				{
					this.Fire();
					this.isClicked = false;
				}
			}
		}
	}

	// Token: 0x060006FD RID: 1789 RVA: 0x0002F106 File Offset: 0x0002D306
	private IEnumerator LaunchWithDelay()
	{
		yield return new WaitForSeconds(0.1f);
		this.Fire();
		yield break;
	}

	// Token: 0x060006FE RID: 1790 RVA: 0x0002F118 File Offset: 0x0002D318
	private void Fire()
	{
		if (PauseManager.Instance.pause || !this.playerController.SyncAccessor_canMove)
		{
			return;
		}
		if (!this.playerController.IsOwner)
		{
			return;
		}
		if (this.grenade && this.behaviour.playerPickup.currentEnvironmentInteractable != null)
		{
			return;
		}
		if (this.changePitchOnShoot)
		{
			this.audio.pitch = global::UnityEngine.Random.Range(0.97f, 1.03f);
		}
		if (this.fireTimer > 0f)
		{
			return;
		}
		this.fireTimer = this.timeBetweenFire;
		if (base.SyncAccessor_currentAmmo <= 0)
		{
			this.audio.PlayOneShot(this.nobulletClip);
			this.noAmmoClicks++;
			if (this.noAmmoClicks > 1 && this.inRightHand)
			{
				this.rootObject.GetComponent<PlayerPickup>().RightHandDrop();
			}
			if (this.noAmmoClicks > 1 && this.inLeftHand)
			{
				this.rootObject.GetComponent<PlayerPickup>().LeftHandDrop();
			}
		}
		if (base.SyncAccessor_currentAmmo <= 0)
		{
			this.audio.PlayOneShot(this.nobulletClip);
		}
		if (base.SyncAccessor_currentAmmo <= 0)
		{
			return;
		}
		if (this.kanye && this.kanyeShoot && this.kanyeBullets.Count > 1)
		{
			this.kanyeShoot = false;
			this.kanyeBullets[0].transform.SetParent(null);
			global::UnityEngine.Object.Destroy(this.kanyeBullets[0]);
			this.kanyeBullets.Remove(this.kanyeBullets[0]);
		}
		else if (this.kanye)
		{
			this.kanyeShoot = true;
		}
		Vector3 vector = this.cam.transform.position;
		Vector3 forward = this.cam.transform.forward;
		if (this.shrapnel)
		{
			RaycastHit raycastHit;
			if (Physics.Raycast(vector, forward, out raycastHit, float.PositiveInfinity, this.defaultLayer))
			{
				vector = raycastHit.point;
				this.SpawnBulletTrailServer(raycastHit.point);
			}
			else
			{
				vector += forward * 15f;
				this.SpawnBulletTrailServer(vector);
			}
		}
		if (base.IsServer)
		{
			this.SpawnProjectile(vector, forward, 0f);
		}
		this.ServerFire(vector, forward, base.TimeManager.Tick);
		if (this.playerKnockback != 0f)
		{
			this.playerController.AddForce(-this.cam.transform.forward, this.playerKnockback);
		}
		if (base.gameObject.name == "RocketLauncher(Clone)")
		{
			base.StartCoroutine(this.RocketJumpCheck(this.rootObject.transform.position));
		}
		base.CameraAnimation();
		base.WeaponAnimation();
	}

	// Token: 0x060006FF RID: 1791 RVA: 0x0002F3BE File Offset: 0x0002D5BE
	private IEnumerator RocketJumpCheck(Vector3 firstPosition)
	{
		yield return new WaitForSeconds(0.55f);
		if (this.rootObject != null && this.rootObject.transform.position.y - firstPosition.y > 2f)
		{
			Settings.Instance.rocketJumps += 1f;
		}
		yield break;
	}

	// Token: 0x06000700 RID: 1792 RVA: 0x0002F3D4 File Offset: 0x0002D5D4
	private void SpawnProjectile(Vector3 position, Vector3 direction, float passedTime)
	{
		if (this.grenade)
		{
			GameObject gameObject = global::UnityEngine.Object.Instantiate<GameObject>(this.trickShot.template.gameObject);
			gameObject.GetComponent<PhysicsGrenade>().Initialize(this.rootObject, base.gameObject, passedTime, this.grenadeTimer);
			gameObject.GetComponent<PhysicsGrenade>().isOwner = base.IsOwner;
			gameObject.GetComponent<PhysicsGrenade>().weapon = this;
			this.trickShot.Shoot(gameObject);
			return;
		}
		if (this.shrapnel)
		{
			ShrapnelBallistic shrapnelBallistic = global::UnityEngine.Object.Instantiate<ShrapnelBallistic>(this.shrapnelProj, position, Quaternion.LookRotation(direction));
			shrapnelBallistic.Initialize(this.rootObject, base.gameObject, passedTime);
			shrapnelBallistic.isOwner = base.IsOwner;
			shrapnelBallistic.weapon = this;
			return;
		}
		if (this.obus)
		{
			GameObject gameObject2 = global::UnityEngine.Object.Instantiate<GameObject>(this.trickShot.template.gameObject);
			gameObject2.GetComponent<Obus>().Initialize(this.rootObject, base.gameObject, passedTime);
			gameObject2.GetComponent<Obus>().isOwner = base.IsOwner;
			gameObject2.GetComponent<Obus>().weapon = this;
			this.trickShot.Shoot(gameObject2);
			return;
		}
		if (this.bubble)
		{
			GameObject gameObject3 = global::UnityEngine.Object.Instantiate<GameObject>(this.trickShot.template.gameObject);
			gameObject3.GetComponent<Bubble>().Initialize(this.rootObject, base.gameObject, passedTime);
			gameObject3.GetComponent<Bubble>().isOwner = base.IsOwner;
			gameObject3.GetComponent<Bubble>().weapon = this;
			this.trickShot.Shoot(gameObject3);
			return;
		}
		if (this.rebond)
		{
			RebondBalle rebondBalle = global::UnityEngine.Object.Instantiate<RebondBalle>(this._projectile2, position, Quaternion.identity);
			rebondBalle.Initialize(direction, this.launchForce, passedTime, this.rootObject, base.gameObject);
			rebondBalle.weapon = this;
			return;
		}
		PredictedProjectile predictedProjectile = global::UnityEngine.Object.Instantiate<PredictedProjectile>(this._projectile, position, Quaternion.LookRotation(direction));
		predictedProjectile.Initialize(direction, this.launchForce, passedTime, this.rootObject, base.gameObject);
		predictedProjectile.isOwner = base.IsOwner;
		predictedProjectile.weapon = this;
	}

	// Token: 0x06000701 RID: 1793 RVA: 0x0002F5C0 File Offset: 0x0002D7C0
	[ServerRpc(RunLocally = true)]
	private void ServerFire(Vector3 position, Vector3 direction, uint tick)
	{
		this.RpcWriter___Server_ServerFire_2754081237(position, direction, tick);
		this.RpcLogic___ServerFire_2754081237(position, direction, tick);
	}

	// Token: 0x06000702 RID: 1794 RVA: 0x0002F5F4 File Offset: 0x0002D7F4
	[ObserversRpc(ExcludeOwner = true)]
	private void ObserversFire(Vector3 position, Vector3 direction, uint tick)
	{
		this.RpcWriter___Observers_ObserversFire_2754081237(position, direction, tick);
	}

	// Token: 0x06000703 RID: 1795 RVA: 0x0002F614 File Offset: 0x0002D814
	[ObserversRpc(RunLocally = true, ExcludeOwner = true)]
	private void ShootObserversEffect()
	{
		this.RpcWriter___Observers_ShootObserversEffect_2166136261();
		this.RpcLogic___ShootObserversEffect_2166136261();
	}

	// Token: 0x06000704 RID: 1796 RVA: 0x0002F62D File Offset: 0x0002D82D
	[ServerRpc(RunLocally = true)]
	private void GrenadeSFXServer(int j)
	{
		this.RpcWriter___Server_GrenadeSFXServer_3316948804(j);
		this.RpcLogic___GrenadeSFXServer_3316948804(j);
	}

	// Token: 0x06000705 RID: 1797 RVA: 0x0002F643 File Offset: 0x0002D843
	[ObserversRpc(RunLocally = true, ExcludeOwner = true)]
	private void GrenadeSFX(int j)
	{
		this.RpcWriter___Observers_GrenadeSFX_3316948804(j);
		this.RpcLogic___GrenadeSFX_3316948804(j);
	}

	// Token: 0x06000706 RID: 1798 RVA: 0x00026D1D File Offset: 0x00024F1D
	private void LocalSound(int index)
	{
		if (index == 0)
		{
			this.audio.PlayOneShot(this.headHitClip);
		}
	}

	// Token: 0x06000707 RID: 1799 RVA: 0x0002F659 File Offset: 0x0002D859
	[ServerRpc(RunLocally = true)]
	private void SpawnBulletTrailServer(Vector3 hitPoint)
	{
		this.RpcWriter___Server_SpawnBulletTrailServer_4276783012(hitPoint);
		this.RpcLogic___SpawnBulletTrailServer_4276783012(hitPoint);
	}

	// Token: 0x06000708 RID: 1800 RVA: 0x0002F670 File Offset: 0x0002D870
	[ObserversRpc(RunLocally = true, ExcludeOwner = true)]
	private void SpawnBulletTrail(Vector3 hitPoint)
	{
		this.RpcWriter___Observers_SpawnBulletTrail_4276783012(hitPoint);
		this.RpcLogic___SpawnBulletTrail_4276783012(hitPoint);
	}

	// Token: 0x0600070A RID: 1802 RVA: 0x0002F6BC File Offset: 0x0002D8BC
	public override void NetworkInitialize___Early()
	{
		if (this.NetworkInitializeEarly_DualLauncher_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeEarly_DualLauncher_Assembly-CSharp.dll = true;
		base.NetworkInitialize___Early();
		this.syncVar___grenadeOpen = new SyncVar<bool>(this, 1U, WritePermission.ServerOnly, ReadPermission.Observers, -1f, Channel.Reliable, this.grenadeOpen);
		base.RegisterServerRpc(15U, new ServerRpcDelegate(this.RpcReader___Server_SetBool_1140765316));
		base.RegisterServerRpc(16U, new ServerRpcDelegate(this.RpcReader___Server_ServerFire_2754081237));
		base.RegisterObserversRpc(17U, new ClientRpcDelegate(this.RpcReader___Observers_ObserversFire_2754081237));
		base.RegisterObserversRpc(18U, new ClientRpcDelegate(this.RpcReader___Observers_ShootObserversEffect_2166136261));
		base.RegisterServerRpc(19U, new ServerRpcDelegate(this.RpcReader___Server_GrenadeSFXServer_3316948804));
		base.RegisterObserversRpc(20U, new ClientRpcDelegate(this.RpcReader___Observers_GrenadeSFX_3316948804));
		base.RegisterServerRpc(21U, new ServerRpcDelegate(this.RpcReader___Server_SpawnBulletTrailServer_4276783012));
		base.RegisterObserversRpc(22U, new ClientRpcDelegate(this.RpcReader___Observers_SpawnBulletTrail_4276783012));
		base.RegisterSyncVarRead(new SyncVarReadDelegate(this.ReadSyncVar___DualLauncher));
	}

	// Token: 0x0600070B RID: 1803 RVA: 0x0002F7D5 File Offset: 0x0002D9D5
	public override void NetworkInitialize__Late()
	{
		if (this.NetworkInitializeLate_DualLauncher_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeLate_DualLauncher_Assembly-CSharp.dll = true;
		base.NetworkInitialize__Late();
		this.syncVar___grenadeOpen.SetRegistered();
	}

	// Token: 0x0600070C RID: 1804 RVA: 0x0002F7F9 File Offset: 0x0002D9F9
	public override void NetworkInitializeIfDisabled()
	{
		this.NetworkInitialize___Early();
		this.NetworkInitialize__Late();
	}

	// Token: 0x0600070D RID: 1805 RVA: 0x0002F808 File Offset: 0x0002DA08
	private void RpcWriter___Server_SetBool_1140765316(bool value)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteBoolean(value);
		base.SendServerRpc(15U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x0600070E RID: 1806 RVA: 0x0002F909 File Offset: 0x0002DB09
	private void RpcLogic___SetBool_1140765316(bool value)
	{
		this.sync___set_value_grenadeOpen(value, true);
	}

	// Token: 0x0600070F RID: 1807 RVA: 0x0002F914 File Offset: 0x0002DB14
	private void RpcReader___Server_SetBool_1140765316(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		bool flag = PooledReader0.ReadBoolean();
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___SetBool_1140765316(flag);
	}

	// Token: 0x06000710 RID: 1808 RVA: 0x0002F964 File Offset: 0x0002DB64
	private void RpcWriter___Server_ServerFire_2754081237(Vector3 position, Vector3 direction, uint tick)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteVector3(position);
		writer.WriteVector3(direction);
		writer.WriteUInt32(tick, AutoPackType.Packed);
		base.SendServerRpc(16U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000711 RID: 1809 RVA: 0x0002FA84 File Offset: 0x0002DC84
	private void RpcLogic___ServerFire_2754081237(Vector3 position, Vector3 direction, uint tick)
	{
		if (this.needsAmmo)
		{
			base.sync___set_value_currentAmmo(base.SyncAccessor_currentAmmo - 1, true);
		}
		float num = (float)base.TimeManager.TimePassed(tick, false);
		num = Mathf.Min(0.15f, num);
		this.ShootObserversEffect();
		if (!base.IsServer)
		{
			this.SpawnProjectile(position, direction, num);
		}
		this.ObserversFire(position, direction, tick);
	}

	// Token: 0x06000712 RID: 1810 RVA: 0x0002FAE4 File Offset: 0x0002DCE4
	private void RpcReader___Server_ServerFire_2754081237(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		Vector3 vector = PooledReader0.ReadVector3();
		Vector3 vector2 = PooledReader0.ReadVector3();
		uint num = PooledReader0.ReadUInt32(AutoPackType.Packed);
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___ServerFire_2754081237(vector, vector2, num);
	}

	// Token: 0x06000713 RID: 1811 RVA: 0x0002FB5C File Offset: 0x0002DD5C
	private void RpcWriter___Observers_ObserversFire_2754081237(Vector3 position, Vector3 direction, uint tick)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteVector3(position);
		writer.WriteVector3(direction);
		writer.WriteUInt32(tick, AutoPackType.Packed);
		base.SendObserversRpc(17U, writer, channel, DataOrderType.Default, false, false, true);
		writer.Store();
	}

	// Token: 0x06000714 RID: 1812 RVA: 0x0002FC34 File Offset: 0x0002DE34
	private void RpcLogic___ObserversFire_2754081237(Vector3 position, Vector3 direction, uint tick)
	{
		float num = (float)base.TimeManager.TimePassed(tick, false);
		num = Mathf.Min(0.3f, num);
		this.SpawnProjectile(position, direction, num);
	}

	// Token: 0x06000715 RID: 1813 RVA: 0x0002FC68 File Offset: 0x0002DE68
	private void RpcReader___Observers_ObserversFire_2754081237(PooledReader PooledReader0, Channel channel)
	{
		Vector3 vector = PooledReader0.ReadVector3();
		Vector3 vector2 = PooledReader0.ReadVector3();
		uint num = PooledReader0.ReadUInt32(AutoPackType.Packed);
		if (!base.IsClient)
		{
			return;
		}
		this.RpcLogic___ObserversFire_2754081237(vector, vector2, num);
	}

	// Token: 0x06000716 RID: 1814 RVA: 0x0002FCC0 File Offset: 0x0002DEC0
	private void RpcWriter___Observers_ShootObserversEffect_2166136261()
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendObserversRpc(18U, writer, channel, DataOrderType.Default, false, false, true);
		writer.Store();
	}

	// Token: 0x06000717 RID: 1815 RVA: 0x0002FD6C File Offset: 0x0002DF6C
	private void RpcLogic___ShootObserversEffect_2166136261()
	{
		if (Settings.Instance.reduceVFX)
		{
			return;
		}
		this.audio.PlayOneShot(this.fireClip);
		if (!this.grenade)
		{
			GameObject gameObject = global::UnityEngine.Object.Instantiate<GameObject>(this.muzzleFlash, this.muzzleFlashPoint.position, this.shootPoint.rotation);
			foreach (Transform transform in gameObject.transform.GetComponentsInChildren<Transform>(true))
			{
				if (transform.GetComponent<Light>() == null && transform.tag != "vfx" && base.IsOwner)
				{
					transform.gameObject.layer = 8;
				}
				if (transform.GetComponent<Light>() != null)
				{
					transform.GetComponent<Light>().intensity = this.lightIntensity;
				}
			}
			ParticleSystem[] componentsInChildren2 = gameObject.GetComponentsInChildren<ParticleSystem>();
			for (int i = 0; i < componentsInChildren2.Length; i++)
			{
				componentsInChildren2[i].Play();
			}
		}
	}

	// Token: 0x06000718 RID: 1816 RVA: 0x0002FE58 File Offset: 0x0002E058
	private void RpcReader___Observers_ShootObserversEffect_2166136261(PooledReader PooledReader0, Channel channel)
	{
		if (!base.IsClient)
		{
			return;
		}
		if (base.IsHost)
		{
			return;
		}
		this.RpcLogic___ShootObserversEffect_2166136261();
	}

	// Token: 0x06000719 RID: 1817 RVA: 0x0002FE84 File Offset: 0x0002E084
	private void RpcWriter___Server_GrenadeSFXServer_3316948804(int j)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteInt32(j, AutoPackType.Packed);
		base.SendServerRpc(19U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x0600071A RID: 1818 RVA: 0x0002FF8A File Offset: 0x0002E18A
	private void RpcLogic___GrenadeSFXServer_3316948804(int j)
	{
		this.GrenadeSFX(j);
	}

	// Token: 0x0600071B RID: 1819 RVA: 0x0002FF94 File Offset: 0x0002E194
	private void RpcReader___Server_GrenadeSFXServer_3316948804(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		int num = PooledReader0.ReadInt32(AutoPackType.Packed);
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___GrenadeSFXServer_3316948804(num);
	}

	// Token: 0x0600071C RID: 1820 RVA: 0x0002FFE8 File Offset: 0x0002E1E8
	private void RpcWriter___Observers_GrenadeSFX_3316948804(int j)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteInt32(j, AutoPackType.Packed);
		base.SendObserversRpc(20U, writer, channel, DataOrderType.Default, false, false, true);
		writer.Store();
	}

	// Token: 0x0600071D RID: 1821 RVA: 0x000300A3 File Offset: 0x0002E2A3
	private void RpcLogic___GrenadeSFX_3316948804(int j)
	{
		if (j == 0)
		{
			this.audio.PlayOneShot(this.douilleClip);
		}
		if (j == 1)
		{
			this.audio.PlayOneShot(this.beforeDetonationClip);
		}
	}

	// Token: 0x0600071E RID: 1822 RVA: 0x000300D0 File Offset: 0x0002E2D0
	private void RpcReader___Observers_GrenadeSFX_3316948804(PooledReader PooledReader0, Channel channel)
	{
		int num = PooledReader0.ReadInt32(AutoPackType.Packed);
		if (!base.IsClient)
		{
			return;
		}
		if (base.IsHost)
		{
			return;
		}
		this.RpcLogic___GrenadeSFX_3316948804(num);
	}

	// Token: 0x0600071F RID: 1823 RVA: 0x00030110 File Offset: 0x0002E310
	private void RpcWriter___Server_SpawnBulletTrailServer_4276783012(Vector3 hitPoint)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteVector3(hitPoint);
		base.SendServerRpc(21U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000720 RID: 1824 RVA: 0x00030211 File Offset: 0x0002E411
	private void RpcLogic___SpawnBulletTrailServer_4276783012(Vector3 hitPoint)
	{
		this.SpawnBulletTrail(hitPoint);
	}

	// Token: 0x06000721 RID: 1825 RVA: 0x0003021C File Offset: 0x0002E41C
	private void RpcReader___Server_SpawnBulletTrailServer_4276783012(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		Vector3 vector = PooledReader0.ReadVector3();
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___SpawnBulletTrailServer_4276783012(vector);
	}

	// Token: 0x06000722 RID: 1826 RVA: 0x0003026C File Offset: 0x0002E46C
	private void RpcWriter___Observers_SpawnBulletTrail_4276783012(Vector3 hitPoint)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteVector3(hitPoint);
		base.SendObserversRpc(22U, writer, channel, DataOrderType.Default, false, false, true);
		writer.Store();
	}

	// Token: 0x06000723 RID: 1827 RVA: 0x00030324 File Offset: 0x0002E524
	private void RpcLogic___SpawnBulletTrail_4276783012(Vector3 hitPoint)
	{
		GameObject gameObject = global::UnityEngine.Object.Instantiate<GameObject>(this.bulletTrailLocal.gameObject, this.shootPoint.position, Quaternion.identity);
		LineRenderer component = gameObject.GetComponent<LineRenderer>();
		component.SetPosition(0, this.shootPoint.position);
		component.SetPosition(1, hitPoint);
		global::UnityEngine.Object.Destroy(gameObject, 0.4f);
	}

	// Token: 0x06000724 RID: 1828 RVA: 0x0003037C File Offset: 0x0002E57C
	private void RpcReader___Observers_SpawnBulletTrail_4276783012(PooledReader PooledReader0, Channel channel)
	{
		Vector3 vector = PooledReader0.ReadVector3();
		if (!base.IsClient)
		{
			return;
		}
		if (base.IsHost)
		{
			return;
		}
		this.RpcLogic___SpawnBulletTrail_4276783012(vector);
	}

	// Token: 0x1700007B RID: 123
	// (get) Token: 0x06000725 RID: 1829 RVA: 0x000303B7 File Offset: 0x0002E5B7
	// (set) Token: 0x06000726 RID: 1830 RVA: 0x000303BF File Offset: 0x0002E5BF
	public bool SyncAccessor_grenadeOpen
	{
		get
		{
			return this.grenadeOpen;
		}
		set
		{
			if (value || !base.IsServer)
			{
				this.grenadeOpen = value;
			}
			this.syncVar___grenadeOpen.SetValue(value, value);
		}
	}

	// Token: 0x06000727 RID: 1831 RVA: 0x000303F4 File Offset: 0x0002E5F4
	public virtual bool ReadSyncVar___DualLauncher(PooledReader PooledReader0, uint UInt321, bool Boolean2)
	{
		if (UInt321 != 1U)
		{
			return false;
		}
		if (PooledReader0 == null)
		{
			this.sync___set_value_grenadeOpen(this.syncVar___grenadeOpen.GetValue(true), true);
			return true;
		}
		bool flag = PooledReader0.ReadBoolean();
		this.sync___set_value_grenadeOpen(flag, Boolean2);
		return true;
	}

	// Token: 0x06000728 RID: 1832 RVA: 0x00030446 File Offset: 0x0002E646
	public override void Awake()
	{
		this.NetworkInitialize___Early();
		base.Awake();
		this.NetworkInitialize__Late();
	}

	// Token: 0x06000729 RID: 1833 RVA: 0x000023D6 File Offset: 0x000005D6
	public override void Awake___UserLogic()
	{
	}

	// Token: 0x0400068A RID: 1674
	[Header("Weapon Specials")]
	[SerializeField]
	private float launchForce = 12f;

	// Token: 0x0400068B RID: 1675
	[SerializeField]
	private float playerKnockback = 2f;

	// Token: 0x0400068C RID: 1676
	[SerializeField]
	private PredictedProjectile _projectile;

	// Token: 0x0400068D RID: 1677
	[SerializeField]
	private ShrapnelBallistic shrapnelProj;

	// Token: 0x0400068E RID: 1678
	[SerializeField]
	private RebondBalle _projectile2;

	// Token: 0x0400068F RID: 1679
	[SerializeField]
	private HandGrenadeTwo _projectile3;

	// Token: 0x04000690 RID: 1680
	[SerializeField]
	private TrickShot trickShot;

	// Token: 0x04000691 RID: 1681
	[SerializeField]
	private Obus _projectile4;

	// Token: 0x04000692 RID: 1682
	private const float MAX_PASSED_TIME = 0.3f;

	// Token: 0x04000693 RID: 1683
	[SerializeField]
	private bool rebond;

	// Token: 0x04000694 RID: 1684
	[SerializeField]
	private bool grenade;

	// Token: 0x04000695 RID: 1685
	[SerializeField]
	private bool obus;

	// Token: 0x04000696 RID: 1686
	[SerializeField]
	private bool kanye;

	// Token: 0x04000697 RID: 1687
	[SerializeField]
	private bool kanyeShoot;

	// Token: 0x04000698 RID: 1688
	[SerializeField]
	private bool shrapnel;

	// Token: 0x04000699 RID: 1689
	[SerializeField]
	private bool bubble;

	// Token: 0x0400069A RID: 1690
	[SerializeField]
	private List<GameObject> kanyeBullets = new List<GameObject>();

	// Token: 0x0400069B RID: 1691
	private float fireTimer;

	// Token: 0x0400069C RID: 1692
	[Space]
	[SerializeField]
	private GameObject douilleGrenade;

	// Token: 0x0400069D RID: 1693
	[SerializeField]
	private float timeBeforeExplosion;

	// Token: 0x0400069E RID: 1694
	[SerializeField]
	private AudioClip douilleClip;

	// Token: 0x0400069F RID: 1695
	[SerializeField]
	private AudioClip beforeDetonationClip;

	// Token: 0x040006A0 RID: 1696
	[SerializeField]
	private ParticleSystem grenadeSmoke;

	// Token: 0x040006A1 RID: 1697
	private float grenadeTimer;

	// Token: 0x040006A2 RID: 1698
	[SyncVar]
	public bool grenadeOpen;

	// Token: 0x040006A3 RID: 1699
	public SyncVar<bool> syncVar___grenadeOpen;

	// Token: 0x040006A4 RID: 1700
	private bool NetworkInitializeEarly_DualLauncher_Assembly-CSharp.dll;

	// Token: 0x040006A5 RID: 1701
	private bool NetworkInitializeLate_DualLauncher_Assembly-CSharp.dll;
}

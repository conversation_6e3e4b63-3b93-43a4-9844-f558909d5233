﻿using System;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

// Token: 0x02000046 RID: 70
public class CosmeticInstance : MonoBeh<PERSON>our, IPointerEnterHandler, IEventSystemHandler, IPointerExitHandler
{
	// Token: 0x0600035A RID: 858 RVA: 0x0001A2CC File Offset: 0x000184CC
	private void Start()
	{
		base.transform.Find("Image").GetComponent<Image>().sprite = this.sprite;
		this.img = base.transform.Find("Image").GetComponent<Image>();
		this.button = base.GetComponent<Button>();
		if (this.unlockWithXp)
		{
			foreach (ProgressInstance progressInstance in ProgressManager.Instance.instances)
			{
				if (progressInstance.cosmetic != null && progressInstance.cosmetic == this)
				{
					this.challengeDescription = "Reach " + progressInstance.xpToUnlock.ToString() + " xp to unlock";
				}
			}
		}
		this.img.color = (this.acquired ? this.unlockedColor : this.lockedColor);
		if (SteamLobby.ownDlc0 && this.unlockWithDlc)
		{
			this.Unlock();
		}
		if (SteamLobby.ownDlc1 && this.unlockWithSupporterDlc)
		{
			this.Unlock();
		}
	}

	// Token: 0x0600035B RID: 859 RVA: 0x0001A3CC File Offset: 0x000185CC
	private void OnEnable()
	{
		if (SteamLobby.ownDlc0 && this.unlockWithDlc)
		{
			this.Unlock();
		}
		if (SteamLobby.ownDlc1 && this.unlockWithSupporterDlc)
		{
			this.Unlock();
		}
	}

	// Token: 0x0600035C RID: 860 RVA: 0x0001A3F8 File Offset: 0x000185F8
	public void ChangeDress()
	{
		CosmeticsManager.Instance.ChangeDress(this);
	}

	// Token: 0x0600035D RID: 861 RVA: 0x0001A408 File Offset: 0x00018608
	private void Update()
	{
		this.button.interactable = this.acquired;
		if (this.img != null)
		{
			this.img.color = (this.acquired ? this.unlockedColor : this.lockedColor);
		}
	}

	// Token: 0x0600035E RID: 862 RVA: 0x0001A455 File Offset: 0x00018655
	public void Unlock()
	{
		this.acquired = true;
	}

	// Token: 0x0600035F RID: 863 RVA: 0x0001A460 File Offset: 0x00018660
	public void OnPointerEnter(PointerEventData eventData)
	{
		if (this.unlockWithSupporterDlc)
		{
			if (!this.acquired)
			{
				FloatingName.Instance.nameToShow = "Get the Supporter Edition DLC !";
				return;
			}
			if (this.isHat)
			{
				FloatingName.Instance.nameToShow = "Supporter DLC Hat unlocked !";
				return;
			}
			if (this.isCig)
			{
				FloatingName.Instance.nameToShow = "Supporter DLC Cig unlocked !";
				return;
			}
			FloatingName.Instance.nameToShow = "Supporter DLC Suit unlocked !";
			return;
		}
		else if (this.unlockWithDlc)
		{
			if (!this.acquired)
			{
				FloatingName.Instance.nameToShow = "Get the DLC !";
				return;
			}
			if (this.isHat)
			{
				FloatingName.Instance.nameToShow = "DLC Hat unlocked !";
				return;
			}
			if (this.isCig)
			{
				FloatingName.Instance.nameToShow = "DLC Cig unlocked !";
				return;
			}
			FloatingName.Instance.nameToShow = "DLC Suit unlocked !";
			return;
		}
		else
		{
			if (!this.acquired)
			{
				FloatingName.Instance.nameToShow = this.challengeDescription;
				return;
			}
			FloatingName.Instance.nameToShow = ((this.challengeDescription != "") ? ("<s>" + this.challengeDescription + "</s> Done!") : "");
			return;
		}
	}

	// Token: 0x06000360 RID: 864 RVA: 0x0001A57E File Offset: 0x0001877E
	public void OnPointerExit(PointerEventData eventData)
	{
		FloatingName.Instance.nameToShow = "";
	}

	// Token: 0x040003BB RID: 955
	public bool isHat = true;

	// Token: 0x040003BC RID: 956
	public bool isCig;

	// Token: 0x040003BD RID: 957
	public GameObject hat;

	// Token: 0x040003BE RID: 958
	private Button button;

	// Token: 0x040003BF RID: 959
	public Sprite sprite;

	// Token: 0x040003C0 RID: 960
	public bool acquired;

	// Token: 0x040003C1 RID: 961
	private Image img;

	// Token: 0x040003C2 RID: 962
	[Space]
	public string challengeDescription;

	// Token: 0x040003C3 RID: 963
	[Space]
	public string cosmeticName;

	// Token: 0x040003C4 RID: 964
	[Space]
	public int index;

	// Token: 0x040003C5 RID: 965
	[Space]
	public bool unlockWithXp;

	// Token: 0x040003C6 RID: 966
	public bool unlockWithDlc;

	// Token: 0x040003C7 RID: 967
	public bool unlockWithSupporterDlc;

	// Token: 0x040003C8 RID: 968
	public bool unlockWithChallenge;

	// Token: 0x040003C9 RID: 969
	[Space]
	[SerializeField]
	private Color unlockedColor;

	// Token: 0x040003CA RID: 970
	[SerializeField]
	private Color lockedColor;
}

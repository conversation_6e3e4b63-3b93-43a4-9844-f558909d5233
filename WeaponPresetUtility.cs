﻿using System;
using System.Collections.Generic;
using Newtonsoft.Json.Linq;
using UnityEngine;

// Token: 0x02000144 RID: 324
public class WeaponPresetUtility : MonoBehaviour, ISaveable
{
	// Token: 0x06000EAC RID: 3756 RVA: 0x00060A34 File Offset: 0x0005EC34
	[ContextMenu("Save Preset")]
	public void SaveCurrentPreset()
	{
		string text = this.defaultPresetName;
		if (!string.IsNullOrEmpty(WeaponPresetManager.Instance.presetNameField.text))
		{
			text = WeaponPresetManager.Instance.presetNameField.text;
		}
		WeaponPreset weaponPreset = new WeaponPreset();
		foreach (WeaponData weaponData in Spawner.weaponInfo.Values)
		{
			weaponPreset.weapons.Add(weaponData);
		}
		this.Presets[text] = weaponPreset;
		WeaponPresetManager.Instance.PopulatePresetList();
		SaveLoadSystem.Instance.Save();
	}

	// Token: 0x06000EAD RID: 3757 RVA: 0x00060AE4 File Offset: 0x0005ECE4
	[ContextMenu("Load Preset")]
	public void LoadPreset(string presetName)
	{
		WeaponPreset weaponPreset;
		if (!this.Presets.TryGetValue(presetName, out weaponPreset))
		{
			Debug.LogError("Preset '" + presetName + "' not found.");
			return;
		}
		Spawner.weaponInfo.Clear();
		foreach (WeaponData weaponData in weaponPreset.weapons)
		{
			Spawner.weaponInfo[weaponData.WeaponName] = weaponData;
		}
		Spawner.UpdateSpawnableWeapons();
		WeaponRandomizationMenu.UpdateAllSpawnProbabilities();
	}

	// Token: 0x06000EAE RID: 3758 RVA: 0x00060B7C File Offset: 0x0005ED7C
	public object SaveState()
	{
		JObject jobject = new JObject();
		foreach (KeyValuePair<string, WeaponPreset> keyValuePair in this.Presets)
		{
			jobject[keyValuePair.Key] = JObject.FromObject(keyValuePair.Value);
		}
		return jobject;
	}

	// Token: 0x06000EAF RID: 3759 RVA: 0x00060BE8 File Offset: 0x0005EDE8
	public void LoadState(JObject state)
	{
		this.Presets.Clear();
		foreach (KeyValuePair<string, JToken> keyValuePair in state)
		{
			string key = keyValuePair.Key;
			WeaponPreset weaponPreset = keyValuePair.Value.ToObject<WeaponPreset>();
			this.Presets[key] = weaponPreset;
		}
		WeaponPresetManager instance = WeaponPresetManager.Instance;
		if (instance == null)
		{
			return;
		}
		instance.PopulatePresetList();
	}

	// Token: 0x04000D47 RID: 3399
	public string defaultPresetName = "DefaultPreset";

	// Token: 0x04000D48 RID: 3400
	public readonly Dictionary<string, WeaponPreset> Presets = new Dictionary<string, WeaponPreset>();
}

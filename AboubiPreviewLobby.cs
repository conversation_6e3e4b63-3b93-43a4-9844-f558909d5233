﻿using System;
using System.Collections;
using DG.Tweening;
using FishNet.Object;
using UnityEngine;

// Token: 0x0200003E RID: 62
public class AboubiPreviewLobby : MonoBehaviour
{
	// Token: 0x06000315 RID: 789 RVA: 0x00019746 File Offset: 0x00017946
	private void Start()
	{
		this.steamLobbyScript = SteamLobby.Instance;
		this.initialPosition = base.transform.position;
		this.initialDirection = base.transform.forward;
	}

	// Token: 0x06000316 RID: 790 RVA: 0x00019778 File Offset: 0x00017978
	public void ChangeDress(GameObject hat, Material mat, GameObject cig)
	{
		if (hat != null)
		{
			if (this.currentHat != null)
			{
				global::UnityEngine.Object.Destroy(this.currentHat);
			}
			this.currentHat = global::UnityEngine.Object.Instantiate<GameObject>(hat, this.hatToWearPosition.position, this.hatToWearPosition.rotation, this.hatToWearPosition);
			this.currentHat.AddComponent<HatPosition>();
			this.currentHat.GetComponent<HatPosition>().reference = this.hatToWearPosition;
			this.currentHat.transform.localPosition = Vector3.zero;
			global::UnityEngine.Object.Destroy(this.currentHat.GetComponent<NetworkObject>());
			this.currentHat.SetActive(true);
			this.SetGameLayerRecursive(this.currentHat, 23);
		}
		if (cig != null)
		{
			if (this.currentCig != null)
			{
				global::UnityEngine.Object.Destroy(this.currentCig);
			}
			this.currentCig = global::UnityEngine.Object.Instantiate<GameObject>(cig, this.hatToWearPosition.position, this.hatToWearPosition.rotation, this.hatToWearPosition);
			this.currentCig.AddComponent<HatPosition>();
			this.currentCig.GetComponent<HatPosition>().reference = this.hatToWearPosition;
			this.currentCig.transform.localPosition = Vector3.zero;
			global::UnityEngine.Object.Destroy(this.currentCig.GetComponent<NetworkObject>());
			this.currentCig.SetActive(true);
			this.SetGameLayerRecursive(this.currentCig, 23);
		}
		if (mat != null)
		{
			GameObject[] array = this.meshesToChange;
			for (int i = 0; i < array.Length; i++)
			{
				array[i].GetComponent<SkinnedMeshRenderer>().material = mat;
			}
		}
	}

	// Token: 0x06000317 RID: 791 RVA: 0x00019910 File Offset: 0x00017B10
	private void SetGameLayerRecursive(GameObject _go, int _layer)
	{
		_go.layer = _layer;
		foreach (object obj in _go.transform)
		{
			Transform transform = (Transform)obj;
			transform.gameObject.layer = _layer;
			if (transform.GetComponentInChildren<Transform>() != null)
			{
				this.SetGameLayerRecursive(transform.gameObject, _layer);
			}
		}
	}

	// Token: 0x06000318 RID: 792 RVA: 0x00019990 File Offset: 0x00017B90
	private void Update()
	{
		if (this.isRunning)
		{
			base.transform.forward = -Vector3.forward;
		}
		else
		{
			base.transform.Rotate(0f, this.rotateSpeed * Time.deltaTime, 0f);
		}
		if (this.parentObj == null && this.previewObject.activeSelf)
		{
			this.previewObject.SetActive(false);
		}
		if (!this.meshesToChange[0].GetComponent<SkinnedMeshRenderer>().enabled)
		{
			GameObject[] array = this.meshesToChange;
			for (int i = 0; i < array.Length; i++)
			{
				array[i].GetComponent<SkinnedMeshRenderer>().enabled = true;
			}
		}
	}

	// Token: 0x06000319 RID: 793 RVA: 0x00019A3B File Offset: 0x00017C3B
	public IEnumerator RunIntoLobby()
	{
		this.isRunning = true;
		base.transform.position = this.initialPosition + Vector3.forward * 6f;
		base.transform.DOMove(this.initialPosition, 1f, false);
		yield return new WaitForSeconds(1f);
		this.isRunning = false;
		yield break;
	}

	// Token: 0x0400039F RID: 927
	[SerializeField]
	private float rotateSpeed = 20f;

	// Token: 0x040003A0 RID: 928
	[SerializeField]
	public GameObject previewObject;

	// Token: 0x040003A1 RID: 929
	[SerializeField]
	private GameObject[] meshesToChange;

	// Token: 0x040003A2 RID: 930
	[SerializeField]
	private Transform hatToWearPosition;

	// Token: 0x040003A3 RID: 931
	private GameObject currentHat;

	// Token: 0x040003A4 RID: 932
	private GameObject currentCig;

	// Token: 0x040003A5 RID: 933
	private SteamLobby steamLobbyScript;

	// Token: 0x040003A6 RID: 934
	public GameObject parentObj;

	// Token: 0x040003A7 RID: 935
	private Vector3 initialPosition;

	// Token: 0x040003A8 RID: 936
	private Vector3 initialDirection;

	// Token: 0x040003A9 RID: 937
	private bool isRunning;
}

﻿using System;
using FishNet.Object;
using UnityEngine;

// Token: 0x02000040 RID: 64
public class AnimationSoundEvent : NetworkBehaviour
{
	// Token: 0x06000321 RID: 801 RVA: 0x00019B0B File Offset: 0x00017D0B
	private void Start()
	{
		if (base.GetComponent<AudioSource>() != null)
		{
			this.audio = base.GetComponent<AudioSource>();
			return;
		}
		this.audio = base.gameObject.AddComponent<AudioSource>();
	}

	// Token: 0x06000322 RID: 802 RVA: 0x00019B39 File Offset: 0x00017D39
	public void PlaySound(int index)
	{
		this.audio.PlayOneShot(this.clips[index]);
	}

	// Token: 0x06000323 RID: 803 RVA: 0x00019B39 File Offset: 0x00017D39
	private void PlaySoundObservers(int index)
	{
		this.audio.PlayOneShot(this.clips[index]);
	}

	// Token: 0x06000325 RID: 805 RVA: 0x00019B4E File Offset: 0x00017D4E
	public virtual void NetworkInitialize___Early()
	{
		if (this.NetworkInitializeEarly_AnimationSoundEvent_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeEarly_AnimationSoundEvent_Assembly-CSharp.dll = true;
	}

	// Token: 0x06000326 RID: 806 RVA: 0x00019B61 File Offset: 0x00017D61
	public virtual void NetworkInitialize__Late()
	{
		if (this.NetworkInitializeLate_AnimationSoundEvent_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeLate_AnimationSoundEvent_Assembly-CSharp.dll = true;
	}

	// Token: 0x06000327 RID: 807 RVA: 0x00019B74 File Offset: 0x00017D74
	public override void NetworkInitializeIfDisabled()
	{
		this.NetworkInitialize___Early();
		this.NetworkInitialize__Late();
	}

	// Token: 0x06000328 RID: 808 RVA: 0x00019B74 File Offset: 0x00017D74
	public virtual void Awake()
	{
		this.NetworkInitialize___Early();
		this.NetworkInitialize__Late();
	}

	// Token: 0x06000329 RID: 809 RVA: 0x000023D6 File Offset: 0x000005D6
	public virtual void Awake___UserLogic()
	{
	}

	// Token: 0x040003AD RID: 941
	private AudioSource audio;

	// Token: 0x040003AE RID: 942
	[SerializeField]
	private AudioClip[] clips;

	// Token: 0x040003AF RID: 943
	private bool NetworkInitializeEarly_AnimationSoundEvent_Assembly-CSharp.dll;

	// Token: 0x040003B0 RID: 944
	private bool NetworkInitializeLate_AnimationSoundEvent_Assembly-CSharp.dll;
}

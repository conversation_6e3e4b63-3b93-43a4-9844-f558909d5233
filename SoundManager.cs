﻿using System;
using UnityEngine;
using UnityEngine.UI;

// Token: 0x02000112 RID: 274
public class SoundManager : MonoBehaviour
{
	// Token: 0x06000D89 RID: 3465 RVA: 0x0005C00C File Offset: 0x0005A20C
	private void Awake()
	{
		if (SoundManager.Instance == null)
		{
			SoundManager.Instance = this;
			global::UnityEngine.Object.DontDestroyOnLoad(base.gameObject);
		}
		else
		{
			global::UnityEngine.Object.Destroy(base.gameObject);
		}
		this.settings = base.GetComponent<Settings>();
	}

	// Token: 0x06000D8A RID: 3466 RVA: 0x0005C045 File Offset: 0x0005A245
	private void Start()
	{
		this.pauseManager = PauseManager.Instance;
	}

	// Token: 0x06000D8B RID: 3467 RVA: 0x0005C054 File Offset: 0x0005A254
	private void Update()
	{
		if (!this.highpassFilter || !this.lowpassFilter)
		{
			return;
		}
		if (!this.pauseManager.inMainMenu)
		{
			this.highpassFilter.cutoffFrequency = 10f;
			this.lowpassFilter.cutoffFrequency = 22000f;
			return;
		}
		if (this.SetLowpass)
		{
			this.highpassFilter.cutoffFrequency = Mathf.Lerp(this.highpassFilter.cutoffFrequency, 10f, this.lerpSpeed * Time.deltaTime);
			this.lowpassFilter.cutoffFrequency = Mathf.Lerp(this.lowpassFilter.cutoffFrequency, this.lowpass, this.lerpSpeed * Time.deltaTime);
			return;
		}
		if (this.SetHighpass)
		{
			this.highpassFilter.cutoffFrequency = Mathf.Lerp(this.highpassFilter.cutoffFrequency, this.highpass, this.lerpSpeed * Time.deltaTime);
			this.lowpassFilter.cutoffFrequency = Mathf.Lerp(this.lowpassFilter.cutoffFrequency, 22000f, this.lerpSpeed * Time.deltaTime);
			return;
		}
		this.highpassFilter.cutoffFrequency = Mathf.Lerp(this.highpassFilter.cutoffFrequency, 10f, this.lerpSpeed * 0.05f * Time.deltaTime);
		this.lowpassFilter.cutoffFrequency = Mathf.Lerp(this.lowpassFilter.cutoffFrequency, 22000f, this.lerpSpeed * 0.05f * Time.deltaTime);
	}

	// Token: 0x06000D8C RID: 3468 RVA: 0x0005C1D2 File Offset: 0x0005A3D2
	public void PlaySound(AudioClip clip)
	{
		this._effectsSource.pitch = 1f;
		this._effectsSource.PlayOneShot(clip);
	}

	// Token: 0x06000D8D RID: 3469 RVA: 0x0005C1F0 File Offset: 0x0005A3F0
	public void PlaySoundWithPitch(AudioClip clip, float pitch)
	{
		this._effectsSource.pitch = pitch;
		this._effectsSource.PlayOneShot(clip);
	}

	// Token: 0x06000D8E RID: 3470 RVA: 0x0005C20A File Offset: 0x0005A40A
	public void PlayAmbientSound(AudioClip clip)
	{
		this._ambientSource.PlayOneShot(clip);
	}

	// Token: 0x06000D8F RID: 3471 RVA: 0x0005C218 File Offset: 0x0005A418
	public void PlayMusic(AudioClip clip, int loop)
	{
		this._musicSource.PlayOneShot(clip);
		if (loop == 1)
		{
			this._musicSource.loop = true;
		}
		if (loop == 0)
		{
			this._musicSource.loop = false;
		}
	}

	// Token: 0x06000D90 RID: 3472 RVA: 0x0005C245 File Offset: 0x0005A445
	public void StopAllMusic()
	{
		this._musicSource.Stop();
	}

	// Token: 0x06000D91 RID: 3473 RVA: 0x0005C252 File Offset: 0x0005A452
	public void StopSound()
	{
		this._effectsSource.Stop();
	}

	// Token: 0x06000D92 RID: 3474 RVA: 0x0005C25F File Offset: 0x0005A45F
	public void ChangeMasterVolume(float value)
	{
		AudioListener.volume = value;
	}

	// Token: 0x06000D93 RID: 3475 RVA: 0x0005C267 File Offset: 0x0005A467
	public void ChangeMusicVolume(float value)
	{
		this._musicSource.volume = value;
	}

	// Token: 0x06000D94 RID: 3476 RVA: 0x0005C275 File Offset: 0x0005A475
	public void ChangeEffectVolume(float value)
	{
		this._effectsSource.volume = value;
	}

	// Token: 0x06000D95 RID: 3477 RVA: 0x0005C283 File Offset: 0x0005A483
	public void ChangeAmbientVolume(float value)
	{
		this._ambientSource.volume = value;
	}

	// Token: 0x06000D96 RID: 3478 RVA: 0x0005C291 File Offset: 0x0005A491
	public void ChangeMasterVolumeSlider(Slider value)
	{
		AudioListener.volume = value.value;
		PlayerPrefs.SetFloat("masterVolume", AudioListener.volume);
	}

	// Token: 0x06000D97 RID: 3479 RVA: 0x0005C2AD File Offset: 0x0005A4AD
	public void ChangeMusicVolumeSlider(Slider value)
	{
		this._musicSource.volume = value.value;
		PlayerPrefs.SetFloat("musicVolume", this._musicSource.volume);
	}

	// Token: 0x06000D98 RID: 3480 RVA: 0x0005C2D5 File Offset: 0x0005A4D5
	public void ChangeEffectVolumeSlider(Slider value)
	{
		this._effectsSource.volume = value.value;
		PlayerPrefs.SetFloat("effectsVolume", this._effectsSource.volume);
	}

	// Token: 0x06000D99 RID: 3481 RVA: 0x0005C2FD File Offset: 0x0005A4FD
	public void ChangeAmbientVolumeSlider(Slider value)
	{
		this._ambientSource.volume = value.value;
		PlayerPrefs.SetFloat("ambientVolume", this._ambientSource.volume);
	}

	// Token: 0x06000D9A RID: 3482 RVA: 0x0005C325 File Offset: 0x0005A525
	public void ChangeMenuMusicVolumeSlider(Slider value)
	{
		this.menuMusicVolume = value.value;
		PlayerPrefs.SetFloat("menuMusicVolume", this.menuMusicVolume);
	}

	// Token: 0x06000D9B RID: 3483 RVA: 0x0005C343 File Offset: 0x0005A543
	public void ToggleEffects()
	{
		this._effectsSource.mute = !this._effectsSource.mute;
	}

	// Token: 0x06000D9C RID: 3484 RVA: 0x0005C35E File Offset: 0x0005A55E
	public void ToggleMusic()
	{
		this._musicSource.mute = !this._musicSource.mute;
	}

	// Token: 0x04000C00 RID: 3072
	public static SoundManager Instance;

	// Token: 0x04000C01 RID: 3073
	public PauseManager pauseManager;

	// Token: 0x04000C02 RID: 3074
	private Settings settings;

	// Token: 0x04000C03 RID: 3075
	[SerializeField]
	public AudioSource _musicSource;

	// Token: 0x04000C04 RID: 3076
	[SerializeField]
	public AudioSource _effectsSource;

	// Token: 0x04000C05 RID: 3077
	[SerializeField]
	public AudioSource _ambientSource;

	// Token: 0x04000C06 RID: 3078
	[SerializeField]
	private AudioLowPassFilter lowpassFilter;

	// Token: 0x04000C07 RID: 3079
	[SerializeField]
	private AudioHighPassFilter highpassFilter;

	// Token: 0x04000C08 RID: 3080
	[SerializeField]
	private float lowpass = 12000f;

	// Token: 0x04000C09 RID: 3081
	[SerializeField]
	private float highpass = 14000f;

	// Token: 0x04000C0A RID: 3082
	public bool SetLowpass;

	// Token: 0x04000C0B RID: 3083
	public bool SetHighpass;

	// Token: 0x04000C0C RID: 3084
	public float menuMusicVolume = 1f;

	// Token: 0x04000C0D RID: 3085
	public float lerpSpeed = 1f;
}

﻿using System;
using UnityEngine;

// Token: 0x02000108 RID: 264
public class SetAmbientVolume : MonoBehaviour
{
	// Token: 0x06000D2D RID: 3373 RVA: 0x0005964E File Offset: 0x0005784E
	private void Awake()
	{
		this.source = SoundManager.Instance._ambientSource;
		this.audio = base.GetComponent<AudioSource>();
	}

	// Token: 0x06000D2E RID: 3374 RVA: 0x0005966C File Offset: 0x0005786C
	private void Update()
	{
		if (this.audio.volume != this.source.volume)
		{
			this.audio.volume = this.source.volume;
		}
	}

	// Token: 0x04000B63 RID: 2915
	private AudioSource source;

	// Token: 0x04000B64 RID: 2916
	private AudioSource audio;
}

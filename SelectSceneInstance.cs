﻿using System;
using DG.Tweening;
using DG.Tweening.Core;
using DG.Tweening.Plugins.Options;
using TMPro;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

// Token: 0x02000107 RID: 263
public class SelectSceneInstance : MonoBehaviour, IPointerEnterHandler, IEventSystemHandler, IPointerExitHandler
{
	// Token: 0x06000D25 RID: 3365 RVA: 0x00059346 File Offset: 0x00057546
	private void Awake()
	{
		this.button = base.GetComponent<Button>();
		this.button.transform.GetComponentInChildren<TextMeshProUGUI>().text = this.sceneName;
	}

	// Token: 0x06000D26 RID: 3366 RVA: 0x00059370 File Offset: 0x00057570
	private void Start()
	{
		if (!Application.isEditor)
		{
			this.sprite = (Texture2D)Resources.Load("MapSprites/" + this.sceneName, typeof(Texture2D));
		}
		this.mapImg.texture = this.sprite;
		this.UpdateUI();
	}

	// Token: 0x06000D27 RID: 3367 RVA: 0x000593C8 File Offset: 0x000575C8
	public void UpdateUI()
	{
		this.button.transform.GetComponentInChildren<TextMeshProUGUI>().text = this.sceneName;
		ColorBlock colors = this.button.colors;
		colors.normalColor = (this.selected ? this.selectedColor : this.deselectedColor);
		colors.selectedColor = (this.selected ? this.selectedColor : this.deselectedColor);
		colors.disabledColor = (this.selected ? this.selectedColor : this.deselectedColor);
		this.button.colors = colors;
		base.transform.DOScale(this.selected ? Vector3.one : new Vector3(0.9f, 0.9f, 0.9f), 0.3f).SetEase(Ease.OutBounce);
	}

	// Token: 0x06000D28 RID: 3368 RVA: 0x0005949C File Offset: 0x0005769C
	public void ChangeState()
	{
		this.selected = !this.selected;
		ColorBlock colors = this.button.colors;
		colors.normalColor = (this.selected ? this.selectedColor : this.deselectedColor);
		colors.selectedColor = (this.selected ? this.selectedColor : this.deselectedColor);
		colors.disabledColor = (this.selected ? this.selectedColor : this.deselectedColor);
		this.button.colors = colors;
		this.mapSelectionScript.UpdateScenes();
		base.transform.DOScale(this.selected ? Vector3.one : new Vector3(0.9f, 0.9f, 0.9f), 0.3f).SetEase(Ease.OutBounce);
	}

	// Token: 0x06000D29 RID: 3369 RVA: 0x00059570 File Offset: 0x00057770
	public void SetState(bool state)
	{
		this.selected = state;
		ColorBlock colors = this.button.colors;
		colors.normalColor = (this.selected ? this.selectedColor : this.deselectedColor);
		colors.selectedColor = (this.selected ? this.selectedColor : this.deselectedColor);
		colors.disabledColor = (this.selected ? this.selectedColor : this.deselectedColor);
		this.button.colors = colors;
		base.transform.DOScale(this.selected ? Vector3.one : new Vector3(0.9f, 0.9f, 0.9f), 0.3f).SetEase(Ease.OutBounce);
	}

	// Token: 0x06000D2A RID: 3370 RVA: 0x0005962E File Offset: 0x0005782E
	public void OnPointerEnter(PointerEventData eventData)
	{
		FloatingName.Instance.nameToShow = (this.selected ? "Unselect map" : "Select map");
	}

	// Token: 0x06000D2B RID: 3371 RVA: 0x0001A57E File Offset: 0x0001877E
	public void OnPointerExit(PointerEventData eventData)
	{
		FloatingName.Instance.nameToShow = "";
	}

	// Token: 0x04000B5B RID: 2907
	public string sceneName;

	// Token: 0x04000B5C RID: 2908
	public bool selected;

	// Token: 0x04000B5D RID: 2909
	[SerializeField]
	private Color selectedColor;

	// Token: 0x04000B5E RID: 2910
	[SerializeField]
	private Color deselectedColor;

	// Token: 0x04000B5F RID: 2911
	[SerializeField]
	private RawImage mapImg;

	// Token: 0x04000B60 RID: 2912
	private Button button;

	// Token: 0x04000B61 RID: 2913
	private Texture2D sprite;

	// Token: 0x04000B62 RID: 2914
	public MapSelection mapSelectionScript;
}

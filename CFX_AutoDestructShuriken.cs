﻿using System;
using System.Collections;
using UnityEngine;

// Token: 0x02000169 RID: 361
[RequireComponent(typeof(ParticleSystem))]
public class CFX_AutoDestructShuriken : MonoBehaviour
{
	// Token: 0x06000F48 RID: 3912 RVA: 0x00064E48 File Offset: 0x00063048
	private void OnEnable()
	{
		base.StartCoroutine("CheckIfAlive");
	}

	// Token: 0x06000F49 RID: 3913 RVA: 0x00064E56 File Offset: 0x00063056
	private IEnumerator CheckIfAlive()
	{
		do
		{
			yield return new WaitForSeconds(0.5f);
		}
		while (base.GetComponent<ParticleSystem>().IsAlive(true));
		if (this.OnlyDeactivate)
		{
			base.gameObject.SetActive(false);
		}
		else
		{
			global::UnityEngine.Object.Destroy(base.gameObject);
		}
		yield break;
	}

	// Token: 0x04000E16 RID: 3606
	public bool OnlyDeactivate;
}

﻿using System;
using System.Collections.Generic;
using UnityEngine;

// Token: 0x02000170 RID: 368
public class CFX_SpawnSystem : MonoBehaviour
{
	// Token: 0x06000F69 RID: 3945 RVA: 0x000653D4 File Offset: 0x000635D4
	public static GameObject GetNextObject(GameObject sourceObj, bool activateObject = true)
	{
		int instanceID = sourceObj.GetInstanceID();
		if (!CFX_SpawnSystem.instance.poolCursors.ContainsKey(instanceID))
		{
			Debug.LogError(string.Concat(new string[]
			{
				"[CFX_SpawnSystem.GetNextObject()] Object hasn't been preloaded: ",
				sourceObj.name,
				" (ID:",
				instanceID.ToString(),
				")\n"
			}), CFX_SpawnSystem.instance);
			return null;
		}
		int num = CFX_SpawnSystem.instance.poolCursors[instanceID];
		GameObject gameObject;
		if (CFX_SpawnSystem.instance.onlyGetInactiveObjects)
		{
			int num2 = num;
			do
			{
				gameObject = CFX_SpawnSystem.instance.instantiatedObjects[instanceID][num];
				CFX_SpawnSystem.instance.increasePoolCursor(instanceID);
				num = CFX_SpawnSystem.instance.poolCursors[instanceID];
				if (gameObject != null && !gameObject.activeSelf)
				{
					goto IL_015F;
				}
			}
			while (num != num2);
			if (!CFX_SpawnSystem.instance.instantiateIfNeeded)
			{
				Debug.LogWarning("[CFX_SpawnSystem.GetNextObject()] There are no active instances available in the pool for \"" + sourceObj.name + "\"\nYou may need to increase the preloaded object count for this prefab?", CFX_SpawnSystem.instance);
				return null;
			}
			Debug.Log("[CFX_SpawnSystem.GetNextObject()] A new instance has been created for \"" + sourceObj.name + "\" because no active instance were found in the pool.\n", CFX_SpawnSystem.instance);
			CFX_SpawnSystem.PreloadObject(sourceObj, 1);
			List<GameObject> list = CFX_SpawnSystem.instance.instantiatedObjects[instanceID];
			gameObject = list[list.Count - 1];
		}
		else
		{
			gameObject = CFX_SpawnSystem.instance.instantiatedObjects[instanceID][num];
			CFX_SpawnSystem.instance.increasePoolCursor(instanceID);
		}
		IL_015F:
		if (activateObject && gameObject != null)
		{
			gameObject.SetActive(true);
		}
		return gameObject;
	}

	// Token: 0x06000F6A RID: 3946 RVA: 0x00065554 File Offset: 0x00063754
	public static void PreloadObject(GameObject sourceObj, int poolSize = 1)
	{
		CFX_SpawnSystem.instance.addObjectToPool(sourceObj, poolSize);
	}

	// Token: 0x06000F6B RID: 3947 RVA: 0x00065562 File Offset: 0x00063762
	public static void UnloadObjects(GameObject sourceObj)
	{
		CFX_SpawnSystem.instance.removeObjectsFromPool(sourceObj);
	}

	// Token: 0x170000E8 RID: 232
	// (get) Token: 0x06000F6C RID: 3948 RVA: 0x0006556F File Offset: 0x0006376F
	public static bool AllObjectsLoaded
	{
		get
		{
			return CFX_SpawnSystem.instance.allObjectsLoaded;
		}
	}

	// Token: 0x06000F6D RID: 3949 RVA: 0x0006557C File Offset: 0x0006377C
	private void addObjectToPool(GameObject sourceObject, int number)
	{
		int instanceID = sourceObject.GetInstanceID();
		if (!this.instantiatedObjects.ContainsKey(instanceID))
		{
			this.instantiatedObjects.Add(instanceID, new List<GameObject>());
			this.poolCursors.Add(instanceID, 0);
		}
		for (int i = 0; i < number; i++)
		{
			GameObject gameObject = global::UnityEngine.Object.Instantiate<GameObject>(sourceObject);
			gameObject.SetActive(false);
			CFX_AutoDestructShuriken[] componentsInChildren = gameObject.GetComponentsInChildren<CFX_AutoDestructShuriken>(true);
			for (int j = 0; j < componentsInChildren.Length; j++)
			{
				componentsInChildren[j].OnlyDeactivate = true;
			}
			CFX_LightIntensityFade[] componentsInChildren2 = gameObject.GetComponentsInChildren<CFX_LightIntensityFade>(true);
			for (int j = 0; j < componentsInChildren2.Length; j++)
			{
				componentsInChildren2[j].autodestruct = false;
			}
			this.instantiatedObjects[instanceID].Add(gameObject);
			if (this.hideObjectsInHierarchy)
			{
				gameObject.hideFlags = HideFlags.HideInHierarchy;
			}
			if (this.spawnAsChildren)
			{
				gameObject.transform.parent = base.transform;
			}
		}
	}

	// Token: 0x06000F6E RID: 3950 RVA: 0x00065664 File Offset: 0x00063864
	private void removeObjectsFromPool(GameObject sourceObject)
	{
		int instanceID = sourceObject.GetInstanceID();
		if (!this.instantiatedObjects.ContainsKey(instanceID))
		{
			Debug.LogWarning(string.Concat(new string[]
			{
				"[CFX_SpawnSystem.removeObjectsFromPool()] There aren't any preloaded object for: ",
				sourceObject.name,
				" (ID:",
				instanceID.ToString(),
				")\n"
			}), base.gameObject);
			return;
		}
		for (int i = this.instantiatedObjects[instanceID].Count - 1; i >= 0; i--)
		{
			global::UnityEngine.Object @object = this.instantiatedObjects[instanceID][i];
			this.instantiatedObjects[instanceID].RemoveAt(i);
			global::UnityEngine.Object.Destroy(@object);
		}
		this.instantiatedObjects.Remove(instanceID);
		this.poolCursors.Remove(instanceID);
	}

	// Token: 0x06000F6F RID: 3951 RVA: 0x0006572C File Offset: 0x0006392C
	private void increasePoolCursor(int uniqueId)
	{
		Dictionary<int, int> dictionary = CFX_SpawnSystem.instance.poolCursors;
		int num = dictionary[uniqueId];
		dictionary[uniqueId] = num + 1;
		if (CFX_SpawnSystem.instance.poolCursors[uniqueId] >= CFX_SpawnSystem.instance.instantiatedObjects[uniqueId].Count)
		{
			CFX_SpawnSystem.instance.poolCursors[uniqueId] = 0;
		}
	}

	// Token: 0x06000F70 RID: 3952 RVA: 0x0006578E File Offset: 0x0006398E
	private void Awake()
	{
		if (CFX_SpawnSystem.instance != null)
		{
			Debug.LogWarning("CFX_SpawnSystem: There should only be one instance of CFX_SpawnSystem per Scene!\n", base.gameObject);
		}
		CFX_SpawnSystem.instance = this;
	}

	// Token: 0x06000F71 RID: 3953 RVA: 0x000657B4 File Offset: 0x000639B4
	private void Start()
	{
		this.allObjectsLoaded = false;
		for (int i = 0; i < this.objectsToPreload.Length; i++)
		{
			CFX_SpawnSystem.PreloadObject(this.objectsToPreload[i], this.objectsToPreloadTimes[i]);
		}
		this.allObjectsLoaded = true;
	}

	// Token: 0x04000E33 RID: 3635
	private static CFX_SpawnSystem instance;

	// Token: 0x04000E34 RID: 3636
	public GameObject[] objectsToPreload = new GameObject[0];

	// Token: 0x04000E35 RID: 3637
	public int[] objectsToPreloadTimes = new int[0];

	// Token: 0x04000E36 RID: 3638
	public bool hideObjectsInHierarchy;

	// Token: 0x04000E37 RID: 3639
	public bool spawnAsChildren = true;

	// Token: 0x04000E38 RID: 3640
	public bool onlyGetInactiveObjects;

	// Token: 0x04000E39 RID: 3641
	public bool instantiateIfNeeded;

	// Token: 0x04000E3A RID: 3642
	private bool allObjectsLoaded;

	// Token: 0x04000E3B RID: 3643
	private Dictionary<int, List<GameObject>> instantiatedObjects = new Dictionary<int, List<GameObject>>();

	// Token: 0x04000E3C RID: 3644
	private Dictionary<int, int> poolCursors = new Dictionary<int, int>();
}

﻿using System;
using System.Collections;
using UnityEngine;

namespace UnitySA.Utility
{
	// Token: 0x0200017C RID: 380
	[Serializable]
	public class LerpCtrlBob
	{
		// Token: 0x06000FAE RID: 4014 RVA: 0x00066EC9 File Offset: 0x000650C9
		public float Offset()
		{
			return this.m_Offset;
		}

		// Token: 0x06000FAF RID: 4015 RVA: 0x00066ED1 File Offset: 0x000650D1
		public IEnumerator DoBobCycle()
		{
			float t = 0f;
			while (t < this.BobDuration)
			{
				this.m_Offset = Mathf.Lerp(0f, this.BobAmount, t / this.BobDuration);
				t += Time.deltaTime;
				yield return new WaitForFixedUpdate();
			}
			t = 0f;
			while (t < this.BobDuration)
			{
				this.m_Offset = Mathf.Lerp(this.BobAmount, 0f, t / this.BobDuration);
				t += Time.deltaTime;
				yield return new WaitForFixedUpdate();
			}
			this.m_Offset = 0f;
			yield break;
		}

		// Token: 0x04000E95 RID: 3733
		public float BobDuration;

		// Token: 0x04000E96 RID: 3734
		public float BobAmount;

		// Token: 0x04000E97 RID: 3735
		private float m_Offset;
	}
}

﻿using System;
using FishNet.Object;
using UnityEngine;

// Token: 0x02000039 RID: 57
public class Suppression : NetworkBehaviour
{
	// Token: 0x060002FA RID: 762 RVA: 0x0001917F File Offset: 0x0001737F
	private void Start()
	{
		this.audio = base.GetComponent<AudioSource>();
	}

	// Token: 0x060002FB RID: 763 RVA: 0x0001918D File Offset: 0x0001738D
	public void SuppressionTrigger()
	{
		if (base.IsOwner)
		{
			this.audio.PlayOneShot(this.supClip[Mathf.RoundToInt((float)global::UnityEngine.Random.Range(0, this.supClip.Length))]);
			this.camPP.TrigSup();
		}
	}

	// Token: 0x060002FD RID: 765 RVA: 0x000191C8 File Offset: 0x000173C8
	public virtual void NetworkInitialize___Early()
	{
		if (this.NetworkInitializeEarly_Suppression_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeEarly_Suppression_Assembly-CSharp.dll = true;
	}

	// Token: 0x060002FE RID: 766 RVA: 0x000191DB File Offset: 0x000173DB
	public virtual void NetworkInitialize__Late()
	{
		if (this.NetworkInitializeLate_Suppression_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeLate_Suppression_Assembly-CSharp.dll = true;
	}

	// Token: 0x060002FF RID: 767 RVA: 0x000191EE File Offset: 0x000173EE
	public override void NetworkInitializeIfDisabled()
	{
		this.NetworkInitialize___Early();
		this.NetworkInitialize__Late();
	}

	// Token: 0x06000300 RID: 768 RVA: 0x000191EE File Offset: 0x000173EE
	public virtual void Awake()
	{
		this.NetworkInitialize___Early();
		this.NetworkInitialize__Late();
	}

	// Token: 0x06000301 RID: 769 RVA: 0x000023D6 File Offset: 0x000005D6
	public virtual void Awake___UserLogic()
	{
	}

	// Token: 0x04000383 RID: 899
	private AudioSource audio;

	// Token: 0x04000384 RID: 900
	[SerializeField]
	private AudioClip[] supClip;

	// Token: 0x04000385 RID: 901
	[SerializeField]
	private CameraEffect camPP;

	// Token: 0x04000386 RID: 902
	private bool NetworkInitializeEarly_Suppression_Assembly-CSharp.dll;

	// Token: 0x04000387 RID: 903
	private bool NetworkInitializeLate_Suppression_Assembly-CSharp.dll;
}

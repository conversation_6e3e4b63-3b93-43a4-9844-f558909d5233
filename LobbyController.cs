﻿using System;
using System.Collections.Generic;
using System.Linq;
using FishNet;
using FishNet.Managing;
using FishNet.Object;
using Steamworks;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

// Token: 0x020000D0 RID: 208
public class LobbyController : MonoBehaviour
{
	// Token: 0x06000B84 RID: 2948 RVA: 0x00050E25 File Offset: 0x0004F025
	private void Awake()
	{
		if (LobbyController.Instance == null)
		{
			LobbyController.Instance = this;
			global::UnityEngine.Object.DontDestroyOnLoad(base.gameObject);
			return;
		}
		global::UnityEngine.Object.Destroy(base.gameObject);
	}

	// Token: 0x06000B85 RID: 2949 RVA: 0x00050E51 File Offset: 0x0004F051
	private void Start()
	{
		this.manager = SteamLobby.Instance;
		this.networkManager = InstanceFinder.NetworkManager;
	}

	// Token: 0x06000B86 RID: 2950 RVA: 0x00050E69 File Offset: 0x0004F069
	public void ReadyPlayer()
	{
		this.LocalPlayerController.ChangeReady();
	}

	// Token: 0x06000B87 RID: 2951 RVA: 0x00050E76 File Offset: 0x0004F076
	public void UpdateButton()
	{
		if (this.LocalPlayerController.Ready)
		{
			this.ReadyButtonText.text = "Ready !";
			return;
		}
		this.ReadyButtonText.text = "Ready Up";
	}

	// Token: 0x06000B88 RID: 2952 RVA: 0x00050EA6 File Offset: 0x0004F0A6
	public bool HasEnoughPlayers()
	{
		return SteamLobby.Instance.players.Count >= 2 || Application.isEditor;
	}

	// Token: 0x06000B89 RID: 2953 RVA: 0x00050EC1 File Offset: 0x0004F0C1
	private void Update()
	{
		if (this.LocalPlayerController == null)
		{
			return;
		}
		if (this.PlayerListItems.Count > SteamLobby.Instance.maxPlayers)
		{
			LobbyController.Instance.RemoveExtraPlayerItem();
		}
	}

	// Token: 0x06000B8A RID: 2954 RVA: 0x00050EF4 File Offset: 0x0004F0F4
	public void CheckIfAllReady()
	{
		bool flag = true;
		using (List<NetworkObject>.Enumerator enumerator = this.manager.players.GetEnumerator())
		{
			while (enumerator.MoveNext())
			{
				if (!enumerator.Current.GetComponent<ClientInstance>().Ready)
				{
					flag = false;
					break;
				}
			}
		}
		this.StartGameButton.interactable = flag && this.HasEnoughPlayers() && this.LocalPlayerController.IsServer;
		SteamLobby.Instance.AllReady = this.StartGameButton.interactable;
	}

	// Token: 0x06000B8B RID: 2955 RVA: 0x00050F90 File Offset: 0x0004F190
	public void UpdateLobbyName()
	{
		this.CurrentLobbyID = this.manager.CurrentLobbyID;
		this.LobbyNameText.text = SteamMatchmaking.GetLobbyData(new CSteamID(this.CurrentLobbyID), "name");
	}

	// Token: 0x06000B8C RID: 2956 RVA: 0x00050FC4 File Offset: 0x0004F1C4
	public void UpdatePlayerList()
	{
		if (this.PlayerListItems.Count == 0)
		{
			this.PlayerItemCreated = false;
		}
		if (!this.PlayerItemCreated)
		{
			this.CreateHostPlayerItem();
		}
		if (this.PlayerListItems.Count < this.manager.players.Count)
		{
			this.CreateClientPlayerItem();
		}
		if (this.PlayerListItems.Count > this.manager.players.Count)
		{
			this.RemovePlayerItem();
		}
		if (this.PlayerListItems.Count == this.manager.players.Count)
		{
			this.UpdatePlayerItem();
		}
	}

	// Token: 0x06000B8D RID: 2957 RVA: 0x0005105C File Offset: 0x0004F25C
	public void CreateHostPlayerItem()
	{
		foreach (NetworkObject networkObject in this.manager.players)
		{
			GameObject gameObject = global::UnityEngine.Object.Instantiate<GameObject>(this.PlayerListItemPrefab);
			PlayerListItem component = gameObject.GetComponent<PlayerListItem>();
			component.PlayerName = networkObject.GetComponent<ClientInstance>().PlayerName;
			component.ConnectionID = networkObject.GetComponent<ClientInstance>().ConnectionID;
			component.PlayerIdNumber = networkObject.GetComponent<ClientInstance>().PlayerId;
			component.PlayerSteamID = networkObject.GetComponent<ClientInstance>().PlayerSteamID;
			component.Ready = networkObject.GetComponent<ClientInstance>().Ready;
			component.SetPlayerValues();
			gameObject.transform.SetParent(this.PlayerListViewContent.transform);
			gameObject.transform.localScale = Vector3.one;
			gameObject.transform.position = this.hostPosition.position;
			this.PlayerListItems.Add(component);
		}
		foreach (NetworkObject networkObject2 in this.manager.players)
		{
			GameObject gameObject2 = global::UnityEngine.Object.Instantiate<GameObject>(this.PlayerListItemPrefab);
			PlayerListItem component2 = gameObject2.GetComponent<PlayerListItem>();
			component2.PlayerName = networkObject2.GetComponent<ClientInstance>().PlayerName;
			component2.ConnectionID = networkObject2.GetComponent<ClientInstance>().ConnectionID;
			component2.PlayerIdNumber = networkObject2.GetComponent<ClientInstance>().PlayerId;
			component2.PlayerSteamID = networkObject2.GetComponent<ClientInstance>().PlayerSteamID;
			component2.Ready = networkObject2.GetComponent<ClientInstance>().Ready;
			component2.SetPlayerValues();
			gameObject2.transform.SetParent(this.tabScreen);
			gameObject2.transform.localScale = Vector3.one;
			gameObject2.transform.position = this.tabhostPosition.position;
			this.PlayerListItemsTab.Add(component2);
		}
		this.PlayerItemCreated = true;
	}

	// Token: 0x06000B8E RID: 2958 RVA: 0x0005126C File Offset: 0x0004F46C
	public void CreateClientPlayerItem()
	{
		using (List<NetworkObject>.Enumerator enumerator = this.manager.players.GetEnumerator())
		{
			while (enumerator.MoveNext())
			{
				NetworkObject player = enumerator.Current;
				if (!this.PlayerListItems.Any((PlayerListItem b) => b.ConnectionID == player.Owner.ClientId))
				{
					GameObject gameObject = global::UnityEngine.Object.Instantiate<GameObject>(this.PlayerListItemPrefab);
					PlayerListItem component = gameObject.GetComponent<PlayerListItem>();
					component.PlayerName = player.GetComponent<ClientInstance>().PlayerName;
					component.ConnectionID = player.GetComponent<ClientInstance>().ConnectionID;
					component.PlayerIdNumber = player.GetComponent<ClientInstance>().PlayerId;
					component.PlayerSteamID = player.GetComponent<ClientInstance>().PlayerSteamID;
					component.Ready = player.GetComponent<ClientInstance>().Ready;
					component.SetPlayerValues();
					gameObject.transform.SetParent(this.PlayerListViewContent.transform);
					gameObject.transform.localScale = Vector3.one;
					gameObject.transform.position = this.clientPosition[player.GetComponent<ClientInstance>().PlayerId - 1].position;
					this.PlayerListItems.Add(component);
				}
				if (!this.PlayerListItemsTab.Any((PlayerListItem b) => b.ConnectionID == player.Owner.ClientId))
				{
					GameObject gameObject2 = global::UnityEngine.Object.Instantiate<GameObject>(this.PlayerListItemPrefab);
					PlayerListItem component2 = gameObject2.GetComponent<PlayerListItem>();
					component2.PlayerName = player.GetComponent<ClientInstance>().PlayerName;
					component2.ConnectionID = player.GetComponent<ClientInstance>().ConnectionID;
					component2.PlayerIdNumber = player.GetComponent<ClientInstance>().PlayerId;
					component2.PlayerSteamID = player.GetComponent<ClientInstance>().PlayerSteamID;
					component2.Ready = player.GetComponent<ClientInstance>().Ready;
					component2.SetPlayerValues();
					gameObject2.transform.SetParent(this.tabScreen);
					gameObject2.transform.localScale = Vector3.one;
					gameObject2.transform.position = this.tabclientPosition[player.GetComponent<ClientInstance>().PlayerId - 1].position;
					this.PlayerListItemsTab.Add(component2);
				}
			}
		}
	}

	// Token: 0x06000B8F RID: 2959 RVA: 0x000514D0 File Offset: 0x0004F6D0
	public void UpdatePlayerItem()
	{
		foreach (NetworkObject networkObject in this.manager.players)
		{
			foreach (PlayerListItem playerListItem in this.PlayerListItems)
			{
				if (playerListItem.ConnectionID == networkObject.GetComponent<ClientInstance>().ConnectionID)
				{
					playerListItem.PlayerName = networkObject.GetComponent<ClientInstance>().PlayerName;
					playerListItem.Ready = networkObject.GetComponent<ClientInstance>().Ready;
					playerListItem.SetPlayerValues();
					if (networkObject == this.LocalPlayerController.GetComponent<NetworkObject>())
					{
						this.UpdateButton();
					}
				}
			}
			foreach (PlayerListItem playerListItem2 in this.PlayerListItemsTab)
			{
				if (playerListItem2.ConnectionID == networkObject.GetComponent<ClientInstance>().ConnectionID)
				{
					playerListItem2.PlayerName = networkObject.GetComponent<ClientInstance>().PlayerName;
					playerListItem2.Ready = networkObject.GetComponent<ClientInstance>().Ready;
					playerListItem2.SetPlayerValues();
					if (networkObject == this.LocalPlayerController.GetComponent<NetworkObject>())
					{
						this.UpdateButton();
					}
				}
			}
		}
		this.CheckIfAllReady();
	}

	// Token: 0x06000B90 RID: 2960 RVA: 0x00051674 File Offset: 0x0004F874
	public void RemovePlayerItem()
	{
		List<PlayerListItem> list = new List<PlayerListItem>();
		List<PlayerListItem> list2 = new List<PlayerListItem>();
		using (List<PlayerListItem>.Enumerator enumerator = this.PlayerListItems.GetEnumerator())
		{
			while (enumerator.MoveNext())
			{
				PlayerListItem playerlistItem2 = enumerator.Current;
				if (!this.manager.players.Any((NetworkObject b) => b.GetComponent<ClientInstance>().ConnectionID == playerlistItem2.ConnectionID))
				{
					list.Add(playerlistItem2);
				}
			}
		}
		if (list.Count > 0)
		{
			foreach (PlayerListItem playerListItem in list)
			{
				global::UnityEngine.Object gameObject = playerListItem.gameObject;
				this.PlayerListItems.Remove(playerListItem);
				global::UnityEngine.Object.Destroy(gameObject);
			}
		}
		using (List<PlayerListItem>.Enumerator enumerator = this.PlayerListItemsTab.GetEnumerator())
		{
			while (enumerator.MoveNext())
			{
				PlayerListItem playerlistItem = enumerator.Current;
				if (!this.manager.players.Any((NetworkObject b) => b.GetComponent<ClientInstance>().ConnectionID == playerlistItem.ConnectionID))
				{
					list2.Add(playerlistItem);
				}
			}
		}
		if (list2.Count > 0)
		{
			foreach (PlayerListItem playerListItem2 in list2)
			{
				global::UnityEngine.Object gameObject2 = playerListItem2.gameObject;
				this.PlayerListItemsTab.Remove(playerListItem2);
				global::UnityEngine.Object.Destroy(gameObject2);
			}
		}
	}

	// Token: 0x06000B91 RID: 2961 RVA: 0x0005182C File Offset: 0x0004FA2C
	public void RemoveExtraPlayerItem()
	{
		List<PlayerListItem> list = new List<PlayerListItem>();
		for (int i = 0; i < this.PlayerListItems.Count; i++)
		{
			if (i > 1)
			{
				list.Add(this.PlayerListItems[i]);
			}
		}
		if (list.Count > 0)
		{
			foreach (PlayerListItem playerListItem in list)
			{
				global::UnityEngine.Object gameObject = playerListItem.gameObject;
				this.PlayerListItems.Remove(playerListItem);
				global::UnityEngine.Object.Destroy(gameObject);
			}
		}
		List<PlayerListItem> list2 = new List<PlayerListItem>();
		for (int j = 0; j < this.PlayerListItemsTab.Count; j++)
		{
			if (j > 1)
			{
				list2.Add(this.PlayerListItemsTab[j]);
			}
		}
		if (list2.Count > 0)
		{
			foreach (PlayerListItem playerListItem2 in list2)
			{
				global::UnityEngine.Object gameObject2 = playerListItem2.gameObject;
				this.PlayerListItemsTab.Remove(playerListItem2);
				global::UnityEngine.Object.Destroy(gameObject2);
			}
		}
	}

	// Token: 0x040009A8 RID: 2472
	public static LobbyController Instance;

	// Token: 0x040009A9 RID: 2473
	public TextMeshProUGUI LobbyNameText;

	// Token: 0x040009AA RID: 2474
	[SerializeField]
	private Transform hostPosition;

	// Token: 0x040009AB RID: 2475
	public Transform[] clientPosition;

	// Token: 0x040009AC RID: 2476
	[SerializeField]
	private Transform tabhostPosition;

	// Token: 0x040009AD RID: 2477
	public Transform[] tabclientPosition;

	// Token: 0x040009AE RID: 2478
	[Space]
	public AboubiPreviewLobby[] previews;

	// Token: 0x040009AF RID: 2479
	[Space]
	public GameObject PlayerListViewContent;

	// Token: 0x040009B0 RID: 2480
	public GameObject PlayerListItemPrefab;

	// Token: 0x040009B1 RID: 2481
	public GameObject LocalPlayerObject;

	// Token: 0x040009B2 RID: 2482
	public ulong CurrentLobbyID;

	// Token: 0x040009B3 RID: 2483
	public bool PlayerItemCreated;

	// Token: 0x040009B4 RID: 2484
	private List<PlayerListItem> PlayerListItems = new List<PlayerListItem>();

	// Token: 0x040009B5 RID: 2485
	private List<PlayerListItem> PlayerListItemsTab = new List<PlayerListItem>();

	// Token: 0x040009B6 RID: 2486
	public ClientInstance LocalPlayerController;

	// Token: 0x040009B7 RID: 2487
	[SerializeField]
	private Button StartGameButton;

	// Token: 0x040009B8 RID: 2488
	[SerializeField]
	private TextMeshProUGUI ReadyButtonText;

	// Token: 0x040009B9 RID: 2489
	private SteamLobby manager;

	// Token: 0x040009BA RID: 2490
	private NetworkManager networkManager;

	// Token: 0x040009BB RID: 2491
	[Space]
	[SerializeField]
	private Transform tabScreen;
}

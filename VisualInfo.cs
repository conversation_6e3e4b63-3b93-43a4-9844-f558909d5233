﻿using System;
using FishNet.Object;
using TMPro;
using UnityEngine;

// Token: 0x02000076 RID: 118
public class VisualInfo : NetworkBehaviour
{
	// Token: 0x06000516 RID: 1302 RVA: 0x000219B0 File Offset: 0x0001FBB0
	private void Update()
	{
		if (this.cam == null || base.IsOwner)
		{
			this.container.SetActive(false);
			return;
		}
		this.container.SetActive(true);
		Vector3 vector = this.cam.WorldToScreenPoint(this.lookAt.position + this.offset);
		if (base.transform.position != vector)
		{
			base.transform.position = vector;
		}
	}

	// Token: 0x06000518 RID: 1304 RVA: 0x00021A2D File Offset: 0x0001FC2D
	public virtual void NetworkInitialize___Early()
	{
		if (this.NetworkInitializeEarly_VisualInfo_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeEarly_VisualInfo_Assembly-CSharp.dll = true;
	}

	// Token: 0x06000519 RID: 1305 RVA: 0x00021A40 File Offset: 0x0001FC40
	public virtual void NetworkInitialize__Late()
	{
		if (this.NetworkInitializeLate_VisualInfo_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeLate_VisualInfo_Assembly-CSharp.dll = true;
	}

	// Token: 0x0600051A RID: 1306 RVA: 0x00021A53 File Offset: 0x0001FC53
	public override void NetworkInitializeIfDisabled()
	{
		this.NetworkInitialize___Early();
		this.NetworkInitialize__Late();
	}

	// Token: 0x0600051B RID: 1307 RVA: 0x00021A53 File Offset: 0x0001FC53
	public virtual void Awake()
	{
		this.NetworkInitialize___Early();
		this.NetworkInitialize__Late();
	}

	// Token: 0x0600051C RID: 1308 RVA: 0x000023D6 File Offset: 0x000005D6
	public virtual void Awake___UserLogic()
	{
	}

	// Token: 0x04000509 RID: 1289
	public new TextMeshProUGUI name;

	// Token: 0x0400050A RID: 1290
	[Header("Tweaks")]
	public Transform lookAt;

	// Token: 0x0400050B RID: 1291
	public Vector3 offset;

	// Token: 0x0400050C RID: 1292
	[Header("Logic")]
	public Camera cam;

	// Token: 0x0400050D RID: 1293
	public GameObject container;

	// Token: 0x0400050E RID: 1294
	private bool NetworkInitializeEarly_VisualInfo_Assembly-CSharp.dll;

	// Token: 0x0400050F RID: 1295
	private bool NetworkInitializeLate_VisualInfo_Assembly-CSharp.dll;
}

﻿using System;
using DG.Tweening;
using TMPro;
using UnityEngine;

// Token: 0x02000057 RID: 87
public class HealthTween : MonoBehaviour
{
	// Token: 0x060003DD RID: 989 RVA: 0x0001BEFF File Offset: 0x0001A0FF
	private void Start()
	{
		this.timer = this.timeBetweenHeartBeat;
		this.timeBetweenHeartBeatFunc = this.timeBetweenHeartBeat;
	}

	// Token: 0x060003DE RID: 990 RVA: 0x0001BF1C File Offset: 0x0001A11C
	private void Update()
	{
		this.timer -= Time.deltaTime;
		this.text.text = Mathf.Ceil(this.health / 4f * 100f).ToString();
		if (this.timer < 0f)
		{
			this.timer = this.timeBetweenHeartBeatFunc;
			this.heart.DOPunchScale(new Vector3(this.heartScale, this.heartScale, this.heartScale), this.heartSpeed, 10, 1f).OnComplete(delegate
			{
				this.heart.DOPunchScale(new Vector3(this.littleHeartScale, this.littleHeartScale, this.littleHeartScale), this.heartSpeed, 10, 1f);
			});
		}
		this.timeBetweenHeartBeatFunc = Mathf.Lerp(this.timeBetweenHeartBeat, 0.43f, 1f - this.health / 4f);
	}

	// Token: 0x060003DF RID: 991 RVA: 0x0001BFE8 File Offset: 0x0001A1E8
	public void ChangeState()
	{
		base.transform.DOKill(false);
		base.transform.localScale = Vector3.one;
		base.transform.DOPunchScale(this.sizeTween, this.duration, 10, 1f);
		Color color = Color.Lerp(this.firstColor, this.lastColor, 1f - this.health / 4f);
		this.text.color = color;
		this.text.fontSharedMaterial.SetColor(ShaderUtilities.ID_GlowColor, color);
		this.text.outlineColor = color;
	}

	// Token: 0x0400042B RID: 1067
	public float health;

	// Token: 0x0400042C RID: 1068
	[SerializeField]
	private Color firstColor;

	// Token: 0x0400042D RID: 1069
	[SerializeField]
	private Color lastColor;

	// Token: 0x0400042E RID: 1070
	[Space]
	[SerializeField]
	private Vector3 sizeTween = new Vector3(1.2f, 1.2f, 1.2f);

	// Token: 0x0400042F RID: 1071
	[SerializeField]
	private float duration = 0.3f;

	// Token: 0x04000430 RID: 1072
	[SerializeField]
	private int vibrato;

	// Token: 0x04000431 RID: 1073
	[SerializeField]
	private TMP_Text text;

	// Token: 0x04000432 RID: 1074
	[SerializeField]
	private Transform heart;

	// Token: 0x04000433 RID: 1075
	[SerializeField]
	private float timeBetweenHeartBeat = 1f;

	// Token: 0x04000434 RID: 1076
	[SerializeField]
	private float heartScale = 1.09f;

	// Token: 0x04000435 RID: 1077
	[SerializeField]
	private float littleHeartScale = 1.04f;

	// Token: 0x04000436 RID: 1078
	[SerializeField]
	private float heartSpeed = 0.08f;

	// Token: 0x04000437 RID: 1079
	private float timeBetweenHeartBeatFunc;

	// Token: 0x04000438 RID: 1080
	private float timer;
}

﻿using System;
using UnityEngine;

// Token: 0x02000056 RID: 86
public class HatPosition : MonoBehaviour
{
	// Token: 0x060003DB RID: 987 RVA: 0x0001BEA4 File Offset: 0x0001A0A4
	private void Update()
	{
		if (base.transform.parent == null || this.reference == null)
		{
			return;
		}
		base.transform.position = this.reference.position;
		base.transform.rotation = this.reference.rotation;
	}

	// Token: 0x0400042A RID: 1066
	public Transform reference;
}

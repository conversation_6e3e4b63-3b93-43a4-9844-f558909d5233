﻿using System;
using System.Collections;
using DG.Tweening;
using FishNet;
using FishNet.Connection;
using FishNet.Managing;
using FishNet.Object;
using FishNet.Object.Delegating;
using FishNet.Object.Synchronizing;
using FishNet.Serializing;
using FishNet.Serializing.Generated;
using FishNet.Transporting;
using UnityEngine;
using UnityEngine.Animations.Rigging;
using UnityEngine.InputSystem;

// Token: 0x0200002C RID: 44
public class PlayerPickup : NetworkBehaviour
{
	// Token: 0x06000228 RID: 552 RVA: 0x00012868 File Offset: 0x00010A68
	public void SetRightIKTarget(Transform transform)
	{
		this.TargetForRightIK = transform;
	}

	// Token: 0x06000229 RID: 553 RVA: 0x00012871 File Offset: 0x00010A71
	public void SetLeftIKTarget(Transform transform)
	{
		this.TargetForLeftIK = transform;
	}

	// Token: 0x0600022A RID: 554 RVA: 0x0001287C File Offset: 0x00010A7C
	public void UpdateIKPoistion()
	{
		if (this.TargetForRightIK)
		{
			this.RightHandIKTarget.SetPositionAndRotation(this.TargetForRightIK.position, this.TargetForRightIK.rotation);
		}
		else
		{
			this.RightHandIKTarget.SetPositionAndRotation(this.RightIdlePosition.position, this.RightIdlePosition.rotation);
		}
		if (this.TargetForLeftIK)
		{
			this.LeftHandIKTarget.SetPositionAndRotation(this.TargetForLeftIK.position, this.TargetForLeftIK.rotation);
			return;
		}
		this.LeftHandIKTarget.SetPositionAndRotation(this.LeftIdlePosition.position, this.LeftIdlePosition.rotation);
	}

	// Token: 0x0600022B RID: 555 RVA: 0x0001292C File Offset: 0x00010B2C
	public override void OnStartClient()
	{
		base.OnStartClient();
		this.cam = base.GetComponent<FirstPersonController>().playerCamera;
		this.camHolder = base.GetComponent<FirstPersonController>().playerCameraHolder;
		this.playerController = base.GetComponent<FirstPersonController>();
		this.pickupPositionRightHand = new Transform[this.RightHandPositions.GetComponentsInChildren<ItemPosition>().Length];
		for (int i = 0; i < this.pickupPositionRightHand.Length; i++)
		{
			this.pickupPositionRightHand[i] = this.RightHandPositions.GetComponentsInChildren<ItemPosition>()[i].transform;
		}
		this.pickupPositionLeftHand = new Transform[this.LeftHandPositions.GetComponentsInChildren<ItemPosition>().Length];
		for (int j = 0; j < this.pickupPositionLeftHand.Length; j++)
		{
			this.pickupPositionLeftHand[j] = this.LeftHandPositions.GetComponentsInChildren<ItemPosition>()[j].transform;
		}
		this.pickupPositionBothHand = new Transform[this.BothHandPositions.GetComponentsInChildren<ItemPosition>().Length];
		for (int k = 0; k < this.pickupPositionBothHand.Length; k++)
		{
			this.pickupPositionBothHand[k] = this.BothHandPositions.GetComponentsInChildren<ItemPosition>()[k].transform;
		}
		this.aimPositionBothHand = new Transform[this.AimBothHandPositions.GetComponentsInChildren<ItemPosition>().Length];
		for (int l = 0; l < this.aimPositionBothHand.Length; l++)
		{
			this.aimPositionBothHand[l] = this.AimBothHandPositions.GetComponentsInChildren<ItemPosition>()[l].transform;
		}
		this.aimPositionRightHand = new Transform[this.AimRightHandPositions.GetComponentsInChildren<ItemPosition>().Length];
		for (int m = 0; m < this.aimPositionRightHand.Length; m++)
		{
			this.aimPositionRightHand[m] = this.AimRightHandPositions.GetComponentsInChildren<ItemPosition>()[m].transform;
		}
		this.pickupPositionOnline = new Transform[this.OnlinePositions.GetComponentsInChildren<ItemPosition>().Length];
		for (int n = 0; n < this.pickupPositionOnline.Length; n++)
		{
			this.pickupPositionOnline[n] = this.OnlinePositions.GetComponentsInChildren<ItemPosition>()[n].transform;
		}
	}

	// Token: 0x0600022C RID: 556 RVA: 0x00012B17 File Offset: 0x00010D17
	public virtual void Awake()
	{
		this.NetworkInitialize___Early();
		this.Awake___UserLogic();
		this.NetworkInitialize__Late();
	}

	// Token: 0x0600022D RID: 557 RVA: 0x00012B2C File Offset: 0x00010D2C
	private void OnEnable()
	{
		this.interact = this.playerControls.Player.Interact;
		this.interact.Enable();
		this.interact.performed += this.HandleInteraction;
		this.drop = this.playerControls.Player.Drop;
		this.drop.Enable();
		this.drop.performed += this.HandleDrop;
		this.change = this.playerControls.Player.ChangeWeapon;
		this.change.Enable();
		this.change.performed += this.HandleDrop;
	}

	// Token: 0x0600022E RID: 558 RVA: 0x00012BEC File Offset: 0x00010DEC
	private void OnDisable()
	{
		this.interact.Disable();
		this.drop.Disable();
		this.change.Disable();
		this.change.performed -= this.HandleDrop;
		this.drop.performed -= this.HandleDrop;
		this.interact.performed -= this.HandleInteraction;
	}

	// Token: 0x0600022F RID: 559 RVA: 0x00012C60 File Offset: 0x00010E60
	private void Update()
	{
		this.UpdateIKPoistion();
		if (!base.IsOwner)
		{
			return;
		}
		this.RightHandFix();
		this.LeftHandFix();
		this.dropTimer -= Time.deltaTime;
		this.interactTimer -= Time.deltaTime;
		if (!this.SyncAccessor_hasObjectInHand)
		{
			this.playerController.movementFactor = 1f;
			this.playerController.jumpFactor = 1f;
			this.playerController.maxWallJumps = 1;
			this.playerController.wallJumpFactor = 1f;
		}
		if (this.cam != null)
		{
			this.HandleInteractionCheck();
			this.HandleInteractEnvironment();
			this.HandleAboubiGrab();
			if (this.SyncAccessor_objInHand == null && this.SyncAccessor_objInLeftHand == null)
			{
				this.animator.SetBool("TwoHanded", false);
				this.animator.SetBool("DoubleHanded", false);
				this.animator.SetBool("RightHanded", false);
				this.globalAnimator.SetBool("TwoHanded", false);
				this.globalAnimator.SetBool("DoubleSingle", false);
				this.globalAnimator.SetBool("SingleHanded", false);
				this.globalAnimator.SetBool("LeftHanded", false);
				return;
			}
			if (this.SyncAccessor_objInLeftHand == null && this.SyncAccessor_objInHand != null)
			{
				if (this.weaponInHand.requireBothHands)
				{
					if (this.behaviourInHand.rightHandAnim == "")
					{
						this.animator.SetBool("TwoHanded", true);
						this.animator.SetBool("DoubleHanded", false);
						this.animator.SetBool("RightHanded", false);
					}
					this.globalAnimator.SetBool("TwoHanded", true);
					this.globalAnimator.SetBool("DoubleSingle", false);
					this.globalAnimator.SetBool("SingleHanded", false);
					this.globalAnimator.SetBool("LeftHanded", false);
					return;
				}
				if (!this.weaponInHand.requireBothHands)
				{
					if (this.behaviourInHand.rightHandAnim == "")
					{
						this.animator.SetBool("TwoHanded", false);
						this.animator.SetBool("DoubleHanded", false);
						this.animator.SetBool("RightHanded", true);
					}
					this.globalAnimator.SetBool("TwoHanded", false);
					this.globalAnimator.SetBool("DoubleSingle", false);
					this.globalAnimator.SetBool("SingleHanded", true);
					this.globalAnimator.SetBool("LeftHanded", false);
					return;
				}
			}
			else
			{
				if (this.SyncAccessor_objInLeftHand != null && this.SyncAccessor_objInHand != null)
				{
					if (this.behaviourInLeftHand.leftHandAnim == "" && this.behaviourInHand.rightHandAnim == "")
					{
						this.animator.SetBool("TwoHanded", false);
						this.animator.SetBool("DoubleHanded", true);
						this.animator.SetBool("RightHanded", false);
					}
					this.globalAnimator.SetBool("TwoHanded", false);
					this.globalAnimator.SetBool("DoubleSingle", true);
					this.globalAnimator.SetBool("SingleHanded", false);
					this.globalAnimator.SetBool("LeftHanded", false);
					return;
				}
				if (this.SyncAccessor_objInLeftHand != null && this.SyncAccessor_objInHand == null)
				{
					if (this.behaviourInLeftHand.leftHandAnim == "")
					{
						this.animator.SetBool("TwoHanded", false);
						this.animator.SetBool("DoubleHanded", false);
						this.animator.SetBool("RightHanded", true);
					}
					this.globalAnimator.SetBool("TwoHanded", false);
					this.globalAnimator.SetBool("DoubleSingle", false);
					this.globalAnimator.SetBool("SingleHanded", false);
					this.globalAnimator.SetBool("LeftHanded", true);
					return;
				}
				if (!this.weaponInHand.requireBothHands)
				{
					if (this.behaviourInHand.rightHandAnim == "")
					{
						this.animator.SetBool("TwoHanded", false);
						this.animator.SetBool("DoubleHanded", false);
						this.animator.SetBool("RightHanded", true);
					}
					this.globalAnimator.SetBool("TwoHanded", false);
					this.globalAnimator.SetBool("DoubleSingle", false);
					this.globalAnimator.SetBool("SingleHanded", true);
					this.globalAnimator.SetBool("LeftHanded", false);
				}
			}
		}
	}

	// Token: 0x06000230 RID: 560 RVA: 0x00013124 File Offset: 0x00011324
	private void RightHandPickup()
	{
		RaycastHit raycastHit;
		RaycastHit raycastHit2;
		if (Physics.Raycast(this.cam.transform.position, this.cam.transform.forward, out raycastHit, this.interactionDistance, this.interactionLayer))
		{
			if (!this.SyncAccessor_hasObjectInHand && raycastHit.transform.gameObject.layer == 7)
			{
				SoundManager.Instance.PlaySound(this.pickupClip);
				this.sync___set_value_objInHand(raycastHit.transform.gameObject, true);
				this.sync___set_value_hasObjectInHand(true, true);
				if (this.SyncAccessor_objInHand.GetComponent<Weapon>().requireBothHands)
				{
					this.SetObjectInHandServer(this.SyncAccessor_objInHand, this.pickupPositionBothHand[this.SyncAccessor_objInHand.GetComponent<ItemBehaviour>().camChildIndex].position, this.pickupPositionBothHand[this.SyncAccessor_objInHand.GetComponent<ItemBehaviour>().camChildIndex].rotation, this.cam.gameObject, true);
					this.SetRightIKTarget(this.SyncAccessor_objInHand.GetComponent<ItemBehaviour>().gripRight);
					this.SetLeftIKTarget(this.SyncAccessor_objInHand.GetComponent<ItemBehaviour>().gripLeft);
				}
				else
				{
					SoundManager.Instance.PlaySound(this.pickupClip);
					this.SetObjectInHandServer(this.SyncAccessor_objInHand, this.pickupPositionRightHand[this.SyncAccessor_objInHand.GetComponent<ItemBehaviour>().camChildIndex].position, this.pickupPositionRightHand[this.SyncAccessor_objInHand.GetComponent<ItemBehaviour>().camChildIndex].rotation, this.cam.gameObject, true);
					this.SetRightIKTarget(this.SyncAccessor_objInHand.GetComponent<ItemBehaviour>().gripRight);
				}
				this.RigBuilder.Build();
				return;
			}
			if (this.SyncAccessor_hasObjectInHand)
			{
				SoundManager.Instance.PlaySound(this.pickupClip);
				this.RightHandDrop();
				this.sync___set_value_objInHand(raycastHit.transform.gameObject, true);
				this.sync___set_value_hasObjectInHand(true, true);
				if (this.SyncAccessor_objInHand.GetComponent<Weapon>().requireBothHands)
				{
					this.SetObjectInHandServer(this.SyncAccessor_objInHand, this.pickupPositionBothHand[this.SyncAccessor_objInHand.GetComponent<ItemBehaviour>().camChildIndex].position, this.pickupPositionBothHand[this.SyncAccessor_objInHand.GetComponent<ItemBehaviour>().camChildIndex].rotation, this.cam.gameObject, true);
					this.SetRightIKTarget(this.SyncAccessor_objInHand.GetComponent<ItemBehaviour>().gripRight);
					this.SetLeftIKTarget(this.SyncAccessor_objInHand.GetComponent<ItemBehaviour>().gripLeft);
				}
				else
				{
					this.SetObjectInHandServer(this.SyncAccessor_objInHand, this.pickupPositionRightHand[this.SyncAccessor_objInHand.GetComponent<ItemBehaviour>().camChildIndex].position, this.pickupPositionRightHand[this.SyncAccessor_objInHand.GetComponent<ItemBehaviour>().camChildIndex].rotation, this.cam.gameObject, true);
					this.SetRightIKTarget(this.SyncAccessor_objInHand.GetComponent<ItemBehaviour>().gripRight);
				}
				this.RigBuilder.Build();
				return;
			}
		}
		else if (Physics.SphereCast(this.cam.transform.position, this.sphereRadius, this.cam.transform.forward, out raycastHit2, this.currentHitDistance, this.interactionLayer))
		{
			if (!this.SyncAccessor_hasObjectInHand && raycastHit2.transform.gameObject.layer == 7)
			{
				SoundManager.Instance.PlaySound(this.pickupClip);
				this.sync___set_value_objInHand(raycastHit2.transform.gameObject, true);
				this.sync___set_value_hasObjectInHand(true, true);
				if (this.SyncAccessor_objInHand.GetComponent<Weapon>().requireBothHands)
				{
					this.SetObjectInHandServer(this.SyncAccessor_objInHand, this.pickupPositionBothHand[this.SyncAccessor_objInHand.GetComponent<ItemBehaviour>().camChildIndex].position, this.pickupPositionBothHand[this.SyncAccessor_objInHand.GetComponent<ItemBehaviour>().camChildIndex].rotation, this.cam.gameObject, true);
					this.SetRightIKTarget(this.SyncAccessor_objInHand.GetComponent<ItemBehaviour>().gripRight);
					this.SetLeftIKTarget(this.SyncAccessor_objInHand.GetComponent<ItemBehaviour>().gripLeft);
				}
				else
				{
					this.SetObjectInHandServer(this.SyncAccessor_objInHand, this.pickupPositionRightHand[this.SyncAccessor_objInHand.GetComponent<ItemBehaviour>().camChildIndex].position, this.pickupPositionRightHand[this.SyncAccessor_objInHand.GetComponent<ItemBehaviour>().camChildIndex].rotation, this.cam.gameObject, true);
					this.SetRightIKTarget(this.SyncAccessor_objInHand.GetComponent<ItemBehaviour>().gripRight);
				}
				this.RigBuilder.Build();
				return;
			}
			if (this.SyncAccessor_hasObjectInHand)
			{
				SoundManager.Instance.PlaySound(this.pickupClip);
				this.RightHandDrop();
				this.sync___set_value_objInHand(raycastHit2.transform.gameObject, true);
				this.sync___set_value_hasObjectInHand(true, true);
				if (this.SyncAccessor_objInHand.GetComponent<Weapon>().requireBothHands)
				{
					this.SetObjectInHandServer(this.SyncAccessor_objInHand, this.pickupPositionBothHand[this.SyncAccessor_objInHand.GetComponent<ItemBehaviour>().camChildIndex].position, this.pickupPositionBothHand[this.SyncAccessor_objInHand.GetComponent<ItemBehaviour>().camChildIndex].rotation, this.cam.gameObject, true);
					this.SetRightIKTarget(this.SyncAccessor_objInHand.GetComponent<ItemBehaviour>().gripRight);
					this.SetLeftIKTarget(this.SyncAccessor_objInHand.GetComponent<ItemBehaviour>().gripLeft);
				}
				else
				{
					this.SetObjectInHandServer(this.SyncAccessor_objInHand, this.pickupPositionRightHand[this.SyncAccessor_objInHand.GetComponent<ItemBehaviour>().camChildIndex].position, this.pickupPositionRightHand[this.SyncAccessor_objInHand.GetComponent<ItemBehaviour>().camChildIndex].rotation, this.cam.gameObject, true);
					this.SetRightIKTarget(this.SyncAccessor_objInHand.GetComponent<ItemBehaviour>().gripRight);
				}
				this.RigBuilder.Build();
				return;
			}
		}
		else if (this.SyncAccessor_hasObjectInHand)
		{
			this.RightHandDrop();
		}
	}

	// Token: 0x06000231 RID: 561 RVA: 0x000136F0 File Offset: 0x000118F0
	private void LeftHandPickup()
	{
		RaycastHit raycastHit;
		RaycastHit raycastHit2;
		if (Physics.Raycast(this.cam.transform.position, this.cam.transform.forward, out raycastHit, this.interactionDistance, this.interactionLayer))
		{
			if (!this.SyncAccessor_hasObjectInLeftHand && raycastHit.transform.gameObject.layer == 7)
			{
				SoundManager.Instance.PlaySound(this.pickupClip);
				this.SetObjectInHandServer(raycastHit.transform.gameObject, this.pickupPositionLeftHand[raycastHit.transform.GetComponent<ItemBehaviour>().camChildIndexLeftHand].position, this.pickupPositionLeftHand[raycastHit.transform.GetComponent<ItemBehaviour>().camChildIndexLeftHand].rotation, this.cam.gameObject, false);
				this.sync___set_value_objInLeftHand(raycastHit.transform.gameObject, true);
				this.sync___set_value_hasObjectInLeftHand(true, true);
				this.SetLeftIKTarget(raycastHit.transform.GetComponent<ItemBehaviour>().gripLeft);
				this.RigBuilder.Build();
				return;
			}
			if (this.SyncAccessor_hasObjectInLeftHand)
			{
				SoundManager.Instance.PlaySound(this.pickupClip);
				this.LeftHandDrop();
				this.SetObjectInHandServer(raycastHit.transform.gameObject, this.pickupPositionLeftHand[raycastHit.transform.GetComponent<ItemBehaviour>().camChildIndexLeftHand].position, this.pickupPositionLeftHand[raycastHit.transform.GetComponent<ItemBehaviour>().camChildIndexLeftHand].rotation, this.cam.gameObject, false);
				this.sync___set_value_objInLeftHand(raycastHit.transform.gameObject, true);
				this.sync___set_value_hasObjectInLeftHand(true, true);
				this.SetLeftIKTarget(raycastHit.transform.GetComponent<ItemBehaviour>().gripLeft);
				this.RigBuilder.Build();
				return;
			}
		}
		else if (Physics.SphereCast(this.cam.transform.position, this.sphereRadius, this.cam.transform.forward, out raycastHit2, this.currentHitDistance, this.interactionLayer))
		{
			if (!this.SyncAccessor_hasObjectInLeftHand && raycastHit2.transform.gameObject.layer == 7)
			{
				SoundManager.Instance.PlaySound(this.pickupClip);
				this.SetObjectInHandServer(raycastHit2.transform.gameObject, this.pickupPositionLeftHand[raycastHit2.transform.GetComponent<ItemBehaviour>().camChildIndexLeftHand].position, this.pickupPositionLeftHand[raycastHit2.transform.GetComponent<ItemBehaviour>().camChildIndexLeftHand].rotation, this.cam.gameObject, false);
				this.sync___set_value_objInLeftHand(raycastHit2.transform.gameObject, true);
				this.sync___set_value_hasObjectInLeftHand(true, true);
				this.SetLeftIKTarget(raycastHit2.transform.GetComponent<ItemBehaviour>().gripLeft);
				this.RigBuilder.Build();
				return;
			}
			if (this.SyncAccessor_hasObjectInLeftHand)
			{
				SoundManager.Instance.PlaySound(this.pickupClip);
				this.LeftHandDrop();
				this.SetObjectInHandServer(raycastHit2.transform.gameObject, this.pickupPositionLeftHand[raycastHit2.transform.GetComponent<ItemBehaviour>().camChildIndexLeftHand].position, this.pickupPositionLeftHand[raycastHit2.transform.GetComponent<ItemBehaviour>().camChildIndexLeftHand].rotation, this.cam.gameObject, false);
				this.sync___set_value_objInLeftHand(raycastHit2.transform.gameObject, true);
				this.sync___set_value_hasObjectInLeftHand(true, true);
				this.SetLeftIKTarget(raycastHit2.transform.GetComponent<ItemBehaviour>().gripLeft);
				this.RigBuilder.Build();
				return;
			}
		}
		else if (this.SyncAccessor_hasObjectInLeftHand)
		{
			this.LeftHandDrop();
		}
	}

	// Token: 0x06000232 RID: 562 RVA: 0x00013A90 File Offset: 0x00011C90
	public void RightHandDrop()
	{
		if (!this.SyncAccessor_hasObjectInHand || this.currentEnvironmentInteractable != null)
		{
			return;
		}
		SoundManager.Instance.PlaySound(this.dropClip);
		this.SyncAccessor_objInHand.GetComponent<ItemBehaviour>().StickOnGround();
		this.pauseManager.ChangeAmmoText("---", "", true);
		this.DropObjectServer(this.SyncAccessor_objInHand, true);
		this.sync___set_value_hasObjectInHand(false, true);
		if (this.SyncAccessor_objInHand.GetComponent<Weapon>().requireBothHands)
		{
			this.SetLeftIKTarget(this.LeftIdlePosition);
		}
		this.SetRightIKTarget(this.RightIdlePosition);
		this.sync___set_value_objInHand(null, true);
		this.RigBuilder.Build();
		if (this.SyncAccessor_hasObjectInLeftHand && this.currentInteractable == null)
		{
			this.SwitchWeapons();
		}
	}

	// Token: 0x06000233 RID: 563 RVA: 0x00013B5C File Offset: 0x00011D5C
	public void LeftHandDrop()
	{
		if (!this.SyncAccessor_hasObjectInLeftHand || this.currentEnvironmentInteractable != null)
		{
			return;
		}
		SoundManager.Instance.PlaySound(this.dropClip);
		this.SyncAccessor_objInLeftHand.GetComponent<ItemBehaviour>().StickOnGround();
		this.pauseManager.ChangeAmmoText("---", "", false);
		this.DropObjectServer(this.SyncAccessor_objInLeftHand, false);
		this.sync___set_value_hasObjectInLeftHand(false, true);
		this.SetLeftIKTarget(this.LeftIdlePosition);
		this.sync___set_value_objInLeftHand(null, true);
		this.RigBuilder.Build();
	}

	// Token: 0x06000234 RID: 564 RVA: 0x00013BEC File Offset: 0x00011DEC
	public void RightHandFix()
	{
		if (!this.SyncAccessor_hasObjectInHand)
		{
			return;
		}
		if (this.SyncAccessor_objInHand.layer == 7 || this.SyncAccessor_objInHand.layer == 9)
		{
			this.RightHandDrop();
			SoundManager.Instance.PlaySound(this.dropClip);
			this.pauseManager.ChangeAmmoText("---", "", true);
			this.sync___set_value_hasObjectInHand(false, true);
			if (this.SyncAccessor_objInLeftHand == null)
			{
				this.SetLeftIKTarget(this.LeftIdlePosition);
			}
			this.SetRightIKTarget(this.RightIdlePosition);
			this.sync___set_value_objInHand(null, true);
			this.RigBuilder.Build();
		}
	}

	// Token: 0x06000235 RID: 565 RVA: 0x00013C90 File Offset: 0x00011E90
	public void LeftHandFix()
	{
		if (!this.SyncAccessor_hasObjectInLeftHand)
		{
			return;
		}
		if (this.SyncAccessor_objInLeftHand.layer == 7 || this.SyncAccessor_objInLeftHand.layer == 9)
		{
			this.LeftHandDrop();
			SoundManager.Instance.PlaySound(this.dropClip);
			this.pauseManager.ChangeAmmoText("---", "", false);
			this.sync___set_value_hasObjectInLeftHand(false, true);
			this.SetLeftIKTarget(this.LeftIdlePosition);
			this.sync___set_value_objInLeftHand(null, true);
			this.RigBuilder.Build();
		}
	}

	// Token: 0x06000236 RID: 566 RVA: 0x00013D18 File Offset: 0x00011F18
	public void HandsReconstruct()
	{
		this.SetLeftIKTarget(this.LeftIdlePosition);
		this.SetRightIKTarget(this.RightIdlePosition);
		if (this.SyncAccessor_objInHand != null && this.SyncAccessor_objInHand.GetComponent<Weapon>().requireBothHands)
		{
			this.SetRightIKTarget(this.SyncAccessor_objInHand.GetComponent<ItemBehaviour>().gripRight);
			this.SetLeftIKTarget(this.SyncAccessor_objInHand.GetComponent<ItemBehaviour>().gripLeft);
		}
		else if (this.SyncAccessor_objInHand != null)
		{
			this.SetRightIKTarget(this.SyncAccessor_objInHand.GetComponent<ItemBehaviour>().gripRight);
		}
		if (this.SyncAccessor_objInLeftHand != null)
		{
			this.SetRightIKTarget(this.SyncAccessor_objInHand.GetComponent<ItemBehaviour>().gripLeft);
		}
		this.RigBuilder.Build();
	}

	// Token: 0x06000237 RID: 567 RVA: 0x00013DDF File Offset: 0x00011FDF
	private IEnumerator BuildOnDrop()
	{
		yield return new WaitForSeconds(0.1f);
		yield break;
	}

	// Token: 0x06000238 RID: 568 RVA: 0x00013DE7 File Offset: 0x00011FE7
	[ServerRpc]
	private void GiveOwnerToObj(GameObject obj)
	{
		this.RpcWriter___Server_GiveOwnerToObj_1934289915(obj);
	}

	// Token: 0x06000239 RID: 569 RVA: 0x00013DF4 File Offset: 0x00011FF4
	private void HandleInteraction(InputAction.CallbackContext ctx)
	{
		if (this.pauseManager.pause || this.pauseManager.chatting || this.pauseManager.startRound || this.interactTimer > 0f || !this.playerController.SyncAccessor_canMove)
		{
			return;
		}
		this.interactTimer = 0.1f;
		bool flag = true;
		this.PlayerGrab();
		Weapon weapon;
		if (this.currentInteractable != null && this.currentInteractable.TryGetComponent<Weapon>(out weapon))
		{
			flag = weapon.SyncAccessor_currentAmmo > 0 && !weapon.cantTakeSafeBool;
		}
		if (!flag)
		{
			return;
		}
		if (this.currentInteractable && !this.currentInteractable.canTake)
		{
			return;
		}
		if (this.currentInteractable != null && this.interactionSphere)
		{
			this.currentInteractable.OnInteract();
		}
		if (this.currentEnvironmentInteractable != null)
		{
			this.currentEnvironmentInteractable.OnInteract(base.transform);
			this.currentEnvironmentInteractable.GetComponent<NetworkObject>().RemoveOwnership();
			this.GiveOwnerToObj(this.currentEnvironmentInteractable.gameObject);
		}
		if (this.SyncAccessor_hasObjectInHand && this.SyncAccessor_objInHand.GetComponent<DualLauncher>() != null && this.SyncAccessor_objInHand.GetComponent<DualLauncher>().SyncAccessor_grenadeOpen)
		{
			return;
		}
		if (this.SyncAccessor_hasObjectInLeftHand && this.SyncAccessor_objInLeftHand.GetComponent<DualLauncher>() != null && this.SyncAccessor_objInLeftHand.GetComponent<DualLauncher>().SyncAccessor_grenadeOpen)
		{
			return;
		}
		if (this.cam != null && this.dropTimer < 0f)
		{
			this.dropTimer = 0.1f;
			if (this.currentInteractable != null && this.interactionSphere && !this.SyncAccessor_hasObjectInHand)
			{
				this.RightHandPickup();
				if (this.SyncAccessor_hasObjectInLeftHand && this.currentInteractable.GetComponent<Weapon>().requireBothHands)
				{
					this.LeftHandDrop();
					return;
				}
			}
			else if (this.currentInteractable != null && this.interactionSphere && this.SyncAccessor_hasObjectInHand)
			{
				if (this.SyncAccessor_objInHand.GetComponent<Weapon>().requireBothHands)
				{
					this.RightHandPickup();
					Weapon weapon2;
					this.currentInteractable.TryGetComponent<Weapon>(out weapon2);
					return;
				}
				Weapon weapon3;
				if (this.currentInteractable.TryGetComponent<Weapon>(out weapon3) && this.SyncAccessor_hasObjectInHand && this.SyncAccessor_hasObjectInLeftHand)
				{
					if (weapon3.requireBothHands)
					{
						this.RightHandDrop();
						this.LeftHandDrop();
						this.RightHandPickup();
						return;
					}
					if (this.SyncAccessor_objInHand.GetComponent<Weapon>().SyncAccessor_currentAmmo <= 0)
					{
						this.RightHandPickup();
						return;
					}
					this.LeftHandPickup();
					return;
				}
				else
				{
					Weapon weapon4;
					if (!this.currentInteractable.TryGetComponent<Weapon>(out weapon4))
					{
						this.LeftHandPickup();
						return;
					}
					if (weapon4.requireBothHands)
					{
						this.RightHandPickup();
						return;
					}
					if (this.SyncAccessor_objInHand.GetComponent<Weapon>().SyncAccessor_currentAmmo <= 0)
					{
						this.RightHandPickup();
						return;
					}
					if (!this.SyncAccessor_hasObjectInLeftHand)
					{
						this.LeftHandPickup();
						return;
					}
					if (this.SyncAccessor_objInLeftHand.GetComponent<Weapon>().SyncAccessor_currentAmmo <= 0)
					{
						this.LeftHandPickup();
						return;
					}
				}
			}
			else if (this.currentInteractable == null && this.SyncAccessor_hasObjectInLeftHand)
			{
				if (!this.SyncAccessor_hasObjectInHand)
				{
					this.LeftHandPickup();
					return;
				}
				if (this.SyncAccessor_objInLeftHand.GetComponent<Weapon>().SyncAccessor_currentAmmo <= 0)
				{
					this.LeftHandPickup();
					return;
				}
				if (this.SyncAccessor_objInHand.GetComponent<Weapon>().SyncAccessor_currentAmmo <= 0)
				{
					this.RightHandPickup();
					return;
				}
				this.LeftHandPickup();
				return;
			}
			else if (this.currentInteractable == null && this.SyncAccessor_hasObjectInHand)
			{
				this.RightHandPickup();
			}
		}
	}

	// Token: 0x0600023A RID: 570 RVA: 0x0001416E File Offset: 0x0001236E
	private void HandleDrop(InputAction.CallbackContext ctx)
	{
		this.SwitchWeapons();
	}

	// Token: 0x0600023B RID: 571 RVA: 0x00014178 File Offset: 0x00012378
	private void SwitchWeapons()
	{
		if (this.pauseManager.pause || this.pauseManager.chatting)
		{
			return;
		}
		if (this.cam != null)
		{
			if (this.dropTimer > 0f)
			{
				return;
			}
			if (this.SyncAccessor_hasObjectInHand && this.SyncAccessor_hasObjectInLeftHand)
			{
				this.dropTimer = 0.1f;
				if (this.SyncAccessor_objInHand.GetComponent<Weapon>().requireBothHands)
				{
					return;
				}
				SoundManager.Instance.PlaySound(this.switchWeaponsClip);
				GameObject gameObject = this.SyncAccessor_objInHand;
				GameObject gameObject2 = this.SyncAccessor_objInLeftHand;
				this.DropObjectServer(this.SyncAccessor_objInHand, true);
				this.DropObjectServer(this.SyncAccessor_objInLeftHand, false);
				this.SetObjectInHandServer(gameObject, this.pickupPositionLeftHand[gameObject.GetComponent<ItemBehaviour>().camChildIndexLeftHand].position, this.pickupPositionLeftHand[gameObject.GetComponent<ItemBehaviour>().camChildIndexLeftHand].rotation, this.cam.gameObject, false);
				this.SetObjectInHandServer(gameObject2, this.pickupPositionRightHand[gameObject2.GetComponent<ItemBehaviour>().camChildIndex].position, this.pickupPositionRightHand[gameObject2.GetComponent<ItemBehaviour>().camChildIndex].rotation, this.cam.gameObject, true);
				this.SetRightIKTarget(gameObject2.GetComponent<ItemBehaviour>().gripRight);
				this.SetLeftIKTarget(gameObject.GetComponent<ItemBehaviour>().gripLeft);
				this.sync___set_value_objInHand(gameObject2, true);
				this.sync___set_value_objInLeftHand(gameObject, true);
				this.RigBuilder.Build();
				return;
			}
			else
			{
				if (this.SyncAccessor_hasObjectInLeftHand)
				{
					this.dropTimer = 0.1f;
					SoundManager.Instance.PlaySound(this.switchWeaponsClip);
					this.pauseManager.ChangeAmmoText("---", "", false);
					GameObject gameObject3 = this.SyncAccessor_objInLeftHand;
					this.DropObjectServer(this.SyncAccessor_objInLeftHand, false);
					this.SetObjectInHandServer(gameObject3, this.pickupPositionRightHand[gameObject3.GetComponent<ItemBehaviour>().camChildIndex].position, this.pickupPositionRightHand[gameObject3.GetComponent<ItemBehaviour>().camChildIndex].rotation, this.cam.gameObject, true);
					this.SetRightIKTarget(gameObject3.GetComponent<ItemBehaviour>().gripRight);
					this.SetLeftIKTarget(this.LeftIdlePosition);
					this.sync___set_value_objInHand(gameObject3, true);
					this.sync___set_value_objInLeftHand(null, true);
					this.sync___set_value_hasObjectInLeftHand(false, true);
					this.sync___set_value_hasObjectInHand(true, true);
					this.RigBuilder.Build();
					return;
				}
				if (this.SyncAccessor_hasObjectInHand)
				{
					this.dropTimer = 0.1f;
					if (this.weaponInHand.requireBothHands)
					{
						return;
					}
					SoundManager.Instance.PlaySound(this.switchWeaponsClip);
					this.pauseManager.ChangeAmmoText("---", "", true);
					GameObject gameObject4 = this.SyncAccessor_objInHand;
					this.DropObjectServer(this.SyncAccessor_objInHand, true);
					this.SetObjectInHandServer(gameObject4, this.pickupPositionLeftHand[gameObject4.GetComponent<ItemBehaviour>().camChildIndexLeftHand].position, this.pickupPositionLeftHand[gameObject4.GetComponent<ItemBehaviour>().camChildIndexLeftHand].rotation, this.cam.gameObject, false);
					this.SetRightIKTarget(this.RightIdlePosition);
					this.SetLeftIKTarget(gameObject4.GetComponent<ItemBehaviour>().gripLeft);
					this.sync___set_value_objInHand(null, true);
					this.sync___set_value_objInLeftHand(gameObject4, true);
					this.sync___set_value_hasObjectInLeftHand(true, true);
					this.sync___set_value_hasObjectInHand(false, true);
					this.RigBuilder.Build();
				}
			}
		}
	}

	// Token: 0x0600023C RID: 572 RVA: 0x000144AF File Offset: 0x000126AF
	[ServerRpc(RunLocally = true)]
	private void SetObjectInHandServer(GameObject obj, Vector3 position, Quaternion rotation, GameObject player, bool rightHand)
	{
		this.RpcWriter___Server_SetObjectInHandServer_46969756(obj, position, rotation, player, rightHand);
		this.RpcLogic___SetObjectInHandServer_46969756(obj, position, rotation, player, rightHand);
	}

	// Token: 0x0600023D RID: 573 RVA: 0x000144E8 File Offset: 0x000126E8
	[ObserversRpc(RunLocally = true, ExcludeOwner = true)]
	private void SetObjectInHandObserver(GameObject obj, Vector3 position, Quaternion rotation, GameObject player, bool rightHand)
	{
		this.RpcWriter___Observers_SetObjectInHandObserver_46969756(obj, position, rotation, player, rightHand);
		this.RpcLogic___SetObjectInHandObserver_46969756(obj, position, rotation, player, rightHand);
	}

	// Token: 0x0600023E RID: 574 RVA: 0x00014529 File Offset: 0x00012729
	[ServerRpc(RunLocally = true)]
	private void DropObjectServer(GameObject obj, bool rightHand)
	{
		this.RpcWriter___Server_DropObjectServer_2127535046(obj, rightHand);
		this.RpcLogic___DropObjectServer_2127535046(obj, rightHand);
	}

	// Token: 0x0600023F RID: 575 RVA: 0x00014548 File Offset: 0x00012748
	[ObserversRpc(RunLocally = true, ExcludeOwner = true)]
	private void DropObjectObserver(GameObject obj, bool rightHand)
	{
		this.RpcWriter___Observers_DropObjectObserver_2127535046(obj, rightHand);
		this.RpcLogic___DropObjectObserver_2127535046(obj, rightHand);
	}

	// Token: 0x06000240 RID: 576 RVA: 0x00014574 File Offset: 0x00012774
	private void HandleInteractEnvironment()
	{
		RaycastHit raycastHit;
		if (Physics.Raycast(this.cam.transform.position, this.cam.transform.forward, out raycastHit, this.interactionDistance, this.environmentInteractionLayer) && this.currentInteractable == null)
		{
			raycastHit.collider.TryGetComponent<InteractEnvironment>(out this.currentEnvironmentInteractable);
			if (!this.currentEnvironmentObject)
			{
				this.currentEnvironmentInteractable.OnFocus();
			}
			this.currentEnvironmentObject = raycastHit.collider.gameObject;
		}
		else if (this.currentEnvironmentInteractable)
		{
			this.currentEnvironmentInteractable.OnLoseFocus();
			this.currentEnvironmentInteractable = null;
			this.currentEnvironmentObject = null;
		}
		if (this.currentEnvironmentInteractable != null && this.SyncAccessor_objInHand == null && Input.GetMouseButtonDown(0))
		{
			this.currentEnvironmentInteractable.OnInteract(base.transform);
			this.currentEnvironmentInteractable.GetComponent<NetworkObject>().RemoveOwnership();
			this.GiveOwnerToObj(this.currentEnvironmentInteractable.gameObject);
		}
		if (this.currentEnvironmentInteractable == null)
		{
			PauseManager.Instance.interactPopup.gameObject.SetActive(false);
		}
	}

	// Token: 0x06000241 RID: 577 RVA: 0x000146A8 File Offset: 0x000128A8
	private void HandleInteractionCheck()
	{
		RaycastHit raycastHit;
		if (Physics.Raycast(this.cam.transform.position, this.cam.transform.forward, out raycastHit, this.maxSphereDistance, this.landLayer))
		{
			this.currentHitObject = raycastHit.transform.gameObject;
			this.currentHitDistance = raycastHit.distance + this.sphereRadius;
		}
		else
		{
			this.currentHitDistance = this.maxSphereDistance;
			this.currentHitObject = null;
		}
		RaycastHit raycastHit2;
		if (Physics.Raycast(this.cam.transform.position, this.cam.transform.forward, out raycastHit2, this.maxInteractionDistance, this.landLayer))
		{
			if (raycastHit2.transform.gameObject.layer == 7)
			{
				this.currentHitObject = raycastHit2.transform.gameObject;
			}
			this.interactionDistance = raycastHit2.distance;
		}
		else
		{
			this.interactionDistance = this.maxInteractionDistance;
		}
		RaycastHit raycastHit3;
		RaycastHit raycastHit4;
		if (Physics.Raycast(this.cam.transform.position, this.cam.transform.forward, out raycastHit3, this.interactionDistance, this.interactionLayer))
		{
			this.interactionSphere = true;
			Interactable interactable = (this.currentInteractable ? this.currentInteractable : null);
			raycastHit3.collider.TryGetComponent<Interactable>(out this.currentInteractable);
			if (interactable && interactable != this.currentInteractable)
			{
				interactable.OnLoseFocus();
			}
			this.currentObject = raycastHit3.collider.gameObject;
			if (this.currentInteractable)
			{
				this.currentInteractable.OnFocus();
			}
		}
		else if (Physics.SphereCast(this.cam.transform.position, this.sphereRadius, this.cam.transform.forward, out raycastHit4, this.currentHitDistance, this.interactionLayer))
		{
			this.interactionSphere = true;
			Interactable interactable2 = (this.currentInteractable ? this.currentInteractable : null);
			raycastHit4.collider.TryGetComponent<Interactable>(out this.currentInteractable);
			if (interactable2 && interactable2 != this.currentInteractable)
			{
				interactable2.OnLoseFocus();
			}
			this.currentObject = raycastHit4.collider.gameObject;
			if (this.currentInteractable)
			{
				this.currentInteractable.OnFocus();
			}
		}
		else if (this.currentInteractable)
		{
			this.interactionSphere = false;
			this.currentInteractable.OnLoseFocus();
			this.currentInteractable = null;
			this.currentObject = null;
		}
		if (this.currentInteractable == null)
		{
			PauseManager.Instance.grabPopup.gameObject.SetActive(false);
			this.interactionSphere = false;
		}
	}

	// Token: 0x06000242 RID: 578 RVA: 0x00014972 File Offset: 0x00012B72
	private void OnDestroy()
	{
		PauseManager.Instance.grabPopup.gameObject.SetActive(false);
		PauseManager.Instance.interactPopup.gameObject.SetActive(false);
	}

	// Token: 0x06000243 RID: 579 RVA: 0x000149A0 File Offset: 0x00012BA0
	private void OnDrawGizmos()
	{
		if (this.cam == null)
		{
			return;
		}
		Gizmos.color = Color.red;
		Debug.DrawLine(this.cam.transform.position, this.cam.transform.position + this.cam.transform.forward * this.currentHitDistance);
		Gizmos.DrawWireSphere(this.cam.transform.position + this.cam.transform.forward * this.currentHitDistance, this.sphereRadius);
		Debug.DrawRay(this.cam.transform.position, this.cam.transform.forward * this.interactionDistance, Color.green);
	}

	// Token: 0x06000244 RID: 580 RVA: 0x00014A7C File Offset: 0x00012C7C
	private void PlayerGrab()
	{
		RaycastHit raycastHit;
		if (this.ragdoll)
		{
			this.ragdoll.SetParent(null);
			foreach (Rigidbody rigidbody in this.ragdoll.GetComponentsInChildren<Rigidbody>())
			{
				rigidbody.isKinematic = false;
				rigidbody.AddExplosionForce(150f, this.cam.transform.position - this.cam.transform.forward, 0f, 1f, ForceMode.Impulse);
			}
			this.hips = null;
			this.ragdoll = null;
		}
		else if (Physics.Raycast(this.cam.transform.position, this.cam.transform.forward, out raycastHit, this.interactionDistance, this.ragdollInteractionLayer) && raycastHit.transform.gameObject.layer == LayerMask.NameToLayer("Ragdoll"))
		{
			this.RightHandDrop();
			this.LeftHandDrop();
			this.ragdoll = raycastHit.transform.root;
			this.ragdoll.SetParent(this.cam.transform);
			foreach (Rigidbody rigidbody2 in this.ragdoll.GetComponentsInChildren<Rigidbody>())
			{
				if (rigidbody2.gameObject.name == "Hips")
				{
					this.hips = rigidbody2.transform;
					rigidbody2.isKinematic = true;
				}
			}
		}
		if (this.enemyBody)
		{
			this.SetEnemyParent(false, this.camPoint, this.enemyBody);
			this.BumpPlayerServer(this.cam.transform.forward, 70f, this.enemyBody);
			return;
		}
		if (!this.playerController.SyncAccessor_canMove)
		{
			return;
		}
		RaycastHit raycastHit2;
		if (Physics.Raycast(this.cam.transform.position, this.cam.transform.forward, out raycastHit2, this.interactionDistance, this.bodyInteractionLayer) && raycastHit2.transform.gameObject.layer == LayerMask.NameToLayer("Body"))
		{
			this.enemyBody = raycastHit2.transform.GetComponentInParent<PlayerHealth>();
			if (!this.enemyBody.controller.SyncAccessor_canMove)
			{
				this.SetEnemyParent(true, base.transform, this.enemyBody);
				return;
			}
			this.enemyBody = null;
		}
	}

	// Token: 0x06000245 RID: 581 RVA: 0x00014CD0 File Offset: 0x00012ED0
	private Transform FindRecursive(string name, Transform root)
	{
		Transform[] componentsInChildren = root.GetComponentsInChildren<Transform>(true);
		Transform transform = null;
		foreach (Transform transform2 in componentsInChildren)
		{
			if (transform2.gameObject.name == name)
			{
				transform = transform2;
				break;
			}
		}
		return transform;
	}

	// Token: 0x06000246 RID: 582 RVA: 0x00014D14 File Offset: 0x00012F14
	private void HandleAboubiGrab()
	{
		if (this.hips)
		{
			this.hips.position = this.cam.transform.position + this.cam.transform.forward * 1.5f;
		}
	}

	// Token: 0x06000247 RID: 583 RVA: 0x00014D68 File Offset: 0x00012F68
	[ServerRpc]
	private void BumpPlayerServer(Vector3 direction, float force, PlayerHealth ph)
	{
		this.RpcWriter___Server_BumpPlayerServer_1076951378(direction, force, ph);
	}

	// Token: 0x06000248 RID: 584 RVA: 0x00014D7C File Offset: 0x00012F7C
	[ServerRpc]
	private void SetEnemyParent(bool set, Transform t, PlayerHealth ph)
	{
		this.RpcWriter___Server_SetEnemyParent_3492863138(set, t, ph);
	}

	// Token: 0x06000249 RID: 585 RVA: 0x00014D90 File Offset: 0x00012F90
	[ObserversRpc]
	private void SetEnemyParentObservers(bool set)
	{
		this.RpcWriter___Observers_SetEnemyParentObservers_1140765316(set);
	}

	// Token: 0x0600024B RID: 587 RVA: 0x00014DC8 File Offset: 0x00012FC8
	public virtual void NetworkInitialize___Early()
	{
		if (this.NetworkInitializeEarly_PlayerPickup_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeEarly_PlayerPickup_Assembly-CSharp.dll = true;
		this.syncVar___objInLeftHand = new SyncVar<GameObject>(this, 3U, WritePermission.ServerOnly, ReadPermission.Observers, -1f, Channel.Reliable, this.objInLeftHand);
		this.syncVar___hasObjectInLeftHand = new SyncVar<bool>(this, 2U, WritePermission.ServerOnly, ReadPermission.Observers, -1f, Channel.Reliable, this.hasObjectInLeftHand);
		this.syncVar___objInHand = new SyncVar<GameObject>(this, 1U, WritePermission.ServerOnly, ReadPermission.Observers, -1f, Channel.Reliable, this.objInHand);
		this.syncVar___hasObjectInHand = new SyncVar<bool>(this, 0U, WritePermission.ServerOnly, ReadPermission.Observers, -1f, Channel.Reliable, this.hasObjectInHand);
		base.RegisterServerRpc(0U, new ServerRpcDelegate(this.RpcReader___Server_GiveOwnerToObj_1934289915));
		base.RegisterServerRpc(1U, new ServerRpcDelegate(this.RpcReader___Server_SetObjectInHandServer_46969756));
		base.RegisterObserversRpc(2U, new ClientRpcDelegate(this.RpcReader___Observers_SetObjectInHandObserver_46969756));
		base.RegisterServerRpc(3U, new ServerRpcDelegate(this.RpcReader___Server_DropObjectServer_2127535046));
		base.RegisterObserversRpc(4U, new ClientRpcDelegate(this.RpcReader___Observers_DropObjectObserver_2127535046));
		base.RegisterServerRpc(5U, new ServerRpcDelegate(this.RpcReader___Server_BumpPlayerServer_1076951378));
		base.RegisterServerRpc(6U, new ServerRpcDelegate(this.RpcReader___Server_SetEnemyParent_3492863138));
		base.RegisterObserversRpc(7U, new ClientRpcDelegate(this.RpcReader___Observers_SetEnemyParentObservers_1140765316));
		base.RegisterSyncVarRead(new SyncVarReadDelegate(this.ReadSyncVar___PlayerPickup));
	}

	// Token: 0x0600024C RID: 588 RVA: 0x00014F5C File Offset: 0x0001315C
	public virtual void NetworkInitialize__Late()
	{
		if (this.NetworkInitializeLate_PlayerPickup_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeLate_PlayerPickup_Assembly-CSharp.dll = true;
		this.syncVar___objInLeftHand.SetRegistered();
		this.syncVar___hasObjectInLeftHand.SetRegistered();
		this.syncVar___objInHand.SetRegistered();
		this.syncVar___hasObjectInHand.SetRegistered();
	}

	// Token: 0x0600024D RID: 589 RVA: 0x00014F9B File Offset: 0x0001319B
	public override void NetworkInitializeIfDisabled()
	{
		this.NetworkInitialize___Early();
		this.NetworkInitialize__Late();
	}

	// Token: 0x0600024E RID: 590 RVA: 0x00014FAC File Offset: 0x000131AC
	private void RpcWriter___Server_GiveOwnerToObj_1934289915(GameObject obj)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteGameObject(obj);
		base.SendServerRpc(0U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x0600024F RID: 591 RVA: 0x000150AD File Offset: 0x000132AD
	private void RpcLogic___GiveOwnerToObj_1934289915(GameObject obj)
	{
		obj.transform.GetComponent<NetworkObject>().GiveOwnership(base.Owner);
	}

	// Token: 0x06000250 RID: 592 RVA: 0x000150C8 File Offset: 0x000132C8
	private void RpcReader___Server_GiveOwnerToObj_1934289915(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		GameObject gameObject = PooledReader0.ReadGameObject();
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		this.RpcLogic___GiveOwnerToObj_1934289915(gameObject);
	}

	// Token: 0x06000251 RID: 593 RVA: 0x0001510C File Offset: 0x0001330C
	private void RpcWriter___Server_SetObjectInHandServer_46969756(GameObject obj, Vector3 position, Quaternion rotation, GameObject player, bool rightHand)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteGameObject(obj);
		writer.WriteVector3(position);
		writer.WriteQuaternion(rotation, AutoPackType.Packed);
		writer.WriteGameObject(player);
		writer.WriteBoolean(rightHand);
		base.SendServerRpc(1U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000252 RID: 594 RVA: 0x00015246 File Offset: 0x00013446
	private void RpcLogic___SetObjectInHandServer_46969756(GameObject obj, Vector3 position, Quaternion rotation, GameObject player, bool rightHand)
	{
		this.SetObjectInHandObserver(obj, position, rotation, player, rightHand);
		obj.GetComponent<NetworkObject>().GiveOwnership(base.Owner);
	}

	// Token: 0x06000253 RID: 595 RVA: 0x00015268 File Offset: 0x00013468
	private void RpcReader___Server_SetObjectInHandServer_46969756(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		GameObject gameObject = PooledReader0.ReadGameObject();
		Vector3 vector = PooledReader0.ReadVector3();
		Quaternion quaternion = PooledReader0.ReadQuaternion(AutoPackType.Packed);
		GameObject gameObject2 = PooledReader0.ReadGameObject();
		bool flag = PooledReader0.ReadBoolean();
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___SetObjectInHandServer_46969756(gameObject, vector, quaternion, gameObject2, flag);
	}

	// Token: 0x06000254 RID: 596 RVA: 0x00015300 File Offset: 0x00013500
	private void RpcWriter___Observers_SetObjectInHandObserver_46969756(GameObject obj, Vector3 position, Quaternion rotation, GameObject player, bool rightHand)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteGameObject(obj);
		writer.WriteVector3(position);
		writer.WriteQuaternion(rotation, AutoPackType.Packed);
		writer.WriteGameObject(player);
		writer.WriteBoolean(rightHand);
		base.SendObserversRpc(2U, writer, channel, DataOrderType.Default, false, false, true);
		writer.Store();
	}

	// Token: 0x06000255 RID: 597 RVA: 0x000153F0 File Offset: 0x000135F0
	private void RpcLogic___SetObjectInHandObserver_46969756(GameObject obj, Vector3 position, Quaternion rotation, GameObject player, bool rightHand)
	{
		if (!base.IsOwner)
		{
			obj.transform.position = this.pickupPositionOnline[rightHand ? 0 : 1].position;
			obj.transform.rotation = this.pickupPositionOnline[rightHand ? 0 : 1].rotation;
		}
		if (base.IsOwner)
		{
			obj.transform.localScale = new Vector3(2f, 2f, 2f);
		}
		if (!base.IsOwner)
		{
			obj.transform.localScale = new Vector3(1f, 1f, 1f);
			obj.GetComponent<ItemBehaviour>().KillAnimation();
		}
		if (base.IsOwner)
		{
			PauseManager.Instance.MoveAmmoDisplay(true, rightHand);
			if (rightHand)
			{
				this.weaponInHand = obj.GetComponent<Weapon>();
				this.behaviourInHand = obj.GetComponent<ItemBehaviour>();
			}
			else if (!rightHand)
			{
				this.weaponInLeftHand = obj.GetComponent<Weapon>();
				this.behaviourInLeftHand = obj.GetComponent<ItemBehaviour>();
			}
		}
		obj.transform.parent = (obj.GetComponent<Weapon>().requireBothHands ? this.pickupPositionBothHand[obj.GetComponent<ItemBehaviour>().camChildIndex].transform : (rightHand ? this.pickupPositionRightHand[obj.GetComponent<ItemBehaviour>().camChildIndex].transform : this.pickupPositionLeftHand[obj.GetComponent<ItemBehaviour>().camChildIndexLeftHand].transform));
		obj.GetComponent<ItemBehaviour>().playerPickup = this;
		if (obj.GetComponent<ItemBehaviour>().rightHandAnim != "")
		{
			this.animator.SetBool("TwoHanded", false);
			this.animator.SetBool("DoubleHanded", false);
			this.animator.SetBool("RightHanded", false);
			this.animator.SetBool(rightHand ? obj.GetComponent<ItemBehaviour>().rightHandAnim : obj.GetComponent<ItemBehaviour>().leftHandAnim, true);
		}
		obj.GetComponent<ItemBehaviour>().playerController = base.GetComponent<FirstPersonController>();
		obj.GetComponent<ItemBehaviour>().rootObject = base.gameObject;
		obj.GetComponent<ItemBehaviour>().OnGrab(base.IsOwner, rightHand);
		obj.GetComponent<ItemBehaviour>().lastPlayerHolder = base.gameObject;
		obj.GetComponent<ItemBehaviour>().KillTweens();
		obj.GetComponent<Weapon>().camAnimScript = this.camAnimScript;
		obj.GetComponent<Weapon>().heldOnce = true;
		obj.GetComponent<Weapon>().playerValues = this.playerValues;
		obj.GetComponent<ItemBehaviour>().isTaken = true;
		obj.GetComponent<ItemBehaviour>().cam = this.cam;
		obj.GetComponent<Weapon>().inRightHand = rightHand;
		obj.GetComponent<Weapon>().inLeftHand = !rightHand;
		obj.GetComponent<Weapon>().fpArms = this.fpArms;
		obj.GetComponent<ItemBehaviour>().SetLayer();
		obj.layer = 8;
		if (obj.GetComponent<ItemBehaviour>().aimWeapon && base.IsOwner)
		{
			this.playerController.zoomFOV = obj.GetComponent<ItemBehaviour>().aimFOV + ((!Crosshair.Instance.canScopeAim) ? (this.playerController.defaultFOV - 68f) : 0f);
		}
	}

	// Token: 0x06000256 RID: 598 RVA: 0x000156FC File Offset: 0x000138FC
	private void RpcReader___Observers_SetObjectInHandObserver_46969756(PooledReader PooledReader0, Channel channel)
	{
		GameObject gameObject = PooledReader0.ReadGameObject();
		Vector3 vector = PooledReader0.ReadVector3();
		Quaternion quaternion = PooledReader0.ReadQuaternion(AutoPackType.Packed);
		GameObject gameObject2 = PooledReader0.ReadGameObject();
		bool flag = PooledReader0.ReadBoolean();
		if (!base.IsClient)
		{
			return;
		}
		if (base.IsHost)
		{
			return;
		}
		this.RpcLogic___SetObjectInHandObserver_46969756(gameObject, vector, quaternion, gameObject2, flag);
	}

	// Token: 0x06000257 RID: 599 RVA: 0x00015780 File Offset: 0x00013980
	private void RpcWriter___Server_DropObjectServer_2127535046(GameObject obj, bool rightHand)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteGameObject(obj);
		writer.WriteBoolean(rightHand);
		base.SendServerRpc(3U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000258 RID: 600 RVA: 0x0001588E File Offset: 0x00013A8E
	private void RpcLogic___DropObjectServer_2127535046(GameObject obj, bool rightHand)
	{
		this.DropObjectObserver(obj, rightHand);
	}

	// Token: 0x06000259 RID: 601 RVA: 0x00015898 File Offset: 0x00013A98
	private void RpcReader___Server_DropObjectServer_2127535046(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		GameObject gameObject = PooledReader0.ReadGameObject();
		bool flag = PooledReader0.ReadBoolean();
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___DropObjectServer_2127535046(gameObject, flag);
	}

	// Token: 0x0600025A RID: 602 RVA: 0x000158F8 File Offset: 0x00013AF8
	private void RpcWriter___Observers_DropObjectObserver_2127535046(GameObject obj, bool rightHand)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteGameObject(obj);
		writer.WriteBoolean(rightHand);
		base.SendObserversRpc(4U, writer, channel, DataOrderType.Default, false, false, true);
		writer.Store();
	}

	// Token: 0x0600025B RID: 603 RVA: 0x000159BC File Offset: 0x00013BBC
	private void RpcLogic___DropObjectObserver_2127535046(GameObject obj, bool rightHand)
	{
		if (!base.IsOwner)
		{
			obj.GetComponent<ItemBehaviour>().StickOnGroundObservers();
		}
		obj.transform.DOKill(false);
		if (base.IsOwner)
		{
			PauseManager.Instance.MoveAmmoDisplay(false, rightHand);
			this.playerController.isScopeAiming = false;
			if (rightHand)
			{
				this.weaponInHand = null;
				this.behaviourInHand = null;
			}
			else if (!rightHand)
			{
				this.weaponInLeftHand = null;
				this.behaviourInLeftHand = null;
			}
		}
		if (obj.GetComponent<ItemBehaviour>().rightHandAnim != "")
		{
			this.animator.SetBool(rightHand ? obj.GetComponent<ItemBehaviour>().rightHandAnim : obj.GetComponent<ItemBehaviour>().leftHandAnim, false);
		}
		this.camAnimScript.rotateBack = true;
		obj.GetComponent<ItemBehaviour>().playerPickup = null;
		obj.GetComponent<ItemBehaviour>().playerController = null;
		obj.GetComponent<ItemBehaviour>().rootObject = null;
		obj.GetComponent<ItemBehaviour>().OnDrop(this.cam);
		obj.GetComponent<Weapon>().camAnimScript = null;
		obj.GetComponent<ItemBehaviour>().cam = null;
		obj.transform.parent = null;
		obj.transform.localScale = new Vector3(2f, 2f, 2f);
		obj.GetComponent<ItemBehaviour>().UnsetLayer();
		obj.layer = 7;
		this.RigBuilder.Build();
	}

	// Token: 0x0600025C RID: 604 RVA: 0x00015B10 File Offset: 0x00013D10
	private void RpcReader___Observers_DropObjectObserver_2127535046(PooledReader PooledReader0, Channel channel)
	{
		GameObject gameObject = PooledReader0.ReadGameObject();
		bool flag = PooledReader0.ReadBoolean();
		if (!base.IsClient)
		{
			return;
		}
		if (base.IsHost)
		{
			return;
		}
		this.RpcLogic___DropObjectObserver_2127535046(gameObject, flag);
	}

	// Token: 0x0600025D RID: 605 RVA: 0x00015B5C File Offset: 0x00013D5C
	private void RpcWriter___Server_BumpPlayerServer_1076951378(Vector3 direction, float force, PlayerHealth ph)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteVector3(direction);
		writer.WriteSingle(force, AutoPackType.Unpacked);
		writer.Write___PlayerHealthFishNet.Serializing.Generated(ph);
		base.SendServerRpc(5U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x0600025E RID: 606 RVA: 0x00015C7C File Offset: 0x00013E7C
	private void RpcLogic___BumpPlayerServer_1076951378(Vector3 direction, float force, PlayerHealth ph)
	{
		ph.bounceDirection = direction;
		ph.bounceForce = force;
		ph.shouldBounce = true;
	}

	// Token: 0x0600025F RID: 607 RVA: 0x00015C94 File Offset: 0x00013E94
	private void RpcReader___Server_BumpPlayerServer_1076951378(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		Vector3 vector = PooledReader0.ReadVector3();
		float num = PooledReader0.ReadSingle(AutoPackType.Unpacked);
		PlayerHealth playerHealth = global::FishNet.Serializing.Generated.GeneratedReaders___Internal.Read___PlayerHealthFishNet.Serializing.Generateds(PooledReader0);
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		this.RpcLogic___BumpPlayerServer_1076951378(vector, num, playerHealth);
	}

	// Token: 0x06000260 RID: 608 RVA: 0x00015D00 File Offset: 0x00013F00
	private void RpcWriter___Server_SetEnemyParent_3492863138(bool set, Transform t, PlayerHealth ph)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteBoolean(set);
		writer.WriteTransform(t);
		writer.Write___PlayerHealthFishNet.Serializing.Generated(ph);
		base.SendServerRpc(6U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000261 RID: 609 RVA: 0x00015E1B File Offset: 0x0001401B
	private void RpcLogic___SetEnemyParent_3492863138(bool set, Transform t, PlayerHealth ph)
	{
		if (!set)
		{
			ph.GetComponent<NetworkObject>().UnsetParent();
			ph.controller.sync___set_value_canMove(true, true);
		}
		else
		{
			ph.GetComponent<NetworkObject>().SetParent(t.GetComponent<NetworkBehaviour>());
		}
		this.SetEnemyParentObservers(set);
	}

	// Token: 0x06000262 RID: 610 RVA: 0x00015E54 File Offset: 0x00014054
	private void RpcReader___Server_SetEnemyParent_3492863138(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		bool flag = PooledReader0.ReadBoolean();
		Transform transform = PooledReader0.ReadTransform();
		PlayerHealth playerHealth = global::FishNet.Serializing.Generated.GeneratedReaders___Internal.Read___PlayerHealthFishNet.Serializing.Generateds(PooledReader0);
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		this.RpcLogic___SetEnemyParent_3492863138(flag, transform, playerHealth);
	}

	// Token: 0x06000263 RID: 611 RVA: 0x00015EB8 File Offset: 0x000140B8
	private void RpcWriter___Observers_SetEnemyParentObservers_1140765316(bool set)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteBoolean(set);
		base.SendObserversRpc(7U, writer, channel, DataOrderType.Default, false, false, false);
		writer.Store();
	}

	// Token: 0x06000264 RID: 612 RVA: 0x00015F6E File Offset: 0x0001416E
	private void RpcLogic___SetEnemyParentObservers_1140765316(bool set)
	{
		if (!set)
		{
			this.enemyBody = null;
		}
	}

	// Token: 0x06000265 RID: 613 RVA: 0x00015F7C File Offset: 0x0001417C
	private void RpcReader___Observers_SetEnemyParentObservers_1140765316(PooledReader PooledReader0, Channel channel)
	{
		bool flag = PooledReader0.ReadBoolean();
		if (!base.IsClient)
		{
			return;
		}
		this.RpcLogic___SetEnemyParentObservers_1140765316(flag);
	}

	// Token: 0x1700004B RID: 75
	// (get) Token: 0x06000266 RID: 614 RVA: 0x00015FAD File Offset: 0x000141AD
	// (set) Token: 0x06000267 RID: 615 RVA: 0x00015FB5 File Offset: 0x000141B5
	public bool SyncAccessor_hasObjectInHand
	{
		get
		{
			return this.hasObjectInHand;
		}
		set
		{
			if (value || !base.IsServer)
			{
				this.hasObjectInHand = value;
			}
			this.syncVar___hasObjectInHand.SetValue(value, value);
		}
	}

	// Token: 0x06000268 RID: 616 RVA: 0x00015FEC File Offset: 0x000141EC
	public virtual bool ReadSyncVar___PlayerPickup(PooledReader PooledReader0, uint UInt321, bool Boolean2)
	{
		if (UInt321 == 3U)
		{
			if (PooledReader0 == null)
			{
				this.sync___set_value_objInLeftHand(this.syncVar___objInLeftHand.GetValue(true), true);
				return true;
			}
			GameObject gameObject = PooledReader0.ReadGameObject();
			this.sync___set_value_objInLeftHand(gameObject, Boolean2);
			return true;
		}
		else if (UInt321 == 2U)
		{
			if (PooledReader0 == null)
			{
				this.sync___set_value_hasObjectInLeftHand(this.syncVar___hasObjectInLeftHand.GetValue(true), true);
				return true;
			}
			bool flag = PooledReader0.ReadBoolean();
			this.sync___set_value_hasObjectInLeftHand(flag, Boolean2);
			return true;
		}
		else if (UInt321 == 1U)
		{
			if (PooledReader0 == null)
			{
				this.sync___set_value_objInHand(this.syncVar___objInHand.GetValue(true), true);
				return true;
			}
			GameObject gameObject2 = PooledReader0.ReadGameObject();
			this.sync___set_value_objInHand(gameObject2, Boolean2);
			return true;
		}
		else
		{
			if (UInt321 != 0U)
			{
				return false;
			}
			if (PooledReader0 == null)
			{
				this.sync___set_value_hasObjectInHand(this.syncVar___hasObjectInHand.GetValue(true), true);
				return true;
			}
			bool flag2 = PooledReader0.ReadBoolean();
			this.sync___set_value_hasObjectInHand(flag2, Boolean2);
			return true;
		}
	}

	// Token: 0x1700004C RID: 76
	// (get) Token: 0x06000269 RID: 617 RVA: 0x0001610A File Offset: 0x0001430A
	// (set) Token: 0x0600026A RID: 618 RVA: 0x00016112 File Offset: 0x00014312
	public GameObject SyncAccessor_objInHand
	{
		get
		{
			return this.objInHand;
		}
		set
		{
			if (value || !base.IsServer)
			{
				this.objInHand = value;
			}
			this.syncVar___objInHand.SetValue(value, value);
		}
	}

	// Token: 0x1700004D RID: 77
	// (get) Token: 0x0600026B RID: 619 RVA: 0x00016147 File Offset: 0x00014347
	// (set) Token: 0x0600026C RID: 620 RVA: 0x0001614F File Offset: 0x0001434F
	public bool SyncAccessor_hasObjectInLeftHand
	{
		get
		{
			return this.hasObjectInLeftHand;
		}
		set
		{
			if (value || !base.IsServer)
			{
				this.hasObjectInLeftHand = value;
			}
			this.syncVar___hasObjectInLeftHand.SetValue(value, value);
		}
	}

	// Token: 0x1700004E RID: 78
	// (get) Token: 0x0600026D RID: 621 RVA: 0x00016184 File Offset: 0x00014384
	// (set) Token: 0x0600026E RID: 622 RVA: 0x0001618C File Offset: 0x0001438C
	public GameObject SyncAccessor_objInLeftHand
	{
		get
		{
			return this.objInLeftHand;
		}
		set
		{
			if (value || !base.IsServer)
			{
				this.objInLeftHand = value;
			}
			this.syncVar___objInLeftHand.SetValue(value, value);
		}
	}

	// Token: 0x0600026F RID: 623 RVA: 0x000161C1 File Offset: 0x000143C1
	public virtual void Awake___UserLogic()
	{
		this.playerControls = InputManager.inputActions;
		this.playerValues = base.GetComponent<PlayerValues>();
		this.pauseManager = PauseManager.Instance;
	}

	// Token: 0x040002A0 RID: 672
	private PlayerControls playerControls;

	// Token: 0x040002A1 RID: 673
	private FirstPersonController playerController;

	// Token: 0x040002A2 RID: 674
	private InputAction interact;

	// Token: 0x040002A3 RID: 675
	private InputAction drop;

	// Token: 0x040002A4 RID: 676
	private InputAction change;

	// Token: 0x040002A5 RID: 677
	[Header("Interaction")]
	[SerializeField]
	private float interactionDistance;

	// Token: 0x040002A6 RID: 678
	[SerializeField]
	private LayerMask interactionLayer;

	// Token: 0x040002A7 RID: 679
	[SerializeField]
	private LayerMask environmentInteractionLayer;

	// Token: 0x040002A8 RID: 680
	[SerializeField]
	private LayerMask bodyInteractionLayer;

	// Token: 0x040002A9 RID: 681
	[SerializeField]
	private LayerMask ragdollInteractionLayer;

	// Token: 0x040002AA RID: 682
	[SerializeField]
	private float sphereRadius = 0.5f;

	// Token: 0x040002AB RID: 683
	[SerializeField]
	private float maxSphereDistance = 5f;

	// Token: 0x040002AC RID: 684
	[SerializeField]
	private float maxInteractionDistance = 5f;

	// Token: 0x040002AD RID: 685
	[SerializeField]
	private float currentHitDistance;

	// Token: 0x040002AE RID: 686
	[SerializeField]
	private LayerMask landLayer;

	// Token: 0x040002AF RID: 687
	[SerializeField]
	private Animator animator;

	// Token: 0x040002B0 RID: 688
	[SerializeField]
	public Animator globalAnimator;

	// Token: 0x040002B1 RID: 689
	public Transform fpArms;

	// Token: 0x040002B2 RID: 690
	public Interactable currentInteractable;

	// Token: 0x040002B3 RID: 691
	public InteractEnvironment currentEnvironmentInteractable;

	// Token: 0x040002B4 RID: 692
	public InteractEnvironment heldEnvironmentInteractable;

	// Token: 0x040002B5 RID: 693
	public GameObject currentObject;

	// Token: 0x040002B6 RID: 694
	public GameObject currentHitObject;

	// Token: 0x040002B7 RID: 695
	public GameObject currentEnvironmentObject;

	// Token: 0x040002B8 RID: 696
	private bool interactionSphere;

	// Token: 0x040002B9 RID: 697
	[HideInInspector]
	public PlayerValues playerValues;

	// Token: 0x040002BA RID: 698
	public Transform[] pickupPositionRightHand;

	// Token: 0x040002BB RID: 699
	public Transform[] pickupPositionBothHand;

	// Token: 0x040002BC RID: 700
	public Transform[] pickupPositionLeftHand;

	// Token: 0x040002BD RID: 701
	public Transform[] aimPositionBothHand;

	// Token: 0x040002BE RID: 702
	public Transform[] aimPositionRightHand;

	// Token: 0x040002BF RID: 703
	public Transform[] pickupPositionOnline;

	// Token: 0x040002C0 RID: 704
	public Transform RightHandIKTarget;

	// Token: 0x040002C1 RID: 705
	public Transform LeftHandIKTarget;

	// Token: 0x040002C2 RID: 706
	private Transform TargetForRightIK;

	// Token: 0x040002C3 RID: 707
	private Transform TargetForLeftIK;

	// Token: 0x040002C4 RID: 708
	[SerializeField]
	private Transform LeftIdlePosition;

	// Token: 0x040002C5 RID: 709
	[SerializeField]
	private Transform RightIdlePosition;

	// Token: 0x040002C6 RID: 710
	[SerializeField]
	private RigBuilder RigBuilder;

	// Token: 0x040002C7 RID: 711
	[Space]
	[SerializeField]
	private AudioClip pickupClip;

	// Token: 0x040002C8 RID: 712
	[SerializeField]
	private AudioClip dropClip;

	// Token: 0x040002C9 RID: 713
	[SerializeField]
	private AudioClip switchWeaponsClip;

	// Token: 0x040002CA RID: 714
	[Space]
	private Camera cam;

	// Token: 0x040002CB RID: 715
	[SerializeField]
	private GameObject graphics;

	// Token: 0x040002CC RID: 716
	[SerializeField]
	private GameObject RightHandPositions;

	// Token: 0x040002CD RID: 717
	[SerializeField]
	private GameObject BothHandPositions;

	// Token: 0x040002CE RID: 718
	[SerializeField]
	private GameObject LeftHandPositions;

	// Token: 0x040002CF RID: 719
	[SerializeField]
	private GameObject AimBothHandPositions;

	// Token: 0x040002D0 RID: 720
	[SerializeField]
	private GameObject AimRightHandPositions;

	// Token: 0x040002D1 RID: 721
	[SerializeField]
	private GameObject OnlinePositions;

	// Token: 0x040002D2 RID: 722
	private GameObject camHolder;

	// Token: 0x040002D3 RID: 723
	public CameraShakeConstrains camAnimScript;

	// Token: 0x040002D4 RID: 724
	[SyncVar]
	public bool hasObjectInHand;

	// Token: 0x040002D5 RID: 725
	[SyncVar]
	public GameObject objInHand;

	// Token: 0x040002D6 RID: 726
	private Weapon weaponInHand;

	// Token: 0x040002D7 RID: 727
	private ItemBehaviour behaviourInHand;

	// Token: 0x040002D8 RID: 728
	[SyncVar]
	public bool hasObjectInLeftHand;

	// Token: 0x040002D9 RID: 729
	[SyncVar]
	public GameObject objInLeftHand;

	// Token: 0x040002DA RID: 730
	private Weapon weaponInLeftHand;

	// Token: 0x040002DB RID: 731
	private ItemBehaviour behaviourInLeftHand;

	// Token: 0x040002DC RID: 732
	private PauseManager pauseManager;

	// Token: 0x040002DD RID: 733
	private float dropTimer;

	// Token: 0x040002DE RID: 734
	private float interactTimer;

	// Token: 0x040002DF RID: 735
	private PlayerHealth enemyBody;

	// Token: 0x040002E0 RID: 736
	private Transform ragdoll;

	// Token: 0x040002E1 RID: 737
	private Transform hips;

	// Token: 0x040002E2 RID: 738
	[SerializeField]
	private Transform camPoint;

	// Token: 0x040002E3 RID: 739
	public SyncVar<bool> syncVar___hasObjectInHand;

	// Token: 0x040002E4 RID: 740
	public SyncVar<GameObject> syncVar___objInHand;

	// Token: 0x040002E5 RID: 741
	public SyncVar<bool> syncVar___hasObjectInLeftHand;

	// Token: 0x040002E6 RID: 742
	public SyncVar<GameObject> syncVar___objInLeftHand;

	// Token: 0x040002E7 RID: 743
	private bool NetworkInitializeEarly_PlayerPickup_Assembly-CSharp.dll;

	// Token: 0x040002E8 RID: 744
	private bool NetworkInitializeLate_PlayerPickup_Assembly-CSharp.dll;
}

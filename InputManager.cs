﻿using System;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.InputSystem;

// Token: 0x020000FA RID: 250
public class InputManager : MonoBehaviour
{
	// Token: 0x14000003 RID: 3
	// (add) Token: 0x06000CBD RID: 3261 RVA: 0x00056C08 File Offset: 0x00054E08
	// (remove) Token: 0x06000CBE RID: 3262 RVA: 0x00056C3C File Offset: 0x00054E3C
	public static event Action rebindComplete;

	// Token: 0x14000004 RID: 4
	// (add) Token: 0x06000CBF RID: 3263 RVA: 0x00056C70 File Offset: 0x00054E70
	// (remove) Token: 0x06000CC0 RID: 3264 RVA: 0x00056CA4 File Offset: 0x00054EA4
	public static event Action rebindCanceled;

	// Token: 0x14000005 RID: 5
	// (add) Token: 0x06000CC1 RID: 3265 RVA: 0x00056CD8 File Offset: 0x00054ED8
	// (remove) Token: 0x06000CC2 RID: 3266 RVA: 0x00056D0C File Offset: 0x00054F0C
	public static event Action<InputAction, int> rebindStarted;

	// Token: 0x06000CC3 RID: 3267 RVA: 0x00056D3F File Offset: 0x00054F3F
	private void Awake()
	{
		if (InputManager.inputActions == null)
		{
			InputManager.inputActions = new PlayerControls();
		}
	}

	// Token: 0x06000CC4 RID: 3268 RVA: 0x00056D54 File Offset: 0x00054F54
	public static void RestartEngine()
	{
		InputManager.inputActions = new PlayerControls();
		foreach (ReBindUI reBindUI in InputManager.rebindFields)
		{
			reBindUI.Restart();
		}
	}

	// Token: 0x06000CC5 RID: 3269 RVA: 0x00056DB0 File Offset: 0x00054FB0
	public static void StartRebind(string actionName, int bindingIndex, TextMeshProUGUI statusText, bool excludeMouse, bool sequenceDisplay)
	{
		InputAction inputAction = InputManager.inputActions.asset.FindAction(actionName, false);
		if (inputAction == null || inputAction.bindings.Count <= bindingIndex)
		{
			Debug.Log("Couldnt find action or binding");
			return;
		}
		if (inputAction.bindings[bindingIndex].isComposite)
		{
			int num = bindingIndex + 1;
			if (num < inputAction.bindings.Count && inputAction.bindings[num].isPartOfComposite)
			{
				InputManager.DoRebind(inputAction, num, statusText, true, excludeMouse, sequenceDisplay);
				return;
			}
		}
		else
		{
			InputManager.DoRebind(inputAction, bindingIndex, statusText, false, excludeMouse, sequenceDisplay);
		}
	}

	// Token: 0x06000CC6 RID: 3270 RVA: 0x00056E50 File Offset: 0x00055050
	private static void DoRebind(InputAction actionToRebind, int bindingIndex, TextMeshProUGUI statusText, bool allCompositeParts, bool excludeMouse, bool sequenceDisplay)
	{
		if (actionToRebind == null || bindingIndex < 0)
		{
			return;
		}
		if (sequenceDisplay)
		{
			PauseManager.Instance.sequenceDisplayGameObject.SetActive(true);
		}
		statusText.text = "Press a " + actionToRebind.expectedControlType;
		actionToRebind.Disable();
		InputActionRebindingExtensions.RebindingOperation rebindingOperation = actionToRebind.PerformInteractiveRebinding(bindingIndex).WithCancelingThrough("<Keyboard>/escape");
		rebindingOperation.OnComplete(delegate(InputActionRebindingExtensions.RebindingOperation operation)
		{
			actionToRebind.Enable();
			operation.Dispose();
			if (allCompositeParts)
			{
				int num = bindingIndex + 1;
				if (num < actionToRebind.bindings.Count && actionToRebind.bindings[num].isPartOfComposite)
				{
					if (sequenceDisplay)
					{
						PauseManager.Instance.sequenceDisplayGameObject.SetActive(true);
					}
					InputManager.DoRebind(actionToRebind, num, statusText, allCompositeParts, excludeMouse, sequenceDisplay);
				}
			}
			InputManager.SaveBindingOverride(actionToRebind);
			Action action2 = InputManager.rebindComplete;
			if (action2 == null)
			{
				return;
			}
			action2();
		});
		rebindingOperation.OnCancel(delegate(InputActionRebindingExtensions.RebindingOperation operation)
		{
			actionToRebind.Enable();
			operation.Dispose();
			Action action3 = InputManager.rebindCanceled;
			if (action3 == null)
			{
				return;
			}
			action3();
		});
		Action<InputAction, int> action = InputManager.rebindStarted;
		if (action != null)
		{
			action(actionToRebind, bindingIndex);
		}
		rebindingOperation.Start();
	}

	// Token: 0x06000CC7 RID: 3271 RVA: 0x00056F47 File Offset: 0x00055147
	public static string GetBindingName(string actionName, int bindingIndex)
	{
		if (InputManager.inputActions == null)
		{
			InputManager.inputActions = new PlayerControls();
		}
		return InputManager.inputActions.asset.FindAction(actionName, false).GetBindingDisplayString(bindingIndex, (InputBinding.DisplayStringOptions)0);
	}

	// Token: 0x06000CC8 RID: 3272 RVA: 0x00056F74 File Offset: 0x00055174
	private static void SaveBindingOverride(InputAction action)
	{
		for (int i = 0; i < action.bindings.Count; i++)
		{
			InputActionMap actionMap = action.actionMap;
			PlayerPrefs.SetString(((actionMap != null) ? actionMap.ToString() : null) + action.name + i.ToString(), action.bindings[i].overridePath);
		}
	}

	// Token: 0x06000CC9 RID: 3273 RVA: 0x00056FDC File Offset: 0x000551DC
	public static void LoadBindingOverride(string actionName)
	{
		if (InputManager.inputActions == null)
		{
			InputManager.inputActions = new PlayerControls();
		}
		InputAction inputAction = InputManager.inputActions.asset.FindAction(actionName, false);
		for (int i = 0; i < inputAction.bindings.Count; i++)
		{
			InputActionMap actionMap = inputAction.actionMap;
			if (!string.IsNullOrEmpty(PlayerPrefs.GetString(((actionMap != null) ? actionMap.ToString() : null) + inputAction.name + i.ToString(), inputAction.bindings[i].overridePath)))
			{
				InputAction inputAction2 = inputAction;
				int num = i;
				InputActionMap actionMap2 = inputAction.actionMap;
				inputAction2.ApplyBindingOverride(num, PlayerPrefs.GetString(((actionMap2 != null) ? actionMap2.ToString() : null) + inputAction.name + i.ToString(), inputAction.bindings[i].overridePath));
			}
		}
	}

	// Token: 0x06000CCA RID: 3274 RVA: 0x000570BC File Offset: 0x000552BC
	public static void ResetBinding(string actionName, int bindingIndex)
	{
		InputAction inputAction = InputManager.inputActions.asset.FindAction(actionName, false);
		if (inputAction == null || inputAction.bindings.Count <= bindingIndex)
		{
			Debug.Log("Couldnt find action or binding");
			return;
		}
		if (inputAction.bindings[bindingIndex].isComposite)
		{
			for (int i = bindingIndex; i < inputAction.bindings.Count; i++)
			{
				if (!inputAction.bindings[i].isComposite && !inputAction.bindings[i].isPartOfComposite)
				{
					break;
				}
				inputAction.RemoveBindingOverride(i);
			}
		}
		else
		{
			inputAction.RemoveBindingOverride(bindingIndex);
		}
		InputManager.SaveBindingOverride(inputAction);
	}

	// Token: 0x04000AFB RID: 2811
	public static PlayerControls inputActions;

	// Token: 0x04000AFC RID: 2812
	private static int iteration;

	// Token: 0x04000B00 RID: 2816
	public static List<ReBindUI> rebindFields = new List<ReBindUI>();
}

﻿using System;
using UnityEngine;

// Token: 0x02000157 RID: 343
public class SpawnEffect : MonoBehaviour
{
	// Token: 0x06000EF9 RID: 3833 RVA: 0x00062BCC File Offset: 0x00060DCC
	private void Start()
	{
		this.shaderProperty = Shader.PropertyToID("_cutoff");
		this._renderer = base.GetComponent<Renderer>();
		this.ps = base.GetComponentInChildren<ParticleSystem>();
		this.ps.main.duration = this.spawnEffectTime;
		this.ps.Play();
	}

	// Token: 0x06000EFA RID: 3834 RVA: 0x00062C28 File Offset: 0x00060E28
	private void Update()
	{
		if (this.timer < this.spawnEffectTime + this.pause)
		{
			this.timer += Time.deltaTime;
		}
		else
		{
			this.ps.Play();
			this.timer = 0f;
		}
		this._renderer.material.SetFloat(this.shaderProperty, this.fadeIn.Evaluate(Mathf.InverseLerp(0f, this.spawnEffectTime, this.timer)));
	}

	// Token: 0x04000D96 RID: 3478
	public float spawnEffectTime = 2f;

	// Token: 0x04000D97 RID: 3479
	public float pause = 1f;

	// Token: 0x04000D98 RID: 3480
	public AnimationCurve fadeIn;

	// Token: 0x04000D99 RID: 3481
	private ParticleSystem ps;

	// Token: 0x04000D9A RID: 3482
	private float timer;

	// Token: 0x04000D9B RID: 3483
	private Renderer _renderer;

	// Token: 0x04000D9C RID: 3484
	private int shaderProperty;
}

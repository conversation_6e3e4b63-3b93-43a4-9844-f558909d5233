﻿using System;
using FishNet;
using UnityEngine;

// Token: 0x02000131 RID: 305
public class OnlyForHost : MonoBehaviour
{
	// Token: 0x06000E70 RID: 3696 RVA: 0x0005F9D4 File Offset: 0x0005DBD4
	private void Update()
	{
		if (InstanceFinder.NetworkManager == null)
		{
			return;
		}
		if (!InstanceFinder.NetworkManager.IsServer)
		{
			base.transform.localScale = Vector3.zero;
			return;
		}
		base.transform.localScale = Vector3.one;
	}
}

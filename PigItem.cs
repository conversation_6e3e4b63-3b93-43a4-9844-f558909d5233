﻿using System;
using UnityEngine;

// Token: 0x02000006 RID: 6
public class PigItem : MonoBehaviour
{
	// Token: 0x06000028 RID: 40 RVA: 0x00002EED File Offset: 0x000010ED
	private void Awake()
	{
		this.visual = base.GetComponentInChildren<SkinnedMeshRenderer>();
		this.visual.enabled = false;
	}

	// Token: 0x06000029 RID: 41 RVA: 0x00002F08 File Offset: 0x00001108
	private void Start()
	{
		Physics.IgnoreCollision(this.pigReal.GetComponent<Collider>(), base.GetComponent<Collider>());
		this.anim = base.GetComponent<Animator>();
		this.hatMesh = this.hatHeld.GetComponent<MeshRenderer>();
		this.hatMesh.enabled = false;
		this.SetDropped();
		Debug.Log("caca");
	}

	// Token: 0x0600002A RID: 42 RVA: 0x00002F64 File Offset: 0x00001164
	private void SetHeld()
	{
		this.pigReal.SetActive(false);
		this.visual.enabled = true;
		base.transform.localRotation = Quaternion.Euler(3.1f, 30f, 0f);
		base.transform.localPosition = new Vector3(0.15f, -0.759f, 1.199f);
		this.angerTime = global::UnityEngine.Random.Range(this.angerTimeMin, this.angerTimeMax);
		if (this.hatHeld.activeSelf)
		{
			this.hatMesh.enabled = true;
		}
		this.held = true;
	}

	// Token: 0x0600002B RID: 43 RVA: 0x00003000 File Offset: 0x00001200
	private void SetDropped()
	{
		Rigidbody rigidbody;
		if (base.TryGetComponent<Rigidbody>(out rigidbody))
		{
			rigidbody.isKinematic = true;
		}
		this.visual.enabled = false;
		this.pigReal.transform.position = base.transform.position;
		this.pigReal.SetActive(true);
		if (this.hatHeld.activeSelf)
		{
			this.hatMesh.enabled = false;
		}
		this.held = false;
	}

	// Token: 0x0600002C RID: 44 RVA: 0x00003074 File Offset: 0x00001274
	private void Update()
	{
		if (this.pigReal == null)
		{
			global::UnityEngine.Object.Destroy(this);
		}
		if (base.transform.root.CompareTag("Player"))
		{
			if (!this.held)
			{
				this.SetHeld();
				return;
			}
		}
		else if (this.held)
		{
			this.SetDropped();
		}
	}

	// Token: 0x0600002D RID: 45 RVA: 0x000030CC File Offset: 0x000012CC
	private void LateUpdate()
	{
		if (!this.held)
		{
			base.transform.position = this.pigReal.transform.position;
			return;
		}
		this.angerTimer += Time.deltaTime;
		if (this.angerTimer > this.angerTime)
		{
			this.Anger();
			this.angerTimer = 0f;
		}
	}

	// Token: 0x0600002E RID: 46 RVA: 0x0000312E File Offset: 0x0000132E
	private void Anger()
	{
		this.anim.SetTrigger("HeldAngry");
	}

	// Token: 0x0400003C RID: 60
	private SkinnedMeshRenderer visual;

	// Token: 0x0400003D RID: 61
	private Animator anim;

	// Token: 0x0400003E RID: 62
	public GameObject pigReal;

	// Token: 0x0400003F RID: 63
	public GameObject hatHeld;

	// Token: 0x04000040 RID: 64
	private MeshRenderer hatMesh;

	// Token: 0x04000041 RID: 65
	private float angerTimeMin = 3f;

	// Token: 0x04000042 RID: 66
	private float angerTimeMax = 5f;

	// Token: 0x04000043 RID: 67
	private float angerTime;

	// Token: 0x04000044 RID: 68
	private float angerTimer;

	// Token: 0x04000045 RID: 69
	private bool held;
}

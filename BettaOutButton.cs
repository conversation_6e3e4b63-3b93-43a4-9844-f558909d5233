﻿using System;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

// Token: 0x02000121 RID: 289
public class BettaOutButton : MonoBehaviour, IPointerEnterHandler, IEventSystemHandler, IPointerExitHandler
{
	// Token: 0x06000E1F RID: 3615 RVA: 0x0005E70C File Offset: 0x0005C90C
	private void Awake()
	{
		this.button = base.GetComponent<Button>();
	}

	// Token: 0x06000E20 RID: 3616 RVA: 0x000023D6 File Offset: 0x000005D6
	private void Start()
	{
	}

	// Token: 0x06000E21 RID: 3617 RVA: 0x000023D6 File Offset: 0x000005D6
	public void OnPointerEnter(PointerEventData eventData)
	{
	}

	// Token: 0x06000E22 RID: 3618 RVA: 0x000023D6 File Offset: 0x000005D6
	public void OnPointerExit(PointerEventData eventData)
	{
	}

	// Token: 0x04000C92 RID: 3218
	private Button button;
}

﻿using System;
using UnityEngine;

// Token: 0x02000167 RID: 359
public class WFX_Demo_RandomDir : MonoBehaviour
{
	// Token: 0x06000F44 RID: 3908 RVA: 0x00064D2C File Offset: 0x00062F2C
	private void Awake()
	{
		base.transform.eulerAngles = new Vector3(global::UnityEngine.Random.Range(this.min.x, this.max.x), global::UnityEngine.Random.Range(this.min.y, this.max.y), global::UnityEngine.Random.Range(this.min.z, this.max.z));
	}

	// Token: 0x04000E13 RID: 3603
	public Vector3 min = new Vector3(0f, 0f, 0f);

	// Token: 0x04000E14 RID: 3604
	public Vector3 max = new Vector3(0f, 360f, 0f);
}

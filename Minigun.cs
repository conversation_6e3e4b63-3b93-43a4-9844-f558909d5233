﻿using System;
using System.Collections;
using DG.Tweening;
using FishNet;
using FishNet.Connection;
using FishNet.Managing;
using FishNet.Object;
using FishNet.Object.Delegating;
using FishNet.Serializing;
using FishNet.Serializing.Generated;
using FishNet.Transporting;
using LambdaTheDev.NetworkAudioSync;
using UnityEngine;
using UnityEngine.UI;

// Token: 0x0200009C RID: 156
public class Minigun : Weapon
{
	// Token: 0x06000852 RID: 2130 RVA: 0x0003B2CB File Offset: 0x000394CB
	private void Start()
	{
		this.chargedBullets = (float)this.ammoCharge;
		base.sync___set_value_currentAmmo(base.SyncAccessor_currentAmmo - this.ammoCharge, true);
		this.networkAudioSource = base.GetComponent<NetworkAudioSource>();
	}

	// Token: 0x06000853 RID: 2131 RVA: 0x0003B2FC File Offset: 0x000394FC
	private void Update()
	{
		base.WeaponUpdate();
		if (this.fireTimer > 0f)
		{
			this.fireTimer -= Time.deltaTime;
		}
		if (base.gameObject.layer == 7)
		{
			return;
		}
		if (base.SyncAccessor_currentAmmo <= 0 && this.noammoBool)
		{
			this.AudioStop();
			this.noammoBool = false;
		}
		if (base.SyncAccessor_currentAmmo > 0)
		{
			this.noammoBool = true;
		}
		if ((this.invertFire ? this.fire2 : this.fire1).ReadValue<float>() > 0.1f && this.inRightHand && base.SyncAccessor_currentAmmo > 0)
		{
			this.shootTimer += Time.deltaTime;
			this.released = false;
		}
		else if (this.fire2.ReadValue<float>() > 0.1f && this.inLeftHand && base.SyncAccessor_currentAmmo > 0)
		{
			this.shootTimer += Time.deltaTime;
			this.released = false;
		}
		else
		{
			this.shootTimer -= Time.deltaTime;
			this.pressed = false;
		}
		if ((this.invertFire ? this.fire2 : this.fire1).ReadValue<float>() > 0.1f && this.inRightHand && !this.pressed && base.SyncAccessor_currentAmmo > 0)
		{
			this.pressed = true;
			this.AudioPlay();
		}
		if ((this.invertFire ? this.fire2 : this.fire1).ReadValue<float>() < 0.1f && this.inRightHand && !this.released)
		{
			this.released = true;
			if (base.SyncAccessor_currentAmmo > 0)
			{
				this.AudioStop();
			}
		}
		this.shootTimer = Mathf.Clamp(this.shootTimer, 0f, this.timeBeforeShooting);
		if ((this.invertFire ? this.fire2 : this.fire1).ReadValue<float>() > 0.1f && this.inRightHand && (this.reloadWeapon || base.SyncAccessor_currentAmmo > 0) && this.shootTimer == this.timeBeforeShooting)
		{
			this.Fire();
		}
		else if (this.fire2.ReadValue<float>() > 0.1f && this.inLeftHand && (this.reloadWeapon || base.SyncAccessor_currentAmmo > 0) && this.shootTimer == this.timeBeforeShooting)
		{
			this.Fire();
		}
		this.rotativePart.Rotate(0f, 0f, Mathf.Lerp(0f, this.rotationSpeed * Time.deltaTime, this.shootTimer / this.timeBeforeShooting));
	}

	// Token: 0x06000854 RID: 2132 RVA: 0x0003B58E File Offset: 0x0003978E
	[ServerRpc]
	private void AudioPlay()
	{
		this.RpcWriter___Server_AudioPlay_2166136261();
	}

	// Token: 0x06000855 RID: 2133 RVA: 0x0003B596 File Offset: 0x00039796
	[ServerRpc]
	private void AudioStop()
	{
		this.RpcWriter___Server_AudioStop_2166136261();
	}

	// Token: 0x06000856 RID: 2134 RVA: 0x0003B5A0 File Offset: 0x000397A0
	private void Fire()
	{
		if (PauseManager.Instance.pause || !this.playerController.SyncAccessor_canMove)
		{
			return;
		}
		if (!this.playerController.IsOwner)
		{
			return;
		}
		if (this.changePitchOnShoot)
		{
			this.audio.pitch = global::UnityEngine.Random.Range(0.97f, 1.03f);
		}
		if (this.fireTimer > 0f)
		{
			this.shot = true;
			return;
		}
		this.shot = false;
		this.fireTimer = this.timeBetweenFire;
		if (base.SyncAccessor_currentAmmo <= 0)
		{
			this.audio.PlayOneShot(this.nobulletClip);
			this.noAmmoClicks++;
			if (this.noAmmoClicks > 1 && this.inRightHand)
			{
				this.rootObject.GetComponent<PlayerPickup>().RightHandDrop();
			}
			if (this.noAmmoClicks > 1 && this.inLeftHand)
			{
				this.rootObject.GetComponent<PlayerPickup>().LeftHandDrop();
			}
		}
		if (!this.reloadWeapon && base.SyncAccessor_currentAmmo <= 0)
		{
			return;
		}
		if (this.reloadWeapon && this.isReloading)
		{
			return;
		}
		if (this.reloadWeapon && this.chargedBullets <= 0f && base.SyncAccessor_currentAmmo > 0)
		{
			base.StartCoroutine(this.Reload());
		}
		if (this.reloadWeapon && this.chargedBullets <= 0f && base.SyncAccessor_currentAmmo <= 0)
		{
			this.audio.PlayOneShot(this.nobulletClip);
		}
		if (this.reloadWeapon && this.chargedBullets <= 0f)
		{
			return;
		}
		if (this.burstGun)
		{
			base.StartCoroutine(this.BurstFire());
			return;
		}
		float num = ((this.ScopeAimWeapon && !this.playerController.isAiming) ? this.notAimingAccuracy : ((this.playerController.isSprinting || !this.playerController.safeGrounded) ? Mathf.Lerp(this.maxSpread, this.minSpread, this.sprintAccuracy) : (this.playerController.isCrouching ? Mathf.Lerp(this.maxSpread, this.minSpread, this.standingAccuracy) : (this.playerController.isWalking ? Mathf.Lerp(this.maxSpread, this.minSpread, this.walkAccuracy) : Mathf.Lerp(this.maxSpread, this.minSpread, this.standingAccuracy)))));
		this.spread = this.cam.transform.right * global::UnityEngine.Random.Range(num, -num) + this.cam.transform.up * global::UnityEngine.Random.Range(num, -num);
		this.ShootServer(this.damage, this.cam.transform.position, this.cam.transform.forward + this.spread);
		if (this.revolverShake)
		{
			base.CameraRevolverAnimation();
		}
		else
		{
			base.CameraAnimation();
		}
		base.WeaponAnimation();
	}

	// Token: 0x06000857 RID: 2135 RVA: 0x0003B884 File Offset: 0x00039A84
	private void ShootServer(float damageToGive, Vector3 position, Vector3 direction)
	{
		this.RemoveAmmo();
		this.ShootServerEffect();
		RaycastHit[] array = Physics.RaycastAll(position, direction, float.PositiveInfinity, this.defaultLayer);
		Array.Sort<RaycastHit>(array, (RaycastHit x, RaycastHit y) => x.distance.CompareTo(y.distance));
		if (array.Length != 0)
		{
			for (int i = 0; i < array.Length; i++)
			{
				base.TriggerEnvironment(array[i].transform.gameObject, array[i].point, direction, array[i].normal);
				this.SpawnBulletTrailServer(array[i].point);
				if (array[i].transform.tag == "ShatterableGlass")
				{
					base.BreakGlassServer(array[i].point, direction, array[i].transform.gameObject);
				}
				else if (array[i].transform.gameObject.layer != LayerMask.NameToLayer("Ragdoll"))
				{
					this.SpawnVFXServer(0, array[i].point, array[i].normal, array[i].transform.tag, array[i].transform);
					this.SpawnVFXServer(1, array[i].point, array[i].normal, array[i].transform.tag, array[i].transform);
				}
				if (array[i].transform.gameObject.layer != 10 && array[i].transform.gameObject.layer != 11 && array[i].transform.gameObject.layer != 14 && array[i].transform.gameObject.layer != 18 && array[i].transform.gameObject.layer != 19 && array[i].transform.gameObject.layer != 24)
				{
					break;
				}
			}
		}
		RaycastHit raycastHit;
		if (Physics.Raycast(position, direction, out raycastHit, float.PositiveInfinity, this.playerLayer) && raycastHit.transform.gameObject.layer == 11 && (raycastHit.transform.root.CompareTag("Player") ? raycastHit.transform.root.TryGetComponent<PlayerHealth>(out this.enemyHealth) : (raycastHit.transform.GetComponentInParent<PlayerHealth>() != null)))
		{
			if (raycastHit.transform.root.tag != "Player")
			{
				this.enemyHealth = raycastHit.transform.GetComponentInParent<PlayerHealth>();
			}
			if (this.enemyHealth.gameObject == base.transform.root.gameObject)
			{
				return;
			}
			if (base.FriendlyFireCheck(this.enemyHealth))
			{
				return;
			}
			bool flag = raycastHit.transform.gameObject.name == "Head_Col" || raycastHit.transform.gameObject.name == "Neck_1_Col";
			if (flag)
			{
				damageToGive *= this.headMultiplier;
				Settings.Instance.IncreaseHeadshotsAmount();
			}
			else
			{
				Settings.Instance.IncreaseBodyshotsAmount();
			}
			if (flag && this.headImpact)
			{
				global::UnityEngine.Object.Instantiate<GameObject>(this.headImpact, raycastHit.point, Quaternion.identity, (this.enemyHealth.SyncAccessor_health - damageToGive > 0f) ? raycastHit.transform : null);
			}
			else
			{
				global::UnityEngine.Object.Instantiate<GameObject>(this.bodyImpact, raycastHit.point, Quaternion.LookRotation(raycastHit.normal));
			}
			if (this.playGenericBodyImpactOnBody ? this.genericBodyImpact : (this.genericBodyImpact && flag))
			{
				global::UnityEngine.Object.Instantiate<GameObject>(this.genericBodyImpact, raycastHit.point, Quaternion.identity, (this.enemyHealth.SyncAccessor_health - damageToGive > 0f) ? raycastHit.transform : null);
			}
			this.ServerFX(raycastHit.point + direction, Quaternion.identity);
			if (this.enemyHealth.SyncAccessor_health - damageToGive <= 0f)
			{
				base.KillShockWave();
			}
			if (this.marker == null)
			{
				this.marker = global::UnityEngine.Object.Instantiate<GameObject>(this.hitMarker, Crosshair.Instance.transform.position, Quaternion.identity, PauseManager.Instance.transform);
				this.marker.transform.DOPunchScale((raycastHit.transform.gameObject.name == "Head_Col") ? new Vector3(2.5f, 2.5f, 2.5f) : Vector3.one, 0.3f, 8, 2f);
				if (raycastHit.transform.gameObject.name == "Head_Col")
				{
					this.marker.GetComponent<Image>().color = Color.red;
				}
				global::UnityEngine.Object.Destroy(this.marker, 0.3f);
			}
			else
			{
				global::UnityEngine.Object.Destroy(this.marker);
				this.marker = global::UnityEngine.Object.Instantiate<GameObject>(this.hitMarker, Crosshair.Instance.transform.position, Quaternion.identity, PauseManager.Instance.transform);
				this.marker.transform.DOPunchScale((raycastHit.transform.gameObject.name == "Head_Col") ? new Vector3(2.5f, 2.5f, 2.5f) : Vector3.one, 0.3f, 8, 2f);
				if (raycastHit.transform.gameObject.name == "Head_Col")
				{
					this.marker.GetComponent<Image>().color = Color.red;
				}
				global::UnityEngine.Object.Destroy(this.marker, 0.3f);
			}
			if (flag)
			{
				this.LocalSound(0);
				this.LocalSound(1);
			}
			else
			{
				this.LocalSound(1);
			}
			if (this.enemyHealth.SyncAccessor_health - damageToGive <= 0f)
			{
				this.LocalSound(2);
				this.enemyHealth.graphics.SetActive(false);
				this.enemyHealth.controller.playerPickupScript.fpArms.gameObject.SetActive(false);
				this.enemyHealth.GetComponent<CharacterController>().enabled = false;
				this.enemyHealth.Explode(false, true, raycastHit.transform.gameObject.name, direction, this.ragdollEjectForce, raycastHit.point);
				this.KillServer(this.enemyHealth);
				this.enemyHealth.DisablePlayerObjectWhenKilled();
				PauseManager.Instance.WriteLog(string.Concat(new string[]
				{
					"<b><color=#",
					PauseManager.Instance.selfNameLogColor,
					">",
					this.enemyHealth.SyncAccessor_playerValues.SyncAccessor_playerClient.PlayerName,
					"</color></b> was ",
					flag ? "headshot" : "killed",
					" with ",
					base.StartsWithVowel(this.behaviour.weaponName) ? "an " : "a",
					" <b><color=white>",
					this.behaviour.weaponName,
					"</color></b> by <b><color=#",
					PauseManager.Instance.enemyNameLogColor,
					">",
					ClientInstance.Instance.PlayerName,
					"</color></b>"
				}));
			}
			else
			{
				this.GiveDamage(damageToGive, this.enemyHealth, raycastHit.transform.gameObject.name);
			}
			this.hitOK = true;
		}
		RaycastHit raycastHit2;
		if (Physics.Raycast(position, direction, out raycastHit2, float.PositiveInfinity, this.supLayer) && !this.hitOK && raycastHit2.transform.gameObject.layer == 17)
		{
			this.SupressionServer(raycastHit2.transform);
		}
		RaycastHit raycastHit3;
		DollHealth dollHealth;
		if (Physics.Raycast(position, direction, out raycastHit3, float.PositiveInfinity) && raycastHit3.transform.TryGetComponent<DollHealth>(out dollHealth))
		{
			dollHealth.health -= damageToGive;
		}
		this.hitOK = false;
	}

	// Token: 0x06000858 RID: 2136 RVA: 0x0003C0F1 File Offset: 0x0003A2F1
	[ServerRpc(RunLocally = true)]
	private void RemoveAmmo()
	{
		this.RpcWriter___Server_RemoveAmmo_2166136261();
		this.RpcLogic___RemoveAmmo_2166136261();
	}

	// Token: 0x06000859 RID: 2137 RVA: 0x0003C0FF File Offset: 0x0003A2FF
	[ServerRpc(RunLocally = true)]
	private void GiveDamage(float damageToGive, PlayerHealth enemyHealth, string name)
	{
		this.RpcWriter___Server_GiveDamage_324487999(damageToGive, enemyHealth, name);
		this.RpcLogic___GiveDamage_324487999(damageToGive, enemyHealth, name);
	}

	// Token: 0x0600085A RID: 2138 RVA: 0x0003C128 File Offset: 0x0003A328
	[ServerRpc]
	private void KillServer(PlayerHealth enemyHealth)
	{
		this.RpcWriter___Server_KillServer_1722911636(enemyHealth);
	}

	// Token: 0x0600085B RID: 2139 RVA: 0x0003C13F File Offset: 0x0003A33F
	[TargetRpc]
	private void KillObserver(NetworkConnection conn, ClientInstance client, PlayerHealth enemyHealth)
	{
		this.RpcWriter___Target_KillObserver_123853379(conn, client, enemyHealth);
	}

	// Token: 0x0600085C RID: 2140 RVA: 0x0003C153 File Offset: 0x0003A353
	[ObserversRpc]
	private void HitFeeback(PlayerHealth enemyHealth)
	{
		this.RpcWriter___Observers_HitFeeback_1722911636(enemyHealth);
	}

	// Token: 0x0600085D RID: 2141 RVA: 0x0003C15F File Offset: 0x0003A35F
	[ServerRpc(RunLocally = true)]
	private void ShootServerEffect()
	{
		this.RpcWriter___Server_ShootServerEffect_2166136261();
		this.RpcLogic___ShootServerEffect_2166136261();
	}

	// Token: 0x0600085E RID: 2142 RVA: 0x0003C16D File Offset: 0x0003A36D
	[ServerRpc(RunLocally = true)]
	private void PlayReleaseClip()
	{
		this.RpcWriter___Server_PlayReleaseClip_2166136261();
		this.RpcLogic___PlayReleaseClip_2166136261();
	}

	// Token: 0x0600085F RID: 2143 RVA: 0x0003C17B File Offset: 0x0003A37B
	[ObserversRpc(RunLocally = true, ExcludeOwner = true)]
	private void PlayReleaseClipObservers()
	{
		this.RpcWriter___Observers_PlayReleaseClipObservers_2166136261();
		this.RpcLogic___PlayReleaseClipObservers_2166136261();
	}

	// Token: 0x06000860 RID: 2144 RVA: 0x0003C189 File Offset: 0x0003A389
	[ServerRpc(RunLocally = true)]
	public void ServerFX(Vector3 position, Quaternion rotation)
	{
		this.RpcWriter___Server_ServerFX_3848837105(position, rotation);
		this.RpcLogic___ServerFX_3848837105(position, rotation);
	}

	// Token: 0x06000861 RID: 2145 RVA: 0x0003C1A7 File Offset: 0x0003A3A7
	[ObserversRpc(RunLocally = true, ExcludeOwner = true)]
	private void ObserversFX(Vector3 position, Quaternion rotation)
	{
		this.RpcWriter___Observers_ObserversFX_3848837105(position, rotation);
		this.RpcLogic___ObserversFX_3848837105(position, rotation);
	}

	// Token: 0x06000862 RID: 2146 RVA: 0x0003C1C8 File Offset: 0x0003A3C8
	[ObserversRpc(RunLocally = true, ExcludeOwner = true)]
	private void ShootObserversEffect()
	{
		this.RpcWriter___Observers_ShootObserversEffect_2166136261();
		this.RpcLogic___ShootObserversEffect_2166136261();
	}

	// Token: 0x06000863 RID: 2147 RVA: 0x0003C1E1 File Offset: 0x0003A3E1
	[ServerRpc(RunLocally = true)]
	private void SpawnBulletTrailServer(Vector3 hitPoint)
	{
		this.RpcWriter___Server_SpawnBulletTrailServer_4276783012(hitPoint);
		this.RpcLogic___SpawnBulletTrailServer_4276783012(hitPoint);
	}

	// Token: 0x06000864 RID: 2148 RVA: 0x0003C1F8 File Offset: 0x0003A3F8
	[ObserversRpc(RunLocally = true, ExcludeOwner = true)]
	private void SpawnBulletTrail(Vector3 hitPoint)
	{
		this.RpcWriter___Observers_SpawnBulletTrail_4276783012(hitPoint);
		this.RpcLogic___SpawnBulletTrail_4276783012(hitPoint);
	}

	// Token: 0x06000865 RID: 2149 RVA: 0x0003C219 File Offset: 0x0003A419
	[ServerRpc(RunLocally = true)]
	private void SpawnVFXServer(int index, Vector3 hitPoint, Vector3 hitNormal, string surface, Transform parent)
	{
		this.RpcWriter___Server_SpawnVFXServer_606331033(index, hitPoint, hitNormal, surface, parent);
		this.RpcLogic___SpawnVFXServer_606331033(index, hitPoint, hitNormal, surface, parent);
	}

	// Token: 0x06000866 RID: 2150 RVA: 0x0003C250 File Offset: 0x0003A450
	[ObserversRpc(RunLocally = true, ExcludeOwner = true)]
	private void SpawnVFX(int index, Vector3 hitPoint, Vector3 hitNormal, string surface, Transform parent)
	{
		this.RpcWriter___Observers_SpawnVFX_606331033(index, hitPoint, hitNormal, surface, parent);
		this.RpcLogic___SpawnVFX_606331033(index, hitPoint, hitNormal, surface, parent);
	}

	// Token: 0x06000867 RID: 2151 RVA: 0x0002B771 File Offset: 0x00029971
	private void LocalSound(int index)
	{
		if (index == 0)
		{
			this.audio.PlayOneShot(this.headHitClip);
		}
		if (index == 1)
		{
			this.audio.PlayOneShot(this.bodyHitClip);
		}
		if (index == 2)
		{
			this.audio.PlayOneShot(this.deathClip);
		}
	}

	// Token: 0x06000868 RID: 2152 RVA: 0x0003C291 File Offset: 0x0003A491
	private IEnumerator Reload()
	{
		this.isReloading = true;
		this.audio.PlayOneShot(this.reloadClip);
		if (this.animator != null)
		{
			this.animator.SetTrigger("Reload");
		}
		yield return new WaitForSeconds(this.reloadTime);
		if (base.SyncAccessor_currentAmmo - this.ammoCharge >= 0)
		{
			base.sync___set_value_currentAmmo(base.SyncAccessor_currentAmmo - this.ammoCharge, true);
			this.chargedBullets = (float)this.ammoCharge;
		}
		else
		{
			this.chargedBullets = (float)base.SyncAccessor_currentAmmo;
			base.sync___set_value_currentAmmo(0, true);
		}
		this.isReloading = false;
		yield break;
	}

	// Token: 0x06000869 RID: 2153 RVA: 0x0003C2A0 File Offset: 0x0003A4A0
	private IEnumerator BurstFire()
	{
		int num2;
		for (int i = 0; i < this.bulletsAmount; i = num2 + 1)
		{
			float num = ((this.playerController.isSprinting || !this.playerController.isGrounded || !this.playerController.safeGrounded) ? Mathf.Lerp(this.maxSpread, this.minSpread, this.sprintAccuracy) : (this.playerController.isCrouching ? Mathf.Lerp(this.maxSpread, this.minSpread, this.standingAccuracy) : (this.playerController.isWalking ? Mathf.Lerp(this.maxSpread, this.minSpread, this.walkAccuracy) : Mathf.Lerp(this.maxSpread, this.minSpread, this.standingAccuracy))));
			this.spread = this.cam.transform.right * global::UnityEngine.Random.Range(num, -num) + this.cam.transform.up * global::UnityEngine.Random.Range(num, -num);
			this.ShootServer(this.damage, this.cam.transform.position, this.cam.transform.forward + this.spread);
			if (this.revolverShake)
			{
				base.CameraRevolverAnimation();
			}
			else
			{
				base.CameraAnimation();
			}
			base.WeaponAnimation();
			yield return new WaitForSeconds(this.timeBetweenBullets);
			num2 = i;
		}
		yield break;
	}

	// Token: 0x0600086A RID: 2154 RVA: 0x0003C2AF File Offset: 0x0003A4AF
	[ServerRpc]
	private void SupressionServer(Transform supp)
	{
		this.RpcWriter___Server_SupressionServer_3068987916(supp);
	}

	// Token: 0x0600086B RID: 2155 RVA: 0x0003C2BB File Offset: 0x0003A4BB
	[ObserversRpc]
	private void SuppressionTarget(Transform supp)
	{
		this.RpcWriter___Observers_SuppressionTarget_3068987916(supp);
	}

	// Token: 0x0600086D RID: 2157 RVA: 0x0003C2F4 File Offset: 0x0003A4F4
	public override void NetworkInitialize___Early()
	{
		if (this.NetworkInitializeEarly_Minigun_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeEarly_Minigun_Assembly-CSharp.dll = true;
		base.NetworkInitialize___Early();
		base.RegisterServerRpc(15U, new ServerRpcDelegate(this.RpcReader___Server_AudioPlay_2166136261));
		base.RegisterServerRpc(16U, new ServerRpcDelegate(this.RpcReader___Server_AudioStop_2166136261));
		base.RegisterServerRpc(17U, new ServerRpcDelegate(this.RpcReader___Server_RemoveAmmo_2166136261));
		base.RegisterServerRpc(18U, new ServerRpcDelegate(this.RpcReader___Server_GiveDamage_324487999));
		base.RegisterServerRpc(19U, new ServerRpcDelegate(this.RpcReader___Server_KillServer_1722911636));
		base.RegisterTargetRpc(20U, new ClientRpcDelegate(this.RpcReader___Target_KillObserver_123853379));
		base.RegisterObserversRpc(21U, new ClientRpcDelegate(this.RpcReader___Observers_HitFeeback_1722911636));
		base.RegisterServerRpc(22U, new ServerRpcDelegate(this.RpcReader___Server_ShootServerEffect_2166136261));
		base.RegisterServerRpc(23U, new ServerRpcDelegate(this.RpcReader___Server_PlayReleaseClip_2166136261));
		base.RegisterObserversRpc(24U, new ClientRpcDelegate(this.RpcReader___Observers_PlayReleaseClipObservers_2166136261));
		base.RegisterServerRpc(25U, new ServerRpcDelegate(this.RpcReader___Server_ServerFX_3848837105));
		base.RegisterObserversRpc(26U, new ClientRpcDelegate(this.RpcReader___Observers_ObserversFX_3848837105));
		base.RegisterObserversRpc(27U, new ClientRpcDelegate(this.RpcReader___Observers_ShootObserversEffect_2166136261));
		base.RegisterServerRpc(28U, new ServerRpcDelegate(this.RpcReader___Server_SpawnBulletTrailServer_4276783012));
		base.RegisterObserversRpc(29U, new ClientRpcDelegate(this.RpcReader___Observers_SpawnBulletTrail_4276783012));
		base.RegisterServerRpc(30U, new ServerRpcDelegate(this.RpcReader___Server_SpawnVFXServer_606331033));
		base.RegisterObserversRpc(31U, new ClientRpcDelegate(this.RpcReader___Observers_SpawnVFX_606331033));
		base.RegisterServerRpc(32U, new ServerRpcDelegate(this.RpcReader___Server_SupressionServer_3068987916));
		base.RegisterObserversRpc(33U, new ClientRpcDelegate(this.RpcReader___Observers_SuppressionTarget_3068987916));
	}

	// Token: 0x0600086E RID: 2158 RVA: 0x0003C4CD File Offset: 0x0003A6CD
	public override void NetworkInitialize__Late()
	{
		if (this.NetworkInitializeLate_Minigun_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeLate_Minigun_Assembly-CSharp.dll = true;
		base.NetworkInitialize__Late();
	}

	// Token: 0x0600086F RID: 2159 RVA: 0x0003C4E6 File Offset: 0x0003A6E6
	public override void NetworkInitializeIfDisabled()
	{
		this.NetworkInitialize___Early();
		this.NetworkInitialize__Late();
	}

	// Token: 0x06000870 RID: 2160 RVA: 0x0003C4F4 File Offset: 0x0003A6F4
	private void RpcWriter___Server_AudioPlay_2166136261()
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendServerRpc(15U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000871 RID: 2161 RVA: 0x0003C5E8 File Offset: 0x0003A7E8
	private void RpcLogic___AudioPlay_2166136261()
	{
		this.networkAudioSource.Play(0);
	}

	// Token: 0x06000872 RID: 2162 RVA: 0x0003C5F8 File Offset: 0x0003A7F8
	private void RpcReader___Server_AudioPlay_2166136261(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		this.RpcLogic___AudioPlay_2166136261();
	}

	// Token: 0x06000873 RID: 2163 RVA: 0x0003C62C File Offset: 0x0003A82C
	private void RpcWriter___Server_AudioStop_2166136261()
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendServerRpc(16U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000874 RID: 2164 RVA: 0x0003C720 File Offset: 0x0003A920
	private void RpcLogic___AudioStop_2166136261()
	{
		this.networkAudioSource.Stop();
		this.PlayReleaseClipObservers();
	}

	// Token: 0x06000875 RID: 2165 RVA: 0x0003C734 File Offset: 0x0003A934
	private void RpcReader___Server_AudioStop_2166136261(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		this.RpcLogic___AudioStop_2166136261();
	}

	// Token: 0x06000876 RID: 2166 RVA: 0x0003C768 File Offset: 0x0003A968
	private void RpcWriter___Server_RemoveAmmo_2166136261()
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendServerRpc(17U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000877 RID: 2167 RVA: 0x0002BFDC File Offset: 0x0002A1DC
	private void RpcLogic___RemoveAmmo_2166136261()
	{
		if (this.reloadWeapon)
		{
			this.chargedBullets -= 1f;
			return;
		}
		base.sync___set_value_currentAmmo(base.SyncAccessor_currentAmmo - 1, true);
	}

	// Token: 0x06000878 RID: 2168 RVA: 0x0003C85C File Offset: 0x0003AA5C
	private void RpcReader___Server_RemoveAmmo_2166136261(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___RemoveAmmo_2166136261();
	}

	// Token: 0x06000879 RID: 2169 RVA: 0x0003C89C File Offset: 0x0003AA9C
	private void RpcWriter___Server_GiveDamage_324487999(float damageToGive, PlayerHealth enemyHealth, string name)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteSingle(damageToGive, AutoPackType.Unpacked);
		writer.Write___PlayerHealthFishNet.Serializing.Generated(enemyHealth);
		writer.WriteString(name);
		base.SendServerRpc(18U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x0600087A RID: 2170 RVA: 0x0003C9BC File Offset: 0x0003ABBC
	private void RpcLogic___GiveDamage_324487999(float damageToGive, PlayerHealth enemyHealth, string name)
	{
		enemyHealth.sync___set_value_killer(this.rootObject.transform, true);
		enemyHealth.sync___set_value_health(enemyHealth.SyncAccessor_health - damageToGive, true);
		enemyHealth.KillCam();
		this.HitFeeback(enemyHealth);
		enemyHealth.Dismemberment(name);
	}

	// Token: 0x0600087B RID: 2171 RVA: 0x0003C9F4 File Offset: 0x0003ABF4
	private void RpcReader___Server_GiveDamage_324487999(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		float num = PooledReader0.ReadSingle(AutoPackType.Unpacked);
		PlayerHealth playerHealth = global::FishNet.Serializing.Generated.GeneratedReaders___Internal.Read___PlayerHealthFishNet.Serializing.Generateds(PooledReader0);
		string text = PooledReader0.ReadString();
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___GiveDamage_324487999(num, playerHealth, text);
	}

	// Token: 0x0600087C RID: 2172 RVA: 0x0003CA6C File Offset: 0x0003AC6C
	private void RpcWriter___Server_KillServer_1722911636(PlayerHealth enemyHealth)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.Write___PlayerHealthFishNet.Serializing.Generated(enemyHealth);
		base.SendServerRpc(19U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x0600087D RID: 2173 RVA: 0x0003CB70 File Offset: 0x0003AD70
	private void RpcLogic___KillServer_1722911636(PlayerHealth enemyHealth)
	{
		enemyHealth.sync___set_value_isShot(true, true);
		enemyHealth.sync___set_value_health(-8f, true);
		GameManager.Instance.PlayerDied(enemyHealth.SyncAccessor_playerValues.SyncAccessor_playerClient.PlayerId);
		if (this.rootObject != null)
		{
			enemyHealth.sync___set_value_killer(this.rootObject.transform, true);
		}
		this.KillObserver(enemyHealth.SyncAccessor_playerValues.SyncAccessor_playerClient.transform.GetComponent<NetworkObject>().Owner, enemyHealth.SyncAccessor_playerValues.SyncAccessor_playerClient, enemyHealth);
	}

	// Token: 0x0600087E RID: 2174 RVA: 0x0003CBF8 File Offset: 0x0003ADF8
	private void RpcReader___Server_KillServer_1722911636(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		PlayerHealth playerHealth = global::FishNet.Serializing.Generated.GeneratedReaders___Internal.Read___PlayerHealthFishNet.Serializing.Generateds(PooledReader0);
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		this.RpcLogic___KillServer_1722911636(playerHealth);
	}

	// Token: 0x0600087F RID: 2175 RVA: 0x0003CC3C File Offset: 0x0003AE3C
	private void RpcWriter___Target_KillObserver_123853379(NetworkConnection conn, ClientInstance client, PlayerHealth enemyHealth)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.Write___ClientInstanceFishNet.Serializing.Generated(client);
		writer.Write___PlayerHealthFishNet.Serializing.Generated(enemyHealth);
		base.SendTargetRpc(20U, writer, channel, DataOrderType.Default, conn, false, true);
		writer.Store();
	}

	// Token: 0x06000880 RID: 2176 RVA: 0x000275DA File Offset: 0x000257DA
	private void RpcLogic___KillObserver_123853379(NetworkConnection conn, ClientInstance client, PlayerHealth enemyHealth)
	{
		enemyHealth.shouldDropWeapon = true;
		enemyHealth.isDeadFromTargetRpc = true;
		if (this.rootObject != null)
		{
			GameObject.Find("Main Camera").GetComponent<KillCam>().enemy = this.rootObject.transform;
		}
	}

	// Token: 0x06000881 RID: 2177 RVA: 0x0003CD00 File Offset: 0x0003AF00
	private void RpcReader___Target_KillObserver_123853379(PooledReader PooledReader0, Channel channel)
	{
		ClientInstance clientInstance = global::FishNet.Serializing.Generated.GeneratedReaders___Internal.Read___ClientInstanceFishNet.Serializing.Generateds(PooledReader0);
		PlayerHealth playerHealth = global::FishNet.Serializing.Generated.GeneratedReaders___Internal.Read___PlayerHealthFishNet.Serializing.Generateds(PooledReader0);
		if (!base.IsClient)
		{
			return;
		}
		this.RpcLogic___KillObserver_123853379(base.LocalConnection, clientInstance, playerHealth);
	}

	// Token: 0x06000882 RID: 2178 RVA: 0x0003CD48 File Offset: 0x0003AF48
	private void RpcWriter___Observers_HitFeeback_1722911636(PlayerHealth enemyHealth)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.Write___PlayerHealthFishNet.Serializing.Generated(enemyHealth);
		base.SendObserversRpc(21U, writer, channel, DataOrderType.Default, false, false, false);
		writer.Store();
	}

	// Token: 0x06000883 RID: 2179 RVA: 0x00027716 File Offset: 0x00025916
	private void RpcLogic___HitFeeback_1722911636(PlayerHealth enemyHealth)
	{
		enemyHealth.HitFeeback();
	}

	// Token: 0x06000884 RID: 2180 RVA: 0x0003CE00 File Offset: 0x0003B000
	private void RpcReader___Observers_HitFeeback_1722911636(PooledReader PooledReader0, Channel channel)
	{
		PlayerHealth playerHealth = global::FishNet.Serializing.Generated.GeneratedReaders___Internal.Read___PlayerHealthFishNet.Serializing.Generateds(PooledReader0);
		if (!base.IsClient)
		{
			return;
		}
		this.RpcLogic___HitFeeback_1722911636(playerHealth);
	}

	// Token: 0x06000885 RID: 2181 RVA: 0x0003CE34 File Offset: 0x0003B034
	private void RpcWriter___Server_ShootServerEffect_2166136261()
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendServerRpc(22U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000886 RID: 2182 RVA: 0x0003CF28 File Offset: 0x0003B128
	private void RpcLogic___ShootServerEffect_2166136261()
	{
		this.ShootObserversEffect();
	}

	// Token: 0x06000887 RID: 2183 RVA: 0x0003CF30 File Offset: 0x0003B130
	private void RpcReader___Server_ShootServerEffect_2166136261(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___ShootServerEffect_2166136261();
	}

	// Token: 0x06000888 RID: 2184 RVA: 0x0003CF70 File Offset: 0x0003B170
	private void RpcWriter___Server_PlayReleaseClip_2166136261()
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendServerRpc(23U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000889 RID: 2185 RVA: 0x0003D064 File Offset: 0x0003B264
	private void RpcLogic___PlayReleaseClip_2166136261()
	{
		this.PlayReleaseClipObservers();
	}

	// Token: 0x0600088A RID: 2186 RVA: 0x0003D06C File Offset: 0x0003B26C
	private void RpcReader___Server_PlayReleaseClip_2166136261(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___PlayReleaseClip_2166136261();
	}

	// Token: 0x0600088B RID: 2187 RVA: 0x0003D0AC File Offset: 0x0003B2AC
	private void RpcWriter___Observers_PlayReleaseClipObservers_2166136261()
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendObserversRpc(24U, writer, channel, DataOrderType.Default, false, false, true);
		writer.Store();
	}

	// Token: 0x0600088C RID: 2188 RVA: 0x0003D155 File Offset: 0x0003B355
	private void RpcLogic___PlayReleaseClipObservers_2166136261()
	{
		this.audio.PlayOneShot(this.releaseClip);
	}

	// Token: 0x0600088D RID: 2189 RVA: 0x0003D168 File Offset: 0x0003B368
	private void RpcReader___Observers_PlayReleaseClipObservers_2166136261(PooledReader PooledReader0, Channel channel)
	{
		if (!base.IsClient)
		{
			return;
		}
		if (base.IsHost)
		{
			return;
		}
		this.RpcLogic___PlayReleaseClipObservers_2166136261();
	}

	// Token: 0x0600088E RID: 2190 RVA: 0x0003D194 File Offset: 0x0003B394
	private void RpcWriter___Server_ServerFX_3848837105(Vector3 position, Quaternion rotation)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteVector3(position);
		writer.WriteQuaternion(rotation, AutoPackType.Packed);
		base.SendServerRpc(25U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x0600088F RID: 2191 RVA: 0x0003D2A7 File Offset: 0x0003B4A7
	public void RpcLogic___ServerFX_3848837105(Vector3 position, Quaternion rotation)
	{
		this.ObserversFX(position, rotation);
	}

	// Token: 0x06000890 RID: 2192 RVA: 0x0003D2B4 File Offset: 0x0003B4B4
	private void RpcReader___Server_ServerFX_3848837105(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		Vector3 vector = PooledReader0.ReadVector3();
		Quaternion quaternion = PooledReader0.ReadQuaternion(AutoPackType.Packed);
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___ServerFX_3848837105(vector, quaternion);
	}

	// Token: 0x06000891 RID: 2193 RVA: 0x0003D31C File Offset: 0x0003B51C
	private void RpcWriter___Observers_ObserversFX_3848837105(Vector3 position, Quaternion rotation)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteVector3(position);
		writer.WriteQuaternion(rotation, AutoPackType.Packed);
		base.SendObserversRpc(26U, writer, channel, DataOrderType.Default, false, false, true);
		writer.Store();
	}

	// Token: 0x06000892 RID: 2194 RVA: 0x000272E4 File Offset: 0x000254E4
	private void RpcLogic___ObserversFX_3848837105(Vector3 position, Quaternion rotation)
	{
		global::UnityEngine.Object.Instantiate<GameObject>(this.bloodSplatter, position, rotation);
	}

	// Token: 0x06000893 RID: 2195 RVA: 0x0003D3E4 File Offset: 0x0003B5E4
	private void RpcReader___Observers_ObserversFX_3848837105(PooledReader PooledReader0, Channel channel)
	{
		Vector3 vector = PooledReader0.ReadVector3();
		Quaternion quaternion = PooledReader0.ReadQuaternion(AutoPackType.Packed);
		if (!base.IsClient)
		{
			return;
		}
		if (base.IsHost)
		{
			return;
		}
		this.RpcLogic___ObserversFX_3848837105(vector, quaternion);
	}

	// Token: 0x06000894 RID: 2196 RVA: 0x0003D438 File Offset: 0x0003B638
	private void RpcWriter___Observers_ShootObserversEffect_2166136261()
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendObserversRpc(27U, writer, channel, DataOrderType.Default, false, false, true);
		writer.Store();
	}

	// Token: 0x06000895 RID: 2197 RVA: 0x0003D4E4 File Offset: 0x0003B6E4
	private void RpcLogic___ShootObserversEffect_2166136261()
	{
		if (Settings.Instance.reduceVFX)
		{
			return;
		}
		this.audio.PlayOneShot(this.fireClip);
		GameObject gameObject = global::UnityEngine.Object.Instantiate<GameObject>(this.muzzleFlash, this.muzzleFlashPoint.position, this.shootPoint.rotation);
		foreach (Transform transform in gameObject.transform.GetComponentsInChildren<Transform>(true))
		{
			if (transform.GetComponent<Light>() == null && transform.tag != "vfx" && base.IsOwner)
			{
				transform.gameObject.layer = 8;
			}
			if (transform.GetComponent<Light>() != null)
			{
				transform.GetComponent<Light>().intensity = this.lightIntensity;
			}
		}
		ParticleSystem[] componentsInChildren2 = gameObject.GetComponentsInChildren<ParticleSystem>();
		for (int i = 0; i < componentsInChildren2.Length; i++)
		{
			componentsInChildren2[i].Play();
		}
	}

	// Token: 0x06000896 RID: 2198 RVA: 0x0003D5C4 File Offset: 0x0003B7C4
	private void RpcReader___Observers_ShootObserversEffect_2166136261(PooledReader PooledReader0, Channel channel)
	{
		if (!base.IsClient)
		{
			return;
		}
		if (base.IsHost)
		{
			return;
		}
		this.RpcLogic___ShootObserversEffect_2166136261();
	}

	// Token: 0x06000897 RID: 2199 RVA: 0x0003D5F0 File Offset: 0x0003B7F0
	private void RpcWriter___Server_SpawnBulletTrailServer_4276783012(Vector3 hitPoint)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteVector3(hitPoint);
		base.SendServerRpc(28U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000898 RID: 2200 RVA: 0x0003D6F1 File Offset: 0x0003B8F1
	private void RpcLogic___SpawnBulletTrailServer_4276783012(Vector3 hitPoint)
	{
		this.SpawnBulletTrail(hitPoint);
	}

	// Token: 0x06000899 RID: 2201 RVA: 0x0003D6FC File Offset: 0x0003B8FC
	private void RpcReader___Server_SpawnBulletTrailServer_4276783012(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		Vector3 vector = PooledReader0.ReadVector3();
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___SpawnBulletTrailServer_4276783012(vector);
	}

	// Token: 0x0600089A RID: 2202 RVA: 0x0003D74C File Offset: 0x0003B94C
	private void RpcWriter___Observers_SpawnBulletTrail_4276783012(Vector3 hitPoint)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteVector3(hitPoint);
		base.SendObserversRpc(29U, writer, channel, DataOrderType.Default, false, false, true);
		writer.Store();
	}

	// Token: 0x0600089B RID: 2203 RVA: 0x0003D804 File Offset: 0x0003BA04
	private void RpcLogic___SpawnBulletTrail_4276783012(Vector3 hitPoint)
	{
		GameObject gameObject = global::UnityEngine.Object.Instantiate<GameObject>(this.bulletTrailLocal.gameObject, this.shootPoint.position, Quaternion.identity);
		LineRenderer component = gameObject.GetComponent<LineRenderer>();
		component.SetPosition(0, this.shootPoint.position);
		component.SetPosition(1, hitPoint);
		global::UnityEngine.Object.Destroy(gameObject, 0.4f);
	}

	// Token: 0x0600089C RID: 2204 RVA: 0x0003D85C File Offset: 0x0003BA5C
	private void RpcReader___Observers_SpawnBulletTrail_4276783012(PooledReader PooledReader0, Channel channel)
	{
		Vector3 vector = PooledReader0.ReadVector3();
		if (!base.IsClient)
		{
			return;
		}
		if (base.IsHost)
		{
			return;
		}
		this.RpcLogic___SpawnBulletTrail_4276783012(vector);
	}

	// Token: 0x0600089D RID: 2205 RVA: 0x0003D898 File Offset: 0x0003BA98
	private void RpcWriter___Server_SpawnVFXServer_606331033(int index, Vector3 hitPoint, Vector3 hitNormal, string surface, Transform parent)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteInt32(index, AutoPackType.Packed);
		writer.WriteVector3(hitPoint);
		writer.WriteVector3(hitNormal);
		writer.WriteString(surface);
		writer.WriteTransform(parent);
		base.SendServerRpc(30U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x0600089E RID: 2206 RVA: 0x0003D9D2 File Offset: 0x0003BBD2
	private void RpcLogic___SpawnVFXServer_606331033(int index, Vector3 hitPoint, Vector3 hitNormal, string surface, Transform parent)
	{
		this.SpawnVFX(index, hitPoint, hitNormal, surface, parent);
	}

	// Token: 0x0600089F RID: 2207 RVA: 0x0003D9E4 File Offset: 0x0003BBE4
	private void RpcReader___Server_SpawnVFXServer_606331033(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		int num = PooledReader0.ReadInt32(AutoPackType.Packed);
		Vector3 vector = PooledReader0.ReadVector3();
		Vector3 vector2 = PooledReader0.ReadVector3();
		string text = PooledReader0.ReadString();
		Transform transform = PooledReader0.ReadTransform();
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___SpawnVFXServer_606331033(num, vector, vector2, text, transform);
	}

	// Token: 0x060008A0 RID: 2208 RVA: 0x0003DA7C File Offset: 0x0003BC7C
	private void RpcWriter___Observers_SpawnVFX_606331033(int index, Vector3 hitPoint, Vector3 hitNormal, string surface, Transform parent)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteInt32(index, AutoPackType.Packed);
		writer.WriteVector3(hitPoint);
		writer.WriteVector3(hitNormal);
		writer.WriteString(surface);
		writer.WriteTransform(parent);
		base.SendObserversRpc(31U, writer, channel, DataOrderType.Default, false, false, true);
		writer.Store();
	}

	// Token: 0x060008A1 RID: 2209 RVA: 0x0003DB6C File Offset: 0x0003BD6C
	private void RpcLogic___SpawnVFX_606331033(int index, Vector3 hitPoint, Vector3 hitNormal, string surface, Transform parent)
	{
		if (Settings.Instance.reduceVFX)
		{
			return;
		}
		if (this.genericImpact)
		{
			global::UnityEngine.Object.Instantiate<GameObject>(this.genericImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
		}
		uint num = <PrivateImplementationDetails>.ComputeStringHash(surface);
		if (num <= 1825421690U)
		{
			if (num <= 1378315797U)
			{
				if (num <= 464173256U)
				{
					if (num != 456440475U)
					{
						if (num == 464173256U)
						{
							if (surface == "Footsteps/Concrete/Dalles")
							{
								if (index == 0)
								{
									global::UnityEngine.Object.Instantiate<GameObject>(this.concreteHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
								}
								if (index == 1)
								{
									global::UnityEngine.Object.Instantiate<GameObject>(this.bulletHole, hitPoint, Quaternion.LookRotation(hitNormal), parent);
									goto IL_06E3;
								}
								goto IL_06E3;
							}
						}
					}
					else if (surface == "Footsteps/Sand")
					{
						if (index == 0)
						{
							global::UnityEngine.Object.Instantiate<GameObject>(this.sandHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
						}
						if (index == 1)
						{
							global::UnityEngine.Object.Instantiate<GameObject>(this.sandHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
							goto IL_06E3;
						}
						goto IL_06E3;
					}
				}
				else if (num != 913360285U)
				{
					if (num != 977466297U)
					{
						if (num == 1378315797U)
						{
							if (surface == "Footsteps/Moquette")
							{
								if (index == 0)
								{
									global::UnityEngine.Object.Instantiate<GameObject>(this.softbodyHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
								}
								if (index == 1)
								{
									global::UnityEngine.Object.Instantiate<GameObject>(this.softbodyHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
									goto IL_06E3;
								}
								goto IL_06E3;
							}
						}
					}
					else if (surface == "Footsteps/Concrete/Solide")
					{
						if (index == 0)
						{
							global::UnityEngine.Object.Instantiate<GameObject>(this.concreteHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
						}
						if (index == 1)
						{
							global::UnityEngine.Object.Instantiate<GameObject>(this.bulletHole, hitPoint, Quaternion.LookRotation(hitNormal), parent);
							goto IL_06E3;
						}
						goto IL_06E3;
					}
				}
				else if (surface == "Grenade")
				{
					goto IL_06E3;
				}
			}
			else if (num <= 1430892386U)
			{
				if (num != 1429664136U)
				{
					if (num == 1430892386U)
					{
						if (surface == "Hat")
						{
							goto IL_06E3;
						}
					}
				}
				else if (surface == "Footsteps/Water")
				{
					if (index == 0)
					{
						global::UnityEngine.Object.Instantiate<GameObject>(this.waterHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
					}
					if (index == 1)
					{
						global::UnityEngine.Object.Instantiate<GameObject>(this.waterHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
						goto IL_06E3;
					}
					goto IL_06E3;
				}
			}
			else if (num != 1610969363U)
			{
				if (num != 1624496828U)
				{
					if (num == 1825421690U)
					{
						if (surface == "Footsteps/Wood/Creux")
						{
							if (index == 0)
							{
								global::UnityEngine.Object.Instantiate<GameObject>(this.woodHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
							}
							if (index == 1)
							{
								global::UnityEngine.Object.Instantiate<GameObject>(this.woodHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
								goto IL_06E3;
							}
							goto IL_06E3;
						}
					}
				}
				else if (surface == "Footsteps/Concrete/Default")
				{
					if (index == 0)
					{
						global::UnityEngine.Object.Instantiate<GameObject>(this.concreteHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
					}
					if (index == 1)
					{
						global::UnityEngine.Object.Instantiate<GameObject>(this.bulletHole, hitPoint, Quaternion.LookRotation(hitNormal), parent);
						goto IL_06E3;
					}
					goto IL_06E3;
				}
			}
			else if (surface == "Footsteps/Concrete/PleinAir")
			{
				if (index == 0)
				{
					global::UnityEngine.Object.Instantiate<GameObject>(this.concreteHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
				}
				if (index == 1)
				{
					global::UnityEngine.Object.Instantiate<GameObject>(this.bulletHole, hitPoint, Quaternion.LookRotation(hitNormal), parent);
					goto IL_06E3;
				}
				goto IL_06E3;
			}
		}
		else if (num <= 2833365437U)
		{
			if (num <= 2180110808U)
			{
				if (num != 1954265137U)
				{
					if (num == 2180110808U)
					{
						if (surface == "Footsteps/Dirt")
						{
							if (index == 0)
							{
								global::UnityEngine.Object.Instantiate<GameObject>(this.dirtHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
							}
							if (index == 1)
							{
								global::UnityEngine.Object.Instantiate<GameObject>(this.dirtHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
								goto IL_06E3;
							}
							goto IL_06E3;
						}
					}
				}
				else if (surface == "NoSound")
				{
					goto IL_06E3;
				}
			}
			else if (num != 2251139421U)
			{
				if (num != 2400559108U)
				{
					if (num == 2833365437U)
					{
						if (surface == "Footsteps/Metal/Pipe2")
						{
							if (index == 0)
							{
								global::UnityEngine.Object.Instantiate<GameObject>(this.metalHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
							}
							if (index == 1)
							{
								global::UnityEngine.Object.Instantiate<GameObject>(this.metalHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
								goto IL_06E3;
							}
							goto IL_06E3;
						}
					}
				}
				else if (surface == "Footsteps/Matelas")
				{
					if (index == 0)
					{
						global::UnityEngine.Object.Instantiate<GameObject>(this.softbodyHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
					}
					if (index == 1)
					{
						global::UnityEngine.Object.Instantiate<GameObject>(this.softbodyHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
						goto IL_06E3;
					}
					goto IL_06E3;
				}
			}
			else if (surface == "Footsteps/Metal/Pipe")
			{
				if (index == 0)
				{
					global::UnityEngine.Object.Instantiate<GameObject>(this.tauleHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
				}
				if (index == 1)
				{
					global::UnityEngine.Object.Instantiate<GameObject>(this.tauleHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
					goto IL_06E3;
				}
				goto IL_06E3;
			}
		}
		else if (num <= 3455850406U)
		{
			if (num != 3075599786U)
			{
				if (num == 3455850406U)
				{
					if (surface == "Footsteps/Graviers")
					{
						if (index == 0)
						{
							global::UnityEngine.Object.Instantiate<GameObject>(this.concreteHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
						}
						if (index == 1)
						{
							global::UnityEngine.Object.Instantiate<GameObject>(this.bulletHole, hitPoint, Quaternion.LookRotation(hitNormal), parent);
							goto IL_06E3;
						}
						goto IL_06E3;
					}
				}
			}
			else if (surface == "Footsteps/Wood/Sec")
			{
				if (index == 0)
				{
					global::UnityEngine.Object.Instantiate<GameObject>(this.woodHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
				}
				if (index == 1)
				{
					global::UnityEngine.Object.Instantiate<GameObject>(this.woodHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
					goto IL_06E3;
				}
				goto IL_06E3;
			}
		}
		else if (num != 3492154005U)
		{
			if (num != 3807801418U)
			{
				if (num == 3848897750U)
				{
					if (surface == "Mine")
					{
						goto IL_06E3;
					}
				}
			}
			else if (surface == "Footsteps/Metal/Grille")
			{
				if (index == 0)
				{
					global::UnityEngine.Object.Instantiate<GameObject>(this.tauleHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
				}
				if (index == 1)
				{
					global::UnityEngine.Object.Instantiate<GameObject>(this.tauleHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
					goto IL_06E3;
				}
				goto IL_06E3;
			}
		}
		else if (surface == "Footsteps/Grass")
		{
			if (index == 0)
			{
				global::UnityEngine.Object.Instantiate<GameObject>(this.dirtHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
			}
			if (index == 1)
			{
				global::UnityEngine.Object.Instantiate<GameObject>(this.dirtHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
				goto IL_06E3;
			}
			goto IL_06E3;
		}
		if (index == 0)
		{
			global::UnityEngine.Object.Instantiate<GameObject>(this.concreteHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
		}
		if (index == 1)
		{
			global::UnityEngine.Object.Instantiate<GameObject>(this.bulletHole, hitPoint, Quaternion.LookRotation(hitNormal), parent);
		}
		IL_06E3:
		if (index == 2)
		{
			global::UnityEngine.Object.Instantiate<GameObject>(this.bodyImpact, hitPoint, Quaternion.identity, parent);
		}
	}

	// Token: 0x060008A2 RID: 2210 RVA: 0x0003E274 File Offset: 0x0003C474
	private void RpcReader___Observers_SpawnVFX_606331033(PooledReader PooledReader0, Channel channel)
	{
		int num = PooledReader0.ReadInt32(AutoPackType.Packed);
		Vector3 vector = PooledReader0.ReadVector3();
		Vector3 vector2 = PooledReader0.ReadVector3();
		string text = PooledReader0.ReadString();
		Transform transform = PooledReader0.ReadTransform();
		if (!base.IsClient)
		{
			return;
		}
		if (base.IsHost)
		{
			return;
		}
		this.RpcLogic___SpawnVFX_606331033(num, vector, vector2, text, transform);
	}

	// Token: 0x060008A3 RID: 2211 RVA: 0x0003E2F8 File Offset: 0x0003C4F8
	private void RpcWriter___Server_SupressionServer_3068987916(Transform supp)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteTransform(supp);
		base.SendServerRpc(32U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x060008A4 RID: 2212 RVA: 0x0003E3F9 File Offset: 0x0003C5F9
	private void RpcLogic___SupressionServer_3068987916(Transform supp)
	{
		this.SuppressionTarget(supp);
	}

	// Token: 0x060008A5 RID: 2213 RVA: 0x0003E404 File Offset: 0x0003C604
	private void RpcReader___Server_SupressionServer_3068987916(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		Transform transform = PooledReader0.ReadTransform();
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		this.RpcLogic___SupressionServer_3068987916(transform);
	}

	// Token: 0x060008A6 RID: 2214 RVA: 0x0003E448 File Offset: 0x0003C648
	private void RpcWriter___Observers_SuppressionTarget_3068987916(Transform supp)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteTransform(supp);
		base.SendObserversRpc(33U, writer, channel, DataOrderType.Default, false, false, false);
		writer.Store();
	}

	// Token: 0x060008A7 RID: 2215 RVA: 0x00027B2A File Offset: 0x00025D2A
	private void RpcLogic___SuppressionTarget_3068987916(Transform supp)
	{
		supp.GetComponent<Suppression>().SuppressionTrigger();
	}

	// Token: 0x060008A8 RID: 2216 RVA: 0x0003E500 File Offset: 0x0003C700
	private void RpcReader___Observers_SuppressionTarget_3068987916(PooledReader PooledReader0, Channel channel)
	{
		Transform transform = PooledReader0.ReadTransform();
		if (!base.IsClient)
		{
			return;
		}
		this.RpcLogic___SuppressionTarget_3068987916(transform);
	}

	// Token: 0x060008A9 RID: 2217 RVA: 0x0003E531 File Offset: 0x0003C731
	public override void Awake()
	{
		this.NetworkInitialize___Early();
		base.Awake();
		this.NetworkInitialize__Late();
	}

	// Token: 0x060008AA RID: 2218 RVA: 0x000023D6 File Offset: 0x000005D6
	public override void Awake___UserLogic()
	{
	}

	// Token: 0x0400075B RID: 1883
	[Header("Weapon Specials")]
	[SerializeField]
	private float reloadTime;

	// Token: 0x0400075C RID: 1884
	[SerializeField]
	private AudioClip reloadClip;

	// Token: 0x0400075D RID: 1885
	[SerializeField]
	private AudioClip releaseClip;

	// Token: 0x0400075E RID: 1886
	[SerializeField]
	private float timeBeforeShooting = 0.6f;

	// Token: 0x0400075F RID: 1887
	[SerializeField]
	private Transform rotativePart;

	// Token: 0x04000760 RID: 1888
	[SerializeField]
	private float rotationSpeed = 1000f;

	// Token: 0x04000761 RID: 1889
	private float fireTimer;

	// Token: 0x04000762 RID: 1890
	private bool touched;

	// Token: 0x04000763 RID: 1891
	private Vector3 spread;

	// Token: 0x04000764 RID: 1892
	private float shootTimer;

	// Token: 0x04000765 RID: 1893
	private bool pressed = true;

	// Token: 0x04000766 RID: 1894
	private bool released = true;

	// Token: 0x04000767 RID: 1895
	private bool noammoBool;

	// Token: 0x04000768 RID: 1896
	private NetworkAudioSource networkAudioSource;

	// Token: 0x04000769 RID: 1897
	private bool hitOK;

	// Token: 0x0400076A RID: 1898
	private PlayerHealth enemyHealth;

	// Token: 0x0400076B RID: 1899
	private bool NetworkInitializeEarly_Minigun_Assembly-CSharp.dll;

	// Token: 0x0400076C RID: 1900
	private bool NetworkInitializeLate_Minigun_Assembly-CSharp.dll;
}

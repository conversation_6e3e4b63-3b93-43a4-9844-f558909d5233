﻿using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Text;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using TMPro;
using UnityEngine;
using UnityEngine.SceneManagement;
using UnityEngine.Serialization;
using UnityEngine.UI;

// Token: 0x020000DA RID: 218
public class MapsManager : MonoBehaviour, ISaveable
{
	// Token: 0x06000BB6 RID: 2998 RVA: 0x00052014 File Offset: 0x00050214
	private void Awake()
	{
		if (MapsManager.Instance == null)
		{
			MapsManager.Instance = this;
		}
		else if (MapsManager.Instance != this)
		{
			global::UnityEngine.Object.Destroy(base.gameObject);
		}
		if (MapsManager.Presets == null)
		{
			MapsManager.Presets = Resources.LoadAll<PlaylistPreset>("PlaylistPresets");
		}
	}

	// Token: 0x06000BB7 RID: 2999 RVA: 0x00052064 File Offset: 0x00050264
	private void Start()
	{
		this.ProgressManager = ProgressManager.Instance;
		this.InitMaps();
		SaveLoadSystem.Instance.Load();
		this.ChangePicture(null, "");
		if (this.Playlists.Count == 0)
		{
			this.AddPresetPlaylists();
		}
		this.UpdatePlaylists(false);
	}

	// Token: 0x06000BB8 RID: 3000 RVA: 0x000520B4 File Offset: 0x000502B4
	public void InitMaps()
	{
		List<string> list = new List<string>();
		List<string> list2 = new List<string>();
		foreach (ProgressInstance progressInstance in this.ProgressManager.instances)
		{
			if (progressInstance.dlcExlusive)
			{
				list2.AddRange(progressInstance.maps);
			}
			if (!progressInstance.unlocked)
			{
				list.AddRange(progressInstance.maps);
			}
		}
		this.allMaps = new Map[SceneManager.sceneCountInBuildSettings - 6];
		for (int j = 0; j < this.allMaps.Length; j++)
		{
			string fileNameWithoutExtension = Path.GetFileNameWithoutExtension(SceneUtility.GetScenePathByBuildIndex(j + 6));
			bool flag = fileNameWithoutExtension.ToLower().EndsWith("_alt");
			Map map = new Map
			{
				index = j,
				mapName = fileNameWithoutExtension,
				isDlcExclusive = (list2.Contains(fileNameWithoutExtension) || flag),
				isAltMap = flag,
				isSelected = false,
				isUnlocked = !list.Contains(fileNameWithoutExtension),
				mapInstance = null
			};
			if (!SteamLobby.ownDlc0 && map.isDlcExclusive)
			{
				map.isUnlocked = false;
			}
			this.allMaps[j] = map;
			this.allMapsDict.Add(map.mapName, map);
			GameObject gameObject = global::UnityEngine.Object.Instantiate<GameObject>(this.mapInstance, this.standardMapParent.position, Quaternion.identity, this.standardMapParent);
			map.mapInstance = gameObject.GetComponent<MapInstance>();
			map.mapInstance.name = map.mapName;
			map.mapInstance.selected = map.isSelected;
			gameObject.SetActive(map.isUnlocked);
		}
		this.SortMapsFromMapInstanceName();
		List<int> list3 = new List<int>();
		foreach (Map map2 in this.allMaps)
		{
			if (map2.isUnlocked)
			{
				list3.Add(map2.index);
			}
		}
		this.unlockedMaps = list3.ToArray();
	}

	// Token: 0x06000BB9 RID: 3001 RVA: 0x000522AC File Offset: 0x000504AC
	public void UpdateUnlockedMaps()
	{
		List<string> list = new List<string>();
		foreach (ProgressInstance progressInstance in this.ProgressManager.instances)
		{
			if (!SteamLobby.ownDlc0 && progressInstance.dlcExlusive)
			{
				list.AddRange(progressInstance.maps);
			}
			if (!progressInstance.unlocked)
			{
				list.AddRange(progressInstance.maps);
			}
		}
		List<int> list2 = new List<int>();
		for (int j = 0; j < this.allMaps.Length; j++)
		{
			Map map = this.allMaps[j];
			map.isUnlocked = !list.Contains(map.mapName);
			map.mapInstance.gameObject.SetActive(map.isUnlocked);
			if (map.isUnlocked)
			{
				list2.Add(map.index);
			}
		}
		this.unlockedMaps = list2.ToArray();
	}

	// Token: 0x06000BBA RID: 3002 RVA: 0x0005238A File Offset: 0x0005058A
	public void OpenFirstPlaylist()
	{
		if (this.playlistParent.childCount > 0 && this.selectedMapParent.childCount == 0)
		{
			this.playlistParent.GetComponentsInChildren<PlaylistInstance>()[0].OpenPlaylist();
		}
	}

	// Token: 0x06000BBB RID: 3003 RVA: 0x000523B9 File Offset: 0x000505B9
	public void AddPlaylist(string[] tempPlaylist, string n)
	{
		this.Playlists.Add(new Playlist(n, tempPlaylist, false));
		this.UpdatePlaylists(true);
	}

	// Token: 0x06000BBC RID: 3004 RVA: 0x000523D8 File Offset: 0x000505D8
	public void DupePlaylist(int i)
	{
		Playlist playlist = this.Playlists[i];
		Playlist playlist2 = new Playlist(playlist.Name + " - copy", playlist.Maps, false);
		this.Playlists.Add(playlist2);
		this.UpdatePlaylists(true);
	}

	// Token: 0x06000BBD RID: 3005 RVA: 0x00052422 File Offset: 0x00050622
	public void ResetActivePlaylist()
	{
		this.activePlaylistIndex = -1;
		this.SetActivePlaylist(this.activePlaylistIndex);
	}

	// Token: 0x06000BBE RID: 3006 RVA: 0x00052438 File Offset: 0x00050638
	public void SetActivePlaylistFromSelectedItem()
	{
		if (this.selectedPlaylistIndex >= this.Playlists.Count || this.selectedPlaylistIndex < 0)
		{
			PauseManager.Instance.WriteOfflineLog("Please open a playlist");
			return;
		}
		this.activePlaylistIndex = this.selectedPlaylistIndex;
		this.SetActivePlaylist(this.activePlaylistIndex);
		SaveLoadSystem.Instance.Save();
	}

	// Token: 0x06000BBF RID: 3007 RVA: 0x00052494 File Offset: 0x00050694
	public void SetActivePlaylist(int index)
	{
		if (index >= this.Playlists.Count)
		{
			index = -1;
		}
		if (index < 0)
		{
			this.activePlaylistText.text = "all maps";
		}
		else
		{
			this.activePlaylistText.text = this.Playlists[index].Name;
		}
		if (SceneMotor.Instance)
		{
			this.LoadActivePlaylist();
		}
	}

	// Token: 0x06000BC0 RID: 3008 RVA: 0x000524F6 File Offset: 0x000506F6
	public void LoadActivePlaylist()
	{
		if (this.activePlaylistIndex < 0 || this.activePlaylistIndex >= this.Playlists.Count)
		{
			return;
		}
		MapSelection.Instance.LoadScenes(this.Playlists[this.activePlaylistIndex].Maps);
	}

	// Token: 0x06000BC1 RID: 3009 RVA: 0x00052538 File Offset: 0x00050738
	public void AddPlaylistWithoutUpdate(string[] tempPlaylist, string n)
	{
		Playlist playlist = new Playlist(n, tempPlaylist, false);
		this.Playlists.Add(playlist);
	}

	// Token: 0x06000BC2 RID: 3010 RVA: 0x0005255A File Offset: 0x0005075A
	public void RemovePlaylist(int i)
	{
		this.Playlists.RemoveAt(i);
		this.UpdatePlaylists(false);
	}

	// Token: 0x06000BC3 RID: 3011 RVA: 0x00052570 File Offset: 0x00050770
	public void CreatePlaylist()
	{
		Playlist playlist = new Playlist("Playlist n°" + (this.Playlists.Count + 1).ToString(), Array.Empty<string>(), false);
		this.Playlists.Add(playlist);
		this.UpdatePlaylists(true);
	}

	// Token: 0x06000BC4 RID: 3012 RVA: 0x000525BC File Offset: 0x000507BC
	public void UpdateLoadLists()
	{
		int childCount = this.playListLoadButtonParent.childCount;
		for (int i = 0; i < childCount; i++)
		{
			global::UnityEngine.Object.Destroy(this.playListLoadButtonParent.GetChild(i).gameObject);
		}
		for (int j = 0; j < this.Playlists.Count; j++)
		{
			LoadListInstance component = global::UnityEngine.Object.Instantiate<GameObject>(this.loadPlayListInstance, this.playListLoadButtonParent.position, Quaternion.identity, this.playListLoadButtonParent).GetComponent<LoadListInstance>();
			component.maps = this.Playlists[j].Maps.ToList<string>();
			component.name = this.Playlists[j].Name;
			component.index = j;
		}
	}

	// Token: 0x06000BC5 RID: 3013 RVA: 0x0005266C File Offset: 0x0005086C
	public void UpdatePlaylists(bool hasCreatedNewPlaylist)
	{
		int childCount = this.playlistParent.childCount;
		for (int i = 0; i < childCount; i++)
		{
			global::UnityEngine.Object.Destroy(this.playlistParent.GetChild(i).gameObject);
		}
		for (int j = 0; j < this.Playlists.Count; j++)
		{
			PlaylistInstance component = global::UnityEngine.Object.Instantiate<GameObject>(this.playlistInstance, this.playlistParent.position, Quaternion.identity, this.playlistParent).GetComponent<PlaylistInstance>();
			component.name = this.Playlists[j].Name;
			component.index = j;
			if (hasCreatedNewPlaylist && j == this.Playlists.Count - 1)
			{
				this.scrollRect.verticalNormalizedPosition = 0f;
				this.scrollRect.verticalNormalizedPosition = 0f;
				component.GetComponent<PlaylistInstance>().button.Select();
				component.GetComponent<PlaylistInstance>().OpenPlaylist();
			}
		}
		SaveLoadSystem.Instance.Save();
	}

	// Token: 0x06000BC6 RID: 3014 RVA: 0x00052760 File Offset: 0x00050960
	public void PopulateMapsFromPlayList(int playListIndex)
	{
		this.selectedMaps.Clear();
		foreach (Map map in this.allMaps)
		{
			map.isSelected = false;
			map.mapInstance.selected = false;
			map.mapInstance.UpdateUI();
		}
		foreach (string text in this.Playlists[playListIndex].Maps)
		{
			Map map2;
			if (this.allMapsDict.TryGetValue(text, out map2) && (SteamLobby.ownDlc0 || !map2.isDlcExclusive) && map2.isUnlocked)
			{
				map2.isSelected = true;
				map2.mapInstance.selected = true;
				map2.mapInstance.UpdateUI();
				this.selectedMaps.Add(map2.index);
			}
		}
		this.SortMapsFromMapInstanceName();
	}

	// Token: 0x06000BC7 RID: 3015 RVA: 0x00052834 File Offset: 0x00050A34
	public void ChangeMapsState(string mapName)
	{
		Map map = this.allMapsDict[mapName];
		map.isSelected = !map.isSelected;
		map.mapInstance.selected = map.isSelected;
		if (map.isSelected)
		{
			this.selectedMaps.Add(map.index);
		}
		else
		{
			this.selectedMaps.Remove(map.index);
		}
		this.SortMapsFromMapInstanceName();
		this.Playlists[this.selectedPlaylistIndex].Maps = this.selectedMaps.Select((int i) => this.allMaps[i].mapName).ToArray<string>();
		SaveLoadSystem.Instance.Save();
	}

	// Token: 0x06000BC8 RID: 3016 RVA: 0x000528E0 File Offset: 0x00050AE0
	public void ChangePicture(Texture2D sprite, string txt)
	{
		if (sprite == null)
		{
			this.mapPreviewImage.enabled = false;
			this.mapText.text = "";
			return;
		}
		this.mapPreviewImage.enabled = true;
		this.mapPreviewImage.texture = sprite;
		this.mapText.text = txt;
	}

	// Token: 0x06000BC9 RID: 3017 RVA: 0x00052938 File Offset: 0x00050B38
	private void Update()
	{
		if (!PauseManager.Instance.inMainMenu)
		{
			this.MenuTrigger = true;
		}
		if (PauseManager.Instance.inMainMenu && this.MenuTrigger && !this.inExplorationMap)
		{
			this.inExplorationMap = false;
			this.MenuTrigger = false;
			this.ReturnToMenu();
		}
	}

	// Token: 0x06000BCA RID: 3018 RVA: 0x00052988 File Offset: 0x00050B88
	private void ReturnToMenu()
	{
		this.UpdatePlaylists(false);
	}

	// Token: 0x06000BCB RID: 3019 RVA: 0x00052994 File Offset: 0x00050B94
	public void SortMapsFromMapInstanceName()
	{
		List<MapInstance> list = new List<MapInstance>();
		List<MapInstance> list2 = new List<MapInstance>();
		List<MapInstance> list3 = new List<MapInstance>();
		List<MapInstance> list4 = new List<MapInstance>();
		List<Map> list5 = this.allMaps.OrderBy((Map map) => map.mapName).ToList<Map>();
		string text = this.selectedMapsSearchText.text.ToLower();
		string text2 = this.unselectedMapsSearchInput.text.ToLower();
		foreach (Map map2 in list5)
		{
			if (map2.isSelected)
			{
				list4.Add(map2.mapInstance);
				if (map2.isUnlocked)
				{
					bool flag = text == "" || map2.mapName.ToLower().Contains(text);
					map2.mapInstance.gameObject.SetActive(flag);
				}
			}
			else
			{
				if (map2.isAltMap)
				{
					list2.Add(map2.mapInstance);
				}
				else if (map2.isDlcExclusive)
				{
					list3.Add(map2.mapInstance);
				}
				else
				{
					list.Add(map2.mapInstance);
				}
				if (map2.isUnlocked)
				{
					bool flag2 = text2 == "" || map2.mapName.ToLower().Contains(text2);
					map2.mapInstance.gameObject.SetActive(flag2);
				}
			}
		}
		for (int i = 0; i < list.Count; i++)
		{
			MapInstance mapInstance = list[i];
			mapInstance.transform.SetParent(this.standardMapParent);
			mapInstance.transform.SetSiblingIndex(i);
		}
		for (int j = 0; j < list2.Count; j++)
		{
			MapInstance mapInstance2 = list2[j];
			mapInstance2.transform.SetParent(this.altMapParent);
			mapInstance2.transform.SetSiblingIndex(j);
		}
		for (int k = 0; k < list3.Count; k++)
		{
			MapInstance mapInstance3 = list3[k];
			mapInstance3.transform.SetParent(this.dlcMapParent);
			mapInstance3.transform.SetSiblingIndex(k);
		}
		for (int l = 0; l < list4.Count; l++)
		{
			MapInstance mapInstance4 = list4[l];
			mapInstance4.transform.SetParent(this.selectedMapParent);
			mapInstance4.transform.SetSiblingIndex(l);
		}
	}

	// Token: 0x06000BCC RID: 3020 RVA: 0x00052C14 File Offset: 0x00050E14
	public void ExportSelectedPlaylistToClipboard()
	{
		Playlist playlist = this.Playlists[this.selectedPlaylistIndex];
		string name = playlist.Name;
		string[] maps = playlist.Maps;
		JObject jobject = new JObject();
		jobject.Add("name", name);
		string text = "maps";
		object[] array = maps;
		jobject.Add(text, new JArray(array));
		jobject.Add("type", "playlist");
		string text2 = jobject.ToString(Formatting.None, Array.Empty<JsonConverter>());
		byte[] bytes = Encoding.UTF8.GetBytes(text2);
		string text3;
		using (MemoryStream memoryStream = new MemoryStream())
		{
			using (GZipStream gzipStream = new GZipStream(memoryStream, CompressionLevel.Optimal))
			{
				gzipStream.Write(bytes, 0, bytes.Length);
			}
			text3 = Convert.ToBase64String(memoryStream.ToArray());
		}
		GUIUtility.systemCopyBuffer = text3;
		PauseManager.Instance.WriteOfflineLog("Exported " + name + " to clipboard");
	}

	// Token: 0x06000BCD RID: 3021 RVA: 0x00052D1C File Offset: 0x00050F1C
	public void ImportPlaylistFromClipboard()
	{
		string systemCopyBuffer = GUIUtility.systemCopyBuffer;
		if (string.IsNullOrEmpty(systemCopyBuffer))
		{
			PauseManager.Instance.WriteOfflineLog("Clipboard is empty");
			return;
		}
		string text;
		try
		{
			using (MemoryStream memoryStream = new MemoryStream(Convert.FromBase64String(systemCopyBuffer)))
			{
				using (GZipStream gzipStream = new GZipStream(memoryStream, CompressionMode.Decompress))
				{
					using (StreamReader streamReader = new StreamReader(gzipStream))
					{
						text = streamReader.ReadToEnd();
					}
				}
			}
		}
		catch (Exception ex)
		{
			Debug.LogError("MapsManager: Failed to decompress clipboard text: " + ex.Message);
			PauseManager.Instance.WriteOfflineLog("Failed to decompress playlist from the clipboard.");
			return;
		}
		JObject jobject;
		try
		{
			jobject = JObject.Parse(text);
		}
		catch (Exception ex2)
		{
			Debug.LogError("MapsManager: Failed to parse clipboard text as JSON: " + ex2.Message);
			PauseManager.Instance.WriteOfflineLog("Failed to parse playlist from the clipboard.");
			return;
		}
		JToken jtoken;
		if (!jobject.TryGetValue("type", out jtoken))
		{
			Debug.LogError("MapsManager: Clipboard JSON does not contain 'type' key.");
			PauseManager.Instance.WriteOfflineLog("Invalid playlist format in clipboard.");
			return;
		}
		if (jtoken.ToString() != "playlist")
		{
			Debug.LogError("MapsManager: Clipboard JSON 'type' is not 'playlist'.");
			PauseManager.Instance.WriteOfflineLog("This is not a playlist!");
			return;
		}
		JToken jtoken2;
		JToken jtoken3;
		if (!jobject.TryGetValue("name", out jtoken2) || !jobject.TryGetValue("maps", out jtoken3))
		{
			Debug.LogError("MapsManager: Clipboard JSON does not contain 'name' or 'maps' keys.");
			PauseManager.Instance.WriteOfflineLog("Invalid playlist format in clipboard, missing a name or maps.");
			return;
		}
		string text2 = jtoken2.ToString();
		JArray jarray = jtoken3 as JArray;
		if (jarray == null || jarray.Count == 0)
		{
			Debug.LogError("MapsManager: 'maps' key is not an array or is empty.");
			PauseManager.Instance.WriteOfflineLog("Invalid playlist format in clipboard.");
			return;
		}
		string[] array = jarray.Select((JToken mapName) => mapName.ToString()).ToArray<string>();
		this.AddPlaylist(array, text2);
	}

	// Token: 0x06000BCE RID: 3022 RVA: 0x00052F3C File Offset: 0x0005113C
	public object SaveState()
	{
		List<string[]> list = new List<string[]>();
		List<string> list2 = new List<string>();
		foreach (Playlist playlist in this.Playlists)
		{
			list.Add(playlist.Maps.ToArray<string>());
			list2.Add(playlist.Name);
		}
		return new MapsManager.SaveData
		{
			savedPlaylists = list,
			savedPlaylistNames = list2,
			savedActivePlaylistIndex = this.activePlaylistIndex
		};
	}

	// Token: 0x06000BCF RID: 3023 RVA: 0x00052FE0 File Offset: 0x000511E0
	public void LoadState(JObject state)
	{
		MapsManager.SaveData saveData = state.ToObject<MapsManager.SaveData>();
		List<string[]> list = saveData.savedPlaylists;
		List<string> list2 = saveData.savedPlaylistNames;
		this.activePlaylistIndex = saveData.savedActivePlaylistIndex;
		this.SetActivePlaylist(this.activePlaylistIndex);
		if (list.Count != list2.Count)
		{
			Debug.LogError(string.Format("MapsManager: Save data playlist array length ({0}) does not match playlist names length ({1})", list.Count, list2.Count));
			int num = Mathf.Min(list.Count, list2.Count);
			List<string[]> list3 = new List<string[]>(num);
			List<string> list4 = new List<string>(num);
			for (int i = 0; i < num; i++)
			{
				list3.Add(list[i]);
				list4.Add(list2[i]);
			}
			list = list3;
			list2 = list4;
		}
		this.Playlists.Clear();
		for (int j = 0; j < list.Count; j++)
		{
			string[] array = list[j];
			string text = list2[j];
			this.Playlists.Add(new Playlist(text, array, false));
		}
		this.AddPresetPlaylists();
	}

	// Token: 0x06000BD0 RID: 3024 RVA: 0x000530F4 File Offset: 0x000512F4
	private void AddPresetPlaylists()
	{
		this.AddPresetPlaylist(new Playlist("Standard Maps", (from map in this.allMaps
			where !map.isDlcExclusive && !map.isAltMap
			select map.mapName).ToArray<string>(), true));
		if (SteamLobby.ownDlc0)
		{
			this.AddPresetPlaylist(new Playlist("DLC Maps", (from map in this.allMaps
				where map.isDlcExclusive && !map.isAltMap
				select map.mapName).ToArray<string>(), true));
			this.AddPresetPlaylist(new Playlist("Alt Maps", (from map in this.allMaps
				where map.isAltMap
				select map.mapName).ToArray<string>(), true));
		}
		this.AddPresetPlaylist(new Playlist("All Maps", this.allMaps.Select((Map map) => map.mapName).ToArray<string>(), true));
		foreach (PlaylistPreset playlistPreset in MapsManager.Presets)
		{
			if (!playlistPreset.IsDlcExclusive || SteamLobby.ownDlc0)
			{
				this.AddPresetPlaylist(new Playlist(playlistPreset.Name, playlistPreset.Maps, true));
			}
		}
	}

	// Token: 0x06000BD1 RID: 3025 RVA: 0x000532B8 File Offset: 0x000514B8
	private void AddPresetPlaylist(Playlist preset)
	{
		if (this.Playlists.Any((Playlist p) => p.Name == preset.Name))
		{
			return;
		}
		this.Playlists.Add(preset);
	}

	// Token: 0x040009DE RID: 2526
	public static MapsManager Instance;

	// Token: 0x040009DF RID: 2527
	public Map[] allMaps;

	// Token: 0x040009E0 RID: 2528
	public Dictionary<string, Map> allMapsDict = new Dictionary<string, Map>();

	// Token: 0x040009E1 RID: 2529
	public int[] unlockedMaps = Array.Empty<int>();

	// Token: 0x040009E2 RID: 2530
	public List<int> selectedMaps = new List<int>();

	// Token: 0x040009E3 RID: 2531
	[Space]
	public List<Playlist> Playlists = new List<Playlist>();

	// Token: 0x040009E4 RID: 2532
	[Space]
	[SerializeField]
	private GameObject playlistInstance;

	// Token: 0x040009E5 RID: 2533
	[FormerlySerializedAs("playlistsViewport")]
	[SerializeField]
	private Transform playlistParent;

	// Token: 0x040009E6 RID: 2534
	[Space]
	[SerializeField]
	private GameObject mapInstance;

	// Token: 0x040009E7 RID: 2535
	[FormerlySerializedAs("mapsViewport")]
	[SerializeField]
	private Transform standardMapParent;

	// Token: 0x040009E8 RID: 2536
	[FormerlySerializedAs("dlcMapsViewport")]
	[SerializeField]
	private Transform dlcMapParent;

	// Token: 0x040009E9 RID: 2537
	[FormerlySerializedAs("altMapsViewport")]
	[SerializeField]
	private Transform altMapParent;

	// Token: 0x040009EA RID: 2538
	[FormerlySerializedAs("mapsAddedViewport")]
	[SerializeField]
	private Transform selectedMapParent;

	// Token: 0x040009EB RID: 2539
	[Space]
	[FormerlySerializedAs("loadListInstance")]
	[SerializeField]
	private GameObject loadPlayListInstance;

	// Token: 0x040009EC RID: 2540
	[FormerlySerializedAs("loadListsViewport")]
	[SerializeField]
	private Transform playListLoadButtonParent;

	// Token: 0x040009ED RID: 2541
	[Space]
	[FormerlySerializedAs("mapScreen")]
	[SerializeField]
	private RawImage mapPreviewImage;

	// Token: 0x040009EE RID: 2542
	[SerializeField]
	private TextMeshProUGUI mapText;

	// Token: 0x040009EF RID: 2543
	[Space]
	[SerializeField]
	private ScrollRect scrollRect;

	// Token: 0x040009F0 RID: 2544
	public TextMeshProUGUI currentPlaylistText;

	// Token: 0x040009F1 RID: 2545
	[Space]
	[SerializeField]
	private MapSelection mapSelectionScript;

	// Token: 0x040009F2 RID: 2546
	private static PlaylistPreset[] Presets;

	// Token: 0x040009F3 RID: 2547
	private ProgressManager ProgressManager;

	// Token: 0x040009F4 RID: 2548
	public int selectedPlaylistIndex;

	// Token: 0x040009F5 RID: 2549
	public int activePlaylistIndex = -1;

	// Token: 0x040009F6 RID: 2550
	[SerializeField]
	private TextMeshProUGUI activePlaylistText;

	// Token: 0x040009F7 RID: 2551
	[FormerlySerializedAs("inTestMap")]
	[HideInInspector]
	public bool inExplorationMap;

	// Token: 0x040009F8 RID: 2552
	private bool MenuTrigger;

	// Token: 0x040009F9 RID: 2553
	[SerializeField]
	private TMP_InputField unselectedMapsSearchInput;

	// Token: 0x040009FA RID: 2554
	[SerializeField]
	private TMP_InputField selectedMapsSearchText;

	// Token: 0x020000DB RID: 219
	[Serializable]
	private struct SaveData
	{
		// Token: 0x040009FB RID: 2555
		public List<string[]> savedPlaylists;

		// Token: 0x040009FC RID: 2556
		public List<string> savedPlaylistNames;

		// Token: 0x040009FD RID: 2557
		public int savedActivePlaylistIndex;
	}
}

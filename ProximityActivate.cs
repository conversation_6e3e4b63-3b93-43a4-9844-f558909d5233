﻿using System;
using UnityEngine;

// Token: 0x0200015A RID: 346
public class ProximityActivate : MonoBehaviour
{
	// Token: 0x06000F01 RID: 3841 RVA: 0x00062E0C File Offset: 0x0006100C
	private void Start()
	{
		this.originRotation = base.transform.rotation;
		this.alpha = (float)(this.activeState ? 1 : (-1));
		if (this.activator == null)
		{
			this.activator = Camera.main.transform;
		}
		this.infoIcon.SetActive(this.infoPanel != null);
	}

	// Token: 0x06000F02 RID: 3842 RVA: 0x00062E74 File Offset: 0x00061074
	private bool IsTargetNear()
	{
		if ((this.distanceActivator.position - this.activator.position).sqrMagnitude < this.distance * this.distance)
		{
			if (this.lookAtActivator != null)
			{
				Vector3 vector = this.lookAtActivator.position - this.activator.position;
				if (Vector3.Dot(this.activator.forward, vector.normalized) > 0.95f)
				{
					return true;
				}
			}
			Vector3 vector2 = this.target.transform.position - this.activator.position;
			if (Vector3.Dot(this.activator.forward, vector2.normalized) > 0.95f)
			{
				return true;
			}
		}
		return false;
	}

	// Token: 0x06000F03 RID: 3843 RVA: 0x00062F44 File Offset: 0x00061144
	private void Update()
	{
		if (!this.activeState)
		{
			if (this.IsTargetNear())
			{
				this.alpha = 1f;
				this.activeState = true;
			}
		}
		else if (!this.IsTargetNear())
		{
			this.alpha = -1f;
			this.activeState = false;
			this.enableInfoPanel = false;
		}
		this.target.alpha = Mathf.Clamp01(this.target.alpha + this.alpha * Time.deltaTime);
		if (this.infoPanel != null)
		{
			if (Input.GetKeyDown(KeyCode.Space))
			{
				this.enableInfoPanel = !this.enableInfoPanel;
			}
			this.infoPanel.alpha = Mathf.Lerp(this.infoPanel.alpha, Mathf.Clamp01(this.enableInfoPanel ? this.alpha : 0f), Time.deltaTime * 10f);
		}
		if (this.lookAtCamera)
		{
			if (this.activeState)
			{
				this.targetRotation = Quaternion.LookRotation(this.activator.position - base.transform.position);
			}
			else
			{
				this.targetRotation = this.originRotation;
			}
			base.transform.rotation = Quaternion.Slerp(base.transform.rotation, this.targetRotation, Time.deltaTime);
		}
	}

	// Token: 0x04000DA2 RID: 3490
	public Transform distanceActivator;

	// Token: 0x04000DA3 RID: 3491
	public Transform lookAtActivator;

	// Token: 0x04000DA4 RID: 3492
	public float distance;

	// Token: 0x04000DA5 RID: 3493
	public Transform activator;

	// Token: 0x04000DA6 RID: 3494
	public bool activeState;

	// Token: 0x04000DA7 RID: 3495
	public CanvasGroup target;

	// Token: 0x04000DA8 RID: 3496
	public bool lookAtCamera = true;

	// Token: 0x04000DA9 RID: 3497
	public bool enableInfoPanel;

	// Token: 0x04000DAA RID: 3498
	public GameObject infoIcon;

	// Token: 0x04000DAB RID: 3499
	private float alpha;

	// Token: 0x04000DAC RID: 3500
	public CanvasGroup infoPanel;

	// Token: 0x04000DAD RID: 3501
	private Quaternion originRotation;

	// Token: 0x04000DAE RID: 3502
	private Quaternion targetRotation;
}

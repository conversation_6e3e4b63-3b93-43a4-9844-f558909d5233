﻿using System;
using UnityEngine;

// Token: 0x0200003C RID: 60
public class CustomPostProcessing : MonoBehaviour
{
	// Token: 0x0600030E RID: 782 RVA: 0x000194BB File Offset: 0x000176BB
	private void OnRenderImage(RenderTexture source, RenderTexture destination)
	{
		if (this.usePostProcessing && this.PostProcessingMaterial != null)
		{
			Graphics.Blit(source, destination, this.PostProcessingMaterial);
		}
	}

	// Token: 0x04000397 RID: 919
	public Material PostProcessingMaterial;

	// Token: 0x04000398 RID: 920
	public bool usePostProcessing;
}

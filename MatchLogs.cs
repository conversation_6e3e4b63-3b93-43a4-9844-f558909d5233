﻿using System;
using System.Collections.Generic;
using FishNet;
using FishNet.Connection;
using FishNet.Managing;
using FishNet.Object;
using FishNet.Object.Delegating;
using FishNet.Serializing;
using FishNet.Transporting;
using Goodgulf.Graphics;
using TMPro;
using UnityEngine;

// Token: 0x020000DF RID: 223
public class MatchLogs : NetworkBehaviour
{
	// Token: 0x170000B0 RID: 176
	// (get) Token: 0x06000BF5 RID: 3061 RVA: 0x00053913 File Offset: 0x00051B13
	// (set) Token: 0x06000BF6 RID: 3062 RVA: 0x0005391A File Offset: 0x00051B1A
	public static MatchLogs Instance { get; private set; }

	// Token: 0x06000BF7 RID: 3063 RVA: 0x00053922 File Offset: 0x00051B22
	public virtual void Awake()
	{
		this.NetworkInitialize___Early();
		this.Awake___UserLogic();
		this.NetworkInitialize__Late();
	}

	// Token: 0x06000BF8 RID: 3064 RVA: 0x000533DB File Offset: 0x000515DB
	public override void OnStartClient()
	{
		base.OnStartClient();
	}

	// Token: 0x06000BF9 RID: 3065 RVA: 0x00053936 File Offset: 0x00051B36
	public void WriteLog(string text)
	{
		this.RpcSendChatLine(text);
	}

	// Token: 0x06000BFA RID: 3066 RVA: 0x0005393F File Offset: 0x00051B3F
	[ServerRpc(RequireOwnership = false)]
	private void RpcSendChatLine(string line)
	{
		this.RpcWriter___Server_RpcSendChatLine_3615296227(line);
	}

	// Token: 0x06000BFB RID: 3067 RVA: 0x0005394C File Offset: 0x00051B4C
	[ObserversRpc]
	private void RpcSendChatLineToAllObservers(string line)
	{
		this.RpcWriter___Observers_RpcSendChatLineToAllObservers_3615296227(line);
	}

	// Token: 0x06000BFC RID: 3068 RVA: 0x00053964 File Offset: 0x00051B64
	public void WriteLocalLog(string line)
	{
		int childCount = this.parentForChatLines.childCount;
		List<GameObject> list = new List<GameObject>();
		for (int i = 0; i < childCount; i++)
		{
			GameObject gameObject = this.parentForChatLines.GetChild(i).gameObject;
			if (gameObject.GetComponentInChildren<TMP_Text>() != null)
			{
				list.Add(gameObject);
			}
		}
		for (int j = list.Count - 1; j >= 0; j--)
		{
			GameObject gameObject2 = list[j];
			if (gameObject2.GetComponent<MatchChatLine>().deleteMe)
			{
				list.RemoveAt(j);
				global::UnityEngine.Object.Destroy(gameObject2);
			}
			else
			{
				RectTransform component = gameObject2.GetComponent<RectTransform>();
				component.anchoredPosition = new Vector2(component.anchoredPosition.x, component.anchoredPosition.y + 35f);
			}
		}
		GameObject gameObject3 = global::UnityEngine.Object.Instantiate<GameObject>(this.chatLinePrefab, this.parentForChatLines);
		gameObject3.GetComponentInChildren<TMP_Text>().text = line;
		MatchChatLine component2 = gameObject3.GetComponent<MatchChatLine>();
		component2.duration = this.duration;
		component2.fadeDuration = this.fadeDuration;
		component2.StartDuration();
		this.previousLine = line;
	}

	// Token: 0x06000BFD RID: 3069 RVA: 0x000023D6 File Offset: 0x000005D6
	private void OnDisable()
	{
	}

	// Token: 0x06000BFF RID: 3071 RVA: 0x00053A70 File Offset: 0x00051C70
	public virtual void NetworkInitialize___Early()
	{
		if (this.NetworkInitializeEarly_MatchLogs_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeEarly_MatchLogs_Assembly-CSharp.dll = true;
		base.RegisterServerRpc(0U, new ServerRpcDelegate(this.RpcReader___Server_RpcSendChatLine_3615296227));
		base.RegisterObserversRpc(1U, new ClientRpcDelegate(this.RpcReader___Observers_RpcSendChatLineToAllObservers_3615296227));
	}

	// Token: 0x06000C00 RID: 3072 RVA: 0x00053ABC File Offset: 0x00051CBC
	public virtual void NetworkInitialize__Late()
	{
		if (this.NetworkInitializeLate_MatchLogs_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeLate_MatchLogs_Assembly-CSharp.dll = true;
	}

	// Token: 0x06000C01 RID: 3073 RVA: 0x00053ACF File Offset: 0x00051CCF
	public override void NetworkInitializeIfDisabled()
	{
		this.NetworkInitialize___Early();
		this.NetworkInitialize__Late();
	}

	// Token: 0x06000C02 RID: 3074 RVA: 0x00053AE0 File Offset: 0x00051CE0
	private void RpcWriter___Server_RpcSendChatLine_3615296227(string line)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteString(line);
		base.SendServerRpc(0U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000C03 RID: 3075 RVA: 0x00053B87 File Offset: 0x00051D87
	private void RpcLogic___RpcSendChatLine_3615296227(string line)
	{
		this.RpcSendChatLineToAllObservers(line);
	}

	// Token: 0x06000C04 RID: 3076 RVA: 0x00053B90 File Offset: 0x00051D90
	private void RpcReader___Server_RpcSendChatLine_3615296227(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		string text = PooledReader0.ReadString();
		if (!base.IsServer)
		{
			return;
		}
		this.RpcLogic___RpcSendChatLine_3615296227(text);
	}

	// Token: 0x06000C05 RID: 3077 RVA: 0x00053BC4 File Offset: 0x00051DC4
	private void RpcWriter___Observers_RpcSendChatLineToAllObservers_3615296227(string line)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteString(line);
		base.SendObserversRpc(1U, writer, channel, DataOrderType.Default, false, false, false);
		writer.Store();
	}

	// Token: 0x06000C06 RID: 3078 RVA: 0x00053C7C File Offset: 0x00051E7C
	private void RpcLogic___RpcSendChatLineToAllObservers_3615296227(string line)
	{
		int childCount = this.parentForChatLines.childCount;
		List<GameObject> list = new List<GameObject>();
		for (int i = 0; i < childCount; i++)
		{
			GameObject gameObject = this.parentForChatLines.GetChild(i).gameObject;
			if (gameObject.GetComponentInChildren<TMP_Text>() != null)
			{
				list.Add(gameObject);
			}
		}
		for (int j = list.Count - 1; j >= 0; j--)
		{
			GameObject gameObject2 = list[j];
			if (gameObject2.GetComponent<MatchChatLine>().deleteMe)
			{
				list.RemoveAt(j);
				global::UnityEngine.Object.Destroy(gameObject2);
			}
			else
			{
				RectTransform component = gameObject2.GetComponent<RectTransform>();
				component.anchoredPosition = new Vector2(component.anchoredPosition.x, component.anchoredPosition.y + 35f);
			}
		}
		GameObject gameObject3 = global::UnityEngine.Object.Instantiate<GameObject>(this.chatLinePrefab, this.parentForChatLines);
		gameObject3.GetComponentInChildren<TMP_Text>().text = line;
		MatchChatLine component2 = gameObject3.GetComponent<MatchChatLine>();
		component2.duration = this.duration;
		component2.fadeDuration = this.fadeDuration;
		component2.StartDuration();
		this.previousLine = line;
	}

	// Token: 0x06000C07 RID: 3079 RVA: 0x00053D88 File Offset: 0x00051F88
	private void RpcReader___Observers_RpcSendChatLineToAllObservers_3615296227(PooledReader PooledReader0, Channel channel)
	{
		string text = PooledReader0.ReadString();
		if (!base.IsClient)
		{
			return;
		}
		this.RpcLogic___RpcSendChatLineToAllObservers_3615296227(text);
	}

	// Token: 0x06000C08 RID: 3080 RVA: 0x00053DB9 File Offset: 0x00051FB9
	public virtual void Awake___UserLogic()
	{
		if (MatchLogs.Instance != null && MatchLogs.Instance != this)
		{
			global::UnityEngine.Object.Destroy(this);
			return;
		}
		MatchLogs.Instance = this;
		this.parentForChatLines = GameObject.Find("LogsPosition").transform;
	}

	// Token: 0x04000A15 RID: 2581
	private GameObject ChatBox;

	// Token: 0x04000A16 RID: 2582
	public GameObject chatLinePrefab;

	// Token: 0x04000A17 RID: 2583
	public Transform parentForChatLines;

	// Token: 0x04000A18 RID: 2584
	public float duration;

	// Token: 0x04000A19 RID: 2585
	public float fadeDuration;

	// Token: 0x04000A1A RID: 2586
	public ClientInstance localPlayer;

	// Token: 0x04000A1B RID: 2587
	private string previousLine;

	// Token: 0x04000A1C RID: 2588
	private bool NetworkInitializeEarly_MatchLogs_Assembly-CSharp.dll;

	// Token: 0x04000A1D RID: 2589
	private bool NetworkInitializeLate_MatchLogs_Assembly-CSharp.dll;
}

﻿using System;
using FishNet;
using FishNet.Connection;
using FishNet.Managing;
using FishNet.Object;
using FishNet.Object.Delegating;
using FishNet.Serializing;
using FishNet.Transporting;
using UnityEngine;
using UnityEngine.InputSystem;

// Token: 0x0200004C RID: 76
public class DollHealth : NetworkBehaviour
{
	// Token: 0x06000380 RID: 896 RVA: 0x0001ABDC File Offset: 0x00018DDC
	private void Start()
	{
		this.maxHealth = this.health;
	}

	// Token: 0x06000381 RID: 897 RVA: 0x0001ABEA File Offset: 0x00018DEA
	private void OnEnable()
	{
		this.menu.Enable();
		this.menu.performed += this.Menu;
	}

	// Token: 0x06000382 RID: 898 RVA: 0x0001AC0E File Offset: 0x00018E0E
	private void OnDisable()
	{
		this.menu.Disable();
		this.menu.performed -= this.Menu;
	}

	// Token: 0x06000383 RID: 899 RVA: 0x0001AC32 File Offset: 0x00018E32
	private void Update()
	{
		if (this.health < 0f)
		{
			this.DollDeath();
		}
	}

	// Token: 0x06000384 RID: 900 RVA: 0x0001AC47 File Offset: 0x00018E47
	private void Menu(InputAction.CallbackContext ctx)
	{
		this.health = this.maxHealth;
		this.DollRevive();
	}

	// Token: 0x06000385 RID: 901 RVA: 0x0001AC5B File Offset: 0x00018E5B
	[ServerRpc(RequireOwnership = false)]
	private void ServerDollDeath()
	{
		this.RpcWriter___Server_ServerDollDeath_2166136261();
	}

	// Token: 0x06000386 RID: 902 RVA: 0x0001AC63 File Offset: 0x00018E63
	[ServerRpc(RequireOwnership = false)]
	private void ServerDollRevive()
	{
		this.RpcWriter___Server_ServerDollRevive_2166136261();
	}

	// Token: 0x06000387 RID: 903 RVA: 0x0001AC6B File Offset: 0x00018E6B
	[ObserversRpc]
	private void DollDeath()
	{
		this.RpcWriter___Observers_DollDeath_2166136261();
	}

	// Token: 0x06000388 RID: 904 RVA: 0x0001AC73 File Offset: 0x00018E73
	[ObserversRpc]
	private void DollRevive()
	{
		this.RpcWriter___Observers_DollRevive_2166136261();
	}

	// Token: 0x0600038A RID: 906 RVA: 0x0001AC90 File Offset: 0x00018E90
	public virtual void NetworkInitialize___Early()
	{
		if (this.NetworkInitializeEarly_DollHealth_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeEarly_DollHealth_Assembly-CSharp.dll = true;
		base.RegisterServerRpc(0U, new ServerRpcDelegate(this.RpcReader___Server_ServerDollDeath_2166136261));
		base.RegisterServerRpc(1U, new ServerRpcDelegate(this.RpcReader___Server_ServerDollRevive_2166136261));
		base.RegisterObserversRpc(2U, new ClientRpcDelegate(this.RpcReader___Observers_DollDeath_2166136261));
		base.RegisterObserversRpc(3U, new ClientRpcDelegate(this.RpcReader___Observers_DollRevive_2166136261));
	}

	// Token: 0x0600038B RID: 907 RVA: 0x0001AD0A File Offset: 0x00018F0A
	public virtual void NetworkInitialize__Late()
	{
		if (this.NetworkInitializeLate_DollHealth_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeLate_DollHealth_Assembly-CSharp.dll = true;
	}

	// Token: 0x0600038C RID: 908 RVA: 0x0001AD1D File Offset: 0x00018F1D
	public override void NetworkInitializeIfDisabled()
	{
		this.NetworkInitialize___Early();
		this.NetworkInitialize__Late();
	}

	// Token: 0x0600038D RID: 909 RVA: 0x0001AD2C File Offset: 0x00018F2C
	private void RpcWriter___Server_ServerDollDeath_2166136261()
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendServerRpc(0U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x0600038E RID: 910 RVA: 0x0001ADC6 File Offset: 0x00018FC6
	private void RpcLogic___ServerDollDeath_2166136261()
	{
		this.DollDeath();
	}

	// Token: 0x0600038F RID: 911 RVA: 0x0001ADD0 File Offset: 0x00018FD0
	private void RpcReader___Server_ServerDollDeath_2166136261(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		if (!base.IsServer)
		{
			return;
		}
		this.RpcLogic___ServerDollDeath_2166136261();
	}

	// Token: 0x06000390 RID: 912 RVA: 0x0001ADF0 File Offset: 0x00018FF0
	private void RpcWriter___Server_ServerDollRevive_2166136261()
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendServerRpc(1U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000391 RID: 913 RVA: 0x0001AE8A File Offset: 0x0001908A
	private void RpcLogic___ServerDollRevive_2166136261()
	{
		this.DollRevive();
	}

	// Token: 0x06000392 RID: 914 RVA: 0x0001AE94 File Offset: 0x00019094
	private void RpcReader___Server_ServerDollRevive_2166136261(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		if (!base.IsServer)
		{
			return;
		}
		this.RpcLogic___ServerDollRevive_2166136261();
	}

	// Token: 0x06000393 RID: 915 RVA: 0x0001AEB4 File Offset: 0x000190B4
	private void RpcWriter___Observers_DollDeath_2166136261()
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendObserversRpc(2U, writer, channel, DataOrderType.Default, false, false, false);
		writer.Store();
	}

	// Token: 0x06000394 RID: 916 RVA: 0x0001AF5D File Offset: 0x0001915D
	private void RpcLogic___DollDeath_2166136261()
	{
		base.GetComponent<MeshRenderer>().enabled = false;
		base.GetComponent<Collider>().enabled = false;
	}

	// Token: 0x06000395 RID: 917 RVA: 0x0001AF78 File Offset: 0x00019178
	private void RpcReader___Observers_DollDeath_2166136261(PooledReader PooledReader0, Channel channel)
	{
		if (!base.IsClient)
		{
			return;
		}
		this.RpcLogic___DollDeath_2166136261();
	}

	// Token: 0x06000396 RID: 918 RVA: 0x0001AF98 File Offset: 0x00019198
	private void RpcWriter___Observers_DollRevive_2166136261()
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendObserversRpc(3U, writer, channel, DataOrderType.Default, false, false, false);
		writer.Store();
	}

	// Token: 0x06000397 RID: 919 RVA: 0x0001B041 File Offset: 0x00019241
	private void RpcLogic___DollRevive_2166136261()
	{
		base.GetComponent<MeshRenderer>().enabled = true;
		base.GetComponent<Collider>().enabled = true;
	}

	// Token: 0x06000398 RID: 920 RVA: 0x0001B05C File Offset: 0x0001925C
	private void RpcReader___Observers_DollRevive_2166136261(PooledReader PooledReader0, Channel channel)
	{
		if (!base.IsClient)
		{
			return;
		}
		this.RpcLogic___DollRevive_2166136261();
	}

	// Token: 0x06000399 RID: 921 RVA: 0x0001AD1D File Offset: 0x00018F1D
	public virtual void Awake()
	{
		this.NetworkInitialize___Early();
		this.NetworkInitialize__Late();
	}

	// Token: 0x0600039A RID: 922 RVA: 0x000023D6 File Offset: 0x000005D6
	public virtual void Awake___UserLogic()
	{
	}

	// Token: 0x040003F4 RID: 1012
	public float health = 4f;

	// Token: 0x040003F5 RID: 1013
	private float maxHealth;

	// Token: 0x040003F6 RID: 1014
	[SerializeField]
	private InputAction menu;

	// Token: 0x040003F7 RID: 1015
	private bool NetworkInitializeEarly_DollHealth_Assembly-CSharp.dll;

	// Token: 0x040003F8 RID: 1016
	private bool NetworkInitializeLate_DollHealth_Assembly-CSharp.dll;
}

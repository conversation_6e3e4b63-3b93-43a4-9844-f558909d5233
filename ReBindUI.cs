﻿using System;
using TMPro;
using UnityEngine;
using UnityEngine.InputSystem;
using UnityEngine.UI;

// Token: 0x020000FC RID: 252
public class ReBindUI : MonoBehaviour
{
	// Token: 0x06000CD0 RID: 3280 RVA: 0x0005725B File Offset: 0x0005545B
	private void Start()
	{
		InputManager.rebindFields.Add(this);
	}

	// Token: 0x06000CD1 RID: 3281 RVA: 0x00057268 File Offset: 0x00055468
	public void OnEnable()
	{
		this.rebindButton.onClick.AddListener(delegate
		{
			this.DoRebind();
		});
		this.resetButton.onClick.AddListener(delegate
		{
			this.ResetBinding();
		});
		if (this.inputActionReference != null)
		{
			InputManager.LoadBindingOverride(this.actionName);
			this.GetBindingInfo();
			this.UpdateUI();
		}
		InputManager.rebindComplete += this.UpdateUI;
		InputManager.rebindCanceled += this.UpdateUI;
	}

	// Token: 0x06000CD2 RID: 3282 RVA: 0x000572F4 File Offset: 0x000554F4
	public void Restart()
	{
		if (this.inputActionReference != null)
		{
			InputManager.LoadBindingOverride(this.actionName);
			this.GetBindingInfo();
			this.UpdateUI();
		}
	}

	// Token: 0x06000CD3 RID: 3283 RVA: 0x0005731B File Offset: 0x0005551B
	private void OnDisable()
	{
		InputManager.rebindComplete -= this.UpdateUI;
		InputManager.rebindCanceled -= this.UpdateUI;
	}

	// Token: 0x06000CD4 RID: 3284 RVA: 0x0005733F File Offset: 0x0005553F
	private void OnValidate()
	{
		if (this.inputActionReference == null)
		{
			return;
		}
		this.GetBindingInfo();
		this.UpdateUI();
	}

	// Token: 0x06000CD5 RID: 3285 RVA: 0x0005735C File Offset: 0x0005555C
	private void GetBindingInfo()
	{
		if (this.inputActionReference.action != null)
		{
			this.actionName = this.inputActionReference.action.name;
		}
		if (this.inputActionReference.action.bindings.Count > this.selectedBinding)
		{
			this.inputBinding = this.inputActionReference.action.bindings[this.selectedBinding];
			this.bindingIndex = this.selectedBinding;
		}
	}

	// Token: 0x06000CD6 RID: 3286 RVA: 0x000573DC File Offset: 0x000555DC
	private void UpdateUI()
	{
		if (PauseManager.Instance != null)
		{
			PauseManager.Instance.sequenceDisplayGameObject.SetActive(false);
		}
		if (this.actionText != null)
		{
			this.actionText.text = this.actionName;
		}
		if (this.rebindText != null)
		{
			if (Application.isPlaying)
			{
				this.rebindText.text = InputManager.GetBindingName(this.actionName, this.bindingIndex);
				return;
			}
			this.rebindText.text = this.inputActionReference.action.GetBindingDisplayString(this.bindingIndex, (InputBinding.DisplayStringOptions)0);
		}
	}

	// Token: 0x06000CD7 RID: 3287 RVA: 0x00057479 File Offset: 0x00055679
	private void DoRebind()
	{
		InputManager.StartRebind(this.actionName, this.bindingIndex, this.rebindText, this.excludeMouse, this.sequenceDisplay);
	}

	// Token: 0x06000CD8 RID: 3288 RVA: 0x0005749E File Offset: 0x0005569E
	public void ResetBinding()
	{
		InputManager.ResetBinding(this.actionName, this.bindingIndex);
		this.UpdateUI();
	}

	// Token: 0x04000B07 RID: 2823
	[SerializeField]
	private InputActionReference inputActionReference;

	// Token: 0x04000B08 RID: 2824
	[SerializeField]
	private bool excludeMouse = true;

	// Token: 0x04000B09 RID: 2825
	[SerializeField]
	private bool sequenceDisplay;

	// Token: 0x04000B0A RID: 2826
	[Range(0f, 30f)]
	[SerializeField]
	private int selectedBinding;

	// Token: 0x04000B0B RID: 2827
	[SerializeField]
	private InputBinding.DisplayStringOptions displayStringOptions;

	// Token: 0x04000B0C RID: 2828
	[Header("Binding info - DO NOT EDIT")]
	[SerializeField]
	private InputBinding inputBinding;

	// Token: 0x04000B0D RID: 2829
	private int bindingIndex;

	// Token: 0x04000B0E RID: 2830
	[HideInInspector]
	public string actionName;

	// Token: 0x04000B0F RID: 2831
	[Header("UI Fields")]
	[SerializeField]
	private TextMeshProUGUI actionText;

	// Token: 0x04000B10 RID: 2832
	[SerializeField]
	private Button rebindButton;

	// Token: 0x04000B11 RID: 2833
	[SerializeField]
	private TextMeshProUGUI rebindText;

	// Token: 0x04000B12 RID: 2834
	[SerializeField]
	private Button resetButton;
}

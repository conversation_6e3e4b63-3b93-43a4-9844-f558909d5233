﻿using System;
using DG.Tweening;
using UnityEngine;
using UnityEngine.SceneManagement;

// Token: 0x02000173 RID: 371
public class WindowPositionTween : MonoBehaviour
{
	// Token: 0x06000F7D RID: 3965 RVA: 0x00065C7C File Offset: 0x00063E7C
	private void Update()
	{
		if (this.options)
		{
			SceneManager.GetActiveScene().name != "MainMenu";
		}
		if (SceneManager.GetActiveScene().name != "MainMenu")
		{
			return;
		}
		if (Input.GetKeyDown(KeyCode.Escape) || Input.GetButtonDown("Menu"))
		{
			if (PauseManager.Instance.gamepad)
			{
				if (this.panel.activeSelf)
				{
					PauseManager.Instance.ChangeSelectedItem(this.optionsButton);
				}
				else
				{
					PauseManager.Instance.ChangeSelectedItem(this.resumeButton);
				}
			}
			this.panel.SetActive(!this.panel.activeSelf);
		}
	}

	// Token: 0x06000F7E RID: 3966 RVA: 0x00065D2C File Offset: 0x00063F2C
	public void ChangeWindowState()
	{
		base.transform.DOKill(false);
		if (base.transform.localScale == Vector3.zero)
		{
			base.transform.localScale = Vector3.one;
			return;
		}
		base.transform.localScale = Vector3.zero;
	}

	// Token: 0x04000E46 RID: 3654
	[SerializeField]
	private GameObject panel;

	// Token: 0x04000E47 RID: 3655
	[SerializeField]
	private GameObject optionsButton;

	// Token: 0x04000E48 RID: 3656
	[SerializeField]
	private GameObject resumeButton;

	// Token: 0x04000E49 RID: 3657
	[SerializeField]
	private float tweenDuration = 0.2f;

	// Token: 0x04000E4A RID: 3658
	[SerializeField]
	private Ease easeType;

	// Token: 0x04000E4B RID: 3659
	[SerializeField]
	private bool options;
}

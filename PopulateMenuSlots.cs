﻿using System;
using UnityEngine;

// Token: 0x02000069 RID: 105
public class PopulateMenuSlots : MonoBehaviour
{
	// Token: 0x060004B1 RID: 1201 RVA: 0x000202FE File Offset: 0x0001E4FE
	private void Start()
	{
		this.steamLobby = SteamLobby.Instance;
	}

	// Token: 0x060004B2 RID: 1202 RVA: 0x0002030B File Offset: 0x0001E50B
	private void Update()
	{
		if (this.id + 1 <= this.steamLobby.maxPlayers)
		{
			base.transform.localScale = Vector3.one;
			return;
		}
		base.transform.localScale = Vector3.zero;
	}

	// Token: 0x040004CB RID: 1227
	private SteamLobby steamLobby;

	// Token: 0x040004CC RID: 1228
	[SerializeField]
	private int id;
}

﻿using System;
using System.Collections;
using DG.Tweening;
using FishNet;
using FishNet.Connection;
using FishNet.Managing;
using FishNet.Object;
using FishNet.Object.Delegating;
using FishNet.Serializing;
using FishNet.Serializing.Generated;
using FishNet.Transporting;
using UnityEngine;
using UnityEngine.UI;

// Token: 0x02000091 RID: 145
public class Gun : Weapon
{
	// Token: 0x06000746 RID: 1862 RVA: 0x00030C1A File Offset: 0x0002EE1A
	private void Start()
	{
		if (InstanceFinder.NetworkManager.IsServer && this.reloadWeapon)
		{
			this.InitChargedBullets();
		}
	}

	// Token: 0x06000747 RID: 1863 RVA: 0x00030C36 File Offset: 0x0002EE36
	[ServerRpc(RequireOwnership = false)]
	private void InitChargedBullets()
	{
		this.RpcWriter___Server_InitChargedBullets_2166136261();
	}

	// Token: 0x06000748 RID: 1864 RVA: 0x00030C3E File Offset: 0x0002EE3E
	[ObserversRpc(BufferLast = true)]
	private void InitChargedBulletsObservers()
	{
		this.RpcWriter___Observers_InitChargedBulletsObservers_2166136261();
	}

	// Token: 0x06000749 RID: 1865 RVA: 0x00030C48 File Offset: 0x0002EE48
	private void Update()
	{
		base.WeaponUpdate();
		if (base.gameObject.layer == 7)
		{
			this.isBursting = false;
		}
		if (this.fireTimer > 0f)
		{
			this.fireTimer -= Time.deltaTime;
		}
		if (base.gameObject.layer == 7)
		{
			return;
		}
		if (!this.onePressShoot)
		{
			if ((this.invertFire ? this.fire2 : this.fire1).ReadValue<float>() > 0.1f && this.inRightHand && (this.reloadWeapon || base.SyncAccessor_currentAmmo > 0))
			{
				this.Fire();
			}
			if ((this.invertFire ? this.fire1 : this.fire2).ReadValue<float>() > 0.1f && this.inLeftHand && (this.reloadWeapon || base.SyncAccessor_currentAmmo > 0))
			{
				this.Fire();
			}
			if ((this.invertFire ? this.fire2 : this.fire1).ReadValue<float>() > 0.1f && this.inRightHand && this.akAnim && base.SyncAccessor_currentAmmo > 0)
			{
				this.camAnimScript.rotateBack = false;
			}
			else if (this.fire2.ReadValue<float>() > 0.1f && this.inLeftHand && this.akAnim && base.SyncAccessor_currentAmmo > 0)
			{
				this.camAnimScript.rotateBack = false;
			}
			else
			{
				this.camAnimScript.rotateBack = true;
			}
			if (!this.reloadWeapon && base.SyncAccessor_currentAmmo <= 0)
			{
				if (this.fire1.ReadValue<float>() < 0.1f && this.inRightHand)
				{
					this.isClicked = true;
				}
				if (this.fire2.ReadValue<float>() < 0.1f && this.inLeftHand)
				{
					this.isClicked = true;
				}
			}
		}
		else
		{
			if ((this.invertFire ? this.fire2 : this.fire1).ReadValue<float>() < 0.1f && this.inRightHand)
			{
				this.isClicked = true;
			}
			if ((this.invertFire ? this.fire1 : this.fire2).ReadValue<float>() < 0.1f && this.inLeftHand)
			{
				this.isClicked = true;
			}
			this.camAnimScript.rotateBack = true;
		}
		if (this.isClicked)
		{
			if ((this.invertFire ? this.fire2 : this.fire1).ReadValue<float>() > 0.1f && this.inRightHand)
			{
				this.Fire();
				this.isClicked = false;
			}
			if ((this.invertFire ? this.fire1 : this.fire2).ReadValue<float>() > 0.1f && this.inLeftHand)
			{
				this.Fire();
				this.isClicked = false;
			}
		}
		if (this.shot && this.timeBetweenFire < 0.2f && this.onePressShoot)
		{
			this.Fire();
		}
	}

	// Token: 0x0600074A RID: 1866 RVA: 0x00030F28 File Offset: 0x0002F128
	private void Fire()
	{
		if (PauseManager.Instance.pause || !this.playerController.SyncAccessor_canMove)
		{
			return;
		}
		if (!this.playerController.IsOwner)
		{
			return;
		}
		if ((this.burstGun || this.aimBurstGun) && this.isBursting)
		{
			return;
		}
		if (this.changePitchOnShoot)
		{
			this.audio.pitch = global::UnityEngine.Random.Range(0.97f, 1.03f);
		}
		if (this.fireTimer > 0f)
		{
			this.shot = true;
			return;
		}
		this.shot = false;
		this.fireTimer = this.timeBetweenFire;
		if (base.SyncAccessor_currentAmmo <= 0 && this.chargedBullets <= 0f)
		{
			this.audio.PlayOneShot(this.nobulletClip);
			this.noAmmoClicks++;
			if (this.noAmmoClicks > 1 && this.inRightHand)
			{
				this.rootObject.GetComponent<PlayerPickup>().RightHandDrop();
			}
			if (this.noAmmoClicks > 1 && this.inLeftHand)
			{
				this.rootObject.GetComponent<PlayerPickup>().LeftHandDrop();
			}
		}
		if (!this.reloadWeapon && base.SyncAccessor_currentAmmo <= 0)
		{
			return;
		}
		if (this.reloadWeapon && this.isReloading)
		{
			return;
		}
		if (this.reloadWeapon && this.chargedBullets <= 0f && base.SyncAccessor_currentAmmo > 0)
		{
			base.StartCoroutine(this.Reload());
		}
		if (this.reloadWeapon && this.chargedBullets <= 0f && base.SyncAccessor_currentAmmo <= 0)
		{
			this.audio.PlayOneShot(this.nobulletClip);
		}
		if (this.reloadWeapon && this.chargedBullets <= 0f)
		{
			return;
		}
		if (this.burstGun)
		{
			base.StartCoroutine(this.BurstFire());
			return;
		}
		if (this.aimBurstGun && this.playerController.isAiming)
		{
			base.StartCoroutine(this.BurstFire());
			return;
		}
		float num = ((this.ScopeAimWeapon && !this.playerController.isAiming) ? this.notAimingAccuracy : ((this.playerController.isSprinting || !this.playerController.safeGrounded) ? Mathf.Lerp(this.maxSpread, this.minSpread, this.sprintAccuracy) : (this.playerController.isCrouching ? Mathf.Lerp(this.maxSpread, this.minSpread, this.standingAccuracy) : (this.playerController.isWalking ? Mathf.Lerp(this.maxSpread, this.minSpread, this.walkAccuracy) : Mathf.Lerp(this.maxSpread, this.minSpread, this.standingAccuracy)))));
		this.spread = this.cam.transform.right * global::UnityEngine.Random.Range(num, -num) + this.cam.transform.up * global::UnityEngine.Random.Range(num, -num);
		this.ShootServer(this.damage, this.cam.transform.position, this.cam.transform.forward + this.spread);
		if (this.revolverShake)
		{
			base.CameraRevolverAnimation();
		}
		else
		{
			base.CameraAnimation();
		}
		base.WeaponAnimation();
	}

	// Token: 0x0600074B RID: 1867 RVA: 0x00031254 File Offset: 0x0002F454
	private void ShootServer(float damageToGive, Vector3 position, Vector3 direction)
	{
		if (this.needsAmmo)
		{
			this.RemoveAmmo();
		}
		this.ShootServerEffect();
		RaycastHit[] array = Physics.RaycastAll(position, direction, float.PositiveInfinity, this.defaultLayer);
		Array.Sort<RaycastHit>(array, (RaycastHit x, RaycastHit y) => x.distance.CompareTo(y.distance));
		if (array.Length != 0)
		{
			for (int i = 0; i < array.Length; i++)
			{
				base.TriggerEnvironment(array[i].transform.gameObject, array[i].point, direction, array[i].normal);
				this.SpawnBulletTrailServer(array[i].point);
				if (array[i].transform.tag == "ShatterableGlass")
				{
					base.BreakGlassServer(array[i].point, direction, array[i].transform.gameObject);
				}
				else if (array[i].transform.gameObject.layer != LayerMask.NameToLayer("Ragdoll"))
				{
					this.SpawnVFXServer(0, array[i].point, array[i].normal, array[i].transform.tag, array[i].transform);
					this.SpawnVFXServer(1, array[i].point, array[i].normal, array[i].transform.tag, array[i].transform);
				}
				if (array[i].transform.gameObject.layer != 10 && array[i].transform.gameObject.layer != 11 && array[i].transform.gameObject.layer != 14 && array[i].transform.gameObject.layer != 18 && array[i].transform.gameObject.layer != 19 && array[i].transform.gameObject.layer != 24)
				{
					break;
				}
			}
		}
		RaycastHit raycastHit;
		if (Physics.Raycast(position, direction, out raycastHit, float.PositiveInfinity, this.playerLayer) && raycastHit.transform.gameObject.layer == 11 && (raycastHit.transform.root.CompareTag("Player") ? raycastHit.transform.root.TryGetComponent<PlayerHealth>(out this.enemyHealth) : (raycastHit.transform.GetComponentInParent<PlayerHealth>() != null)))
		{
			if (raycastHit.transform.root.tag != "Player")
			{
				this.enemyHealth = raycastHit.transform.GetComponentInParent<PlayerHealth>();
			}
			if (this.enemyHealth.gameObject == base.transform.root.gameObject)
			{
				return;
			}
			if (base.FriendlyFireCheck(this.enemyHealth))
			{
				return;
			}
			bool flag = raycastHit.transform.gameObject.name == "Head_Col" || raycastHit.transform.gameObject.name == "Neck_1_Col";
			if (flag)
			{
				damageToGive *= this.headMultiplier;
				Settings.Instance.IncreaseHeadshotsAmount();
			}
			else
			{
				Settings.Instance.IncreaseBodyshotsAmount();
			}
			if (flag && this.headImpact)
			{
				global::UnityEngine.Object.Instantiate<GameObject>(this.headImpact, raycastHit.point, Quaternion.identity, (this.enemyHealth.SyncAccessor_health - damageToGive > 0f) ? raycastHit.transform : null);
			}
			else
			{
				global::UnityEngine.Object.Instantiate<GameObject>(this.bodyImpact, raycastHit.point, Quaternion.LookRotation(raycastHit.normal));
			}
			if (this.playGenericBodyImpactOnBody ? this.genericBodyImpact : (this.genericBodyImpact && flag))
			{
				global::UnityEngine.Object.Instantiate<GameObject>(this.genericBodyImpact, raycastHit.point, Quaternion.identity, (this.enemyHealth.SyncAccessor_health - damageToGive > 0f) ? raycastHit.transform : null);
			}
			this.ServerFX(raycastHit.point + direction, Quaternion.identity);
			if (this.marker == null)
			{
				this.marker = global::UnityEngine.Object.Instantiate<GameObject>(this.hitMarker, Crosshair.Instance.transform.position, Quaternion.identity, PauseManager.Instance.transform);
				this.marker.transform.DOPunchScale((raycastHit.transform.gameObject.name == "Head_Col") ? new Vector3(2.5f, 2.5f, 2.5f) : Vector3.one, 0.3f, 8, 2f);
				if (raycastHit.transform.gameObject.name == "Head_Col")
				{
					this.marker.GetComponent<Image>().color = Color.red;
				}
				global::UnityEngine.Object.Destroy(this.marker, 0.3f);
			}
			else
			{
				global::UnityEngine.Object.Destroy(this.marker);
				this.marker = global::UnityEngine.Object.Instantiate<GameObject>(this.hitMarker, Crosshair.Instance.transform.position, Quaternion.identity, PauseManager.Instance.transform);
				this.marker.transform.DOPunchScale((raycastHit.transform.gameObject.name == "Head_Col") ? new Vector3(2.5f, 2.5f, 2.5f) : Vector3.one, 0.3f, 8, 2f);
				if (raycastHit.transform.gameObject.name == "Head_Col")
				{
					this.marker.GetComponent<Image>().color = Color.red;
				}
				global::UnityEngine.Object.Destroy(this.marker, 0.3f);
			}
			if (flag)
			{
				this.LocalSound(0);
				this.LocalSound(1);
			}
			else
			{
				this.LocalSound(1);
			}
			if (this.enemyHealth.SyncAccessor_health - damageToGive <= 0f)
			{
				this.LocalSound(2);
				this.playerController.KillShockWave();
				this.enemyHealth.graphics.SetActive(false);
				this.enemyHealth.controller.playerPickupScript.fpArms.gameObject.SetActive(false);
				this.enemyHealth.GetComponent<CharacterController>().enabled = false;
				this.enemyHealth.Explode(false, true, raycastHit.transform.gameObject.name, direction, this.ragdollEjectForce, raycastHit.point);
				this.KillServer(this.enemyHealth);
				this.enemyHealth.DisablePlayerObjectWhenKilled();
				PauseManager.Instance.WriteLog(string.Concat(new string[]
				{
					"<b><color=#",
					PauseManager.Instance.selfNameLogColor,
					">",
					this.enemyHealth.SyncAccessor_playerValues.SyncAccessor_playerClient.PlayerName,
					"</color></b> was ",
					flag ? "headshot" : "killed",
					" with ",
					base.StartsWithVowel(this.behaviour.weaponName) ? "an" : "a",
					" <b><color=white>",
					this.behaviour.weaponName,
					"</color></b> by <b><color=#",
					PauseManager.Instance.enemyNameLogColor,
					">",
					ClientInstance.Instance.PlayerName,
					"</color></b>"
				}));
			}
			else
			{
				this.GiveDamage(damageToGive, this.enemyHealth, raycastHit.transform.gameObject.name);
			}
			this.hitOK = true;
		}
		RaycastHit raycastHit2;
		if (Physics.Raycast(position, direction, out raycastHit2, float.PositiveInfinity, this.supLayer) && !this.hitOK && raycastHit2.transform.gameObject.layer == 17)
		{
			this.SupressionServer(raycastHit2.transform);
		}
		RaycastHit raycastHit3;
		DollHealth dollHealth;
		if (Physics.Raycast(position, direction, out raycastHit3, float.PositiveInfinity) && raycastHit3.transform.TryGetComponent<DollHealth>(out dollHealth))
		{
			dollHealth.health -= damageToGive;
		}
		this.hitOK = false;
	}

	// Token: 0x0600074C RID: 1868 RVA: 0x00031ABA File Offset: 0x0002FCBA
	[ServerRpc(RunLocally = true)]
	private void RemoveAmmo()
	{
		this.RpcWriter___Server_RemoveAmmo_2166136261();
		this.RpcLogic___RemoveAmmo_2166136261();
	}

	// Token: 0x0600074D RID: 1869 RVA: 0x00031AC8 File Offset: 0x0002FCC8
	[ServerRpc(RunLocally = true)]
	private void GiveDamage(float damageToGive, PlayerHealth enemyHealth, string name)
	{
		this.RpcWriter___Server_GiveDamage_324487999(damageToGive, enemyHealth, name);
		this.RpcLogic___GiveDamage_324487999(damageToGive, enemyHealth, name);
	}

	// Token: 0x0600074E RID: 1870 RVA: 0x00031AF0 File Offset: 0x0002FCF0
	[ServerRpc]
	private void KillServer(PlayerHealth enemyHealth)
	{
		this.RpcWriter___Server_KillServer_1722911636(enemyHealth);
	}

	// Token: 0x0600074F RID: 1871 RVA: 0x00031B07 File Offset: 0x0002FD07
	[TargetRpc]
	private void KillObserver(NetworkConnection conn, ClientInstance client, PlayerHealth enemyHealth)
	{
		this.RpcWriter___Target_KillObserver_123853379(conn, client, enemyHealth);
	}

	// Token: 0x06000750 RID: 1872 RVA: 0x00031B1B File Offset: 0x0002FD1B
	[ObserversRpc]
	private void HitFeeback(PlayerHealth enemyHealth)
	{
		this.RpcWriter___Observers_HitFeeback_1722911636(enemyHealth);
	}

	// Token: 0x06000751 RID: 1873 RVA: 0x00031B27 File Offset: 0x0002FD27
	[ServerRpc(RunLocally = true)]
	private void ShootServerEffect()
	{
		this.RpcWriter___Server_ShootServerEffect_2166136261();
		this.RpcLogic___ShootServerEffect_2166136261();
	}

	// Token: 0x06000752 RID: 1874 RVA: 0x00031B35 File Offset: 0x0002FD35
	[ServerRpc(RunLocally = true)]
	public void ServerFX(Vector3 position, Quaternion rotation)
	{
		this.RpcWriter___Server_ServerFX_3848837105(position, rotation);
		this.RpcLogic___ServerFX_3848837105(position, rotation);
	}

	// Token: 0x06000753 RID: 1875 RVA: 0x00031B53 File Offset: 0x0002FD53
	[ObserversRpc(RunLocally = true, ExcludeOwner = true)]
	private void ObserversFX(Vector3 position, Quaternion rotation)
	{
		this.RpcWriter___Observers_ObserversFX_3848837105(position, rotation);
		this.RpcLogic___ObserversFX_3848837105(position, rotation);
	}

	// Token: 0x06000754 RID: 1876 RVA: 0x00031B74 File Offset: 0x0002FD74
	[ObserversRpc(RunLocally = true, ExcludeOwner = true)]
	private void ShootObserversEffect()
	{
		this.RpcWriter___Observers_ShootObserversEffect_2166136261();
		this.RpcLogic___ShootObserversEffect_2166136261();
	}

	// Token: 0x06000755 RID: 1877 RVA: 0x00031B8D File Offset: 0x0002FD8D
	[ServerRpc(RunLocally = true)]
	private void SpawnBulletTrailServer(Vector3 hitPoint)
	{
		this.RpcWriter___Server_SpawnBulletTrailServer_4276783012(hitPoint);
		this.RpcLogic___SpawnBulletTrailServer_4276783012(hitPoint);
	}

	// Token: 0x06000756 RID: 1878 RVA: 0x00031BA4 File Offset: 0x0002FDA4
	[ObserversRpc(RunLocally = true, ExcludeOwner = true)]
	private void SpawnBulletTrail(Vector3 hitPoint)
	{
		this.RpcWriter___Observers_SpawnBulletTrail_4276783012(hitPoint);
		this.RpcLogic___SpawnBulletTrail_4276783012(hitPoint);
	}

	// Token: 0x06000757 RID: 1879 RVA: 0x00031BC5 File Offset: 0x0002FDC5
	[ServerRpc(RunLocally = true)]
	private void SpawnVFXServer(int index, Vector3 hitPoint, Vector3 hitNormal, string surface, Transform parent)
	{
		this.RpcWriter___Server_SpawnVFXServer_606331033(index, hitPoint, hitNormal, surface, parent);
		this.RpcLogic___SpawnVFXServer_606331033(index, hitPoint, hitNormal, surface, parent);
	}

	// Token: 0x06000758 RID: 1880 RVA: 0x00031BFC File Offset: 0x0002FDFC
	[ObserversRpc(RunLocally = true, ExcludeOwner = true)]
	private void SpawnVFX(int index, Vector3 hitPoint, Vector3 hitNormal, string surface, Transform parent)
	{
		this.RpcWriter___Observers_SpawnVFX_606331033(index, hitPoint, hitNormal, surface, parent);
		this.RpcLogic___SpawnVFX_606331033(index, hitPoint, hitNormal, surface, parent);
	}

	// Token: 0x06000759 RID: 1881 RVA: 0x0002B771 File Offset: 0x00029971
	private void LocalSound(int index)
	{
		if (index == 0)
		{
			this.audio.PlayOneShot(this.headHitClip);
		}
		if (index == 1)
		{
			this.audio.PlayOneShot(this.bodyHitClip);
		}
		if (index == 2)
		{
			this.audio.PlayOneShot(this.deathClip);
		}
	}

	// Token: 0x0600075A RID: 1882 RVA: 0x00031C3D File Offset: 0x0002FE3D
	private IEnumerator Reload()
	{
		this.isReloading = true;
		this.audio.PlayOneShot(this.reloadClip);
		if (this.animator != null)
		{
			this.animator.SetTrigger("Reload");
		}
		yield return new WaitForSeconds(this.reloadTime);
		if (base.SyncAccessor_currentAmmo - this.ammoCharge >= 0)
		{
			base.sync___set_value_currentAmmo(base.SyncAccessor_currentAmmo - this.ammoCharge, true);
			this.chargedBullets = (float)this.ammoCharge;
		}
		else
		{
			this.chargedBullets = (float)base.SyncAccessor_currentAmmo;
			base.sync___set_value_currentAmmo(0, true);
		}
		this.isReloading = false;
		yield break;
	}

	// Token: 0x0600075B RID: 1883 RVA: 0x00031C4C File Offset: 0x0002FE4C
	private IEnumerator BurstFire()
	{
		this.isBursting = true;
		int num2;
		for (int i = 0; i < this.bulletsAmount; i = num2 + 1)
		{
			if (base.SyncAccessor_currentAmmo > 0)
			{
				float num = ((this.playerController.isSprinting || !this.playerController.isGrounded || !this.playerController.safeGrounded) ? Mathf.Lerp(this.maxSpread, this.minSpread, this.sprintAccuracy) : (this.playerController.isCrouching ? Mathf.Lerp(this.maxSpread, this.minSpread, this.standingAccuracy) : (this.playerController.isWalking ? Mathf.Lerp(this.maxSpread, this.minSpread, this.walkAccuracy) : Mathf.Lerp(this.maxSpread, this.minSpread, this.standingAccuracy))));
				this.spread = this.cam.transform.right * global::UnityEngine.Random.Range(num, -num) + this.cam.transform.up * global::UnityEngine.Random.Range(num, -num);
				this.spread *= this.additionalPrecision;
				this.ShootServer(this.damage, this.cam.transform.position, this.cam.transform.forward + this.spread);
				if (this.revolverShake)
				{
					base.CameraRevolverAnimation();
				}
				else
				{
					base.CameraAnimation();
				}
				base.WeaponAnimation();
				yield return new WaitForSeconds(this.timeBetweenBullets);
			}
			num2 = i;
		}
		this.isBursting = false;
		yield break;
	}

	// Token: 0x0600075C RID: 1884 RVA: 0x00031C5B File Offset: 0x0002FE5B
	[ServerRpc]
	private void SupressionServer(Transform supp)
	{
		this.RpcWriter___Server_SupressionServer_3068987916(supp);
	}

	// Token: 0x0600075D RID: 1885 RVA: 0x00031C67 File Offset: 0x0002FE67
	[ObserversRpc]
	private void SuppressionTarget(Transform supp)
	{
		this.RpcWriter___Observers_SuppressionTarget_3068987916(supp);
	}

	// Token: 0x0600075F RID: 1887 RVA: 0x00031C74 File Offset: 0x0002FE74
	public override void NetworkInitialize___Early()
	{
		if (this.NetworkInitializeEarly_Gun_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeEarly_Gun_Assembly-CSharp.dll = true;
		base.NetworkInitialize___Early();
		base.RegisterServerRpc(15U, new ServerRpcDelegate(this.RpcReader___Server_InitChargedBullets_2166136261));
		base.RegisterObserversRpc(16U, new ClientRpcDelegate(this.RpcReader___Observers_InitChargedBulletsObservers_2166136261));
		base.RegisterServerRpc(17U, new ServerRpcDelegate(this.RpcReader___Server_RemoveAmmo_2166136261));
		base.RegisterServerRpc(18U, new ServerRpcDelegate(this.RpcReader___Server_GiveDamage_324487999));
		base.RegisterServerRpc(19U, new ServerRpcDelegate(this.RpcReader___Server_KillServer_1722911636));
		base.RegisterTargetRpc(20U, new ClientRpcDelegate(this.RpcReader___Target_KillObserver_123853379));
		base.RegisterObserversRpc(21U, new ClientRpcDelegate(this.RpcReader___Observers_HitFeeback_1722911636));
		base.RegisterServerRpc(22U, new ServerRpcDelegate(this.RpcReader___Server_ShootServerEffect_2166136261));
		base.RegisterServerRpc(23U, new ServerRpcDelegate(this.RpcReader___Server_ServerFX_3848837105));
		base.RegisterObserversRpc(24U, new ClientRpcDelegate(this.RpcReader___Observers_ObserversFX_3848837105));
		base.RegisterObserversRpc(25U, new ClientRpcDelegate(this.RpcReader___Observers_ShootObserversEffect_2166136261));
		base.RegisterServerRpc(26U, new ServerRpcDelegate(this.RpcReader___Server_SpawnBulletTrailServer_4276783012));
		base.RegisterObserversRpc(27U, new ClientRpcDelegate(this.RpcReader___Observers_SpawnBulletTrail_4276783012));
		base.RegisterServerRpc(28U, new ServerRpcDelegate(this.RpcReader___Server_SpawnVFXServer_606331033));
		base.RegisterObserversRpc(29U, new ClientRpcDelegate(this.RpcReader___Observers_SpawnVFX_606331033));
		base.RegisterServerRpc(30U, new ServerRpcDelegate(this.RpcReader___Server_SupressionServer_3068987916));
		base.RegisterObserversRpc(31U, new ClientRpcDelegate(this.RpcReader___Observers_SuppressionTarget_3068987916));
	}

	// Token: 0x06000760 RID: 1888 RVA: 0x00031E1F File Offset: 0x0003001F
	public override void NetworkInitialize__Late()
	{
		if (this.NetworkInitializeLate_Gun_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeLate_Gun_Assembly-CSharp.dll = true;
		base.NetworkInitialize__Late();
	}

	// Token: 0x06000761 RID: 1889 RVA: 0x00031E38 File Offset: 0x00030038
	public override void NetworkInitializeIfDisabled()
	{
		this.NetworkInitialize___Early();
		this.NetworkInitialize__Late();
	}

	// Token: 0x06000762 RID: 1890 RVA: 0x00031E48 File Offset: 0x00030048
	private void RpcWriter___Server_InitChargedBullets_2166136261()
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendServerRpc(15U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000763 RID: 1891 RVA: 0x00031EE2 File Offset: 0x000300E2
	private void RpcLogic___InitChargedBullets_2166136261()
	{
		this.InitChargedBulletsObservers();
		base.sync___set_value_currentAmmo(base.SyncAccessor_currentAmmo - this.ammoCharge, true);
	}

	// Token: 0x06000764 RID: 1892 RVA: 0x00031F00 File Offset: 0x00030100
	private void RpcReader___Server_InitChargedBullets_2166136261(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		if (!base.IsServer)
		{
			return;
		}
		this.RpcLogic___InitChargedBullets_2166136261();
	}

	// Token: 0x06000765 RID: 1893 RVA: 0x00031F20 File Offset: 0x00030120
	private void RpcWriter___Observers_InitChargedBulletsObservers_2166136261()
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendObserversRpc(16U, writer, channel, DataOrderType.Default, true, false, false);
		writer.Store();
	}

	// Token: 0x06000766 RID: 1894 RVA: 0x00031FC9 File Offset: 0x000301C9
	private void RpcLogic___InitChargedBulletsObservers_2166136261()
	{
		this.chargedBullets = (float)this.ammoCharge;
	}

	// Token: 0x06000767 RID: 1895 RVA: 0x00031FD8 File Offset: 0x000301D8
	private void RpcReader___Observers_InitChargedBulletsObservers_2166136261(PooledReader PooledReader0, Channel channel)
	{
		if (!base.IsClient)
		{
			return;
		}
		this.RpcLogic___InitChargedBulletsObservers_2166136261();
	}

	// Token: 0x06000768 RID: 1896 RVA: 0x00031FF8 File Offset: 0x000301F8
	private void RpcWriter___Server_RemoveAmmo_2166136261()
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendServerRpc(17U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000769 RID: 1897 RVA: 0x0002BFDC File Offset: 0x0002A1DC
	private void RpcLogic___RemoveAmmo_2166136261()
	{
		if (this.reloadWeapon)
		{
			this.chargedBullets -= 1f;
			return;
		}
		base.sync___set_value_currentAmmo(base.SyncAccessor_currentAmmo - 1, true);
	}

	// Token: 0x0600076A RID: 1898 RVA: 0x000320EC File Offset: 0x000302EC
	private void RpcReader___Server_RemoveAmmo_2166136261(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___RemoveAmmo_2166136261();
	}

	// Token: 0x0600076B RID: 1899 RVA: 0x0003212C File Offset: 0x0003032C
	private void RpcWriter___Server_GiveDamage_324487999(float damageToGive, PlayerHealth enemyHealth, string name)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteSingle(damageToGive, AutoPackType.Unpacked);
		writer.Write___PlayerHealthFishNet.Serializing.Generated(enemyHealth);
		writer.WriteString(name);
		base.SendServerRpc(18U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x0600076C RID: 1900 RVA: 0x0003224C File Offset: 0x0003044C
	private void RpcLogic___GiveDamage_324487999(float damageToGive, PlayerHealth enemyHealth, string name)
	{
		enemyHealth.sync___set_value_killer(this.rootObject.transform, true);
		enemyHealth.sync___set_value_health(enemyHealth.SyncAccessor_health - damageToGive, true);
		enemyHealth.KillCam();
		this.HitFeeback(enemyHealth);
		enemyHealth.Dismemberment(name);
	}

	// Token: 0x0600076D RID: 1901 RVA: 0x00032284 File Offset: 0x00030484
	private void RpcReader___Server_GiveDamage_324487999(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		float num = PooledReader0.ReadSingle(AutoPackType.Unpacked);
		PlayerHealth playerHealth = global::FishNet.Serializing.Generated.GeneratedReaders___Internal.Read___PlayerHealthFishNet.Serializing.Generateds(PooledReader0);
		string text = PooledReader0.ReadString();
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___GiveDamage_324487999(num, playerHealth, text);
	}

	// Token: 0x0600076E RID: 1902 RVA: 0x000322FC File Offset: 0x000304FC
	private void RpcWriter___Server_KillServer_1722911636(PlayerHealth enemyHealth)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.Write___PlayerHealthFishNet.Serializing.Generated(enemyHealth);
		base.SendServerRpc(19U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x0600076F RID: 1903 RVA: 0x00032400 File Offset: 0x00030600
	private void RpcLogic___KillServer_1722911636(PlayerHealth enemyHealth)
	{
		enemyHealth.sync___set_value_isShot(true, true);
		enemyHealth.sync___set_value_health(-8f, true);
		GameManager.Instance.PlayerDied(enemyHealth.SyncAccessor_playerValues.SyncAccessor_playerClient.PlayerId);
		if (this.rootObject != null)
		{
			enemyHealth.sync___set_value_killer(this.rootObject.transform, true);
		}
		this.KillObserver(enemyHealth.SyncAccessor_playerValues.SyncAccessor_playerClient.transform.GetComponent<NetworkObject>().Owner, enemyHealth.SyncAccessor_playerValues.SyncAccessor_playerClient, enemyHealth);
	}

	// Token: 0x06000770 RID: 1904 RVA: 0x00032488 File Offset: 0x00030688
	private void RpcReader___Server_KillServer_1722911636(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		PlayerHealth playerHealth = global::FishNet.Serializing.Generated.GeneratedReaders___Internal.Read___PlayerHealthFishNet.Serializing.Generateds(PooledReader0);
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		this.RpcLogic___KillServer_1722911636(playerHealth);
	}

	// Token: 0x06000771 RID: 1905 RVA: 0x000324CC File Offset: 0x000306CC
	private void RpcWriter___Target_KillObserver_123853379(NetworkConnection conn, ClientInstance client, PlayerHealth enemyHealth)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.Write___ClientInstanceFishNet.Serializing.Generated(client);
		writer.Write___PlayerHealthFishNet.Serializing.Generated(enemyHealth);
		base.SendTargetRpc(20U, writer, channel, DataOrderType.Default, conn, false, true);
		writer.Store();
	}

	// Token: 0x06000772 RID: 1906 RVA: 0x000275DA File Offset: 0x000257DA
	private void RpcLogic___KillObserver_123853379(NetworkConnection conn, ClientInstance client, PlayerHealth enemyHealth)
	{
		enemyHealth.shouldDropWeapon = true;
		enemyHealth.isDeadFromTargetRpc = true;
		if (this.rootObject != null)
		{
			GameObject.Find("Main Camera").GetComponent<KillCam>().enemy = this.rootObject.transform;
		}
	}

	// Token: 0x06000773 RID: 1907 RVA: 0x00032590 File Offset: 0x00030790
	private void RpcReader___Target_KillObserver_123853379(PooledReader PooledReader0, Channel channel)
	{
		ClientInstance clientInstance = global::FishNet.Serializing.Generated.GeneratedReaders___Internal.Read___ClientInstanceFishNet.Serializing.Generateds(PooledReader0);
		PlayerHealth playerHealth = global::FishNet.Serializing.Generated.GeneratedReaders___Internal.Read___PlayerHealthFishNet.Serializing.Generateds(PooledReader0);
		if (!base.IsClient)
		{
			return;
		}
		this.RpcLogic___KillObserver_123853379(base.LocalConnection, clientInstance, playerHealth);
	}

	// Token: 0x06000774 RID: 1908 RVA: 0x000325D8 File Offset: 0x000307D8
	private void RpcWriter___Observers_HitFeeback_1722911636(PlayerHealth enemyHealth)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.Write___PlayerHealthFishNet.Serializing.Generated(enemyHealth);
		base.SendObserversRpc(21U, writer, channel, DataOrderType.Default, false, false, false);
		writer.Store();
	}

	// Token: 0x06000775 RID: 1909 RVA: 0x00027716 File Offset: 0x00025916
	private void RpcLogic___HitFeeback_1722911636(PlayerHealth enemyHealth)
	{
		enemyHealth.HitFeeback();
	}

	// Token: 0x06000776 RID: 1910 RVA: 0x00032690 File Offset: 0x00030890
	private void RpcReader___Observers_HitFeeback_1722911636(PooledReader PooledReader0, Channel channel)
	{
		PlayerHealth playerHealth = global::FishNet.Serializing.Generated.GeneratedReaders___Internal.Read___PlayerHealthFishNet.Serializing.Generateds(PooledReader0);
		if (!base.IsClient)
		{
			return;
		}
		this.RpcLogic___HitFeeback_1722911636(playerHealth);
	}

	// Token: 0x06000777 RID: 1911 RVA: 0x000326C4 File Offset: 0x000308C4
	private void RpcWriter___Server_ShootServerEffect_2166136261()
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendServerRpc(22U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000778 RID: 1912 RVA: 0x000327B8 File Offset: 0x000309B8
	private void RpcLogic___ShootServerEffect_2166136261()
	{
		this.ShootObserversEffect();
	}

	// Token: 0x06000779 RID: 1913 RVA: 0x000327C0 File Offset: 0x000309C0
	private void RpcReader___Server_ShootServerEffect_2166136261(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___ShootServerEffect_2166136261();
	}

	// Token: 0x0600077A RID: 1914 RVA: 0x00032800 File Offset: 0x00030A00
	private void RpcWriter___Server_ServerFX_3848837105(Vector3 position, Quaternion rotation)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteVector3(position);
		writer.WriteQuaternion(rotation, AutoPackType.Packed);
		base.SendServerRpc(23U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x0600077B RID: 1915 RVA: 0x00032913 File Offset: 0x00030B13
	public void RpcLogic___ServerFX_3848837105(Vector3 position, Quaternion rotation)
	{
		this.ObserversFX(position, rotation);
	}

	// Token: 0x0600077C RID: 1916 RVA: 0x00032920 File Offset: 0x00030B20
	private void RpcReader___Server_ServerFX_3848837105(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		Vector3 vector = PooledReader0.ReadVector3();
		Quaternion quaternion = PooledReader0.ReadQuaternion(AutoPackType.Packed);
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___ServerFX_3848837105(vector, quaternion);
	}

	// Token: 0x0600077D RID: 1917 RVA: 0x00032988 File Offset: 0x00030B88
	private void RpcWriter___Observers_ObserversFX_3848837105(Vector3 position, Quaternion rotation)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteVector3(position);
		writer.WriteQuaternion(rotation, AutoPackType.Packed);
		base.SendObserversRpc(24U, writer, channel, DataOrderType.Default, false, false, true);
		writer.Store();
	}

	// Token: 0x0600077E RID: 1918 RVA: 0x000272E4 File Offset: 0x000254E4
	private void RpcLogic___ObserversFX_3848837105(Vector3 position, Quaternion rotation)
	{
		global::UnityEngine.Object.Instantiate<GameObject>(this.bloodSplatter, position, rotation);
	}

	// Token: 0x0600077F RID: 1919 RVA: 0x00032A50 File Offset: 0x00030C50
	private void RpcReader___Observers_ObserversFX_3848837105(PooledReader PooledReader0, Channel channel)
	{
		Vector3 vector = PooledReader0.ReadVector3();
		Quaternion quaternion = PooledReader0.ReadQuaternion(AutoPackType.Packed);
		if (!base.IsClient)
		{
			return;
		}
		if (base.IsHost)
		{
			return;
		}
		this.RpcLogic___ObserversFX_3848837105(vector, quaternion);
	}

	// Token: 0x06000780 RID: 1920 RVA: 0x00032AA4 File Offset: 0x00030CA4
	private void RpcWriter___Observers_ShootObserversEffect_2166136261()
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendObserversRpc(25U, writer, channel, DataOrderType.Default, false, false, true);
		writer.Store();
	}

	// Token: 0x06000781 RID: 1921 RVA: 0x00032B50 File Offset: 0x00030D50
	private void RpcLogic___ShootObserversEffect_2166136261()
	{
		if (Settings.Instance.reduceVFX)
		{
			return;
		}
		this.audio.PlayOneShot(this.fireClip);
		if (this.behaviour.smokeTrail != null && !this.behaviour.smokeTrail.isPlaying)
		{
			this.behaviour.smokeTrail.Play();
		}
		GameObject gameObject = global::UnityEngine.Object.Instantiate<GameObject>(this.muzzleFlash, this.muzzleFlashPoint.position, this.shootPoint.rotation, this.behaviour.vfxAttachedOnGun ? base.transform : null);
		foreach (Transform transform in gameObject.transform.GetComponentsInChildren<Transform>(true))
		{
			if (transform.GetComponent<Light>() == null && transform.tag != "vfx" && base.IsOwner)
			{
				transform.gameObject.layer = 8;
			}
			if (transform.GetComponent<Light>() != null)
			{
				transform.GetComponent<Light>().intensity = this.lightIntensity;
			}
		}
		ParticleSystem[] componentsInChildren2 = gameObject.GetComponentsInChildren<ParticleSystem>();
		for (int i = 0; i < componentsInChildren2.Length; i++)
		{
			componentsInChildren2[i].Play();
		}
	}

	// Token: 0x06000782 RID: 1922 RVA: 0x00032C7C File Offset: 0x00030E7C
	private void RpcReader___Observers_ShootObserversEffect_2166136261(PooledReader PooledReader0, Channel channel)
	{
		if (!base.IsClient)
		{
			return;
		}
		if (base.IsHost)
		{
			return;
		}
		this.RpcLogic___ShootObserversEffect_2166136261();
	}

	// Token: 0x06000783 RID: 1923 RVA: 0x00032CA8 File Offset: 0x00030EA8
	private void RpcWriter___Server_SpawnBulletTrailServer_4276783012(Vector3 hitPoint)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteVector3(hitPoint);
		base.SendServerRpc(26U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000784 RID: 1924 RVA: 0x00032DA9 File Offset: 0x00030FA9
	private void RpcLogic___SpawnBulletTrailServer_4276783012(Vector3 hitPoint)
	{
		this.SpawnBulletTrail(hitPoint);
	}

	// Token: 0x06000785 RID: 1925 RVA: 0x00032DB4 File Offset: 0x00030FB4
	private void RpcReader___Server_SpawnBulletTrailServer_4276783012(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		Vector3 vector = PooledReader0.ReadVector3();
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___SpawnBulletTrailServer_4276783012(vector);
	}

	// Token: 0x06000786 RID: 1926 RVA: 0x00032E04 File Offset: 0x00031004
	private void RpcWriter___Observers_SpawnBulletTrail_4276783012(Vector3 hitPoint)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteVector3(hitPoint);
		base.SendObserversRpc(27U, writer, channel, DataOrderType.Default, false, false, true);
		writer.Store();
	}

	// Token: 0x06000787 RID: 1927 RVA: 0x00032EBC File Offset: 0x000310BC
	private void RpcLogic___SpawnBulletTrail_4276783012(Vector3 hitPoint)
	{
		GameObject gameObject = global::UnityEngine.Object.Instantiate<GameObject>(this.bulletTrailLocal.gameObject, this.shootPoint.position, Quaternion.identity);
		LineRenderer component = gameObject.GetComponent<LineRenderer>();
		component.SetPosition(0, this.shootPoint.position);
		component.SetPosition(1, hitPoint);
		global::UnityEngine.Object.Destroy(gameObject, 0.4f);
	}

	// Token: 0x06000788 RID: 1928 RVA: 0x00032F14 File Offset: 0x00031114
	private void RpcReader___Observers_SpawnBulletTrail_4276783012(PooledReader PooledReader0, Channel channel)
	{
		Vector3 vector = PooledReader0.ReadVector3();
		if (!base.IsClient)
		{
			return;
		}
		if (base.IsHost)
		{
			return;
		}
		this.RpcLogic___SpawnBulletTrail_4276783012(vector);
	}

	// Token: 0x06000789 RID: 1929 RVA: 0x00032F50 File Offset: 0x00031150
	private void RpcWriter___Server_SpawnVFXServer_606331033(int index, Vector3 hitPoint, Vector3 hitNormal, string surface, Transform parent)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteInt32(index, AutoPackType.Packed);
		writer.WriteVector3(hitPoint);
		writer.WriteVector3(hitNormal);
		writer.WriteString(surface);
		writer.WriteTransform(parent);
		base.SendServerRpc(28U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x0600078A RID: 1930 RVA: 0x0003308A File Offset: 0x0003128A
	private void RpcLogic___SpawnVFXServer_606331033(int index, Vector3 hitPoint, Vector3 hitNormal, string surface, Transform parent)
	{
		this.SpawnVFX(index, hitPoint, hitNormal, surface, parent);
	}

	// Token: 0x0600078B RID: 1931 RVA: 0x0003309C File Offset: 0x0003129C
	private void RpcReader___Server_SpawnVFXServer_606331033(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		int num = PooledReader0.ReadInt32(AutoPackType.Packed);
		Vector3 vector = PooledReader0.ReadVector3();
		Vector3 vector2 = PooledReader0.ReadVector3();
		string text = PooledReader0.ReadString();
		Transform transform = PooledReader0.ReadTransform();
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___SpawnVFXServer_606331033(num, vector, vector2, text, transform);
	}

	// Token: 0x0600078C RID: 1932 RVA: 0x00033134 File Offset: 0x00031334
	private void RpcWriter___Observers_SpawnVFX_606331033(int index, Vector3 hitPoint, Vector3 hitNormal, string surface, Transform parent)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteInt32(index, AutoPackType.Packed);
		writer.WriteVector3(hitPoint);
		writer.WriteVector3(hitNormal);
		writer.WriteString(surface);
		writer.WriteTransform(parent);
		base.SendObserversRpc(29U, writer, channel, DataOrderType.Default, false, false, true);
		writer.Store();
	}

	// Token: 0x0600078D RID: 1933 RVA: 0x00033224 File Offset: 0x00031424
	private void RpcLogic___SpawnVFX_606331033(int index, Vector3 hitPoint, Vector3 hitNormal, string surface, Transform parent)
	{
		if (Settings.Instance.reduceVFX)
		{
			return;
		}
		if (this.genericImpact)
		{
			global::UnityEngine.Object.Instantiate<GameObject>(this.genericImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
		}
		uint num = <PrivateImplementationDetails>.ComputeStringHash(surface);
		if (num <= 1825421690U)
		{
			if (num <= 1378315797U)
			{
				if (num <= 464173256U)
				{
					if (num != 456440475U)
					{
						if (num == 464173256U)
						{
							if (surface == "Footsteps/Concrete/Dalles")
							{
								if (index == 0)
								{
									global::UnityEngine.Object.Instantiate<GameObject>(this.concreteHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
								}
								if (index == 1)
								{
									global::UnityEngine.Object.Instantiate<GameObject>(this.bulletHole, hitPoint, Quaternion.LookRotation(hitNormal), parent);
									goto IL_06E3;
								}
								goto IL_06E3;
							}
						}
					}
					else if (surface == "Footsteps/Sand")
					{
						if (index == 0)
						{
							global::UnityEngine.Object.Instantiate<GameObject>(this.sandHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
						}
						if (index == 1)
						{
							global::UnityEngine.Object.Instantiate<GameObject>(this.sandHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
							goto IL_06E3;
						}
						goto IL_06E3;
					}
				}
				else if (num != 913360285U)
				{
					if (num != 977466297U)
					{
						if (num == 1378315797U)
						{
							if (surface == "Footsteps/Moquette")
							{
								if (index == 0)
								{
									global::UnityEngine.Object.Instantiate<GameObject>(this.softbodyHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
								}
								if (index == 1)
								{
									global::UnityEngine.Object.Instantiate<GameObject>(this.softbodyHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
									goto IL_06E3;
								}
								goto IL_06E3;
							}
						}
					}
					else if (surface == "Footsteps/Concrete/Solide")
					{
						if (index == 0)
						{
							global::UnityEngine.Object.Instantiate<GameObject>(this.concreteHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
						}
						if (index == 1)
						{
							global::UnityEngine.Object.Instantiate<GameObject>(this.bulletHole, hitPoint, Quaternion.LookRotation(hitNormal), parent);
							goto IL_06E3;
						}
						goto IL_06E3;
					}
				}
				else if (surface == "Grenade")
				{
					goto IL_06E3;
				}
			}
			else if (num <= 1430892386U)
			{
				if (num != 1429664136U)
				{
					if (num == 1430892386U)
					{
						if (surface == "Hat")
						{
							goto IL_06E3;
						}
					}
				}
				else if (surface == "Footsteps/Water")
				{
					if (index == 0)
					{
						global::UnityEngine.Object.Instantiate<GameObject>(this.waterHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
					}
					if (index == 1)
					{
						global::UnityEngine.Object.Instantiate<GameObject>(this.waterHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
						goto IL_06E3;
					}
					goto IL_06E3;
				}
			}
			else if (num != 1610969363U)
			{
				if (num != 1624496828U)
				{
					if (num == 1825421690U)
					{
						if (surface == "Footsteps/Wood/Creux")
						{
							if (index == 0)
							{
								global::UnityEngine.Object.Instantiate<GameObject>(this.woodHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
							}
							if (index == 1)
							{
								global::UnityEngine.Object.Instantiate<GameObject>(this.woodHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
								goto IL_06E3;
							}
							goto IL_06E3;
						}
					}
				}
				else if (surface == "Footsteps/Concrete/Default")
				{
					if (index == 0)
					{
						global::UnityEngine.Object.Instantiate<GameObject>(this.concreteHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
					}
					if (index == 1)
					{
						global::UnityEngine.Object.Instantiate<GameObject>(this.bulletHole, hitPoint, Quaternion.LookRotation(hitNormal), parent);
						goto IL_06E3;
					}
					goto IL_06E3;
				}
			}
			else if (surface == "Footsteps/Concrete/PleinAir")
			{
				if (index == 0)
				{
					global::UnityEngine.Object.Instantiate<GameObject>(this.concreteHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
				}
				if (index == 1)
				{
					global::UnityEngine.Object.Instantiate<GameObject>(this.bulletHole, hitPoint, Quaternion.LookRotation(hitNormal), parent);
					goto IL_06E3;
				}
				goto IL_06E3;
			}
		}
		else if (num <= 2833365437U)
		{
			if (num <= 2180110808U)
			{
				if (num != 1954265137U)
				{
					if (num == 2180110808U)
					{
						if (surface == "Footsteps/Dirt")
						{
							if (index == 0)
							{
								global::UnityEngine.Object.Instantiate<GameObject>(this.dirtHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
							}
							if (index == 1)
							{
								global::UnityEngine.Object.Instantiate<GameObject>(this.dirtHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
								goto IL_06E3;
							}
							goto IL_06E3;
						}
					}
				}
				else if (surface == "NoSound")
				{
					goto IL_06E3;
				}
			}
			else if (num != 2251139421U)
			{
				if (num != 2400559108U)
				{
					if (num == 2833365437U)
					{
						if (surface == "Footsteps/Metal/Pipe2")
						{
							if (index == 0)
							{
								global::UnityEngine.Object.Instantiate<GameObject>(this.metalHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
							}
							if (index == 1)
							{
								global::UnityEngine.Object.Instantiate<GameObject>(this.metalHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
								goto IL_06E3;
							}
							goto IL_06E3;
						}
					}
				}
				else if (surface == "Footsteps/Matelas")
				{
					if (index == 0)
					{
						global::UnityEngine.Object.Instantiate<GameObject>(this.softbodyHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
					}
					if (index == 1)
					{
						global::UnityEngine.Object.Instantiate<GameObject>(this.softbodyHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
						goto IL_06E3;
					}
					goto IL_06E3;
				}
			}
			else if (surface == "Footsteps/Metal/Pipe")
			{
				if (index == 0)
				{
					global::UnityEngine.Object.Instantiate<GameObject>(this.tauleHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
				}
				if (index == 1)
				{
					global::UnityEngine.Object.Instantiate<GameObject>(this.tauleHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
					goto IL_06E3;
				}
				goto IL_06E3;
			}
		}
		else if (num <= 3455850406U)
		{
			if (num != 3075599786U)
			{
				if (num == 3455850406U)
				{
					if (surface == "Footsteps/Graviers")
					{
						if (index == 0)
						{
							global::UnityEngine.Object.Instantiate<GameObject>(this.concreteHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
						}
						if (index == 1)
						{
							global::UnityEngine.Object.Instantiate<GameObject>(this.bulletHole, hitPoint, Quaternion.LookRotation(hitNormal), parent);
							goto IL_06E3;
						}
						goto IL_06E3;
					}
				}
			}
			else if (surface == "Footsteps/Wood/Sec")
			{
				if (index == 0)
				{
					global::UnityEngine.Object.Instantiate<GameObject>(this.woodHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
				}
				if (index == 1)
				{
					global::UnityEngine.Object.Instantiate<GameObject>(this.woodHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
					goto IL_06E3;
				}
				goto IL_06E3;
			}
		}
		else if (num != 3492154005U)
		{
			if (num != 3807801418U)
			{
				if (num == 3848897750U)
				{
					if (surface == "Mine")
					{
						goto IL_06E3;
					}
				}
			}
			else if (surface == "Footsteps/Metal/Grille")
			{
				if (index == 0)
				{
					global::UnityEngine.Object.Instantiate<GameObject>(this.tauleHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
				}
				if (index == 1)
				{
					global::UnityEngine.Object.Instantiate<GameObject>(this.tauleHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
					goto IL_06E3;
				}
				goto IL_06E3;
			}
		}
		else if (surface == "Footsteps/Grass")
		{
			if (index == 0)
			{
				global::UnityEngine.Object.Instantiate<GameObject>(this.dirtHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
			}
			if (index == 1)
			{
				global::UnityEngine.Object.Instantiate<GameObject>(this.dirtHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
				goto IL_06E3;
			}
			goto IL_06E3;
		}
		if (index == 0)
		{
			global::UnityEngine.Object.Instantiate<GameObject>(this.concreteHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
		}
		if (index == 1)
		{
			global::UnityEngine.Object.Instantiate<GameObject>(this.bulletHole, hitPoint, Quaternion.LookRotation(hitNormal), parent);
		}
		IL_06E3:
		if (index == 2)
		{
			global::UnityEngine.Object.Instantiate<GameObject>(this.bodyImpact, hitPoint, Quaternion.identity, parent);
		}
	}

	// Token: 0x0600078E RID: 1934 RVA: 0x0003392C File Offset: 0x00031B2C
	private void RpcReader___Observers_SpawnVFX_606331033(PooledReader PooledReader0, Channel channel)
	{
		int num = PooledReader0.ReadInt32(AutoPackType.Packed);
		Vector3 vector = PooledReader0.ReadVector3();
		Vector3 vector2 = PooledReader0.ReadVector3();
		string text = PooledReader0.ReadString();
		Transform transform = PooledReader0.ReadTransform();
		if (!base.IsClient)
		{
			return;
		}
		if (base.IsHost)
		{
			return;
		}
		this.RpcLogic___SpawnVFX_606331033(num, vector, vector2, text, transform);
	}

	// Token: 0x0600078F RID: 1935 RVA: 0x000339B0 File Offset: 0x00031BB0
	private void RpcWriter___Server_SupressionServer_3068987916(Transform supp)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteTransform(supp);
		base.SendServerRpc(30U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000790 RID: 1936 RVA: 0x00033AB1 File Offset: 0x00031CB1
	private void RpcLogic___SupressionServer_3068987916(Transform supp)
	{
		this.SuppressionTarget(supp);
	}

	// Token: 0x06000791 RID: 1937 RVA: 0x00033ABC File Offset: 0x00031CBC
	private void RpcReader___Server_SupressionServer_3068987916(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		Transform transform = PooledReader0.ReadTransform();
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		this.RpcLogic___SupressionServer_3068987916(transform);
	}

	// Token: 0x06000792 RID: 1938 RVA: 0x00033B00 File Offset: 0x00031D00
	private void RpcWriter___Observers_SuppressionTarget_3068987916(Transform supp)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteTransform(supp);
		base.SendObserversRpc(31U, writer, channel, DataOrderType.Default, false, false, false);
		writer.Store();
	}

	// Token: 0x06000793 RID: 1939 RVA: 0x00033BB6 File Offset: 0x00031DB6
	private void RpcLogic___SuppressionTarget_3068987916(Transform supp)
	{
		if (supp == null)
		{
			return;
		}
		if (supp.GetComponent<Suppression>() == null)
		{
			return;
		}
		supp.GetComponent<Suppression>().SuppressionTrigger();
	}

	// Token: 0x06000794 RID: 1940 RVA: 0x00033BDC File Offset: 0x00031DDC
	private void RpcReader___Observers_SuppressionTarget_3068987916(PooledReader PooledReader0, Channel channel)
	{
		Transform transform = PooledReader0.ReadTransform();
		if (!base.IsClient)
		{
			return;
		}
		this.RpcLogic___SuppressionTarget_3068987916(transform);
	}

	// Token: 0x06000795 RID: 1941 RVA: 0x00033C0D File Offset: 0x00031E0D
	public override void Awake()
	{
		this.NetworkInitialize___Early();
		base.Awake();
		this.NetworkInitialize__Late();
	}

	// Token: 0x06000796 RID: 1942 RVA: 0x000023D6 File Offset: 0x000005D6
	public override void Awake___UserLogic()
	{
	}

	// Token: 0x040006B6 RID: 1718
	[Header("Weapon Specials")]
	[SerializeField]
	private float reloadTime;

	// Token: 0x040006B7 RID: 1719
	[SerializeField]
	private AudioClip reloadClip;

	// Token: 0x040006B8 RID: 1720
	private float fireTimer;

	// Token: 0x040006B9 RID: 1721
	private bool touched;

	// Token: 0x040006BA RID: 1722
	private Vector3 spread;

	// Token: 0x040006BB RID: 1723
	private bool hitOK;

	// Token: 0x040006BC RID: 1724
	private PlayerHealth enemyHealth;

	// Token: 0x040006BD RID: 1725
	private bool isBursting;

	// Token: 0x040006BE RID: 1726
	private bool NetworkInitializeEarly_Gun_Assembly-CSharp.dll;

	// Token: 0x040006BF RID: 1727
	private bool NetworkInitializeLate_Gun_Assembly-CSharp.dll;
}

﻿using System;
using DG.Tweening;
using DG.Tweening.Core;
using DG.Tweening.Plugins.Options;
using UnityEngine;

// Token: 0x02000134 RID: 308
public class ScaleTweenLoop : MonoBehaviour
{
	// Token: 0x06000E78 RID: 3704 RVA: 0x0005FA94 File Offset: 0x0005DC94
	private void Start()
	{
		if (this.loopOnStart)
		{
			base.transform.DOScale(base.transform.localScale * this.size, this.time).SetEase(this.ease).SetLoops(-1, LoopType.Yoyo);
		}
	}

	// Token: 0x06000E79 RID: 3705 RVA: 0x0005FAE4 File Offset: 0x0005DCE4
	public void TempTween()
	{
		base.transform.DOScale(base.transform.localScale * this.size, this.time).SetEase(this.ease).SetLoops(this.loopsAmount, LoopType.Yoyo);
	}

	// Token: 0x04000D01 RID: 3329
	[SerializeField]
	private Ease ease;

	// Token: 0x04000D02 RID: 3330
	[SerializeField]
	private float time = 0.5f;

	// Token: 0x04000D03 RID: 3331
	[SerializeField]
	private float size = 1.5f;

	// Token: 0x04000D04 RID: 3332
	[SerializeField]
	private int loopsAmount = 2;

	// Token: 0x04000D05 RID: 3333
	[SerializeField]
	private bool loopOnStart;
}

﻿using System;
using UnityEngine;
using UnityEngine.EventSystems;

// Token: 0x02000138 RID: 312
public class Tooltip : <PERSON>o<PERSON><PERSON><PERSON><PERSON>, IPointerEnterHandler, IEventSystemHandler, IPointerExitHandler
{
	// Token: 0x06000E83 RID: 3715 RVA: 0x0005FCFC File Offset: 0x0005DEFC
	public void OnPointerEnter(PointerEventData eventData)
	{
		if (!Application.isFocused)
		{
			return;
		}
		FloatingName.Instance.nameToShow = this.customText;
	}

	// Token: 0x06000E84 RID: 3716 RVA: 0x0005FD16 File Offset: 0x0005DF16
	public void OnPointerExit(PointerEventData eventData)
	{
		if (!Application.isFocused)
		{
			return;
		}
		FloatingName.Instance.nameToShow = "";
	}

	// Token: 0x04000D11 RID: 3345
	[SerializeField]
	private string customText;
}

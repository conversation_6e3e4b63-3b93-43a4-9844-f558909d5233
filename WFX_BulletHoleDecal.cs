﻿using System;
using System.Collections;
using UnityEngine;

// Token: 0x0200016C RID: 364
[RequireComponent(typeof(MeshFilter))]
public class WFX_BulletHoleDecal : MonoBehaviour
{
	// Token: 0x06000F55 RID: 3925 RVA: 0x00064FF8 File Offset: 0x000631F8
	private void Awake()
	{
		this.color = base.GetComponent<Renderer>().material.GetColor("_TintColor");
		this.orgAlpha = this.color.a;
	}

	// Token: 0x06000F56 RID: 3926 RVA: 0x00065028 File Offset: 0x00063228
	private void OnEnable()
	{
		int num = global::UnityEngine.Random.Range(0, (int)(this.frames.x * this.frames.y));
		int num2 = (int)((float)num % this.frames.x);
		int num3 = (int)((float)num / this.frames.y);
		Vector2[] array = new Vector2[4];
		for (int i = 0; i < 4; i++)
		{
			array[i].x = (WFX_BulletHoleDecal.quadUVs[i].x + (float)num2) * (1f / this.frames.x);
			array[i].y = (WFX_BulletHoleDecal.quadUVs[i].y + (float)num3) * (1f / this.frames.y);
		}
		base.GetComponent<MeshFilter>().mesh.uv = array;
		if (this.randomRotation)
		{
			base.transform.Rotate(0f, 0f, global::UnityEngine.Random.Range(0f, 360f), Space.Self);
		}
		this.life = this.lifetime;
		this.fadeout = this.life * (this.fadeoutpercent / 100f);
		this.color.a = this.orgAlpha;
		base.GetComponent<Renderer>().material.SetColor("_TintColor", this.color);
		base.StopAllCoroutines();
		base.StartCoroutine("holeUpdate");
	}

	// Token: 0x06000F57 RID: 3927 RVA: 0x00065188 File Offset: 0x00063388
	private IEnumerator holeUpdate()
	{
		while (this.life > 0f)
		{
			this.life -= Time.deltaTime;
			if (this.life <= this.fadeout)
			{
				this.color.a = Mathf.Lerp(0f, this.orgAlpha, this.life / this.fadeout);
				base.GetComponent<Renderer>().material.SetColor("_TintColor", this.color);
			}
			yield return null;
		}
		yield break;
	}

	// Token: 0x04000E21 RID: 3617
	private static Vector2[] quadUVs = new Vector2[]
	{
		new Vector2(0f, 0f),
		new Vector2(0f, 1f),
		new Vector2(1f, 0f),
		new Vector2(1f, 1f)
	};

	// Token: 0x04000E22 RID: 3618
	public float lifetime = 10f;

	// Token: 0x04000E23 RID: 3619
	public float fadeoutpercent = 80f;

	// Token: 0x04000E24 RID: 3620
	public Vector2 frames;

	// Token: 0x04000E25 RID: 3621
	public bool randomRotation;

	// Token: 0x04000E26 RID: 3622
	public bool deactivate;

	// Token: 0x04000E27 RID: 3623
	private float life;

	// Token: 0x04000E28 RID: 3624
	private float fadeout;

	// Token: 0x04000E29 RID: 3625
	private Color color;

	// Token: 0x04000E2A RID: 3626
	private float orgAlpha;
}

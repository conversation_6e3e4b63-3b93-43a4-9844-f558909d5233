﻿using System;
using UnityEngine;

namespace UnityStandardAssets.Water
{
	// Token: 0x02000199 RID: 409
	[ExecuteInEditMode]
	[RequireComponent(typeof(WaterBase))]
	public class Displace : MonoBehaviour
	{
		// Token: 0x0600103B RID: 4155 RVA: 0x00069736 File Offset: 0x00067936
		public void Awake()
		{
			if (base.enabled)
			{
				this.OnEnable();
				return;
			}
			this.OnDisable();
		}

		// Token: 0x0600103C RID: 4156 RVA: 0x0006974D File Offset: 0x0006794D
		public void OnEnable()
		{
			Shader.EnableKeyword("WATER_VERTEX_DISPLACEMENT_ON");
			Shader.DisableKeyword("WATER_VERTEX_DISPLACEMENT_OFF");
		}

		// Token: 0x0600103D RID: 4157 RVA: 0x00069763 File Offset: 0x00067963
		public void OnDisable()
		{
			Shader.EnableKeyword("WATER_VERTEX_DISPLACEMENT_OFF");
			Shader.DisableKeyword("WATER_VERTEX_DISPLACEMENT_ON");
		}
	}
}

﻿using System;
using UnityEngine;

// Token: 0x0200000B RID: 11
public class TimerTrigger : MonoBehaviour
{
	// Token: 0x0600004F RID: 79 RVA: 0x00003CC8 File Offset: 0x00001EC8
	private void Start()
	{
		this.timerManager = global::UnityEngine.Object.FindObjectOfType<TimerManager>();
		this.courseName = base.transform.parent.name;
		this.renderer = base.GetComponent<Renderer>();
		this.matDefault = this.renderer.material;
		this.HideMat();
	}

	// Token: 0x06000050 RID: 80 RVA: 0x00003D1C File Offset: 0x00001F1C
	public void SetMatActive(string course, int checkpointNumber)
	{
		if (this.courseName != course)
		{
			return;
		}
		if (this.type != TimerTrigger.TriggerType.Checkpoint)
		{
			return;
		}
		if (checkpointNumber == base.transform.GetSiblingIndex() - 1)
		{
			this.renderer.material = this.checkpointMatActive;
			return;
		}
		this.ResetMat();
	}

	// Token: 0x06000051 RID: 81 RVA: 0x00003D6A File Offset: 0x00001F6A
	private void SetMatActiveFirstCheckpoint(string c)
	{
		if (this.courseName != c)
		{
			return;
		}
		if (this.type != TimerTrigger.TriggerType.Checkpoint)
		{
			return;
		}
		this.renderer.material = this.checkpointMatActive;
	}

	// Token: 0x06000052 RID: 82 RVA: 0x00003D96 File Offset: 0x00001F96
	private void SetMatVisible(string c)
	{
		if (this.courseName != c)
		{
			return;
		}
		if (this.type != TimerTrigger.TriggerType.Start)
		{
			return;
		}
		this.ResetMat();
	}

	// Token: 0x06000053 RID: 83 RVA: 0x00003DB6 File Offset: 0x00001FB6
	private void SetMatInvisible(string c)
	{
		if (this.courseName != c)
		{
			return;
		}
		if (this.type != TimerTrigger.TriggerType.Start)
		{
			return;
		}
		this.HideMat();
	}

	// Token: 0x06000054 RID: 84 RVA: 0x00003DD6 File Offset: 0x00001FD6
	public void ResetMat()
	{
		this.renderer.material = this.matDefault;
	}

	// Token: 0x06000055 RID: 85 RVA: 0x00003DE9 File Offset: 0x00001FE9
	public void HideMat()
	{
		if (this.type == TimerTrigger.TriggerType.Start)
		{
			this.ResetMat();
			return;
		}
		this.renderer.material = this.timerManager.invisibleMat;
	}

	// Token: 0x06000056 RID: 86 RVA: 0x00003E10 File Offset: 0x00002010
	private void OnTriggerEnter(Collider other)
	{
		if (other.gameObject.layer == 6)
		{
			if (this.type == TimerTrigger.TriggerType.Start)
			{
				for (int i = 0; i < base.transform.parent.childCount; i++)
				{
					base.transform.parent.GetChild(i).SendMessage("SetMatVisible", this.courseName);
				}
				this.timerManager.SendMessage("TStart", this.courseName, SendMessageOptions.DontRequireReceiver);
				base.transform.parent.GetChild(base.transform.GetSiblingIndex() + 1).SendMessage("SetMatActiveFirstCheckpoint", this.courseName);
				this.timerManager.player = other.GetComponent<FirstPersonController>();
				this.timerManager.currentStartPoint = base.transform;
			}
			if (this.type == TimerTrigger.TriggerType.Checkpoint)
			{
				this.timerManager.SendMessage("TCheckpoint", new object[]
				{
					this.courseName,
					base.transform.GetSiblingIndex() - 1,
					base.transform.parent.childCount - 3
				}, SendMessageOptions.DontRequireReceiver);
			}
			if (this.type == TimerTrigger.TriggerType.End)
			{
				for (int j = 0; j < base.transform.parent.childCount; j++)
				{
					base.transform.parent.GetChild(j).SendMessage("SetMatInvisible", this.courseName);
				}
				this.timerManager.SendMessage("TEnd", SendMessageOptions.DontRequireReceiver);
			}
			if (this.type == TimerTrigger.TriggerType.ShowUI && !this.timerManager.timerStarted)
			{
				this.timerManager.SendMessage("TShowUI", this.courseName, SendMessageOptions.DontRequireReceiver);
			}
		}
	}

	// Token: 0x06000057 RID: 87 RVA: 0x00003FB6 File Offset: 0x000021B6
	private void OnTriggerExit(Collider other)
	{
		if (other.gameObject.layer == 6 && this.type == TimerTrigger.TriggerType.ShowUI)
		{
			this.timerManager.SendMessage("THideUI", this.courseName, SendMessageOptions.DontRequireReceiver);
		}
	}

	// Token: 0x04000064 RID: 100
	[SerializeField]
	private TimerTrigger.TriggerType type;

	// Token: 0x04000065 RID: 101
	private TimerManager timerManager;

	// Token: 0x04000066 RID: 102
	private string courseName;

	// Token: 0x04000067 RID: 103
	private Renderer renderer;

	// Token: 0x04000068 RID: 104
	private Material matDefault;

	// Token: 0x04000069 RID: 105
	public Material checkpointMatActive;

	// Token: 0x0200000C RID: 12
	public enum TriggerType
	{
		// Token: 0x0400006B RID: 107
		Start,
		// Token: 0x0400006C RID: 108
		Checkpoint,
		// Token: 0x0400006D RID: 109
		End,
		// Token: 0x0400006E RID: 110
		ShowUI
	}
}

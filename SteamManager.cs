﻿using System;
using System.Text;
using AOT;
using Steamworks;
using UnityEngine;

// Token: 0x020000BF RID: 191
[DefaultExecutionOrder(-100)]
[DisallowMultipleComponent]
public class SteamManager : MonoBehaviour
{
	// Token: 0x170000A3 RID: 163
	// (get) Token: 0x06000AB6 RID: 2742 RVA: 0x0004CE45 File Offset: 0x0004B045
	protected static SteamManager Instance
	{
		get
		{
			if (SteamManager.s_instance == null)
			{
				return new GameObject("SteamManager").AddComponent<SteamManager>();
			}
			return SteamManager.s_instance;
		}
	}

	// Token: 0x170000A4 RID: 164
	// (get) Token: 0x06000AB7 RID: 2743 RVA: 0x0004CE69 File Offset: 0x0004B069
	public static bool Initialized
	{
		get
		{
			return SteamManager.Instance.m_bInitialized;
		}
	}

	// Token: 0x06000AB8 RID: 2744 RVA: 0x0004CE75 File Offset: 0x0004B075
	[MonoPInvokeCallback(typeof(SteamAPIWarningMessageHook_t))]
	protected static void SteamAPIDebugTextHook(int nSeverity, StringBuilder pchDebugText)
	{
		Debug.LogWarning(pchDebugText);
	}

	// Token: 0x06000AB9 RID: 2745 RVA: 0x0004CE7D File Offset: 0x0004B07D
	[RuntimeInitializeOnLoadMethod(RuntimeInitializeLoadType.SubsystemRegistration)]
	private static void InitOnPlayMode()
	{
		SteamManager.s_EverInitialized = false;
		SteamManager.s_instance = null;
	}

	// Token: 0x06000ABA RID: 2746 RVA: 0x0004CE8C File Offset: 0x0004B08C
	protected virtual void Awake()
	{
		if (SteamManager.s_instance != null)
		{
			global::UnityEngine.Object.Destroy(base.gameObject);
			return;
		}
		SteamManager.s_instance = this;
		if (SteamManager.s_EverInitialized)
		{
			throw new Exception("Tried to Initialize the SteamAPI twice in one session!");
		}
		global::UnityEngine.Object.DontDestroyOnLoad(base.gameObject);
		if (!Packsize.Test())
		{
			Debug.LogError("[Steamworks.NET] Packsize Test returned false, the wrong version of Steamworks.NET is being run in this platform.", this);
		}
		if (!DllCheck.Test())
		{
			Debug.LogError("[Steamworks.NET] DllCheck Test returned false, One or more of the Steamworks binaries seems to be the wrong version.", this);
		}
		try
		{
			if (SteamAPI.RestartAppIfNecessary((AppId_t)((uint)this.appId)))
			{
				Debug.Log("[Steamworks.NET] Shutting down because RestartAppIfNecessary returned true. Steam will restart the application.");
				Application.Quit();
				return;
			}
		}
		catch (DllNotFoundException ex)
		{
			string text = "[Steamworks.NET] Could not load [lib]steam_api.dll/so/dylib. It's likely not in the correct location. Refer to the README for more details.\n";
			DllNotFoundException ex2 = ex;
			Debug.LogError(text + ((ex2 != null) ? ex2.ToString() : null), this);
			Application.Quit();
			return;
		}
		this.m_bInitialized = SteamAPI.Init();
		if (!this.m_bInitialized)
		{
			Debug.LogError("[Steamworks.NET] SteamAPI_Init() failed. Refer to Valve's documentation or the comment above this line for more information.", this);
			return;
		}
		SteamManager.s_EverInitialized = true;
	}

	// Token: 0x06000ABB RID: 2747 RVA: 0x0004CF7C File Offset: 0x0004B17C
	protected virtual void OnEnable()
	{
		if (SteamManager.s_instance == null)
		{
			SteamManager.s_instance = this;
		}
		if (!this.m_bInitialized)
		{
			return;
		}
		if (this.m_SteamAPIWarningMessageHook == null)
		{
			this.m_SteamAPIWarningMessageHook = new SteamAPIWarningMessageHook_t(SteamManager.SteamAPIDebugTextHook);
			SteamClient.SetWarningMessageHook(this.m_SteamAPIWarningMessageHook);
		}
	}

	// Token: 0x06000ABC RID: 2748 RVA: 0x0004CFCA File Offset: 0x0004B1CA
	protected virtual void OnDestroy()
	{
		if (SteamManager.s_instance != this)
		{
			return;
		}
		SteamManager.s_instance = null;
		if (!this.m_bInitialized)
		{
			return;
		}
		SteamAPI.Shutdown();
	}

	// Token: 0x06000ABD RID: 2749 RVA: 0x0004CFEE File Offset: 0x0004B1EE
	protected virtual void Update()
	{
		if (!this.m_bInitialized)
		{
			return;
		}
		SteamAPI.RunCallbacks();
	}

	// Token: 0x04000939 RID: 2361
	protected static bool s_EverInitialized;

	// Token: 0x0400093A RID: 2362
	protected static SteamManager s_instance;

	// Token: 0x0400093B RID: 2363
	[SerializeField]
	private ulong appId = 480UL;

	// Token: 0x0400093C RID: 2364
	protected bool m_bInitialized;

	// Token: 0x0400093D RID: 2365
	protected SteamAPIWarningMessageHook_t m_SteamAPIWarningMessageHook;
}

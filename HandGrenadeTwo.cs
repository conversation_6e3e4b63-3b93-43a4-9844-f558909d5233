﻿using System;
using DG.Tweening;
using UnityEngine;

// Token: 0x02000096 RID: 150
public class HandGrenadeTwo : MonoBehaviour
{
	// Token: 0x060007AD RID: 1965 RVA: 0x000346B7 File Offset: 0x000328B7
	private void Awake()
	{
		this.audio = base.GetComponent<AudioSource>();
		this.character = base.GetComponent<CharacterController>();
		this._gravity = 0f;
	}

	// Token: 0x060007AE RID: 1966 RVA: 0x000346DC File Offset: 0x000328DC
	public void Initialize(Vector3 direction, float force, float passedTime, GameObject rootObject, GameObject gun)
	{
		this._passedTime = passedTime;
		this._rootObject = rootObject;
		this._force = force;
		this._gun = gun;
		this.explosionTimer = this.timeBeforeExplosion;
		this.AddForce(direction, force);
	}

	// Token: 0x060007AF RID: 1967 RVA: 0x00034710 File Offset: 0x00032910
	public void AddForce(Vector3 dir, float force)
	{
		dir.Normalize();
		this.impact += dir.normalized * force / this.mass;
	}

	// Token: 0x060007B0 RID: 1968 RVA: 0x00034744 File Offset: 0x00032944
	private void OnControllerColliderHit(ControllerColliderHit collision)
	{
		if (collision.transform.gameObject.layer == 6 || collision.transform.gameObject.layer == 16)
		{
			return;
		}
		if (this.maxRebond <= 0)
		{
			return;
		}
		if (this.safeTimer > 0f)
		{
			return;
		}
		this.maxRebond--;
		this.safeTimer = 0.1f;
		this.rebondForce -= this.friction;
		if (Vector3.Angle(collision.normal, Vector3.up) < this.groundDetectionSlopeLimit && this.impact.y < 0f)
		{
			float num = -this.impact.y;
			this.AddForce(new Vector3(collision.normal.x, 0f, collision.normal.z), this.rebondForce);
			this.impact.y = num;
			return;
		}
		this.AddForce(collision.normal, this.rebondForce);
	}

	// Token: 0x060007B1 RID: 1969 RVA: 0x00034840 File Offset: 0x00032A40
	private void Update()
	{
		this.velocity = this.lastPosition - this.currentPosition;
		base.transform.rotation = Quaternion.LookRotation(-this.velocity);
		float deltaTime = Time.deltaTime;
		this.HandleExplosion();
		float num = 0f;
		if (this._passedTime > 0f)
		{
			float num2 = this._passedTime * 0.08f;
			this._passedTime -= num2;
			if (this._passedTime <= deltaTime / 2f)
			{
				num2 += this._passedTime;
				this._passedTime = 0f;
			}
			num = num2;
		}
		this.explosionTimer -= deltaTime + num;
		this.safeTimer -= deltaTime + num;
		if (Physics.Raycast(base.transform.position, -Vector3.up, 0.5f, this.rebondLayer))
		{
			this._gravity = 0f;
			this.isGrounded = true;
		}
		this._gravity += this.gravity * (deltaTime + num);
		this.impact = Vector3.Lerp(this.impact, Vector3.zero, ((!this.isGrounded) ? this.airdeceleration : this.deceleration) * (deltaTime + num));
		this.character.Move((this.impact - Vector3.up * this._gravity) * (deltaTime + num));
		this.lastPosition = this.currentPosition;
	}

	// Token: 0x060007B2 RID: 1970 RVA: 0x000349C0 File Offset: 0x00032BC0
	private void HandleExplosion()
	{
		if (this.explosionTimer < 0f && this.explosionTimer > -2f)
		{
			this.explosionTimer = -3f;
			Collider[] array = Physics.OverlapSphere(base.transform.position, this.explosionRadius, this.bodyLayer);
			if (array.Length != 0 && this.isOwner)
			{
				this.ph2 = new PlayerHealth[array.Length];
				for (int i = 0; i < array.Length; i++)
				{
					if (array[i].GetComponentInParent<PlayerHealth>() != null)
					{
						this.ph2[i] = array[i].GetComponentInParent<PlayerHealth>();
					}
				}
				for (int j = 0; j < this.ph2.Length; j++)
				{
					if (this.ph2[j] != null)
					{
						if (this.ph2[j].transform.gameObject == this._rootObject && !this.touched)
						{
							this.ph2[j].ChangeKilledState(true);
							this.ph2[j].RemoveHealth(10f);
							this.ph2[j].Explode(false, true, this.ph2[j].gameObject.name, this.ph2[j].transform.position - base.transform.position, this.ragdollEjectForce, base.transform.position);
							this.ph2[j].SetKiller(this._rootObject.transform);
							this.touched = true;
						}
						else if (this.ph2[j].transform.gameObject != this._rootObject && !this.touched2)
						{
							this.ph2[j].ChangeKilledState(true);
							this.ph2[j].RemoveHealth(10f);
							this.ph2[j].Explode(false, true, this.ph2[j].gameObject.name, this.ph2[j].transform.position - base.transform.position, this.ragdollEjectForce, base.transform.position);
							this.ph2[j].SetKiller(this._rootObject.transform);
							this.touched2 = true;
						}
					}
				}
			}
			GameObject[] array2 = GameObject.FindGameObjectsWithTag("Player");
			for (int k = 0; k < array2.Length; k++)
			{
				float num = Vector3.Distance(base.transform.position, array2[k].transform.position);
				array2[k].GetComponent<PlayerHealth>().LocalScreenshake(this.duration, Mathf.Lerp(this.maxStrength, this.minStrength, Mathf.Clamp(num / this.maxDistance, 0f, 1f)), this.vibrato, this.randomness, this.shakeEase);
			}
			global::UnityEngine.Object.Destroy(base.gameObject);
			global::UnityEngine.Object.Instantiate<GameObject>(this.explosionVfx, base.transform.position, Quaternion.identity);
			SoundManager.Instance.PlaySound(this.explosionClip);
		}
	}

	// Token: 0x040006F4 RID: 1780
	public bool isOwner;

	// Token: 0x040006F5 RID: 1781
	[SerializeField]
	private float mass = 3f;

	// Token: 0x040006F6 RID: 1782
	private Vector3 impact = Vector3.zero;

	// Token: 0x040006F7 RID: 1783
	private CharacterController character;

	// Token: 0x040006F8 RID: 1784
	[SerializeField]
	private float airdeceleration = 0.9f;

	// Token: 0x040006F9 RID: 1785
	[SerializeField]
	private float deceleration = 5f;

	// Token: 0x040006FA RID: 1786
	[SerializeField]
	private int maxRebond = 10;

	// Token: 0x040006FB RID: 1787
	[SerializeField]
	private float groundDetectionSlopeLimit = 60f;

	// Token: 0x040006FC RID: 1788
	[SerializeField]
	private float ragdollEjectForce;

	// Token: 0x040006FD RID: 1789
	[SerializeField]
	private LayerMask playerLayer;

	// Token: 0x040006FE RID: 1790
	[SerializeField]
	private LayerMask bodyLayer;

	// Token: 0x040006FF RID: 1791
	[SerializeField]
	private LayerMask rebondLayer;

	// Token: 0x04000700 RID: 1792
	[SerializeField]
	private GameObject hitVfx;

	// Token: 0x04000701 RID: 1793
	[SerializeField]
	private GameObject explosionVfx;

	// Token: 0x04000702 RID: 1794
	[SerializeField]
	private AudioClip hitClip;

	// Token: 0x04000703 RID: 1795
	[SerializeField]
	private AudioClip explosionClip;

	// Token: 0x04000704 RID: 1796
	[SerializeField]
	private float gravity;

	// Token: 0x04000705 RID: 1797
	private float _gravity;

	// Token: 0x04000706 RID: 1798
	[SerializeField]
	private float friction = 10f;

	// Token: 0x04000707 RID: 1799
	private float _force;

	// Token: 0x04000708 RID: 1800
	[SerializeField]
	private float timeBeforeExplosion = 2f;

	// Token: 0x04000709 RID: 1801
	[SerializeField]
	private float explosionRadius = 3f;

	// Token: 0x0400070A RID: 1802
	private float explosionTimer;

	// Token: 0x0400070B RID: 1803
	[SerializeField]
	private float rebondForce;

	// Token: 0x0400070C RID: 1804
	private bool isGrounded;

	// Token: 0x0400070D RID: 1805
	[Header("Screenshake values")]
	[SerializeField]
	private float duration;

	// Token: 0x0400070E RID: 1806
	[SerializeField]
	private float minStrength;

	// Token: 0x0400070F RID: 1807
	[SerializeField]
	private float maxStrength;

	// Token: 0x04000710 RID: 1808
	[SerializeField]
	private int vibrato;

	// Token: 0x04000711 RID: 1809
	[SerializeField]
	private float randomness;

	// Token: 0x04000712 RID: 1810
	[SerializeField]
	private Ease shakeEase;

	// Token: 0x04000713 RID: 1811
	[SerializeField]
	private float maxDistance;

	// Token: 0x04000714 RID: 1812
	private bool touched;

	// Token: 0x04000715 RID: 1813
	private bool touched2;

	// Token: 0x04000716 RID: 1814
	private GameObject _gun;

	// Token: 0x04000717 RID: 1815
	private PlayerHealth[] ph2;

	// Token: 0x04000718 RID: 1816
	private float _passedTime;

	// Token: 0x04000719 RID: 1817
	private GameObject _rootObject;

	// Token: 0x0400071A RID: 1818
	private AudioSource audio;

	// Token: 0x0400071B RID: 1819
	[HideInInspector]
	public Weapon weapon;

	// Token: 0x0400071C RID: 1820
	private Vector3 currentPosition;

	// Token: 0x0400071D RID: 1821
	private Vector3 lastPosition;

	// Token: 0x0400071E RID: 1822
	private Vector3 velocity;

	// Token: 0x0400071F RID: 1823
	private float safeTimer;
}

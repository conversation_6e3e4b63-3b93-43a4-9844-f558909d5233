﻿using System;
using UnityEngine;

// Token: 0x02000161 RID: 353
public class CFX_Demo_Translate : MonoBehaviour
{
	// Token: 0x06000F14 RID: 3860 RVA: 0x00063458 File Offset: 0x00061658
	private void Start()
	{
		this.dir = new Vector3(global::UnityEngine.Random.Range(0f, 360f), global::UnityEngine.Random.Range(0f, 360f), global::UnityEngine.Random.Range(0f, 360f));
		this.dir.Scale(this.rotation);
		base.transform.localEulerAngles = this.dir;
	}

	// Token: 0x06000F15 RID: 3861 RVA: 0x000634BF File Offset: 0x000616BF
	private void Update()
	{
		base.transform.Translate(this.axis * this.speed * Time.deltaTime, Space.Self);
	}

	// Token: 0x04000DCD RID: 3533
	public float speed = 30f;

	// Token: 0x04000DCE RID: 3534
	public Vector3 rotation = Vector3.forward;

	// Token: 0x04000DCF RID: 3535
	public Vector3 axis = Vector3.forward;

	// Token: 0x04000DD0 RID: 3536
	public bool gravity;

	// Token: 0x04000DD1 RID: 3537
	private Vector3 dir;
}

﻿using System;
using System.Collections;
using UnityEngine;

// Token: 0x02000052 RID: 82
public class FpsCounter : MonoBehaviour
{
	// Token: 0x060003CA RID: 970 RVA: 0x0001BC4A File Offset: 0x00019E4A
	private IEnumerator Start()
	{
		GUI.depth = 2;
		for (;;)
		{
			this.count = 1f / Time.unscaledDeltaTime;
			yield return new WaitForSeconds(0.1f);
		}
		yield break;
	}

	// Token: 0x060003CB RID: 971 RVA: 0x0001BC5C File Offset: 0x00019E5C
	private void OnGUI()
	{
		if (!this.display)
		{
			return;
		}
		GUI.Label(new Rect(5f, 40f, 100f, 25f), "FPS: " + Mathf.Round(this.count).ToString());
	}

	// Token: 0x04000421 RID: 1057
	private float count;

	// Token: 0x04000422 RID: 1058
	public bool display;
}

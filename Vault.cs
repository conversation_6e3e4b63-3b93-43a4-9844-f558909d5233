﻿using System;
using System.Collections;
using UnityEngine;

// Token: 0x0200003A RID: 58
public class Vault : MonoBehaviour
{
	// Token: 0x06000302 RID: 770 RVA: 0x000191FC File Offset: 0x000173FC
	private void Start()
	{
		this.controller = base.GetComponent<CharacterController>();
	}

	// Token: 0x06000303 RID: 771 RVA: 0x0001920A File Offset: 0x0001740A
	private void Update()
	{
		this.Vaulting();
	}

	// Token: 0x06000304 RID: 772 RVA: 0x00019214 File Offset: 0x00017414
	private void Vaulting()
	{
		if (this.controller.isGrounded)
		{
			return;
		}
		RaycastHit raycastHit;
		if (Physics.Raycast(base.transform.position + Vector3.up, base.transform.forward, out raycastHit, 2f, this.vaultLayer) && Input.GetAxisRaw("Vertical") > 0f)
		{
			if (Vector3.Angle(raycastHit.normal, Vector3.up) < 87f || Vector3.Angle(raycastHit.normal, Vector3.up) > 93f)
			{
				return;
			}
			MonoBehaviour.print("vaultable in front");
			RaycastHit raycastHit2;
			if (Physics.Raycast(raycastHit.point + base.transform.forward + Vector3.up * this.vaultHeight, Vector3.down, out raycastHit2, this.playerHeight))
			{
				MonoBehaviour.print("found place to land");
				base.StartCoroutine(this.LerpVault(raycastHit2.point, this.lerpTime));
			}
		}
	}

	// Token: 0x06000305 RID: 773 RVA: 0x0001931E File Offset: 0x0001751E
	private IEnumerator LerpVault(Vector3 targetPosition, float duration)
	{
		float time = 0f;
		Vector3 startPosition = base.transform.position;
		this.vaulting = true;
		while (time < duration - 0.1f)
		{
			base.transform.position = Vector3.Lerp(startPosition, targetPosition, time / duration);
			time += Time.deltaTime;
			if (base.GetComponent<CharacterController>().velocity.magnitude == 0f)
			{
				break;
			}
			yield return null;
		}
		this.vaulting = false;
		yield break;
	}

	// Token: 0x06000306 RID: 774 RVA: 0x0001933C File Offset: 0x0001753C
	private Vector3 Caca(Vector3 vector3, int decimalPlaces)
	{
		float num = 1f;
		for (int i = 0; i < decimalPlaces; i++)
		{
			num *= 10f;
		}
		return new Vector3(Mathf.Round(vector3.x * num) / num, Mathf.Round(vector3.y * num) / num, Mathf.Round(vector3.z * num) / num);
	}

	// Token: 0x04000388 RID: 904
	[SerializeField]
	private float lerpTime = 0.4f;

	// Token: 0x04000389 RID: 905
	[SerializeField]
	private float vaultHeight = 1f;

	// Token: 0x0400038A RID: 906
	[SerializeField]
	private LayerMask vaultLayer;

	// Token: 0x0400038B RID: 907
	public bool vaulting;

	// Token: 0x0400038C RID: 908
	public Transform cam;

	// Token: 0x0400038D RID: 909
	private float playerHeight = 4f;

	// Token: 0x0400038E RID: 910
	private float playerRadius = 0.5f;

	// Token: 0x0400038F RID: 911
	private CharacterController controller;
}

﻿using System;
using FishNet;
using FishNet.Broadcast;
using FishNet.Connection;
using FishNet.Transporting;
using TMPro;
using UnityEngine;

// Token: 0x020000C0 RID: 192
public class ChatBroadcast : MonoBehaviour
{
	// Token: 0x06000ABF RID: 2751 RVA: 0x0004D012 File Offset: 0x0004B212
	private void OnEnable()
	{
		InstanceFinder.ClientManager.RegisterBroadcast<ChatBroadcast.Message>(new Action<ChatBroadcast.Message>(this.OnMessageReceived));
		InstanceFinder.ServerManager.RegisterBroadcast<ChatBroadcast.Message>(new Action<NetworkConnection, ChatBroadcast.Message>(this.OnClientMessageReceived), true);
	}

	// Token: 0x06000AC0 RID: 2752 RVA: 0x0004D041 File Offset: 0x0004B241
	private void OnDisable()
	{
		InstanceFinder.ClientManager.UnregisterBroadcast<ChatBroadcast.Message>(new Action<ChatBroadcast.Message>(this.OnMessageReceived));
		InstanceFinder.ServerManager.UnregisterBroadcast<ChatBroadcast.Message>(new Action<NetworkConnection, ChatBroadcast.Message>(this.OnClientMessageReceived));
	}

	// Token: 0x06000AC1 RID: 2753 RVA: 0x0004D06F File Offset: 0x0004B26F
	private void Update()
	{
		if (Input.GetKeyDown(KeyCode.Return))
		{
			this.SendMessage();
		}
	}

	// Token: 0x06000AC2 RID: 2754 RVA: 0x0004D080 File Offset: 0x0004B280
	private void SendMessage()
	{
		ChatBroadcast.Message message = new ChatBroadcast.Message
		{
			username = this.playerUsername.text,
			message = this.playerMessage.text
		};
		if (InstanceFinder.IsServer)
		{
			InstanceFinder.ServerManager.Broadcast<ChatBroadcast.Message>(message, true, Channel.Reliable);
		}
		if (InstanceFinder.IsClient)
		{
			InstanceFinder.ClientManager.Broadcast<ChatBroadcast.Message>(message, Channel.Reliable);
		}
	}

	// Token: 0x06000AC3 RID: 2755 RVA: 0x0004D0E2 File Offset: 0x0004B2E2
	private void OnMessageReceived(ChatBroadcast.Message msg)
	{
		global::UnityEngine.Object.Instantiate<GameObject>(this.msgElement, this.chatHolder).GetComponent<TextMeshProUGUI>().text = msg.username + " : " + msg.message;
	}

	// Token: 0x06000AC4 RID: 2756 RVA: 0x0004D115 File Offset: 0x0004B315
	private void OnClientMessageReceived(NetworkConnection networkConnection, ChatBroadcast.Message msg)
	{
		InstanceFinder.ServerManager.Broadcast<ChatBroadcast.Message>(msg, true, Channel.Reliable);
	}

	// Token: 0x0400093E RID: 2366
	[SerializeField]
	private Transform chatHolder;

	// Token: 0x0400093F RID: 2367
	[SerializeField]
	private GameObject msgElement;

	// Token: 0x04000940 RID: 2368
	[SerializeField]
	private TMP_InputField playerUsername;

	// Token: 0x04000941 RID: 2369
	[SerializeField]
	private TMP_InputField playerMessage;

	// Token: 0x020000C1 RID: 193
	public struct Message : IBroadcast
	{
		// Token: 0x04000942 RID: 2370
		public string username;

		// Token: 0x04000943 RID: 2371
		public string message;
	}
}

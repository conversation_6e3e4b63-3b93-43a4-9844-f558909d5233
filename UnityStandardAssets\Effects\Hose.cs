﻿using System;
using UnityEngine;

namespace UnityStandardAssets.Effects
{
	// Token: 0x02000194 RID: 404
	public class Hose : MonoBehaviour
	{
		// Token: 0x06001030 RID: 4144 RVA: 0x00069410 File Offset: 0x00067610
		private void Update()
		{
			this.m_Power = Mathf.Lerp(this.m_Power, Input.GetMouseButton(0) ? this.maxPower : this.minPower, Time.deltaTime * this.changeSpeed);
			if (Input.GetKeyDown(KeyCode.Alpha1))
			{
				this.systemRenderer.enabled = !this.systemRenderer.enabled;
			}
			foreach (ParticleSystem particleSystem in this.hoseWaterSystems)
			{
				particleSystem.main.startSpeed = this.m_Power;
				particleSystem.emission.enabled = this.m_Power > this.minPower * 1.1f;
			}
		}

		// Token: 0x04000EF8 RID: 3832
		public float maxPower = 20f;

		// Token: 0x04000EF9 RID: 3833
		public float minPower = 5f;

		// Token: 0x04000EFA RID: 3834
		public float changeSpeed = 5f;

		// Token: 0x04000EFB RID: 3835
		public ParticleSystem[] hoseWaterSystems;

		// Token: 0x04000EFC RID: 3836
		public Renderer systemRenderer;

		// Token: 0x04000EFD RID: 3837
		private float m_Power;
	}
}

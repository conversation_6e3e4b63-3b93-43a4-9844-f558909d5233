﻿using System;
using DG.Tweening;
using DG.Tweening.Core;
using DG.Tweening.Plugins.Options;
using UnityEngine;

// Token: 0x0200012F RID: 303
public class MoveUIObject : MonoBehaviour
{
	// Token: 0x06000E67 RID: 3687 RVA: 0x0005F7DC File Offset: 0x0005D9DC
	private void Awake()
	{
		this.activePosition = base.transform.localPosition;
		base.transform.localPosition = this.activePosition + this.offset;
		this.state = false;
		this.initiated = true;
		base.transform.localPosition = this.activePosition + this.offset;
	}

	// Token: 0x06000E68 RID: 3688 RVA: 0x0005F840 File Offset: 0x0005DA40
	private void OnEnable()
	{
		base.transform.localPosition = this.activePosition + this.offset;
		this.state = false;
	}

	// Token: 0x06000E69 RID: 3689 RVA: 0x000023D6 File Offset: 0x000005D6
	private void Update()
	{
	}

	// Token: 0x06000E6A RID: 3690 RVA: 0x0005F868 File Offset: 0x0005DA68
	[ContextMenu("ChangeState")]
	public void ChangeState()
	{
		this.state = !this.state;
		if (this.state)
		{
			base.transform.DOLocalMove(this.activePosition, this.duration, false).SetEase(this.ease);
			return;
		}
		base.transform.DOLocalMove(this.activePosition + this.offset, this.duration, false).SetEase(this.ease);
	}

	// Token: 0x06000E6B RID: 3691 RVA: 0x0005F8E0 File Offset: 0x0005DAE0
	public void ChooseState(bool state)
	{
		if (this.state == state)
		{
			return;
		}
		this.state = state;
		if (state)
		{
			base.transform.DOLocalMove(this.activePosition, this.duration, false).SetEase(this.ease);
			return;
		}
		base.transform.DOLocalMove(this.activePosition + this.offset, this.duration, false).SetEase(this.ease);
	}

	// Token: 0x04000CF7 RID: 3319
	private Vector3 activePosition;

	// Token: 0x04000CF8 RID: 3320
	[SerializeField]
	private Vector3 offset = new Vector3(3f, 0f, 0f);

	// Token: 0x04000CF9 RID: 3321
	[SerializeField]
	private float duration = 0.8f;

	// Token: 0x04000CFA RID: 3322
	[SerializeField]
	private Ease ease = Ease.Linear;

	// Token: 0x04000CFB RID: 3323
	private bool state;

	// Token: 0x04000CFC RID: 3324
	private bool initiated;
}

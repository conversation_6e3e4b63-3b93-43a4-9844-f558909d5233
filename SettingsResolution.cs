﻿using System;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

// Token: 0x0200010F RID: 271
public class SettingsResolution : MonoBehaviour
{
	// Token: 0x06000D7D RID: 3453 RVA: 0x0005BCF0 File Offset: 0x00059EF0
	private void Start()
	{
		this.sortedResolutions = new List<Resolution>(Screen.resolutions);
		this.sortedResolutions.Sort(delegate(Resolution a, Resolution b)
		{
			if (a.width != b.width)
			{
				return b.width.CompareTo(a.width);
			}
			if (a.height != b.height)
			{
				return b.height.CompareTo(a.height);
			}
			return b.refreshRate.CompareTo(a.refreshRate);
		});
		this.resolutionDropdown.ClearOptions();
		List<string> list = new List<string>();
		foreach (Resolution resolution in this.sortedResolutions)
		{
			string aspectRatio = this.GetAspectRatio(resolution.width, resolution.height);
			string text = string.Format("{0}x{1} ({2}) {3:0.##} Hz", new object[] { resolution.width, resolution.height, aspectRatio, resolution.refreshRate });
			list.Add(text);
		}
		this.resolutionDropdown.AddOptions(list);
		this.resolutionDropdown.value = PlayerPrefs.GetInt("resolution");
		this.resolutionDropdown.RefreshShownValue();
		this.SetResolution(Settings.Instance.resolution);
	}

	// Token: 0x06000D7E RID: 3454 RVA: 0x0005BE24 File Offset: 0x0005A024
	public void SetResolution(int resolutionIndex)
	{
		if (this.sortedResolutions.Count == 0 || resolutionIndex < 0 || resolutionIndex >= this.sortedResolutions.Count)
		{
			Debug.LogWarning("Invalid resolution index: " + resolutionIndex.ToString());
			return;
		}
		Resolution resolution = this.sortedResolutions[resolutionIndex];
		Screen.SetResolution(resolution.width, resolution.height, true);
	}

	// Token: 0x06000D7F RID: 3455 RVA: 0x0005BE88 File Offset: 0x0005A088
	public void SetResolutionReal(TMP_Dropdown dropdown)
	{
		Resolution resolution = this.sortedResolutions[dropdown.value];
		Screen.SetResolution(resolution.width, resolution.height, true);
		Settings.Instance.ChangeResolution(dropdown.value);
		float num = (float)resolution.width / (float)resolution.height;
		this.canvasScaler.matchWidthOrHeight = (float)((num <= 1.7777777f) ? 0 : 1);
	}

	// Token: 0x06000D80 RID: 3456 RVA: 0x0005BEF8 File Offset: 0x0005A0F8
	private string GetAspectRatio(int width, int height)
	{
		int num = this.GCD(width, height);
		return string.Format("{0}:{1}", width / num, height / num);
	}

	// Token: 0x06000D81 RID: 3457 RVA: 0x0005BF28 File Offset: 0x0005A128
	private int GCD(int a, int b)
	{
		while (b != 0)
		{
			int num = b;
			b = a % b;
			a = num;
		}
		return a;
	}

	// Token: 0x04000BF8 RID: 3064
	[SerializeField]
	private TMP_Dropdown resolutionDropdown;

	// Token: 0x04000BF9 RID: 3065
	private List<Resolution> sortedResolutions;

	// Token: 0x04000BFA RID: 3066
	private int highestRefreshRate;

	// Token: 0x04000BFB RID: 3067
	[SerializeField]
	private CanvasScaler canvasScaler;
}

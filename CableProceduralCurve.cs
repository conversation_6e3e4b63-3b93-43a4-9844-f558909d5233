﻿using System;
using UnityEngine;

// Token: 0x02000016 RID: 22
[RequireComponent(typeof(LineRenderer))]
[ExecuteAlways]
public class CableProceduralCurve : MonoBehaviour
{
	// Token: 0x060000CE RID: 206 RVA: 0x00005E0C File Offset: 0x0000400C
	private void Start()
	{
		this.line = base.GetComponent<LineRenderer>();
		if (!this.endPointTransform)
		{
			Debug.LogError("No Endpoint Transform assigned to Cable_Procedural component attached to " + base.gameObject.name);
			return;
		}
		this.vectorFromStartToEnd = this.endPointTransform.position - base.transform.position;
		base.transform.forward = this.vectorFromStartToEnd.normalized;
		this.pointsInLineRenderer = Mathf.FloorToInt(this.pointDensity * this.vectorFromStartToEnd.magnitude);
		this.line.positionCount = this.pointsInLineRenderer;
		this.sagDirection = Physics.gravity.normalized;
		this.Draw();
	}

	// Token: 0x060000CF RID: 207 RVA: 0x00005ECC File Offset: 0x000040CC
	private void Draw()
	{
		for (int i = 0; i < this.pointsInLineRenderer; i++)
		{
			float num = (float)i / (float)(this.pointsInLineRenderer - 1);
			Vector3 vector = this.vectorFromStartToEnd * num;
			Vector3 vector2 = this.sagDirection * -this.curve.Evaluate(num);
			Vector3 vector3 = base.transform.position + vector + vector2;
			this.line.SetPosition(i, vector3);
		}
	}

	// Token: 0x040000A4 RID: 164
	private LineRenderer line;

	// Token: 0x040000A5 RID: 165
	[SerializeField]
	private Transform endPointTransform;

	// Token: 0x040000A6 RID: 166
	[SerializeField]
	[Tooltip("Number of points per unit length, using the straight line from the start to the end transform.")]
	private float pointDensity = 3f;

	// Token: 0x040000A7 RID: 167
	[SerializeField]
	[Tooltip("Positive keys are applied downward on cable.  Recommended use is to have keys at 0 at the start and end of the curve.")]
	private AnimationCurve curve;

	// Token: 0x040000A8 RID: 168
	private int pointsInLineRenderer;

	// Token: 0x040000A9 RID: 169
	private Vector3 vectorFromStartToEnd;

	// Token: 0x040000AA RID: 170
	private Vector3 sagDirection;
}

﻿using System;
using UnityEngine;

namespace ch.sycoforge.Decal.Demo
{
	// Token: 0x02000185 RID: 389
	public class Sinoid : MonoBehaviour
	{
		// Token: 0x06000FCD RID: 4045 RVA: 0x000679ED File Offset: 0x00065BED
		private void Start()
		{
			this.startPos = base.transform.position;
		}

		// Token: 0x06000FCE RID: 4046 RVA: 0x00067A00 File Offset: 0x00065C00
		private void Update()
		{
			this.accuTime += Time.deltaTime;
			base.transform.position = this.startPos + Vector3.up * this.Amplitude * Mathf.Sin(this.accuTime * 2f * 3.1415927f * this.SineFreq);
			base.transform.Rotate((Vector3.up + Vector3.forward) * this.AngularVelocity * Time.deltaTime);
		}

		// Token: 0x04000EB4 RID: 3764
		public float AngularVelocity = 2f;

		// Token: 0x04000EB5 RID: 3765
		public float SineFreq = 0.2f;

		// Token: 0x04000EB6 RID: 3766
		public float Amplitude = 0.25f;

		// Token: 0x04000EB7 RID: 3767
		private float accuTime;

		// Token: 0x04000EB8 RID: 3768
		private Vector3 startPos;
	}
}

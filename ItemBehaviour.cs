﻿using System;
using System.Collections.Generic;
using DG.Tweening;
using DG.Tweening.Core;
using DG.Tweening.Plugins.Options;
using FishNet;
using FishNet.Managing;
using FishNet.Object;
using FishNet.Object.Delegating;
using FishNet.Serializing;
using FishNet.Transporting;
using UnityEngine;

// Token: 0x0200007D RID: 125
public class ItemBehaviour : Interactable
{
	// Token: 0x06000543 RID: 1347 RVA: 0x00021D8C File Offset: 0x0001FF8C
	public override void OnFocus()
	{
		PauseManager.Instance.grabPopup.gameObject.SetActive(true);
		PauseManager.Instance.grabPopup.text = this.weaponName.ToLower() + " [" + PauseManager.Instance.InteractPromptLetter.ToLower() + "]";
		foreach (Material material in this.hoveredObjectMat)
		{
			material.SetFloat("_OutlineWidth", 0.007f);
		}
	}

	// Token: 0x06000544 RID: 1348 RVA: 0x00021E34 File Offset: 0x00020034
	public override void OnInteract()
	{
		this.dispenserStart = false;
		base.transform.DOKill(false);
	}

	// Token: 0x06000545 RID: 1349 RVA: 0x00021E4C File Offset: 0x0002004C
	public override void OnLoseFocus()
	{
		PauseManager.Instance.grabPopup.gameObject.SetActive(false);
		foreach (Material material in this.hoveredObjectMat)
		{
			material.SetFloat("_OutlineWidth", 0f);
		}
	}

	// Token: 0x06000546 RID: 1350 RVA: 0x00021EBC File Offset: 0x000200BC
	public void OnGrab(bool owner, bool rightHand)
	{
		if (base.GetComponent<Rigidbody>() != null)
		{
			global::UnityEngine.Object.Destroy(base.GetComponent<Rigidbody>());
		}
		if (!owner || !rightHand)
		{
			return;
		}
		Crosshair.Instance.standCrosshair = this.standCrosshair;
		Crosshair.Instance.sprintCrosshair = this.sprintCrosshair;
		Crosshair.Instance.instantAimLens = this.instantAimLens;
		if (this.aimWeapon && this.aimCrosshair != null)
		{
			Crosshair.Instance.aimCrosshair = this.aimCrosshair;
			Crosshair.Instance.canScopeAim = true;
			return;
		}
		Crosshair.Instance.canScopeAim = false;
	}

	// Token: 0x06000547 RID: 1351 RVA: 0x00021F58 File Offset: 0x00020158
	public void OnDrop(Camera tempCam)
	{
		this.weaponScript.isClicked = false;
		this.dispenserStart = false;
		base.gameObject.AddComponent<Rigidbody>();
		this.tempRb = base.GetComponent<Rigidbody>();
		this.tempRb.collisionDetectionMode = CollisionDetectionMode.ContinuousDynamic;
		this.tempRb.drag = 0f;
		this.tempRb.interpolation = RigidbodyInterpolation.Interpolate;
		this.tempRb.AddForce(tempCam.transform.forward * this.ejectForce, ForceMode.Impulse);
		global::UnityEngine.Random.Range(-1, 1);
		this.tempRb.AddTorque(tempCam.transform.forward * this.torqueForce + base.transform.right * this.torqueForce, ForceMode.Impulse);
	}

	// Token: 0x06000548 RID: 1352 RVA: 0x00022020 File Offset: 0x00020220
	[ObserversRpc(RunLocally = true)]
	public void DispenserDrop(Vector3 dir)
	{
		this.RpcWriter___Observers_DispenserDrop_4276783012(dir);
		this.RpcLogic___DispenserDrop_4276783012(dir);
	}

	// Token: 0x06000549 RID: 1353 RVA: 0x00022044 File Offset: 0x00020244
	private void Start()
	{
		this.maxPivot = -this.maxPivot;
		this.weaponScript = base.GetComponent<Weapon>();
		base.transform.localScale = new Vector3(2f, 2f, 2f);
		this.audio = base.GetComponent<AudioSource>();
		this.initialLocalPosition = base.transform.localPosition;
		this.hoveredObjectRenderer = base.GetComponentsInChildren<MeshRenderer>();
		this.hoveredObjectMat.Clear();
		for (int i = 0; i < this.hoveredObjectRenderer.Length; i++)
		{
			foreach (Material material in this.hoveredObjectRenderer[i].materials)
			{
				this.hoveredObjectMat.Add(material);
			}
		}
		this.col = base.GetComponent<Collider>();
		this.gripRight = base.GetComponentsInChildren<Grip>()[0].transform;
		this.gripLeft = base.GetComponentsInChildren<Grip>()[1].transform;
		if (!this.dispenserStart && base.gameObject.name != "Pig Held Item")
		{
			this.groundMov = base.transform.DOLocalMove(base.transform.localPosition + base.transform.parent.up / 2f, 1.4f, false).SetEase(Ease.InOutSine).SetLoops(-1, LoopType.Yoyo);
		}
		if (base.GetComponentInChildren<AimStrafePivot>() != null)
		{
			this.aimStrafePivot = base.GetComponentInChildren<AimStrafePivot>().transform;
		}
	}

	// Token: 0x0600054A RID: 1354 RVA: 0x000221BC File Offset: 0x000203BC
	private void Update()
	{
		if (base.gameObject.name == "Pig Held Item")
		{
			return;
		}
		if (this.rootObject == null)
		{
			this.col.enabled = true;
		}
		else
		{
			this.col.enabled = false;
		}
		if (this.dispenserStart)
		{
			base.transform.DOKill(false);
		}
		if (this.rootObject == null)
		{
			if (this.tempRb == null && !this.dispenserStart)
			{
				base.transform.Rotate(0f, 20f * Time.deltaTime, 0f);
			}
			return;
		}
		if (this.rootObject.layer == 3)
		{
			base.transform.position = (this.weaponScript.inRightHand ? this.playerPickup.pickupPositionOnline[0].position : this.playerPickup.pickupPositionOnline[1].position);
			base.transform.rotation = (this.weaponScript.inRightHand ? this.playerPickup.pickupPositionOnline[0].rotation : this.playerPickup.pickupPositionOnline[1].rotation);
			if (this.aimStrafePivot)
			{
				this.aimStrafePivot.localRotation = Quaternion.Slerp(this.aimStrafePivot.localRotation, Quaternion.Euler(this.aimStrafePivot.localRotation.x, this.aimStrafePivot.localRotation.y, 0f), this.aimStrafeLeanSpeed * Time.deltaTime);
			}
			return;
		}
		if (this.weaponScript.inRightHand)
		{
			this.CalculateMovementInput();
			if (this.aimCrosshair != null && this.playerController.isAiming && !this.weaponScript.isReloading)
			{
				this.playerController.isScopeAiming = true;
				if (this.aimWeapon && this.aimCrosshair != null)
				{
					Crosshair.Instance.canScopeAim = true;
				}
				else
				{
					Crosshair.Instance.canScopeAim = false;
				}
			}
			else
			{
				this.playerController.isScopeAiming = false;
				if (this.aimWeapon && this.aimCrosshair != null)
				{
					Crosshair.Instance.canScopeAim = false;
				}
			}
			if (this.playerController.isAiming && this.aimWeapon && !this.weaponScript.isReloading)
			{
				this.WeaponTransform(this.weaponScript.requireBothHands ? this.playerPickup.aimPositionBothHand[this.aimIndex] : this.playerPickup.aimPositionRightHand[this.aimIndex]);
				if (this.aimStrafePivot)
				{
					this.aimStrafePivot.localRotation = Quaternion.Slerp(this.aimStrafePivot.localRotation, Quaternion.Euler(this.aimStrafePivot.localRotation.x, this.aimStrafePivot.localRotation.y, (this.currentInputRaw.x != 0f) ? (this.aimStrafePivot.localRotation.z + this.currentInputRaw.x * this.maxPivot) : 0f), this.aimStrafeLeanSpeed * Time.deltaTime);
					return;
				}
			}
			else
			{
				if (this.weaponScript.requireBothHands)
				{
					this.finalPos = this.playerPickup.pickupPositionBothHand[this.camChildIndex].position;
					this.WeaponTransform(this.playerPickup.pickupPositionBothHand[this.camChildIndex]);
					return;
				}
				this.finalPos = this.playerPickup.pickupPositionRightHand[this.camChildIndex].position;
				this.WeaponTransform(this.playerPickup.pickupPositionRightHand[this.camChildIndex]);
				if (this.aimStrafePivot)
				{
					this.aimStrafePivot.localRotation = Quaternion.Slerp(this.aimStrafePivot.localRotation, Quaternion.Euler(this.aimStrafePivot.localRotation.x, this.aimStrafePivot.localRotation.y, 0f), this.aimStrafeLeanSpeed * Time.deltaTime);
					return;
				}
			}
		}
		else if (this.weaponScript.inLeftHand)
		{
			this.CalculateMovementInput();
			this.WeaponTransform(this.playerPickup.pickupPositionLeftHand[this.camChildIndexLeftHand]);
			this.finalPos = this.playerPickup.pickupPositionLeftHand[this.camChildIndexLeftHand].position;
		}
	}

	// Token: 0x0600054B RID: 1355 RVA: 0x00022618 File Offset: 0x00020818
	public void InstantComeBackOnFire()
	{
		if (!this.weaponScript.inRightHand)
		{
			if (this.weaponScript.inLeftHand)
			{
				base.transform.position = this.playerPickup.pickupPositionLeftHand[this.camChildIndexLeftHand].position;
				base.transform.localRotation = this.playerPickup.pickupPositionLeftHand[this.camChildIndexLeftHand].localRotation;
				Physics.SyncTransforms();
			}
			return;
		}
		if (this.playerController.isAiming && this.aimWeapon)
		{
			base.transform.position = (this.weaponScript.requireBothHands ? this.playerPickup.aimPositionBothHand[this.aimIndex].position : this.playerPickup.aimPositionRightHand[this.aimIndex].position);
			base.transform.localRotation = (this.weaponScript.requireBothHands ? this.playerPickup.aimPositionBothHand[this.aimIndex].localRotation : this.playerPickup.aimPositionRightHand[this.aimIndex].localRotation);
			Physics.SyncTransforms();
			return;
		}
		if (this.weaponScript.requireBothHands)
		{
			base.transform.position = this.playerPickup.pickupPositionBothHand[this.camChildIndex].position;
			base.transform.localRotation = this.playerPickup.pickupPositionBothHand[this.camChildIndex].localRotation;
			Physics.SyncTransforms();
			return;
		}
		base.transform.position = this.playerPickup.pickupPositionRightHand[this.camChildIndex].position;
		base.transform.localRotation = this.playerPickup.pickupPositionRightHand[this.camChildIndex].localRotation;
		Physics.SyncTransforms();
	}

	// Token: 0x0600054C RID: 1356 RVA: 0x000023D6 File Offset: 0x000005D6
	private void FixedUpdate()
	{
	}

	// Token: 0x0600054D RID: 1357 RVA: 0x000227DD File Offset: 0x000209DD
	public void KillAnimation()
	{
		if (base.transform != null)
		{
			base.transform.DOKill(false);
		}
	}

	// Token: 0x0600054E RID: 1358 RVA: 0x000227FA File Offset: 0x000209FA
	private void WeaponTransform(Transform pos)
	{
		this.TiltSway(pos.localRotation);
		this.WeaponSway(pos.position);
	}

	// Token: 0x0600054F RID: 1359 RVA: 0x00022814 File Offset: 0x00020A14
	private void CalculateMovementInput()
	{
		if (this.playerController != null)
		{
			this.currentInputRaw = this.playerController.currentInputRaw;
		}
		this.alreadyPlayed = false;
	}

	// Token: 0x06000550 RID: 1360 RVA: 0x0002283C File Offset: 0x00020A3C
	private void ItemMovement(Vector3 initialPosition)
	{
		if (this.currentInputRaw != Vector2.zero)
		{
			this.movTimer += Time.deltaTime * ((Input.GetKey(KeyCode.C) || Input.GetKey(KeyCode.LeftControl)) ? this.crouchBobSpeed : (Input.GetKey(KeyCode.LeftShift) ? this.sprintBobSpeed : this.walkBobSpeed));
		}
		else
		{
			this.movTimer = 0f;
		}
		float y = initialPosition.y;
		if (this.currentInputRaw != Vector2.zero)
		{
			base.transform.position = new Vector3(initialPosition.x, y + Mathf.Sin(this.movTimer) * ((Input.GetKey(KeyCode.C) || Input.GetKey(KeyCode.LeftControl)) ? this.crouchBobAmount : (Input.GetKey(KeyCode.LeftShift) ? this.sprintBobAmount : this.walkBobAmount)), initialPosition.z);
			return;
		}
		base.transform.position = initialPosition;
	}

	// Token: 0x06000551 RID: 1361 RVA: 0x0002293C File Offset: 0x00020B3C
	private void WeaponSway(Vector3 initialPosition)
	{
		RaycastHit raycastHit;
		if (Physics.SphereCast(this.cam.ViewportPointToRay(new Vector3(0.5f, 0.5f, 0f)), this.radius, out raycastHit, this.distance, this.clippingLayerMask))
		{
			base.transform.position = Vector3.Lerp(base.transform.position, initialPosition - this.cam.transform.forward * this.offsetCurve.Evaluate(raycastHit.distance / this.distance), Time.deltaTime * this.smoothAmount);
			return;
		}
		this.InputX = -Input.GetAxis("Mouse X");
		this.InputY = -Input.GetAxis("Mouse Y");
		float num = Mathf.Clamp(this.InputX * this.amount, -this.maxAmount, this.maxAmount);
		float num2 = Mathf.Clamp(this.InputY * this.amount, -this.maxAmount, this.maxAmount);
		initialPosition + new Vector3(num, num2, 0f);
		base.transform.position = Vector3.Lerp(base.transform.position, initialPosition, Time.deltaTime * this.smoothAmount);
	}

	// Token: 0x06000552 RID: 1362 RVA: 0x00022A84 File Offset: 0x00020C84
	private void TiltSway(Quaternion initialRotation)
	{
		float num = -Mathf.Clamp(Input.GetAxis("Mouse X") * this.rotationAmount, -this.maxRotationAmount, this.maxRotationAmount);
		float num2 = -Mathf.Clamp(-Input.GetAxis("Mouse Y") * this.rotationAmount, -this.maxRotationAmount, this.maxRotationAmount);
		initialRotation * Quaternion.Euler(new Vector3(this.rotationX ? num2 : 0f, this.rotationY ? num : 0f, this.rotationZ ? num : 0f));
		base.transform.localRotation = Quaternion.Slerp(base.transform.localRotation, initialRotation, Time.deltaTime * this.smoothRotation);
	}

	// Token: 0x06000553 RID: 1363 RVA: 0x00022B48 File Offset: 0x00020D48
	public void StickOnGround()
	{
		if (this.rootObject == null)
		{
			return;
		}
		Collider[] array = Physics.OverlapSphere(this.finalPos, 0.7f, this.groundLayer);
		Collider[] array2 = Physics.OverlapSphere(this.rootObject.transform.position + Vector3.up + this.rootObject.transform.forward, 0.7f, this.groundLayer);
		if (array.Length != 0 || array2.Length != 0)
		{
			RaycastHit raycastHit;
			if (Physics.Raycast(this.rootObject.transform.position, -Vector3.up, out raycastHit, float.PositiveInfinity, this.groundLayer))
			{
				this.tween1 = base.transform.DOMove(raycastHit.point + new Vector3(0f, 0.5f, 0f), 0.5f, false).SetEase(Ease.OutQuart);
				this.tween2 = base.transform.DORotate(new Vector3(raycastHit.normal.x, this.rootObject.transform.eulerAngles.y, raycastHit.normal.z), 0.5f, RotateMode.Fast);
				return;
			}
		}
		else
		{
			Quaternion.Euler(base.transform.rotation.x, base.transform.eulerAngles.y - 90f, base.transform.rotation.z);
			base.transform.position = this.cam.transform.position + this.cam.transform.forward;
		}
	}

	// Token: 0x06000554 RID: 1364 RVA: 0x00022CF8 File Offset: 0x00020EF8
	public void StickOnGroundObservers()
	{
		if (this.rootObject == null)
		{
			return;
		}
		Collider[] array = Physics.OverlapSphere(this.finalPos, 0.7f, this.groundLayer);
		Collider[] array2 = Physics.OverlapSphere(this.rootObject.transform.position + Vector3.up + this.rootObject.transform.forward, 0.7f, this.groundLayer);
		if (array.Length != 0 || array2.Length != 0)
		{
			RaycastHit raycastHit;
			if (Physics.Raycast(this.rootObject.transform.position, -Vector3.up, out raycastHit, float.PositiveInfinity, this.groundLayer))
			{
				base.transform.position = raycastHit.point + new Vector3(0f, 0.5f, 0f);
				base.transform.rotation = Quaternion.Euler(raycastHit.normal.x, this.rootObject.transform.eulerAngles.y, raycastHit.normal.z);
				return;
			}
		}
		else
		{
			Quaternion.Euler(base.transform.rotation.x, base.transform.eulerAngles.y - 90f, base.transform.rotation.z);
			base.transform.position = this.cam.transform.position + this.cam.transform.forward;
		}
	}

	// Token: 0x06000555 RID: 1365 RVA: 0x000023D6 File Offset: 0x000005D6
	public void GroundMovement()
	{
	}

	// Token: 0x06000556 RID: 1366 RVA: 0x00022E8C File Offset: 0x0002108C
	private void SetLayerAllChildren(Transform root, int layer)
	{
		foreach (Transform transform in root.GetComponentsInChildren<Transform>(true))
		{
			if (transform.tag == "vfx")
			{
				return;
			}
			transform.gameObject.layer = layer;
		}
	}

	// Token: 0x06000557 RID: 1367 RVA: 0x00022ED2 File Offset: 0x000210D2
	public void KillTweens()
	{
		if (base.transform != null)
		{
			base.transform.DOPause();
			base.transform.DOKill(false);
		}
	}

	// Token: 0x06000558 RID: 1368 RVA: 0x00022EFB File Offset: 0x000210FB
	public void SetLayer()
	{
		this.SetLayerAllChildren(base.transform, 8);
	}

	// Token: 0x06000559 RID: 1369 RVA: 0x00022F0A File Offset: 0x0002110A
	public void UnsetLayer()
	{
		this.SetLayerAllChildren(base.transform, 0);
	}

	// Token: 0x0600055A RID: 1370 RVA: 0x0000B53E File Offset: 0x0000973E
	private void OnDrawGizmos()
	{
		Gizmos.color = Color.red;
	}

	// Token: 0x0600055B RID: 1371 RVA: 0x00022F1C File Offset: 0x0002111C
	private void OnCollisionEnter(Collision col)
	{
		if (this.tempRb && !this.alreadyPlayed)
		{
			this.alreadyPlayed = true;
			if (this.tempRb.velocity.magnitude > 0.5f)
			{
				this.audio.spatialBlend = 1f;
				this.audio.PlayOneShot(this.hitSurfaceClip, Mathf.Clamp(this.tempRb.velocity.magnitude, 1.3f, 5f) / 5f);
			}
		}
	}

	// Token: 0x0600055D RID: 1373 RVA: 0x000230D6 File Offset: 0x000212D6
	public override void NetworkInitialize___Early()
	{
		if (this.NetworkInitializeEarly_ItemBehaviour_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeEarly_ItemBehaviour_Assembly-CSharp.dll = true;
		base.NetworkInitialize___Early();
		base.RegisterObserversRpc(0U, new ClientRpcDelegate(this.RpcReader___Observers_DispenserDrop_4276783012));
	}

	// Token: 0x0600055E RID: 1374 RVA: 0x00023106 File Offset: 0x00021306
	public override void NetworkInitialize__Late()
	{
		if (this.NetworkInitializeLate_ItemBehaviour_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeLate_ItemBehaviour_Assembly-CSharp.dll = true;
		base.NetworkInitialize__Late();
	}

	// Token: 0x0600055F RID: 1375 RVA: 0x0002311F File Offset: 0x0002131F
	public override void NetworkInitializeIfDisabled()
	{
		this.NetworkInitialize___Early();
		this.NetworkInitialize__Late();
	}

	// Token: 0x06000560 RID: 1376 RVA: 0x00023130 File Offset: 0x00021330
	private void RpcWriter___Observers_DispenserDrop_4276783012(Vector3 dir)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteVector3(dir);
		base.SendObserversRpc(0U, writer, channel, DataOrderType.Default, false, false, false);
		writer.Store();
	}

	// Token: 0x06000561 RID: 1377 RVA: 0x000231E8 File Offset: 0x000213E8
	public void RpcLogic___DispenserDrop_4276783012(Vector3 dir)
	{
		this.dispenserStart = true;
		base.gameObject.AddComponent<Rigidbody>();
		this.tempRb = base.GetComponent<Rigidbody>();
		this.tempRb.collisionDetectionMode = CollisionDetectionMode.ContinuousDynamic;
		this.tempRb.drag = 0f;
		this.tempRb.interpolation = RigidbodyInterpolation.Interpolate;
		this.tempRb.AddForce(dir * this.ejectForce / 2f, ForceMode.Impulse);
		this.tempRb.AddTorque(dir * this.torqueForce + base.transform.right * this.torqueForce, ForceMode.Impulse);
		global::UnityEngine.Random.Range(-1, 1);
	}

	// Token: 0x06000562 RID: 1378 RVA: 0x0002329C File Offset: 0x0002149C
	private void RpcReader___Observers_DispenserDrop_4276783012(PooledReader PooledReader0, Channel channel)
	{
		Vector3 vector = PooledReader0.ReadVector3();
		if (!base.IsClient)
		{
			return;
		}
		if (base.IsHost)
		{
			return;
		}
		this.RpcLogic___DispenserDrop_4276783012(vector);
	}

	// Token: 0x06000563 RID: 1379 RVA: 0x000232D7 File Offset: 0x000214D7
	public override void Awake()
	{
		this.NetworkInitialize___Early();
		base.Awake();
		this.NetworkInitialize__Late();
	}

	// Token: 0x06000564 RID: 1380 RVA: 0x000023D6 File Offset: 0x000005D6
	public override void Awake___UserLogic()
	{
	}

	// Token: 0x0400051B RID: 1307
	public string weaponName;

	// Token: 0x0400051C RID: 1308
	public bool heavy;

	// Token: 0x0400051D RID: 1309
	public bool vertical;

	// Token: 0x0400051E RID: 1310
	public int camChildIndex;

	// Token: 0x0400051F RID: 1311
	public int camChildIndexLeftHand;

	// Token: 0x04000520 RID: 1312
	public int aimIndex;

	// Token: 0x04000521 RID: 1313
	public bool aimWeapon = true;

	// Token: 0x04000522 RID: 1314
	public float aimFOV = 45f;

	// Token: 0x04000523 RID: 1315
	[Space]
	public bool vfxAttachedOnGun;

	// Token: 0x04000524 RID: 1316
	public ParticleSystem smokeTrail;

	// Token: 0x04000525 RID: 1317
	[Space]
	public string rightHandAnim;

	// Token: 0x04000526 RID: 1318
	public string leftHandAnim;

	// Token: 0x04000527 RID: 1319
	public string outofhandsAnim;

	// Token: 0x04000528 RID: 1320
	[SerializeField]
	private Sprite standCrosshair;

	// Token: 0x04000529 RID: 1321
	[SerializeField]
	private Sprite sprintCrosshair;

	// Token: 0x0400052A RID: 1322
	[SerializeField]
	private bool instantAimLens;

	// Token: 0x0400052B RID: 1323
	[SerializeField]
	private Sprite aimCrosshair;

	// Token: 0x0400052C RID: 1324
	[SerializeField]
	private float ejectForce = 9.5f;

	// Token: 0x0400052D RID: 1325
	[SerializeField]
	private float torqueForce = 18f;

	// Token: 0x0400052E RID: 1326
	[SerializeField]
	private float gravityAdded = 2f;

	// Token: 0x0400052F RID: 1327
	[SerializeField]
	private AudioClip hitSurfaceClip;

	// Token: 0x04000530 RID: 1328
	public Transform gripRight;

	// Token: 0x04000531 RID: 1329
	public Transform gripLeft;

	// Token: 0x04000532 RID: 1330
	private Vector3 finalPos;

	// Token: 0x04000533 RID: 1331
	[SerializeField]
	private AudioClip grabClip;

	// Token: 0x04000534 RID: 1332
	public GameObject depopVFX;

	// Token: 0x04000535 RID: 1333
	[SerializeField]
	private LayerMask groundLayer;

	// Token: 0x04000536 RID: 1334
	public Camera cam;

	// Token: 0x04000537 RID: 1335
	public bool isTaken;

	// Token: 0x04000538 RID: 1336
	private Tween groundMov;

	// Token: 0x04000539 RID: 1337
	private Tween tween1;

	// Token: 0x0400053A RID: 1338
	private Tween tween2;

	// Token: 0x0400053B RID: 1339
	private Tween tween3;

	// Token: 0x0400053C RID: 1340
	private Tween tween4;

	// Token: 0x0400053D RID: 1341
	public GameObject rootObject;

	// Token: 0x0400053E RID: 1342
	public GameObject lastPlayerHolder;

	// Token: 0x0400053F RID: 1343
	public Transform[] fpArms = new Transform[2];

	// Token: 0x04000540 RID: 1344
	public PlayerPickup playerPickup;

	// Token: 0x04000541 RID: 1345
	public FirstPersonController playerController;

	// Token: 0x04000542 RID: 1346
	private Weapon weaponScript;

	// Token: 0x04000543 RID: 1347
	private MeshRenderer[] hoveredObjectRenderer = new MeshRenderer[2];

	// Token: 0x04000544 RID: 1348
	private List<Material> hoveredObjectMat = new List<Material>();

	// Token: 0x04000545 RID: 1349
	private AudioSource audio;

	// Token: 0x04000546 RID: 1350
	private Rigidbody tempRb;

	// Token: 0x04000547 RID: 1351
	public bool dispenserStart;

	// Token: 0x04000548 RID: 1352
	[Header("AimStrafe Lean")]
	[SerializeField]
	private Transform aimStrafePivot;

	// Token: 0x04000549 RID: 1353
	[SerializeField]
	private float maxPivot = 3f;

	// Token: 0x0400054A RID: 1354
	[SerializeField]
	private float aimStrafeLeanSpeed = 10f;

	// Token: 0x0400054B RID: 1355
	[Header("Position")]
	public float amount = 0.02f;

	// Token: 0x0400054C RID: 1356
	public float maxAmount = 0.06f;

	// Token: 0x0400054D RID: 1357
	public float smoothAmount = 6f;

	// Token: 0x0400054E RID: 1358
	[Header("Rotation")]
	public float rotationAmount = 4f;

	// Token: 0x0400054F RID: 1359
	public float maxRotationAmount = 5f;

	// Token: 0x04000550 RID: 1360
	public float smoothRotation = 12f;

	// Token: 0x04000551 RID: 1361
	[Space]
	public bool rotationX = true;

	// Token: 0x04000552 RID: 1362
	public bool rotationY = true;

	// Token: 0x04000553 RID: 1363
	public bool rotationZ = true;

	// Token: 0x04000554 RID: 1364
	[Header("Headbob")]
	[SerializeField]
	public float walkBobSpeed = 14f;

	// Token: 0x04000555 RID: 1365
	[SerializeField]
	public float walkBobAmount = 0.02f;

	// Token: 0x04000556 RID: 1366
	[SerializeField]
	public float sprintBobSpeed = 18f;

	// Token: 0x04000557 RID: 1367
	[SerializeField]
	public float sprintBobAmount = 0.05f;

	// Token: 0x04000558 RID: 1368
	[SerializeField]
	public float crouchBobSpeed = 8f;

	// Token: 0x04000559 RID: 1369
	[SerializeField]
	public float crouchBobAmount = 0.008f;

	// Token: 0x0400055A RID: 1370
	private float InputX;

	// Token: 0x0400055B RID: 1371
	private float InputY;

	// Token: 0x0400055C RID: 1372
	private Vector2 currentInputRaw;

	// Token: 0x0400055D RID: 1373
	private float verticalInputRaw;

	// Token: 0x0400055E RID: 1374
	private float horizontalInputRaw;

	// Token: 0x0400055F RID: 1375
	private float movTimer;

	// Token: 0x04000560 RID: 1376
	private Collider col;

	// Token: 0x04000561 RID: 1377
	private bool alreadyPlayed;

	// Token: 0x04000562 RID: 1378
	[Header("Wall Clip Fix")]
	[SerializeField]
	private float distance = 1f;

	// Token: 0x04000563 RID: 1379
	[SerializeField]
	private float radius = 0.125f;

	// Token: 0x04000564 RID: 1380
	[SerializeField]
	private LayerMask clippingLayerMask;

	// Token: 0x04000565 RID: 1381
	[SerializeField]
	private AnimationCurve offsetCurve;

	// Token: 0x04000566 RID: 1382
	private Vector3 initialLocalPosition;

	// Token: 0x04000567 RID: 1383
	private bool NetworkInitializeEarly_ItemBehaviour_Assembly-CSharp.dll;

	// Token: 0x04000568 RID: 1384
	private bool NetworkInitializeLate_ItemBehaviour_Assembly-CSharp.dll;
}

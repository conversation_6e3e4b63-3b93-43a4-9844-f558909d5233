﻿using System;
using TMPro;
using UnityEngine;

// Token: 0x02000159 RID: 345
public class AdjustTimeScale : MonoBehaviour
{
	// Token: 0x06000EFD RID: 3837 RVA: 0x00062CEB File Offset: 0x00060EEB
	private void Start()
	{
		this.textMesh = base.GetComponent<TextMeshProUGUI>();
	}

	// Token: 0x06000EFE RID: 3838 RVA: 0x00062CFC File Offset: 0x00060EFC
	private void Update()
	{
		if (Input.GetAxis("Mouse ScrollWheel") > 0f)
		{
			if (Time.timeScale < 1f)
			{
				Time.timeScale += 0.1f;
			}
			Time.fixedDeltaTime = 0.02f * Time.timeScale;
			if (this.textMesh != null)
			{
				this.textMesh.text = "Time Scale : " + Math.Round((double)Time.timeScale, 2).ToString();
				return;
			}
		}
		else if (Input.GetAxis("Mouse ScrollWheel") < 0f)
		{
			if (Time.timeScale >= 0.2f)
			{
				Time.timeScale -= 0.1f;
			}
			Time.fixedDeltaTime = 0.02f * Time.timeScale;
			if (this.textMesh != null)
			{
				this.textMesh.text = "Time Scale : " + Math.Round((double)Time.timeScale, 2).ToString();
			}
		}
	}

	// Token: 0x06000EFF RID: 3839 RVA: 0x00062DF5 File Offset: 0x00060FF5
	private void OnApplicationQuit()
	{
		Time.timeScale = 1f;
		Time.fixedDeltaTime = 0.02f;
	}

	// Token: 0x04000DA1 RID: 3489
	private TextMeshProUGUI textMesh;
}

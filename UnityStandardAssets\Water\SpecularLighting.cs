﻿using System;
using UnityEngine;

namespace UnityStandardAssets.Water
{
	// Token: 0x0200019D RID: 413
	[RequireComponent(typeof(WaterBase))]
	[ExecuteInEditMode]
	public class SpecularLighting : MonoBehaviour
	{
		// Token: 0x06001052 RID: 4178 RVA: 0x00069FD1 File Offset: 0x000681D1
		public void Start()
		{
			this.m_WaterBase = (WaterBase)base.gameObject.GetComponent(typeof(WaterBase));
		}

		// Token: 0x06001053 RID: 4179 RVA: 0x00069FF4 File Offset: 0x000681F4
		public void Update()
		{
			if (!this.m_WaterBase)
			{
				this.m_WaterBase = (WaterBase)base.gameObject.GetComponent(typeof(WaterBase));
			}
			if (this.specularLight && this.m_WaterBase.sharedMaterial)
			{
				this.m_WaterBase.sharedMaterial.SetVector("_WorldLightDir", this.specularLight.transform.forward);
			}
		}

		// Token: 0x04000F10 RID: 3856
		public Transform specularLight;

		// Token: 0x04000F11 RID: 3857
		private WaterBase m_WaterBase;
	}
}

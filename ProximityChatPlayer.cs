﻿using System;
using FishNet;
using FishNet.Connection;
using FishNet.Managing;
using FishNet.Object;
using FishNet.Object.Delegating;
using FishNet.Serializing;
using FishNet.Transporting;
using LambdaTheDev.NetworkAudioSync;
using UnityEngine;

// Token: 0x020000F9 RID: 249
public class ProximityChatPlayer : NetworkBehaviour
{
	// Token: 0x06000CB2 RID: 3250 RVA: 0x00056A2A File Offset: 0x00054C2A
	[ServerRpc]
	private void PlaybackServer(bool play)
	{
		this.RpcWriter___Server_PlaybackServer_1140765316(play);
	}

	// Token: 0x06000CB3 RID: 3251 RVA: 0x00056A36 File Offset: 0x00054C36
	public override void OnStartClient()
	{
		base.OnStartClient();
		if (base.IsOwner)
		{
			base.GetComponent<AudioSource>().volume = 0f;
		}
	}

	// Token: 0x06000CB5 RID: 3253 RVA: 0x00056A56 File Offset: 0x00054C56
	public virtual void NetworkInitialize___Early()
	{
		if (this.NetworkInitializeEarly_ProximityChatPlayer_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeEarly_ProximityChatPlayer_Assembly-CSharp.dll = true;
		base.RegisterServerRpc(0U, new ServerRpcDelegate(this.RpcReader___Server_PlaybackServer_1140765316));
	}

	// Token: 0x06000CB6 RID: 3254 RVA: 0x00056A80 File Offset: 0x00054C80
	public virtual void NetworkInitialize__Late()
	{
		if (this.NetworkInitializeLate_ProximityChatPlayer_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeLate_ProximityChatPlayer_Assembly-CSharp.dll = true;
	}

	// Token: 0x06000CB7 RID: 3255 RVA: 0x00056A93 File Offset: 0x00054C93
	public override void NetworkInitializeIfDisabled()
	{
		this.NetworkInitialize___Early();
		this.NetworkInitialize__Late();
	}

	// Token: 0x06000CB8 RID: 3256 RVA: 0x00056AA4 File Offset: 0x00054CA4
	private void RpcWriter___Server_PlaybackServer_1140765316(bool play)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteBoolean(play);
		base.SendServerRpc(0U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000CB9 RID: 3257 RVA: 0x00056BA5 File Offset: 0x00054DA5
	private void RpcLogic___PlaybackServer_1140765316(bool play)
	{
		if (play)
		{
			this.outputSource.Play(0);
			return;
		}
		this.outputSource.Stop();
	}

	// Token: 0x06000CBA RID: 3258 RVA: 0x00056BC4 File Offset: 0x00054DC4
	private void RpcReader___Server_PlaybackServer_1140765316(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		bool flag = PooledReader0.ReadBoolean();
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		this.RpcLogic___PlaybackServer_1140765316(flag);
	}

	// Token: 0x06000CBB RID: 3259 RVA: 0x00056A93 File Offset: 0x00054C93
	public virtual void Awake()
	{
		this.NetworkInitialize___Early();
		this.NetworkInitialize__Late();
	}

	// Token: 0x06000CBC RID: 3260 RVA: 0x000023D6 File Offset: 0x000005D6
	public virtual void Awake___UserLogic()
	{
	}

	// Token: 0x04000AF6 RID: 2806
	[SerializeField]
	private NetworkAudioSource outputSource;

	// Token: 0x04000AF7 RID: 2807
	[SerializeField]
	private AudioSource localOutputSource;

	// Token: 0x04000AF8 RID: 2808
	public AudioClip clip;

	// Token: 0x04000AF9 RID: 2809
	private bool NetworkInitializeEarly_ProximityChatPlayer_Assembly-CSharp.dll;

	// Token: 0x04000AFA RID: 2810
	private bool NetworkInitializeLate_ProximityChatPlayer_Assembly-CSharp.dll;
}

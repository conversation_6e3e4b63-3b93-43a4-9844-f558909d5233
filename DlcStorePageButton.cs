﻿using System;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

// Token: 0x02000127 RID: 295
public class DlcStorePageButton : MonoBehaviour, IPointerEnterHandler, IEventSystemHandler, IPointerExitHandler
{
	// Token: 0x06000E39 RID: 3641 RVA: 0x0005EE33 File Offset: 0x0005D033
	private void Awake()
	{
		this.button = base.GetComponent<Button>();
	}

	// Token: 0x06000E3A RID: 3642 RVA: 0x0005EE41 File Offset: 0x0005D041
	private void Start()
	{
		this.steamManager = SteamLobby.Instance;
	}

	// Token: 0x06000E3B RID: 3643 RVA: 0x0005EE50 File Offset: 0x0005D050
	private void Update()
	{
		if (SteamLobby.ownDlc0 && this.notOwnedImage.activeSelf)
		{
			ProgressManager.Instance.ReturnToMenu();
		}
		GameObject[] array;
		if (SteamLobby.ownDlc0)
		{
			this.notOwnedImage.SetActive(false);
			this.ownedImage.SetActive(true);
			array = this.notOwnedObjects;
			for (int i = 0; i < array.Length; i++)
			{
				array[i].SetActive(false);
			}
			array = this.ownedObjects;
			for (int i = 0; i < array.Length; i++)
			{
				array[i].SetActive(true);
			}
			Material[] materials = this.screen.materials;
			materials[1] = this.ownedMat;
			this.screen.materials = materials;
			return;
		}
		this.notOwnedImage.SetActive(true);
		this.ownedImage.SetActive(false);
		array = this.notOwnedObjects;
		for (int i = 0; i < array.Length; i++)
		{
			array[i].SetActive(true);
		}
		array = this.ownedObjects;
		for (int i = 0; i < array.Length; i++)
		{
			array[i].SetActive(false);
		}
		Material[] materials2 = this.screen.materials;
		materials2[1] = this.notOwnedMat;
		this.screen.materials = materials2;
	}

	// Token: 0x06000E3C RID: 3644 RVA: 0x0005EF6D File Offset: 0x0005D16D
	public void OnPointerEnter(PointerEventData eventData)
	{
		if (!SteamLobby.ownDlc0)
		{
			FloatingName.Instance.nameToShow = "Get the DLC !";
			return;
		}
		FloatingName.Instance.nameToShow = "DLC Owned";
	}

	// Token: 0x06000E3D RID: 3645 RVA: 0x0001A57E File Offset: 0x0001877E
	public void OnPointerExit(PointerEventData eventData)
	{
		FloatingName.Instance.nameToShow = "";
	}

	// Token: 0x04000CC3 RID: 3267
	private Button button;

	// Token: 0x04000CC4 RID: 3268
	[SerializeField]
	private GameObject notOwnedImage;

	// Token: 0x04000CC5 RID: 3269
	[SerializeField]
	private GameObject ownedImage;

	// Token: 0x04000CC6 RID: 3270
	[SerializeField]
	private GameObject[] notOwnedObjects;

	// Token: 0x04000CC7 RID: 3271
	[SerializeField]
	private GameObject[] ownedObjects;

	// Token: 0x04000CC8 RID: 3272
	[SerializeField]
	private MeshRenderer screen;

	// Token: 0x04000CC9 RID: 3273
	[SerializeField]
	private Material notOwnedMat;

	// Token: 0x04000CCA RID: 3274
	[SerializeField]
	private Material ownedMat;

	// Token: 0x04000CCB RID: 3275
	private SteamLobby steamManager;
}

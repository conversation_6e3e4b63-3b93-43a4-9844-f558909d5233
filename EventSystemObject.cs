﻿using System;
using UnityEngine;

// Token: 0x020000C8 RID: 200
public class EventSystemObject : MonoBehaviour
{
	// Token: 0x06000B34 RID: 2868 RVA: 0x0004F500 File Offset: 0x0004D700
	private void Awake()
	{
		if (EventSystemObject.Instance == null)
		{
			EventSystemObject.Instance = this;
			global::UnityEngine.Object.DontDestroyOnLoad(base.gameObject);
			return;
		}
		global::UnityEngine.Object.Destroy(base.gameObject);
	}

	// Token: 0x04000974 RID: 2420
	public static EventSystemObject Instance;
}

﻿using System;
using System.Collections.Generic;
using System.Runtime.InteropServices;
using UnityEngine;

namespace FishNet.Serializing.Generated
{
	// Token: 0x020001A9 RID: 425
	[StructLayout(LayoutKind.Auto, CharSet = CharSet.Auto)]
	public static class GeneratedReaders___Internal
	{
		// Token: 0x06001074 RID: 4212 RVA: 0x0006B0C8 File Offset: 0x000692C8
		[RuntimeInitializeOnLoadMethod]
		private static void InitializeOnce()
		{
			GenericReader<ChatBroadcast.Message>.Read = new Func<Reader, ChatBroadcast.Message>(GeneratedReaders___Internal.Read___ChatBroadcast/MessageFishNet.Serializing.Generateds);
			GenericReader<PlayerHealth>.Read = new Func<Reader, PlayerHealth>(GeneratedReaders___Internal.Read___PlayerHealthFishNet.Serializing.Generateds);
			GenericReader<PlayerValues>.Read = new Func<Reader, PlayerValues>(GeneratedReaders___Internal.Read___PlayerValuesFishNet.Serializing.Generateds);
			GenericReader<PlayerSpawnObject>.Read = new Func<Reader, PlayerSpawnObject>(GeneratedReaders___Internal.Read___PlayerSpawnObjectFishNet.Serializing.Generateds);
			GenericReader<ClientInstance>.Read = new Func<Reader, ClientInstance>(GeneratedReaders___Internal.Read___ClientInstanceFishNet.Serializing.Generateds);
			GenericReader<List<int>>.Read = new Func<Reader, List<int>>(GeneratedReaders___Internal.Read___System.Collections.Generic.List`1<System.Int32>FishNet.Serializing.Generateds);
			GenericReader<string[]>.Read = new Func<Reader, string[]>(GeneratedReaders___Internal.Read___System.String[]FishNet.Serializing.Generateds);
			GenericReader<Weapon>.Read = new Func<Reader, Weapon>(GeneratedReaders___Internal.Read___WeaponFishNet.Serializing.Generateds);
			GenericReader<Dictionary<int, int>>.Read = new Func<Reader, Dictionary<int, int>>(GeneratedReaders___Internal.Read___System.Collections.Generic.Dictionary`2<System.Int32,System.Int32>FishNet.Serializing.Generateds);
			GenericReader<WeaponData>.Read = new Func<Reader, WeaponData>(GeneratedReaders___Internal.Read___WeaponDataFishNet.Serializing.Generateds);
		}

		// Token: 0x06001075 RID: 4213 RVA: 0x0006B180 File Offset: 0x00069380
		public static ChatBroadcast.Message Read___ChatBroadcast/MessageFishNet.Serializing.Generateds(Reader reader)
		{
			return new ChatBroadcast.Message
			{
				username = reader.ReadString(),
				message = reader.ReadString()
			};
		}

		// Token: 0x06001076 RID: 4214 RVA: 0x0006B1C0 File Offset: 0x000693C0
		public static PlayerHealth Read___PlayerHealthFishNet.Serializing.Generateds(Reader reader)
		{
			return (PlayerHealth)reader.ReadNetworkBehaviour();
		}

		// Token: 0x06001077 RID: 4215 RVA: 0x0006B1D8 File Offset: 0x000693D8
		public static PlayerValues Read___PlayerValuesFishNet.Serializing.Generateds(Reader reader)
		{
			return (PlayerValues)reader.ReadNetworkBehaviour();
		}

		// Token: 0x06001078 RID: 4216 RVA: 0x0006B1F0 File Offset: 0x000693F0
		public static PlayerSpawnObject Read___PlayerSpawnObjectFishNet.Serializing.Generateds(Reader reader)
		{
			return (PlayerSpawnObject)reader.ReadNetworkBehaviour();
		}

		// Token: 0x06001079 RID: 4217 RVA: 0x0006B208 File Offset: 0x00069408
		public static ClientInstance Read___ClientInstanceFishNet.Serializing.Generateds(Reader reader)
		{
			return (ClientInstance)reader.ReadNetworkBehaviour();
		}

		// Token: 0x0600107A RID: 4218 RVA: 0x0006B220 File Offset: 0x00069420
		public static List<int> Read___System.Collections.Generic.List`1<System.Int32>FishNet.Serializing.Generateds(Reader reader)
		{
			return reader.ReadListAllocated<int>();
		}

		// Token: 0x0600107B RID: 4219 RVA: 0x0006B238 File Offset: 0x00069438
		public static string[] Read___System.String[]FishNet.Serializing.Generateds(Reader reader)
		{
			return reader.ReadArrayAllocated<string>();
		}

		// Token: 0x0600107C RID: 4220 RVA: 0x0006B250 File Offset: 0x00069450
		public static Weapon Read___WeaponFishNet.Serializing.Generateds(Reader reader)
		{
			return (Weapon)reader.ReadNetworkBehaviour();
		}

		// Token: 0x0600107D RID: 4221 RVA: 0x0006B268 File Offset: 0x00069468
		public static Dictionary<int, int> Read___System.Collections.Generic.Dictionary`2<System.Int32,System.Int32>FishNet.Serializing.Generateds(Reader reader)
		{
			return reader.ReadDictionary<int, int>();
		}

		// Token: 0x0600107E RID: 4222 RVA: 0x0006B280 File Offset: 0x00069480
		public static WeaponData Read___WeaponDataFishNet.Serializing.Generateds(Reader reader)
		{
			return new WeaponData
			{
				WeaponName = reader.ReadString(),
				SpawnChance = reader.ReadUInt32(AutoPackType.Packed),
				IsSpawnable = reader.ReadBoolean()
			};
		}
	}
}

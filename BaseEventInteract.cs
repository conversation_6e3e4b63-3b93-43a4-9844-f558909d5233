﻿using System;
using FishNet;
using FishNet.Connection;
using FishNet.Managing;
using FishNet.Object;
using FishNet.Object.Delegating;
using FishNet.Serializing;
using FishNet.Transporting;
using UnityEngine;
using UnityEngine.Events;

// Token: 0x02000043 RID: 67
public abstract class BaseEventInteract : InteractEnvironment
{
	// Token: 0x06000333 RID: 819 RVA: 0x00019CE3 File Offset: 0x00017EE3
	protected virtual void OnEnable()
	{
		PauseManager.OnRoundStarted += this.RoundStart;
	}

	// Token: 0x06000334 RID: 820 RVA: 0x00019CF6 File Offset: 0x00017EF6
	protected virtual void OnDisable()
	{
		PauseManager.OnRoundStarted -= this.RoundStart;
	}

	// Token: 0x06000335 RID: 821 RVA: 0x00019D09 File Offset: 0x00017F09
	private void RoundStart()
	{
		this.IsReady = true;
	}

	// Token: 0x06000336 RID: 822 RVA: 0x00019D12 File Offset: 0x00017F12
	public override void Awake()
	{
		this.NetworkInitialize___Early();
		this.Awake___UserLogic();
		this.NetworkInitialize__Late();
	}

	// Token: 0x06000337 RID: 823 RVA: 0x00019D26 File Offset: 0x00017F26
	public override void OnInteract(Transform player)
	{
		if (!this.IsReady)
		{
			return;
		}
		this.OnInteractServerRpc();
	}

	// Token: 0x06000338 RID: 824 RVA: 0x00019D37 File Offset: 0x00017F37
	[ServerRpc(RequireOwnership = false)]
	private void OnInteractServerRpc()
	{
		this.RpcWriter___Server_OnInteractServerRpc_2166136261();
	}

	// Token: 0x06000339 RID: 825
	protected abstract void OnInteractObserverRpc();

	// Token: 0x0600033A RID: 826 RVA: 0x00019D40 File Offset: 0x00017F40
	public override void OnFocus()
	{
		if (!this.IsReady)
		{
			return;
		}
		PauseManager.Instance.interactPopup.gameObject.SetActive(true);
		PauseManager.Instance.interactPopup.text = this.popupText + " [" + PauseManager.Instance.InteractPromptLetter.ToLower() + "]";
		this.OnFocusServerRpc();
	}

	// Token: 0x0600033B RID: 827 RVA: 0x00019DA4 File Offset: 0x00017FA4
	[ServerRpc(RequireOwnership = false)]
	private void OnFocusServerRpc()
	{
		this.RpcWriter___Server_OnFocusServerRpc_2166136261();
	}

	// Token: 0x0600033C RID: 828 RVA: 0x00019DAC File Offset: 0x00017FAC
	[ObserversRpc]
	private void OnFocusObserverRpc()
	{
		this.RpcWriter___Observers_OnFocusObserverRpc_2166136261();
	}

	// Token: 0x0600033D RID: 829 RVA: 0x00019DB4 File Offset: 0x00017FB4
	public override void OnLoseFocus()
	{
		if (!this.IsReady)
		{
			return;
		}
		this.OnLoseFocusServerRpc();
	}

	// Token: 0x0600033E RID: 830 RVA: 0x00019DC5 File Offset: 0x00017FC5
	[ServerRpc(RequireOwnership = false)]
	private void OnLoseFocusServerRpc()
	{
		this.RpcWriter___Server_OnLoseFocusServerRpc_2166136261();
	}

	// Token: 0x0600033F RID: 831 RVA: 0x00019DCD File Offset: 0x00017FCD
	[ObserversRpc]
	private void OnLoseFocusObserverRpc()
	{
		this.RpcWriter___Observers_OnLoseFocusObserverRpc_2166136261();
	}

	// Token: 0x06000341 RID: 833 RVA: 0x00019DE0 File Offset: 0x00017FE0
	public override void NetworkInitialize___Early()
	{
		if (this.NetworkInitializeEarly_BaseEventInteract_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeEarly_BaseEventInteract_Assembly-CSharp.dll = true;
		base.NetworkInitialize___Early();
		base.RegisterServerRpc(0U, new ServerRpcDelegate(this.RpcReader___Server_OnInteractServerRpc_2166136261));
		base.RegisterServerRpc(1U, new ServerRpcDelegate(this.RpcReader___Server_OnFocusServerRpc_2166136261));
		base.RegisterObserversRpc(2U, new ClientRpcDelegate(this.RpcReader___Observers_OnFocusObserverRpc_2166136261));
		base.RegisterServerRpc(3U, new ServerRpcDelegate(this.RpcReader___Server_OnLoseFocusServerRpc_2166136261));
		base.RegisterObserversRpc(4U, new ClientRpcDelegate(this.RpcReader___Observers_OnLoseFocusObserverRpc_2166136261));
	}

	// Token: 0x06000342 RID: 834 RVA: 0x00019E77 File Offset: 0x00018077
	public override void NetworkInitialize__Late()
	{
		if (this.NetworkInitializeLate_BaseEventInteract_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeLate_BaseEventInteract_Assembly-CSharp.dll = true;
		base.NetworkInitialize__Late();
	}

	// Token: 0x06000343 RID: 835 RVA: 0x00019E90 File Offset: 0x00018090
	public override void NetworkInitializeIfDisabled()
	{
		this.NetworkInitialize___Early();
		this.NetworkInitialize__Late();
	}

	// Token: 0x06000344 RID: 836 RVA: 0x00019EA0 File Offset: 0x000180A0
	private void RpcWriter___Server_OnInteractServerRpc_2166136261()
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendServerRpc(0U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000345 RID: 837 RVA: 0x00019F3A File Offset: 0x0001813A
	private void RpcLogic___OnInteractServerRpc_2166136261()
	{
		this.OnInteractObserverRpc();
	}

	// Token: 0x06000346 RID: 838 RVA: 0x00019F44 File Offset: 0x00018144
	private void RpcReader___Server_OnInteractServerRpc_2166136261(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		if (!base.IsServer)
		{
			return;
		}
		this.RpcLogic___OnInteractServerRpc_2166136261();
	}

	// Token: 0x06000347 RID: 839 RVA: 0x00019F64 File Offset: 0x00018164
	private void RpcWriter___Server_OnFocusServerRpc_2166136261()
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendServerRpc(1U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000348 RID: 840 RVA: 0x00019FFE File Offset: 0x000181FE
	private void RpcLogic___OnFocusServerRpc_2166136261()
	{
		this.OnFocusObserverRpc();
	}

	// Token: 0x06000349 RID: 841 RVA: 0x0001A008 File Offset: 0x00018208
	private void RpcReader___Server_OnFocusServerRpc_2166136261(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		if (!base.IsServer)
		{
			return;
		}
		this.RpcLogic___OnFocusServerRpc_2166136261();
	}

	// Token: 0x0600034A RID: 842 RVA: 0x0001A028 File Offset: 0x00018228
	private void RpcWriter___Observers_OnFocusObserverRpc_2166136261()
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendObserversRpc(2U, writer, channel, DataOrderType.Default, false, false, false);
		writer.Store();
	}

	// Token: 0x0600034B RID: 843 RVA: 0x0001A0D1 File Offset: 0x000182D1
	private void RpcLogic___OnFocusObserverRpc_2166136261()
	{
		UnityEvent unityEvent = this.onFocusEvent;
		if (unityEvent == null)
		{
			return;
		}
		unityEvent.Invoke();
	}

	// Token: 0x0600034C RID: 844 RVA: 0x0001A0E4 File Offset: 0x000182E4
	private void RpcReader___Observers_OnFocusObserverRpc_2166136261(PooledReader PooledReader0, Channel channel)
	{
		if (!base.IsClient)
		{
			return;
		}
		this.RpcLogic___OnFocusObserverRpc_2166136261();
	}

	// Token: 0x0600034D RID: 845 RVA: 0x0001A104 File Offset: 0x00018304
	private void RpcWriter___Server_OnLoseFocusServerRpc_2166136261()
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendServerRpc(3U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x0600034E RID: 846 RVA: 0x0001A19E File Offset: 0x0001839E
	private void RpcLogic___OnLoseFocusServerRpc_2166136261()
	{
		this.OnLoseFocusObserverRpc();
	}

	// Token: 0x0600034F RID: 847 RVA: 0x0001A1A8 File Offset: 0x000183A8
	private void RpcReader___Server_OnLoseFocusServerRpc_2166136261(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		if (!base.IsServer)
		{
			return;
		}
		this.RpcLogic___OnLoseFocusServerRpc_2166136261();
	}

	// Token: 0x06000350 RID: 848 RVA: 0x0001A1C8 File Offset: 0x000183C8
	private void RpcWriter___Observers_OnLoseFocusObserverRpc_2166136261()
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendObserversRpc(4U, writer, channel, DataOrderType.Default, false, false, false);
		writer.Store();
	}

	// Token: 0x06000351 RID: 849 RVA: 0x0001A271 File Offset: 0x00018471
	private void RpcLogic___OnLoseFocusObserverRpc_2166136261()
	{
		UnityEvent unityEvent = this.onLoseFocusEvent;
		if (unityEvent == null)
		{
			return;
		}
		unityEvent.Invoke();
	}

	// Token: 0x06000352 RID: 850 RVA: 0x0001A284 File Offset: 0x00018484
	private void RpcReader___Observers_OnLoseFocusObserverRpc_2166136261(PooledReader PooledReader0, Channel channel)
	{
		if (!base.IsClient)
		{
			return;
		}
		this.RpcLogic___OnLoseFocusObserverRpc_2166136261();
	}

	// Token: 0x06000353 RID: 851 RVA: 0x0001A2A4 File Offset: 0x000184A4
	public override void Awake___UserLogic()
	{
		base.Awake();
	}

	// Token: 0x040003B6 RID: 950
	private bool IsReady;

	// Token: 0x040003B7 RID: 951
	[Header("This event triggers when the player focuses on this object.")]
	[SerializeField]
	private UnityEvent onFocusEvent;

	// Token: 0x040003B8 RID: 952
	[Header("This event triggers when the player loses focus on this object.")]
	[SerializeField]
	private UnityEvent onLoseFocusEvent;

	// Token: 0x040003B9 RID: 953
	private bool NetworkInitializeEarly_BaseEventInteract_Assembly-CSharp.dll;

	// Token: 0x040003BA RID: 954
	private bool NetworkInitializeLate_BaseEventInteract_Assembly-CSharp.dll;
}

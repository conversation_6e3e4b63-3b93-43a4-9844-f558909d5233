﻿using System;
using FishNet;
using FishNet.Connection;
using HeathenEngineering.SteamworksIntegration;
using Steamworks;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

// Token: 0x020000E9 RID: 233
public class PlayerListItem : MonoBehaviour
{
	// Token: 0x06000C62 RID: 3170 RVA: 0x00055520 File Offset: 0x00053720
	public void ChangeReadyStatus()
	{
		if (this.Ready)
		{
			this.PlayerReadyText.text = "ready";
			this.PlayerReadyText.color = this.readyColor;
			return;
		}
		this.PlayerReadyText.text = "not ready";
		this.PlayerReadyText.color = this.notreadyColor;
	}

	// Token: 0x06000C63 RID: 3171 RVA: 0x00055578 File Offset: 0x00053778
	private void Start()
	{
		this.pauseManager = PauseManager.Instance;
		this.lobbyController = LobbyController.Instance;
		this.thisUser = UserData.Get(this.PlayerSteamID);
		this.localSteamId = (ulong)SteamUser.GetSteamID();
		this.lobbyID = SteamLobby.Instance.CurrentLobbyID;
		if (!InstanceFinder.NetworkManager.IsServer)
		{
			this.kickbutton.SetActive(false);
		}
		if (InstanceFinder.NetworkManager.IsServer && this.localSteamId == this.PlayerSteamID)
		{
			this.kickbutton.SetActive(false);
		}
		if (InstanceFinder.NetworkManager.IsServer && this.localSteamId != this.PlayerSteamID && SceneMotor.Instance != null)
		{
			SceneMotor.Instance.CmdChangeRoundAmount();
		}
		if (this.localSteamId != this.PlayerSteamID)
		{
			this.teamIdDropdown.interactable = false;
		}
		if (this.localSteamId == this.PlayerSteamID)
		{
			this.mutebutton.SetActive(false);
		}
		this.ImageLoaded = Callback<AvatarImageLoaded_t>.Create(new Callback<AvatarImageLoaded_t>.DispatchDelegate(this.OnImageLoaded));
		EFriendRelationship friendRelationship = SteamFriends.GetFriendRelationship(this.thisUser);
		Debug.Log(string.Format("PlayerListItem: Friend relationship: {0}", friendRelationship));
		if (friendRelationship == EFriendRelationship.k_EFriendRelationshipFriend || this.localSteamId == this.PlayerSteamID)
		{
			this.addButton.SetActive(false);
		}
		this.teamIdDropdown.value = ScoreManager.Instance.GetTeamId(this.PlayerIdNumber);
		this.teamIdDropdown.gameObject.name = string.Format("TeamIdDropdownPlayer{0}", this.PlayerIdNumber);
		this.gameManager = GameManager.Instance;
	}

	// Token: 0x06000C64 RID: 3172 RVA: 0x00055718 File Offset: 0x00053918
	private void Update()
	{
		this.cosmeticbutton.transform.localScale = (this.pauseManager.inMainMenu ? Vector3.one : Vector3.zero);
		bool flag = base.transform.parent.gameObject.name == "-- TAB SCREEN --";
		if (this.PlayerIdNumber > 0)
		{
			if (flag)
			{
				base.transform.position = this.lobbyController.tabclientPosition[this.PlayerIdNumber - 1].position;
			}
			else
			{
				base.transform.position = this.lobbyController.clientPosition[this.PlayerIdNumber - 1].position;
			}
		}
		if (this.gameManager)
		{
			this.teamIdDropdown.gameObject.SetActive(this.gameManager.SyncAccessor_playingTeams);
			if (InstanceFinder.NetworkManager.IsServer)
			{
				this.teamIdDropdown.interactable = !flag;
				return;
			}
			if (this.localSteamId == this.PlayerSteamID)
			{
				this.teamIdDropdown.interactable = !flag;
			}
		}
	}

	// Token: 0x06000C65 RID: 3173 RVA: 0x00055828 File Offset: 0x00053A28
	private void GetPlayerIcon()
	{
		int largeFriendAvatar = SteamFriends.GetLargeFriendAvatar((CSteamID)this.PlayerSteamID);
		if (largeFriendAvatar == -1)
		{
			return;
		}
		this.PlayerIcon.texture = this.GetSteamImageAsTexture(largeFriendAvatar);
	}

	// Token: 0x06000C66 RID: 3174 RVA: 0x0005585D File Offset: 0x00053A5D
	public void CAcacas()
	{
		MenuController.Instance.ActivateMenu(MenuController.Instance.hatsMenu);
	}

	// Token: 0x06000C67 RID: 3175 RVA: 0x00055873 File Offset: 0x00053A73
	public void SetPlayerValues()
	{
		this.PlayerNameText.text = this.PlayerName;
		this.ChangeReadyStatus();
		if (!this.AvatarReceived)
		{
			this.GetPlayerIcon();
		}
	}

	// Token: 0x06000C68 RID: 3176 RVA: 0x0005589A File Offset: 0x00053A9A
	private void OnImageLoaded(AvatarImageLoaded_t callback)
	{
		if (callback.m_steamID.m_SteamID == this.PlayerSteamID)
		{
			this.PlayerIcon.texture = this.GetSteamImageAsTexture(callback.m_iImage);
			return;
		}
	}

	// Token: 0x06000C69 RID: 3177 RVA: 0x000558C8 File Offset: 0x00053AC8
	public void KickPlayer()
	{
		if (!GameManager.Instance)
		{
			return;
		}
		NetworkConnection networkConnection = null;
		for (int i = 0; i < SteamLobby.Instance.players.Count; i++)
		{
			if (SteamLobby.Instance.players[i].transform.GetComponent<ClientInstance>().ConnectionID == this.ConnectionID)
			{
				networkConnection = SteamLobby.Instance.players[i].Owner;
				break;
			}
		}
		if (networkConnection != null)
		{
			GameManager.Instance.CmdKickPlayer(networkConnection);
			PauseManager.Instance.WriteOfflineLog("Performed Kick");
			return;
		}
		PauseManager.Instance.WriteOfflineLog("Kick Failed");
	}

	// Token: 0x06000C6A RID: 3178 RVA: 0x00055970 File Offset: 0x00053B70
	private Texture2D GetSteamImageAsTexture(int iImage)
	{
		Texture2D texture2D = null;
		uint num;
		uint num2;
		if (SteamUtils.GetImageSize(iImage, out num, out num2))
		{
			byte[] array = new byte[num * num2 * 4U];
			if (SteamUtils.GetImageRGBA(iImage, array, (int)(num * num2 * 4U)))
			{
				texture2D = new Texture2D((int)num, (int)num2, TextureFormat.RGBA32, false, true);
				texture2D.LoadRawTextureData(array);
				texture2D.Apply();
			}
		}
		this.AvatarReceived = true;
		return texture2D;
	}

	// Token: 0x06000C6B RID: 3179 RVA: 0x000559C4 File Offset: 0x00053BC4
	public void SwitchMute()
	{
		foreach (GameObject gameObject in GameObject.FindGameObjectsWithTag("ClientInstance"))
		{
			if (gameObject.GetComponent<ClientInstance>().PlayerSteamID == this.PlayerSteamID)
			{
				gameObject.GetComponent<AudioSource>().mute = !gameObject.GetComponent<AudioSource>().mute;
			}
		}
		this.muteImg.sprite = ((this.muteImg.sprite == this.mute) ? this.notmute : this.mute);
	}

	// Token: 0x06000C6C RID: 3180 RVA: 0x00055A4B File Offset: 0x00053C4B
	private void OnEnable()
	{
		if (this.lobbyID == 0UL)
		{
			this.lobbyID = SteamLobby.Instance.CurrentLobbyID;
		}
		if (this.lobbyID != SteamLobby.Instance.CurrentLobbyID)
		{
			global::UnityEngine.Object.Destroy(base.gameObject);
		}
	}

	// Token: 0x06000C6D RID: 3181 RVA: 0x00055A84 File Offset: 0x00053C84
	public void OnDropdownTeamIdEnabled()
	{
		if (ScoreManager.Instance != null && this.teamIdDropdown.value != ScoreManager.Instance.GetTeamId(this.PlayerIdNumber))
		{
			this.teamIdDropdown.value = ScoreManager.Instance.GetTeamId(this.PlayerIdNumber);
		}
	}

	// Token: 0x06000C6E RID: 3182 RVA: 0x00055AD6 File Offset: 0x00053CD6
	public void AddFriend()
	{
		SteamLobby.Instance.AddSteamFriend(this.thisUser);
	}

	// Token: 0x06000C6F RID: 3183 RVA: 0x00055AE8 File Offset: 0x00053CE8
	public void SetSelfTeamId(TMP_Dropdown dropdown)
	{
		if (ScoreManager.Instance == null)
		{
			return;
		}
		if (base.transform.parent.gameObject.name == "-- TAB SCREEN --")
		{
			return;
		}
		if (this.teamIdDropdown.value == ScoreManager.Instance.GetTeamId(this.PlayerIdNumber))
		{
			return;
		}
		if (LobbyController.Instance.LocalPlayerController.PlayerId != this.PlayerIdNumber && !InstanceFinder.NetworkManager.IsServer)
		{
			return;
		}
		ScoreManager.Instance.SetTeamIdServer(this.PlayerIdNumber, dropdown.value);
	}

	// Token: 0x04000A97 RID: 2711
	public string PlayerName;

	// Token: 0x04000A98 RID: 2712
	public int ConnectionID;

	// Token: 0x04000A99 RID: 2713
	public int PlayerIdNumber;

	// Token: 0x04000A9A RID: 2714
	public ulong PlayerSteamID;

	// Token: 0x04000A9B RID: 2715
	public bool AvatarReceived;

	// Token: 0x04000A9C RID: 2716
	private ulong lobbyID;

	// Token: 0x04000A9D RID: 2717
	public TextMeshProUGUI PlayerNameText;

	// Token: 0x04000A9E RID: 2718
	public RawImage PlayerIcon;

	// Token: 0x04000A9F RID: 2719
	public TextMeshProUGUI PlayerReadyText;

	// Token: 0x04000AA0 RID: 2720
	public bool Ready;

	// Token: 0x04000AA1 RID: 2721
	protected Callback<AvatarImageLoaded_t> ImageLoaded;

	// Token: 0x04000AA2 RID: 2722
	[SerializeField]
	private GameObject kickbutton;

	// Token: 0x04000AA3 RID: 2723
	[SerializeField]
	private GameObject cosmeticbutton;

	// Token: 0x04000AA4 RID: 2724
	[SerializeField]
	private GameObject mutebutton;

	// Token: 0x04000AA5 RID: 2725
	[SerializeField]
	private GameObject addButton;

	// Token: 0x04000AA6 RID: 2726
	[SerializeField]
	private TMP_Dropdown teamIdDropdown;

	// Token: 0x04000AA7 RID: 2727
	[Space]
	[SerializeField]
	private Color readyColor;

	// Token: 0x04000AA8 RID: 2728
	[SerializeField]
	private Color notreadyColor;

	// Token: 0x04000AA9 RID: 2729
	private PauseManager pauseManager;

	// Token: 0x04000AAA RID: 2730
	private LobbyController lobbyController;

	// Token: 0x04000AAB RID: 2731
	private GameManager gameManager;

	// Token: 0x04000AAC RID: 2732
	private UserData thisUser;

	// Token: 0x04000AAD RID: 2733
	private ulong localSteamId;

	// Token: 0x04000AAE RID: 2734
	[Space]
	[SerializeField]
	private Image muteImg;

	// Token: 0x04000AAF RID: 2735
	[SerializeField]
	private Sprite mute;

	// Token: 0x04000AB0 RID: 2736
	[SerializeField]
	private Sprite notmute;
}

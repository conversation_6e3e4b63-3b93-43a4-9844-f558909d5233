﻿using System;
using DG.Tweening;
using TMPro;
using UnityEngine;

// Token: 0x0200005C RID: 92
public class KillCam : MonoBehaviour
{
	// Token: 0x06000410 RID: 1040 RVA: 0x0001CA88 File Offset: 0x0001AC88
	private void Start()
	{
		this.switchCamText = GameObject.Find("SwitchCamText").GetComponent<TextMeshProUGUI>();
		this.playerNameText = GameObject.Find("PlayerNameText").GetComponent<TextMeshProUGUI>();
		this.playerHpText = GameObject.Find("PlayerHpText").GetComponent<TextMeshProUGUI>();
	}

	// Token: 0x06000411 RID: 1041 RVA: 0x0001CAD4 File Offset: 0x0001ACD4
	private void Update()
	{
		if (PauseManager.Instance.nonSteamworksTransport)
		{
			return;
		}
		Input.GetAxis("Mouse X");
		Input.GetAxis("Mouse Y");
		this.timer -= Time.deltaTime;
		if (this.enemy)
		{
			this.deltaPosition = this.enemy.position - this.tempPosition;
		}
		if (!this.midMatchJoin && GameObject.FindGameObjectsWithTag("PlayerGraphics").Length != 0)
		{
			if (LobbyController.Instance.LocalPlayerController.PlayerSpawner.player == null)
			{
				this.ragdoll = GameObject.FindGameObjectsWithTag("PlayerGraphics")[0].transform;
				this.midMatchJoin = true;
			}
			else
			{
				this.midMatchJoin = true;
			}
		}
		if (this.enemy == null)
		{
			this.switchCamText.text = "";
			this.playerHpText.text = "";
			this.playerNameText.text = "";
			this.triggerLookAtBody = true;
		}
		else if (!this.enemy.gameObject.activeSelf || (this.enemy.gameObject.name != "Graphics" && this.enemy.Find("Graphics") == null))
		{
			this.players = GameObject.FindGameObjectsWithTag("PlayerGraphics");
			if (this.players.Length != 0)
			{
				this.enemy = this.players[0].transform;
			}
			else
			{
				this.enemy = null;
			}
		}
		if (this.ragdoll)
		{
			if (this.triggerLookAtBody)
			{
				this.players = GameObject.FindGameObjectsWithTag("PlayerGraphics");
				if (this.players.Length != 0)
				{
					this.enemy = this.players[0].transform;
				}
				this.timer = 3f;
				Transform transform = this.ragdoll;
				this.ragdoll = this.FindRecursive("Hips", transform);
				this.triggerLookAtBody = false;
			}
			if (this.timer < 0f)
			{
				if (this.isDead)
				{
					this.switchCamText.text = "Press your mouse buttons to switch player";
					if (this.enemy.GetComponent<PlayerHealth>() != null)
					{
						this.playerHpText.text = string.Format("Health : {0}", Mathf.Ceil(this.enemy.GetComponent<PlayerHealth>().SyncAccessor_health / 4f * 100f));
					}
					else if (this.enemy.GetComponentInParent<PlayerHealth>() != null)
					{
						this.playerHpText.text = string.Format("Health : {0}", Mathf.Ceil(this.enemy.GetComponentInParent<PlayerHealth>().SyncAccessor_health / 4f * 100f));
					}
					if (this.enemy.GetComponent<PlayerValues>() != null)
					{
						this.playerNameText.text = "Player : " + this.enemy.GetComponent<PlayerValues>().SyncAccessor_playerClient.PlayerName;
					}
					else if (this.enemy.GetComponentInParent<PlayerValues>() != null)
					{
						this.playerNameText.text = "Player : " + this.enemy.GetComponentInParent<PlayerValues>().SyncAccessor_playerClient.PlayerName;
					}
				}
				this.players = GameObject.FindGameObjectsWithTag("PlayerGraphics");
				this.alivePlayerCount = this.players.Length;
				if (Input.GetMouseButtonDown(0))
				{
					if (this.spectatePlayerId == this.alivePlayerCount - 1)
					{
						this.spectatePlayerId = 0;
					}
					else
					{
						this.spectatePlayerId++;
					}
					this.spectatePlayerId = Mathf.Clamp(this.spectatePlayerId, 0, this.alivePlayerCount - 1);
					this.enemy = this.players[this.spectatePlayerId].transform;
				}
				if (Input.GetMouseButtonDown(1))
				{
					if (this.spectatePlayerId == 0)
					{
						this.spectatePlayerId = this.alivePlayerCount - 1;
					}
					else
					{
						this.spectatePlayerId--;
					}
					this.spectatePlayerId = Mathf.Clamp(this.spectatePlayerId, 0, this.alivePlayerCount - 1);
					this.enemy = this.players[this.spectatePlayerId].transform;
				}
				base.transform.DOMove(this.enemy.position + new Vector3(0f, 2.3f, 0f) - this.enemy.forward * (float)((this.alivePlayerCount > 1) ? 1 : (-1)) * 2.3f, (float)((this.timer > -1f) ? 1 : 0), false);
				base.transform.DOLookAt(this.enemy.position + new Vector3(0f, 1.7f, 0f), (this.timer > -1f) ? 0.4f : 0f, AxisConstraint.None, null);
			}
			else
			{
				base.transform.DOLookAt(this.ragdoll.position, 0f, AxisConstraint.None, null);
				base.transform.position = this.firstPosition;
				base.transform.SetParent(null);
			}
		}
		if (Input.GetMouseButtonDown(0) && this.timer > 0f && this.timer < 2.7f)
		{
			this.timer = 0f;
		}
		if (this.enemy)
		{
			this.tempPosition = this.enemy.position;
		}
	}

	// Token: 0x06000412 RID: 1042 RVA: 0x0001D040 File Offset: 0x0001B240
	private Transform FindRecursive(string name, Transform root)
	{
		Transform[] componentsInChildren = root.GetComponentsInChildren<Transform>(true);
		Transform transform = null;
		foreach (Transform transform2 in componentsInChildren)
		{
			if (transform2.gameObject.name == name)
			{
				transform = transform2;
				break;
			}
		}
		return transform;
	}

	// Token: 0x06000413 RID: 1043 RVA: 0x0001D081 File Offset: 0x0001B281
	private void OnDisable()
	{
		this.switchCamText.text = "";
		this.playerHpText.text = "";
		this.playerNameText.text = "";
	}

	// Token: 0x04000456 RID: 1110
	public Transform enemy;

	// Token: 0x04000457 RID: 1111
	public Transform ragdoll;

	// Token: 0x04000458 RID: 1112
	public Vector3 firstPosition;

	// Token: 0x04000459 RID: 1113
	private bool killcam;

	// Token: 0x0400045A RID: 1114
	public bool triggerLookAtBody;

	// Token: 0x0400045B RID: 1115
	public bool ragdollCam;

	// Token: 0x0400045C RID: 1116
	public bool isDead;

	// Token: 0x0400045D RID: 1117
	private float timer;

	// Token: 0x0400045E RID: 1118
	private Vector3 deltaPosition;

	// Token: 0x0400045F RID: 1119
	private Vector3 tempPosition;

	// Token: 0x04000460 RID: 1120
	private GameObject[] players;

	// Token: 0x04000461 RID: 1121
	private int alivePlayerCount;

	// Token: 0x04000462 RID: 1122
	private int spectatePlayerId;

	// Token: 0x04000463 RID: 1123
	private TextMeshProUGUI switchCamText;

	// Token: 0x04000464 RID: 1124
	private TextMeshProUGUI playerNameText;

	// Token: 0x04000465 RID: 1125
	private TextMeshProUGUI playerHpText;

	// Token: 0x04000466 RID: 1126
	private bool midMatchJoin;
}

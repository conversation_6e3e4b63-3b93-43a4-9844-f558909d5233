﻿using System;
using UnityEngine;

// Token: 0x0200001D RID: 29
public class CameraShakeConstrains : MonoBehaviour
{
	// Token: 0x060000E4 RID: 228 RVA: 0x00006718 File Offset: 0x00004918
	private void Update()
	{
		if (!this.rotateBack)
		{
			return;
		}
		base.transform.localRotation = Quaternion.Slerp(base.transform.localRotation, Quaternion.Euler(base.transform.localRotation.x, base.transform.localRotation.y, 0f), this.baseSpeed * Time.deltaTime);
	}

	// Token: 0x040000D0 RID: 208
	[SerializeField]
	private float constrain = 0.1f;

	// Token: 0x040000D1 RID: 209
	public float baseSpeed = 3f;

	// Token: 0x040000D2 RID: 210
	public bool rotateBack;

	// Token: 0x040000D3 RID: 211
	[SerializeField]
	private float highSpeed = 25f;
}

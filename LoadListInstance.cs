﻿using System;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

// Token: 0x020000CF RID: 207
public class LoadListInstance : MonoBehaviour
{
	// Token: 0x06000B81 RID: 2945 RVA: 0x00050D64 File Offset: 0x0004EF64
	private void Start()
	{
		this.text.text = this.name;
		for (int i = this.maps.Count - 1; i >= 0; i--)
		{
			string text = this.maps[i];
			Map map;
			if (!MapsManager.Instance.allMapsDict.TryGetValue(text, out map) || (!SteamLobby.ownDlc0 && map.isDlcExclusive))
			{
				this.maps.RemoveAt(i);
			}
		}
		this.maps.Sort();
	}

	// Token: 0x06000B82 RID: 2946 RVA: 0x00050DE1 File Offset: 0x0004EFE1
	public void LoadPlaylist()
	{
		MapSelection.Instance.LoadScenes(this.maps.ToArray());
		PauseManager.Instance.WriteOfflineLog("You loaded " + this.name);
	}

	// Token: 0x040009A1 RID: 2465
	public List<string> maps = new List<string>();

	// Token: 0x040009A2 RID: 2466
	public new string name;

	// Token: 0x040009A3 RID: 2467
	public int index;

	// Token: 0x040009A4 RID: 2468
	[SerializeField]
	private TextMeshProUGUI text;

	// Token: 0x040009A5 RID: 2469
	[SerializeField]
	private Color selectedColor;

	// Token: 0x040009A6 RID: 2470
	[SerializeField]
	private Color deselectedColor;

	// Token: 0x040009A7 RID: 2471
	[SerializeField]
	private Button button;
}

﻿using System;
using FishNet;
using FishNet.Object;
using UnityEngine;

// Token: 0x020000EB RID: 235
public class PlayerTrackerEnable : MonoBehaviour
{
	// Token: 0x06000C73 RID: 3187 RVA: 0x00055BBF File Offset: 0x00053DBF
	private void Update()
	{
		if (InstanceFinder.ServerManager.Started)
		{
			this.playerTracker.enabled = true;
			base.enabled = false;
			base.GetComponent<NetworkObject>().enabled = true;
			return;
		}
		this.playerTracker.enabled = false;
	}

	// Token: 0x04000AB2 RID: 2738
	[SerializeField]
	private Behaviour playerTracker;
}

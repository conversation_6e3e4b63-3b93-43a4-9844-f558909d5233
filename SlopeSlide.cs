﻿using System;
using FishNet;
using FishNet.Connection;
using FishNet.Managing;
using FishNet.Object;
using FishNet.Object.Delegating;
using FishNet.Serializing;
using FishNet.Transporting;
using LambdaTheDev.NetworkAudioSync;
using UnityEngine;

// Token: 0x02000038 RID: 56
public class SlopeSlide : NetworkBehaviour
{
	// Token: 0x060002E9 RID: 745 RVA: 0x00018687 File Offset: 0x00016887
	public virtual void Awake()
	{
		this.NetworkInitialize___Early();
		this.Awake___UserLogic();
		this.NetworkInitialize__Late();
	}

	// Token: 0x060002EA RID: 746 RVA: 0x0001869C File Offset: 0x0001689C
	private void Update()
	{
		if (this.controller.isSprinting && (this.controller.isCrouching || this.controller.isSliding))
		{
			this.sprintSlideTrigger = true;
			this.sprintLateTrigger = true;
		}
		this.downRay = Physics.Raycast(base.transform.position, -Vector3.up, out this.slopeHit, 8f, this.controller.landLayer);
		this.isCrouchSlopeSliding = this.controller.safeGrounded && this.downRay && this.controller.isCrouching && Vector3.Angle(this.slopeHit.normal, Vector3.up) > this.minSlopeAngle && (this.onIce || (!this.controller.isSprinting && this.controller.isSliding) || this.sprintSlideTrigger || this.slopeSlideTrigger2);
		this.speedFactor = Mathf.Clamp(Vector3.Angle(this.slopeHit.normal, Vector3.up), this.firstClamp, this.secondClamp) / (this.secondClamp - this.firstClamp);
		this.walkSpeedFactor = Mathf.Clamp(Vector3.Angle(this.slopeHit.normal, Vector3.up), this.walkFirstClamp, this.walkSecondClamp) / (this.secondClamp - this.firstClamp);
		if (this.isCrouchSlopeSliding)
		{
			this.slopeSlideMove = Vector3.Lerp(this.slopeSlideMove, new Vector3(this.slopeHit.normal.x, -this.slopeHit.normal.y, this.slopeHit.normal.z) * this.speedFactor * this.currentSpeed, this.currentAcceleration * Time.deltaTime);
			this.localSlopeSlideStopTrigger = true;
		}
		else
		{
			this.slopeSlideMove = Vector3.Lerp(new Vector3(this.slopeSlideMove.x, 0f, this.slopeSlideMove.z), Vector3.zero, this.currentDeceleration * Time.deltaTime);
			this.sprintSlideTrigger = false;
			this.slopeSlideTrigger2 = false;
			this.localSlopeSlideTrigger = true;
			this.slopeSlideTrigger = true;
		}
		if (this.onIce && this.controller.safeGrounded)
		{
			this.steepSlopeSlideMove = Vector3.Lerp(this.steepSlopeSlideMove, new Vector3(this.slopeHit.normal.x, 0f, this.slopeHit.normal.z) * Mathf.Lerp(this.minWalkIceSlideSpeed, this.maxWalkIceSlideSpeed, this.walkSpeedFactor), this.currentAcceleration * Time.deltaTime);
		}
		else
		{
			this.steepSlopeSlideMove = Vector3.Lerp(new Vector3(this.steepSlopeSlideMove.x, 0f, this.steepSlopeSlideMove.z), Vector3.zero, this.steepSlopeSlideDeceleration * Time.deltaTime);
		}
		if (!this.controller.isCrouching && !this.onIce)
		{
			this.slopeSlideMove = Vector3.zero;
		}
		if (this.localSlopeSlideTrigger && this.sprintSlideTrigger && this.isCrouchSlopeSliding && this.sprintLateTrigger)
		{
			this.sprintLateTrigger = false;
			this.sprintInTrigger = true;
			this.localSlopeSlideTrigger = false;
			this.slideCancelTimer = 2.1f;
			this.audio.volume = 1f;
			this.initSoundVolume = this.audio.volume;
			this.SlideAudioPlay();
			this.stopped = true;
		}
		else if (this.localSlopeSlideTrigger && this.isCrouchSlopeSliding && this.sprintLateTrigger)
		{
			this.slopeSlideTrigger2 = true;
			this.sprintLateTrigger = false;
			this.sprintInTrigger = true;
			this.localSlopeSlideTrigger = false;
			this.slideCancelTimer = 2.1f;
			this.audio.volume = 1f;
			this.initSoundVolume = this.audio.volume;
			this.stopped = true;
		}
		if (this.controller.isCrouching)
		{
			this.crouchOutTrigger = true;
		}
		if (this.crouchOutTrigger && !this.controller.isCrouching && this.audio.volume > 0.1f && this.sprintInTrigger)
		{
			this.initSoundVolume = this.audio.volume;
			this.sprintInTrigger = false;
			this.crouchOutTrigger = false;
			this.slideCancelTimer = 0f;
		}
		this.SlideFadeOut((!this.controller.isCrouching) ? 0.2f : (this.onIce ? this.iceFadeOutTime : (this.onConcrete ? this.concreteFadeOutTime : this.sandFadeOutTime)));
		if (this.localSlopeSlideStopTrigger && !this.isCrouchSlopeSliding)
		{
			this.initSoundVolume = this.audio.volume;
			this.localSlopeSlideStopTrigger = false;
			this.slideCancelTimer = 0f;
		}
		if (this.downRay)
		{
			string tag = this.slopeHit.transform.tag;
			if (tag == "Footsteps/Ice")
			{
				this.onIce = true;
				this.onSuperIce = false;
				this.onSand = false;
				this.onConcrete = false;
				this.currentSpeed = this.iceSpeed;
				this.currentAcceleration = this.iceAcceleration;
				this.currentDeceleration = this.iceDeceleration;
				return;
			}
			if (tag == "Footsteps/SuperIce")
			{
				this.onIce = true;
				this.onSuperIce = true;
				this.onSand = false;
				this.onConcrete = false;
				this.currentSpeed = this.iceSpeed;
				this.currentAcceleration = this.iceAcceleration;
				this.currentDeceleration = this.iceDeceleration;
				return;
			}
			if (tag == "Footsteps/Sand")
			{
				this.onIce = false;
				this.onSuperIce = false;
				this.onSand = true;
				this.onConcrete = false;
				this.currentSpeed = this.sandSpeed;
				this.currentAcceleration = this.sandAcceleration;
				this.currentDeceleration = this.sandDeceleration;
				return;
			}
			this.onIce = false;
			this.onSuperIce = false;
			this.onSand = false;
			this.onConcrete = true;
			this.currentSpeed = this.concreteSpeed;
			this.currentAcceleration = this.concreteAcceleration;
			this.currentDeceleration = this.concreteDeceleration;
		}
	}

	// Token: 0x060002EB RID: 747 RVA: 0x00018CBC File Offset: 0x00016EBC
	private void SlideFadeOut(float _duration)
	{
		this.slideCancelTimer += Time.deltaTime;
		if (this.slideCancelTimer <= _duration)
		{
			this.slideFadeInTimer = 0f;
			this.audio.volume = Mathf.Lerp(this.initSoundVolume, 0f, this.slideCancelTimer / _duration);
		}
		if ((this.audio.volume < 0.05f || (this.slideCancelTimer > 1f && !this.controller.isCrouching)) && this.stopped)
		{
			this.stopped = false;
			this.SlideAudioStop();
		}
	}

	// Token: 0x060002EC RID: 748 RVA: 0x00018D53 File Offset: 0x00016F53
	[ServerRpc]
	private void SlideAudioPlay()
	{
		this.RpcWriter___Server_SlideAudioPlay_2166136261();
	}

	// Token: 0x060002ED RID: 749 RVA: 0x00018D5B File Offset: 0x00016F5B
	[ServerRpc]
	private void SlideAudioStop()
	{
		this.RpcWriter___Server_SlideAudioStop_2166136261();
	}

	// Token: 0x060002EE RID: 750 RVA: 0x00018D63 File Offset: 0x00016F63
	private void OnDisable()
	{
		this.SlideAudioStop();
	}

	// Token: 0x060002F0 RID: 752 RVA: 0x00018E88 File Offset: 0x00017088
	public virtual void NetworkInitialize___Early()
	{
		if (this.NetworkInitializeEarly_SlopeSlide_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeEarly_SlopeSlide_Assembly-CSharp.dll = true;
		base.RegisterServerRpc(0U, new ServerRpcDelegate(this.RpcReader___Server_SlideAudioPlay_2166136261));
		base.RegisterServerRpc(1U, new ServerRpcDelegate(this.RpcReader___Server_SlideAudioStop_2166136261));
	}

	// Token: 0x060002F1 RID: 753 RVA: 0x00018ED4 File Offset: 0x000170D4
	public virtual void NetworkInitialize__Late()
	{
		if (this.NetworkInitializeLate_SlopeSlide_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeLate_SlopeSlide_Assembly-CSharp.dll = true;
	}

	// Token: 0x060002F2 RID: 754 RVA: 0x00018EE7 File Offset: 0x000170E7
	public override void NetworkInitializeIfDisabled()
	{
		this.NetworkInitialize___Early();
		this.NetworkInitialize__Late();
	}

	// Token: 0x060002F3 RID: 755 RVA: 0x00018EF8 File Offset: 0x000170F8
	private void RpcWriter___Server_SlideAudioPlay_2166136261()
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendServerRpc(0U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x060002F4 RID: 756 RVA: 0x00018FEC File Offset: 0x000171EC
	private void RpcLogic___SlideAudioPlay_2166136261()
	{
		this.networkAudio.Play(0);
	}

	// Token: 0x060002F5 RID: 757 RVA: 0x00018FFC File Offset: 0x000171FC
	private void RpcReader___Server_SlideAudioPlay_2166136261(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		this.RpcLogic___SlideAudioPlay_2166136261();
	}

	// Token: 0x060002F6 RID: 758 RVA: 0x00019030 File Offset: 0x00017230
	private void RpcWriter___Server_SlideAudioStop_2166136261()
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendServerRpc(1U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x060002F7 RID: 759 RVA: 0x00019124 File Offset: 0x00017324
	private void RpcLogic___SlideAudioStop_2166136261()
	{
		this.networkAudio.Stop();
	}

	// Token: 0x060002F8 RID: 760 RVA: 0x00019134 File Offset: 0x00017334
	private void RpcReader___Server_SlideAudioStop_2166136261(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		this.RpcLogic___SlideAudioStop_2166136261();
	}

	// Token: 0x060002F9 RID: 761 RVA: 0x00019165 File Offset: 0x00017365
	public virtual void Awake___UserLogic()
	{
		this.controller = base.GetComponent<FirstPersonController>();
		this.characterController = base.GetComponent<CharacterController>();
	}

	// Token: 0x04000343 RID: 835
	public Vector3 slopeSlideMove;

	// Token: 0x04000344 RID: 836
	public Vector3 steepSlopeSlideMove;

	// Token: 0x04000345 RID: 837
	public bool isCrouchSlopeSliding;

	// Token: 0x04000346 RID: 838
	public bool isSteepSlopeSliding;

	// Token: 0x04000347 RID: 839
	public bool sliding;

	// Token: 0x04000348 RID: 840
	public bool slopeSlideTrigger;

	// Token: 0x04000349 RID: 841
	public bool slopeSlideTrigger2;

	// Token: 0x0400034A RID: 842
	public bool onIce;

	// Token: 0x0400034B RID: 843
	public bool onSuperIce;

	// Token: 0x0400034C RID: 844
	public bool onSand;

	// Token: 0x0400034D RID: 845
	public bool onConcrete;

	// Token: 0x0400034E RID: 846
	public bool sprintSlideTrigger;

	// Token: 0x0400034F RID: 847
	private FirstPersonController controller;

	// Token: 0x04000350 RID: 848
	private CharacterController characterController;

	// Token: 0x04000351 RID: 849
	private Slope slopeScript;

	// Token: 0x04000352 RID: 850
	[SerializeField]
	private NetworkAudioSource networkAudio;

	// Token: 0x04000353 RID: 851
	[SerializeField]
	private AudioSource audio;

	// Token: 0x04000354 RID: 852
	[SerializeField]
	private AudioClip slideOutClip;

	// Token: 0x04000355 RID: 853
	private bool downRay;

	// Token: 0x04000356 RID: 854
	private RaycastHit slopeHit;

	// Token: 0x04000357 RID: 855
	private RaycastHit slopeHit1;

	// Token: 0x04000358 RID: 856
	private RaycastHit slopeHit2;

	// Token: 0x04000359 RID: 857
	private RaycastHit slopeHit3;

	// Token: 0x0400035A RID: 858
	private RaycastHit slopeHit4;

	// Token: 0x0400035B RID: 859
	private bool localSlopeSlideTrigger;

	// Token: 0x0400035C RID: 860
	private bool localSlopeSlideStopTrigger;

	// Token: 0x0400035D RID: 861
	private float slideTimer;

	// Token: 0x0400035E RID: 862
	private float slideCancelTimer;

	// Token: 0x0400035F RID: 863
	private float slideFadeInTimer;

	// Token: 0x04000360 RID: 864
	private float initSoundVolume;

	// Token: 0x04000361 RID: 865
	private bool crouchOutTrigger;

	// Token: 0x04000362 RID: 866
	private bool sprintInTrigger;

	// Token: 0x04000363 RID: 867
	private bool sprintLateTrigger;

	// Token: 0x04000364 RID: 868
	public float speedFactor = 0.1f;

	// Token: 0x04000365 RID: 869
	public float walkSpeedFactor = 0.1f;

	// Token: 0x04000366 RID: 870
	public float currentSpeed = 1f;

	// Token: 0x04000367 RID: 871
	public float currentAcceleration = 1f;

	// Token: 0x04000368 RID: 872
	public float currentDeceleration = 1f;

	// Token: 0x04000369 RID: 873
	[SerializeField]
	private float concreteSpeed = 1f;

	// Token: 0x0400036A RID: 874
	[SerializeField]
	private float iceSpeed = 2f;

	// Token: 0x0400036B RID: 875
	[SerializeField]
	private float minWalkIceSlideSpeed = 2f;

	// Token: 0x0400036C RID: 876
	[SerializeField]
	private float maxWalkIceSlideSpeed = 4f;

	// Token: 0x0400036D RID: 877
	[SerializeField]
	private float sandSpeed = 1f;

	// Token: 0x0400036E RID: 878
	[SerializeField]
	private float concreteFadeOutTime = 2f;

	// Token: 0x0400036F RID: 879
	[SerializeField]
	private float iceFadeOutTime = 5f;

	// Token: 0x04000370 RID: 880
	[SerializeField]
	private float sandFadeOutTime = 1.5f;

	// Token: 0x04000371 RID: 881
	[SerializeField]
	private float concreteAcceleration = 1f;

	// Token: 0x04000372 RID: 882
	[SerializeField]
	private float iceAcceleration = 1f;

	// Token: 0x04000373 RID: 883
	[SerializeField]
	private float sandAcceleration = 1f;

	// Token: 0x04000374 RID: 884
	[SerializeField]
	private float concreteDeceleration;

	// Token: 0x04000375 RID: 885
	[SerializeField]
	private float iceDeceleration;

	// Token: 0x04000376 RID: 886
	[SerializeField]
	private float sandDeceleration;

	// Token: 0x04000377 RID: 887
	[SerializeField]
	private float firstClamp;

	// Token: 0x04000378 RID: 888
	[SerializeField]
	private float secondClamp = 50f;

	// Token: 0x04000379 RID: 889
	[SerializeField]
	private float minSlopeAngle = 13f;

	// Token: 0x0400037A RID: 890
	[SerializeField]
	private float walkFirstClamp = 10f;

	// Token: 0x0400037B RID: 891
	[SerializeField]
	private float walkSecondClamp = 50f;

	// Token: 0x0400037C RID: 892
	[SerializeField]
	private float minimumSteepness = 65f;

	// Token: 0x0400037D RID: 893
	[SerializeField]
	private float steepSlopeSlideSpeed = 6f;

	// Token: 0x0400037E RID: 894
	[SerializeField]
	private float steepSlopeSlideAcceleration = 4f;

	// Token: 0x0400037F RID: 895
	[SerializeField]
	private float steepSlopeSlideDeceleration = 1f;

	// Token: 0x04000380 RID: 896
	private bool stopped;

	// Token: 0x04000381 RID: 897
	private bool NetworkInitializeEarly_SlopeSlide_Assembly-CSharp.dll;

	// Token: 0x04000382 RID: 898
	private bool NetworkInitializeLate_SlopeSlide_Assembly-CSharp.dll;
}

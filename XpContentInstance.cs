﻿using System;
using TMPro;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

// Token: 0x0200013E RID: 318
public class XpContentInstance : MonoBehaviour, IPointerEnterHandler, IEventSystemHandler, IPointerExitHandler
{
	// Token: 0x06000E96 RID: 3734 RVA: 0x000602B8 File Offset: 0x0005E4B8
	private void Start()
	{
		this.contentLayout = base.GetComponentInParent<XpContentLayout>();
		this.frame.color = (ProgressManager.Instance.instances[this.index].dlcExlusive ? this.DlcFrameColor : this.FrameColor);
		this.lockedText.color = (ProgressManager.Instance.instances[this.index].dlcExlusive ? this.DlcLockedTextColor : this.LockedTextColor);
		ProgressManager.Instance.xpContentInstances.Add(this);
		this.UpdateUI();
		if (ProgressManager.Instance.instances[this.index].cosmetic != null)
		{
			if (ProgressManager.Instance.instances[this.index].cosmetic.sprite)
			{
				this.img.texture = ProgressManager.Instance.instances[this.index].cosmetic.sprite.texture;
				return;
			}
		}
		else if (!Application.isEditor)
		{
			this.img.texture = (Texture2D)Resources.Load("MapSprites/" + ProgressManager.Instance.instances[this.index].maps[this.mapIndex], typeof(Texture2D));
		}
	}

	// Token: 0x06000E97 RID: 3735 RVA: 0x00060404 File Offset: 0x0005E604
	public void UpdateUI()
	{
		if (ProgressManager.Instance.instances[this.index].unlocked)
		{
			this.lockedText.text = (ProgressManager.Instance.instances[this.index].dlcExlusive ? "unlocked  DLC" : "unlocked");
			this.text.color = (ProgressManager.Instance.instances[this.index].dlcExlusive ? this.DlcunlockedColor : this.unlockedColor);
			if (ProgressManager.Instance.instances[this.index].cosmetic != null)
			{
				this.text.text = ProgressManager.Instance.instances[this.index].cosmetic.cosmeticName;
			}
			else
			{
				this.text.text = ProgressManager.Instance.instances[this.index].maps[this.mapIndex];
			}
			this.contentLayout.text.color = this.unlockedColor;
			return;
		}
		this.lockedText.text = (ProgressManager.Instance.instances[this.index].dlcExlusive ? "locked  DLC" : "locked");
		this.text.color = (ProgressManager.Instance.instances[this.index].dlcExlusive ? this.DlclockedColor : this.lockedColor);
		this.text.text = "???";
		this.contentLayout.text.color = this.lockedColor;
	}

	// Token: 0x06000E98 RID: 3736 RVA: 0x00060598 File Offset: 0x0005E798
	public void OnPointerEnter(PointerEventData eventData)
	{
		if (!Application.isFocused)
		{
			return;
		}
		PauseManager.Instance.PlayMenuClip(PauseManager.Instance.genericMenuClip);
		if (ProgressManager.Instance.instances[this.index].dlcExlusive)
		{
			FloatingName.Instance.nameToShow = "DLC exclusive content";
		}
	}

	// Token: 0x06000E99 RID: 3737 RVA: 0x000605E8 File Offset: 0x0005E7E8
	public void OnPointerExit(PointerEventData eventData)
	{
		if (!Application.isFocused)
		{
			return;
		}
		PauseManager.Instance.PlayMenuClip(PauseManager.Instance.genericMenuClip);
		if (ProgressManager.Instance.instances[this.index].dlcExlusive)
		{
			FloatingName.Instance.nameToShow = "";
		}
	}

	// Token: 0x04000D23 RID: 3363
	public int index;

	// Token: 0x04000D24 RID: 3364
	public int mapIndex;

	// Token: 0x04000D25 RID: 3365
	[SerializeField]
	private RawImage img;

	// Token: 0x04000D26 RID: 3366
	[SerializeField]
	private RawImage frame;

	// Token: 0x04000D27 RID: 3367
	[SerializeField]
	private Color unlockedColor;

	// Token: 0x04000D28 RID: 3368
	[SerializeField]
	private Color DlcunlockedColor;

	// Token: 0x04000D29 RID: 3369
	[SerializeField]
	private Color lockedColor;

	// Token: 0x04000D2A RID: 3370
	[SerializeField]
	private Color DlclockedColor;

	// Token: 0x04000D2B RID: 3371
	[SerializeField]
	private Color DlcFrameColor;

	// Token: 0x04000D2C RID: 3372
	[SerializeField]
	private Color DlcLockedTextColor;

	// Token: 0x04000D2D RID: 3373
	[SerializeField]
	private Color LockedTextColor;

	// Token: 0x04000D2E RID: 3374
	[SerializeField]
	private Color FrameColor;

	// Token: 0x04000D2F RID: 3375
	[SerializeField]
	private TextMeshProUGUI text;

	// Token: 0x04000D30 RID: 3376
	[SerializeField]
	private TextMeshProUGUI lockedText;

	// Token: 0x04000D31 RID: 3377
	private XpContentLayout contentLayout;
}

﻿using System;
using FishNet;
using FishNet.Connection;
using FishNet.Managing;
using FishNet.Object;
using FishNet.Object.Delegating;
using FishNet.Serializing;
using FishNet.Transporting;
using UnityEngine;

// Token: 0x020000B0 RID: 176
public class WeaponHandSpawner : Weapon
{
	// Token: 0x06000A1D RID: 2589 RVA: 0x0004A550 File Offset: 0x00048750
	private void Update()
	{
		base.WeaponUpdate();
		if (this.fireTimer > 0f)
		{
			this.fireTimer -= Time.deltaTime;
		}
		if (base.gameObject.layer == 7)
		{
			if (this.previewObject.gameObject.activeSelf)
			{
				this.previewObject.gameObject.SetActive(false);
			}
			return;
		}
		this.HandlePlacement();
		if (!this.onePressShoot)
		{
			if ((this.invertFire ? this.fire2 : this.fire1).ReadValue<float>() > 0.1f && this.inRightHand)
			{
				this.Fire();
			}
			if ((this.invertFire ? this.fire1 : this.fire2).ReadValue<float>() > 0.1f && this.inLeftHand)
			{
				this.Fire();
			}
		}
		else
		{
			if ((this.invertFire ? this.fire2 : this.fire1).ReadValue<float>() < 0.1f && this.inRightHand)
			{
				this.isClicked = true;
			}
			if ((this.invertFire ? this.fire1 : this.fire2).ReadValue<float>() < 0.1f && this.inLeftHand)
			{
				this.isClicked = true;
			}
		}
		if (this.isClicked && (this.onePressShoot || (!this.reloadWeapon && base.SyncAccessor_currentAmmo <= 0)))
		{
			if ((this.invertFire ? this.fire2 : this.fire1).ReadValue<float>() > 0.1f && this.inRightHand)
			{
				this.Fire();
				this.isClicked = false;
			}
			if ((this.invertFire ? this.fire1 : this.fire2).ReadValue<float>() > 0.1f && this.inLeftHand)
			{
				this.Fire();
				this.isClicked = false;
			}
		}
	}

	// Token: 0x06000A1E RID: 2590 RVA: 0x0004A718 File Offset: 0x00048918
	private void HandlePlacement()
	{
		if (!base.IsOwner)
		{
			return;
		}
		RaycastHit raycastHit;
		if (Physics.Raycast(this.cam.transform.position, this.cam.transform.forward, out raycastHit, this.maxInteractionDistance, this.landLayer))
		{
			this.interactionDistance = raycastHit.distance;
		}
		else
		{
			this.interactionDistance = this.maxInteractionDistance;
		}
		RaycastHit raycastHit2;
		if (Physics.Raycast(this.rootObject.transform.position + Vector3.up, this.rootObject.transform.forward, out raycastHit2, this.maxInteractionDistance, this.landLayer))
		{
			this.interactionDistanceFront = raycastHit2.distance;
		}
		else
		{
			this.interactionDistanceFront = this.maxInteractionDistance;
		}
		RaycastHit raycastHit3;
		this.place = Physics.Raycast(this.cam.transform.position, this.cam.transform.forward, out raycastHit3, this.interactionDistance + 0.1f, this.landLayer) && (this.apmine ? (Vector3.Angle(raycastHit.normal, Vector3.up) >= 0f) : (this.claymore ? (Vector3.Angle(raycastHit.normal, Vector3.up) > 70f) : (Vector3.Angle(raycastHit.normal, Vector3.up) < 65f)));
		RaycastHit raycastHit4;
		this.placeDown = Physics.Raycast(this.rootObject.transform.position + this.rootObject.transform.forward * this.interactionDistanceFront + Vector3.up, -Vector3.up, out raycastHit4, 2f, this.landLayer) && Vector3.Angle(raycastHit4.normal, Vector3.up) < 65f;
		if (this.place)
		{
			this.previewObject.gameObject.layer = 0;
			this.position = raycastHit3.point;
			this.previewObject.transform.up = raycastHit.normal;
			this.previewObject.position = this.position;
			this.rotation = (this.claymore ? Quaternion.LookRotation(raycastHit3.normal) : this.previewObject.rotation);
			if (!this.previewObject.gameObject.activeSelf)
			{
				this.previewObject.gameObject.SetActive(true);
			}
			this.canPlace = true;
			return;
		}
		if (this.canPlaceMaxDistance && this.placeDown && this.proximityMine)
		{
			this.previewObject.gameObject.layer = 0;
			this.position = raycastHit4.point;
			this.previewObject.transform.up = raycastHit4.normal;
			this.previewObject.position = this.position;
			this.rotation = this.previewObject.rotation;
			if (!this.previewObject.gameObject.activeSelf)
			{
				this.previewObject.gameObject.SetActive(true);
			}
			this.canPlace = true;
			return;
		}
		if (this.previewObject.gameObject.activeSelf)
		{
			this.previewObject.gameObject.SetActive(false);
			this.canPlace = false;
		}
	}

	// Token: 0x06000A1F RID: 2591 RVA: 0x0004AA78 File Offset: 0x00048C78
	private void Fire()
	{
		if (PauseManager.Instance.pause || this.behaviour.playerPickup.currentEnvironmentInteractable != null)
		{
			return;
		}
		if (!this.playerController.IsOwner || !this.playerController.SyncAccessor_canMove)
		{
			return;
		}
		if (this.fireTimer > 0f)
		{
			return;
		}
		this.fireTimer = this.timeBetweenFire;
		if (base.SyncAccessor_currentAmmo <= 0)
		{
			this.audio.PlayOneShot(this.nobulletClip);
		}
		if (base.SyncAccessor_currentAmmo <= 0)
		{
			return;
		}
		if (!this.canPlace)
		{
			return;
		}
		this.SpawnObject(this.objToSpawn, this.position, this.rotation);
		this.RemoveAmmo();
		base.CameraAnimation();
		base.WeaponAnimation();
	}

	// Token: 0x06000A20 RID: 2592 RVA: 0x0004AB36 File Offset: 0x00048D36
	[ServerRpc(RunLocally = true)]
	private void RemoveAmmo()
	{
		this.RpcWriter___Server_RemoveAmmo_2166136261();
		this.RpcLogic___RemoveAmmo_2166136261();
	}

	// Token: 0x06000A21 RID: 2593 RVA: 0x0004AB44 File Offset: 0x00048D44
	[ServerRpc(RunLocally = true)]
	public void SpawnObject(GameObject obj, Vector3 position, Quaternion rotation)
	{
		this.RpcWriter___Server_SpawnObject_2587446063(obj, position, rotation);
		this.RpcLogic___SpawnObject_2587446063(obj, position, rotation);
	}

	// Token: 0x06000A22 RID: 2594 RVA: 0x0004AB75 File Offset: 0x00048D75
	[ObserversRpc(RunLocally = true, ExcludeOwner = true)]
	private void ShootObserversEffect()
	{
		this.RpcWriter___Observers_ShootObserversEffect_2166136261();
		this.RpcLogic___ShootObserversEffect_2166136261();
	}

	// Token: 0x06000A23 RID: 2595 RVA: 0x00026D1D File Offset: 0x00024F1D
	private void LocalSound(int index)
	{
		if (index == 0)
		{
			this.audio.PlayOneShot(this.headHitClip);
		}
	}

	// Token: 0x06000A25 RID: 2597 RVA: 0x0004AB98 File Offset: 0x00048D98
	public override void NetworkInitialize___Early()
	{
		if (this.NetworkInitializeEarly_WeaponHandSpawner_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeEarly_WeaponHandSpawner_Assembly-CSharp.dll = true;
		base.NetworkInitialize___Early();
		base.RegisterServerRpc(15U, new ServerRpcDelegate(this.RpcReader___Server_RemoveAmmo_2166136261));
		base.RegisterServerRpc(16U, new ServerRpcDelegate(this.RpcReader___Server_SpawnObject_2587446063));
		base.RegisterObserversRpc(17U, new ClientRpcDelegate(this.RpcReader___Observers_ShootObserversEffect_2166136261));
	}

	// Token: 0x06000A26 RID: 2598 RVA: 0x0004AC01 File Offset: 0x00048E01
	public override void NetworkInitialize__Late()
	{
		if (this.NetworkInitializeLate_WeaponHandSpawner_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeLate_WeaponHandSpawner_Assembly-CSharp.dll = true;
		base.NetworkInitialize__Late();
	}

	// Token: 0x06000A27 RID: 2599 RVA: 0x0004AC1A File Offset: 0x00048E1A
	public override void NetworkInitializeIfDisabled()
	{
		this.NetworkInitialize___Early();
		this.NetworkInitialize__Late();
	}

	// Token: 0x06000A28 RID: 2600 RVA: 0x0004AC28 File Offset: 0x00048E28
	private void RpcWriter___Server_RemoveAmmo_2166136261()
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendServerRpc(15U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000A29 RID: 2601 RVA: 0x0004AD1C File Offset: 0x00048F1C
	private void RpcLogic___RemoveAmmo_2166136261()
	{
		base.sync___set_value_currentAmmo(base.SyncAccessor_currentAmmo - 1, true);
	}

	// Token: 0x06000A2A RID: 2602 RVA: 0x0004AD30 File Offset: 0x00048F30
	private void RpcReader___Server_RemoveAmmo_2166136261(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___RemoveAmmo_2166136261();
	}

	// Token: 0x06000A2B RID: 2603 RVA: 0x0004AD70 File Offset: 0x00048F70
	private void RpcWriter___Server_SpawnObject_2587446063(GameObject obj, Vector3 position, Quaternion rotation)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteGameObject(obj);
		writer.WriteVector3(position);
		writer.WriteQuaternion(rotation, AutoPackType.Packed);
		base.SendServerRpc(16U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000A2C RID: 2604 RVA: 0x0004AE90 File Offset: 0x00049090
	public void RpcLogic___SpawnObject_2587446063(GameObject obj, Vector3 position, Quaternion rotation)
	{
		GameObject gameObject = global::UnityEngine.Object.Instantiate<GameObject>(obj, position, rotation);
		base.ServerManager.Spawn(gameObject, base.Owner);
		if (this.proximityMine || this.apmine)
		{
			gameObject.GetComponent<ProximityMine>().sync___set_value__rootObject(this.rootObject, true);
			gameObject.GetComponent<ProximityMine>().sync___set_value_weapon(this, true);
			return;
		}
		if (this.claymore)
		{
			gameObject.GetComponent<Claymore>().sync___set_value__rootObject(this.rootObject, true);
			gameObject.GetComponent<Claymore>().sync___set_value_weapon(this, true);
		}
	}

	// Token: 0x06000A2D RID: 2605 RVA: 0x0004AF10 File Offset: 0x00049110
	private void RpcReader___Server_SpawnObject_2587446063(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		GameObject gameObject = PooledReader0.ReadGameObject();
		Vector3 vector = PooledReader0.ReadVector3();
		Quaternion quaternion = PooledReader0.ReadQuaternion(AutoPackType.Packed);
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___SpawnObject_2587446063(gameObject, vector, quaternion);
	}

	// Token: 0x06000A2E RID: 2606 RVA: 0x0004AF88 File Offset: 0x00049188
	private void RpcWriter___Observers_ShootObserversEffect_2166136261()
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendObserversRpc(17U, writer, channel, DataOrderType.Default, false, false, true);
		writer.Store();
	}

	// Token: 0x06000A2F RID: 2607 RVA: 0x0004B031 File Offset: 0x00049231
	private void RpcLogic___ShootObserversEffect_2166136261()
	{
		if (Settings.Instance.reduceVFX)
		{
			return;
		}
		this.audio.PlayOneShot(this.fireClip);
	}

	// Token: 0x06000A30 RID: 2608 RVA: 0x0004B054 File Offset: 0x00049254
	private void RpcReader___Observers_ShootObserversEffect_2166136261(PooledReader PooledReader0, Channel channel)
	{
		if (!base.IsClient)
		{
			return;
		}
		if (base.IsHost)
		{
			return;
		}
		this.RpcLogic___ShootObserversEffect_2166136261();
	}

	// Token: 0x06000A31 RID: 2609 RVA: 0x0004B07E File Offset: 0x0004927E
	public override void Awake()
	{
		this.NetworkInitialize___Early();
		base.Awake();
		this.NetworkInitialize__Late();
	}

	// Token: 0x06000A32 RID: 2610 RVA: 0x000023D6 File Offset: 0x000005D6
	public override void Awake___UserLogic()
	{
	}

	// Token: 0x040008F3 RID: 2291
	[Header("Weapon Specials")]
	[SerializeField]
	private GameObject objToSpawn;

	// Token: 0x040008F4 RID: 2292
	[SerializeField]
	private Transform previewObject;

	// Token: 0x040008F5 RID: 2293
	[SerializeField]
	private LayerMask landLayer;

	// Token: 0x040008F6 RID: 2294
	[SerializeField]
	private float maxInteractionDistance = 5f;

	// Token: 0x040008F7 RID: 2295
	[SerializeField]
	private bool canPlaceMaxDistance;

	// Token: 0x040008F8 RID: 2296
	private float interactionDistance;

	// Token: 0x040008F9 RID: 2297
	private float interactionDistanceFront;

	// Token: 0x040008FA RID: 2298
	[SerializeField]
	private bool proximityMine;

	// Token: 0x040008FB RID: 2299
	[SerializeField]
	private bool claymore;

	// Token: 0x040008FC RID: 2300
	[SerializeField]
	private bool apmine;

	// Token: 0x040008FD RID: 2301
	private float fireTimer;

	// Token: 0x040008FE RID: 2302
	private bool canPlace;

	// Token: 0x040008FF RID: 2303
	private Vector3 position;

	// Token: 0x04000900 RID: 2304
	private Quaternion calculatorRot;

	// Token: 0x04000901 RID: 2305
	private Quaternion rotation;

	// Token: 0x04000902 RID: 2306
	private bool place;

	// Token: 0x04000903 RID: 2307
	private bool placeDown;

	// Token: 0x04000904 RID: 2308
	private bool NetworkInitializeEarly_WeaponHandSpawner_Assembly-CSharp.dll;

	// Token: 0x04000905 RID: 2309
	private bool NetworkInitializeLate_WeaponHandSpawner_Assembly-CSharp.dll;
}

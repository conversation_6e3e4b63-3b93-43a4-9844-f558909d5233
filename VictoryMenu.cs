﻿using System;
using System.Collections.Generic;
using FishNet;
using FishNet.Connection;
using FishNet.Managing;
using FishNet.Object;
using FishNet.Object.Delegating;
using FishNet.Object.Synchronizing;
using FishNet.Serializing;
using FishNet.Serializing.Generated;
using FishNet.Transporting;
using TMPro;
using UnityEngine;
using UnityEngine.InputSystem;

// Token: 0x02000075 RID: 117
public class VictoryMenu : InteractEnvironment
{
	// Token: 0x060004FB RID: 1275 RVA: 0x000210C8 File Offset: 0x0001F2C8
	private void Start()
	{
		this.restPos = base.transform.localPosition;
		if (!InstanceFinder.NetworkManager.IsServer && !this.savePlaylist && !this.restartGame && !this.sharePlaylist)
		{
			this.HideButton();
			return;
		}
		if (this.restartGame)
		{
			this.playersThatCanRequestARestart = SteamLobby.Instance.players;
		}
		if (this.scrambleTeams)
		{
			bool flag = false;
			foreach (int num in GameManager.Instance.GetConnectedTeams())
			{
				List<int> list;
				if (ScoreManager.Instance.TeamIdToPlayerIds.TryGetValue(num, out list) && list.Count > 1)
				{
					flag = true;
					break;
				}
			}
			if (!flag)
			{
				this.HideButton();
				return;
			}
		}
	}

	// Token: 0x060004FC RID: 1276 RVA: 0x0002117C File Offset: 0x0001F37C
	private void HideButton()
	{
		base.transform.localScale = Vector3.zero;
		if (this.textObject != null)
		{
			this.textObject.SetActive(false);
		}
	}

	// Token: 0x060004FD RID: 1277 RVA: 0x000211A8 File Offset: 0x0001F3A8
	public override void OnFocus()
	{
		PauseManager.Instance.interactPopup.gameObject.SetActive(true);
		PauseManager.Instance.interactPopup.text = this.popupText.ToLower();
		this.focused = true;
	}

	// Token: 0x060004FE RID: 1278 RVA: 0x000211E0 File Offset: 0x0001F3E0
	public override void OnInteract(Transform player)
	{
		if (this.returnMenu)
		{
			SteamLobby.Instance.LeaveMatch();
		}
		if (this.restartGame)
		{
			this.VoteRematchServer(null);
		}
		if (this.quitGame)
		{
			Application.Quit();
		}
		if (this.savePlaylist)
		{
			this.SaveCurrentPoolToPlaylist();
		}
		if (this.sharePlaylist)
		{
			this.SharePlaylist();
		}
		if (this.scrambleTeams)
		{
			this.ScrambleTeams();
		}
	}

	// Token: 0x060004FF RID: 1279 RVA: 0x00021245 File Offset: 0x0001F445
	public override void OnLoseFocus()
	{
		this.focused = false;
	}

	// Token: 0x06000500 RID: 1280 RVA: 0x00021250 File Offset: 0x0001F450
	private void Update()
	{
		Vector3 vector = (this.focused ? (this.restPos + new Vector3(this.focusOffset, 0f, 0f)) : (this.act ? (this.restPos + new Vector3(this.pressOffset, 0f, 0f)) : this.restPos));
		base.transform.localPosition = Vector3.Lerp(base.transform.localPosition, vector, this.moveSpeed * Time.deltaTime);
		if (this.restartGame)
		{
			for (int i = this.playersThatCanRequestARestart.Count - 1; i >= 0; i--)
			{
				if (!SteamLobby.Instance.players.Contains(this.playersThatCanRequestARestart[i]))
				{
					this.playersThatCanRequestARestart.RemoveAt(i);
				}
			}
			this.voteRematchText.text = string.Format("REMATCH : ({0}/{1})", this.VotedPlayers.Count, this.playersThatCanRequestARestart.Count);
			string text = "";
			foreach (int num in this.VotedPlayers)
			{
				ClientInstance clientInstance;
				if (ClientInstance.playerInstances.TryGetValue(num, out clientInstance))
				{
					text = text + clientInstance.PlayerName + "\n";
				}
			}
			this.playersText.text = text;
		}
		if (this.rematch && this.restartGame && this.VotedPlayers.Count >= this.playersThatCanRequestARestart.Count)
		{
			SceneMotor.Instance.ServerRestartGameScene();
			this.rematch = false;
		}
	}

	// Token: 0x06000501 RID: 1281 RVA: 0x00021410 File Offset: 0x0001F610
	[ServerRpc(RequireOwnership = false)]
	public void VoteRematchServer(NetworkConnection sender = null)
	{
		this.RpcWriter___Server_VoteRematchServer_328543758(sender);
	}

	// Token: 0x06000502 RID: 1282 RVA: 0x00021428 File Offset: 0x0001F628
	private void SaveCurrentPoolToPlaylist()
	{
		MapsManager.Instance.AddPlaylistWithoutUpdate(SceneMotor.Instance.playedMaps.ToArray(), "Session " + DateTime.Now.ToString());
		PauseManager.Instance.WriteOfflineLog("Session " + DateTime.Now.ToString());
		base.gameObject.SetActive(false);
	}

	// Token: 0x06000503 RID: 1283 RVA: 0x00021492 File Offset: 0x0001F692
	private void SharePlaylist()
	{
		if (this.hasAskedForPlaylist)
		{
			return;
		}
		this.hasAskedForPlaylist = true;
		this.RequestArrayData(null);
	}

	// Token: 0x06000504 RID: 1284 RVA: 0x000214AB File Offset: 0x0001F6AB
	[ServerRpc(RequireOwnership = false)]
	public void RequestArrayData(NetworkConnection sender = null)
	{
		this.RpcWriter___Server_RequestArrayData_328543758(sender);
	}

	// Token: 0x06000505 RID: 1285 RVA: 0x000214B8 File Offset: 0x0001F6B8
	[TargetRpc]
	private void GetPlaylist(NetworkConnection conn, string[] playlist)
	{
		this.RpcWriter___Target_GetPlaylist_2890081366(conn, playlist);
	}

	// Token: 0x06000506 RID: 1286 RVA: 0x000214D3 File Offset: 0x0001F6D3
	private void ScrambleTeams()
	{
		GameManager.Instance.ScrambleTeams();
		PauseManager.Instance.WriteLog("Teams have been mixed");
	}

	// Token: 0x06000508 RID: 1288 RVA: 0x00021544 File Offset: 0x0001F744
	public override void NetworkInitialize___Early()
	{
		if (this.NetworkInitializeEarly_VictoryMenu_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeEarly_VictoryMenu_Assembly-CSharp.dll = true;
		base.NetworkInitialize___Early();
		this.VotedPlayers.InitializeInstance(this, 0U, WritePermission.ServerOnly, ReadPermission.Observers, -1f, Channel.Reliable, true);
		base.RegisterServerRpc(0U, new ServerRpcDelegate(this.RpcReader___Server_VoteRematchServer_328543758));
		base.RegisterServerRpc(1U, new ServerRpcDelegate(this.RpcReader___Server_RequestArrayData_328543758));
		base.RegisterTargetRpc(2U, new ClientRpcDelegate(this.RpcReader___Target_GetPlaylist_2890081366));
	}

	// Token: 0x06000509 RID: 1289 RVA: 0x000215D3 File Offset: 0x0001F7D3
	public override void NetworkInitialize__Late()
	{
		if (this.NetworkInitializeLate_VictoryMenu_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeLate_VictoryMenu_Assembly-CSharp.dll = true;
		base.NetworkInitialize__Late();
		this.VotedPlayers.SetRegistered();
	}

	// Token: 0x0600050A RID: 1290 RVA: 0x000215F7 File Offset: 0x0001F7F7
	public override void NetworkInitializeIfDisabled()
	{
		this.NetworkInitialize___Early();
		this.NetworkInitialize__Late();
	}

	// Token: 0x0600050B RID: 1291 RVA: 0x00021608 File Offset: 0x0001F808
	private void RpcWriter___Server_VoteRematchServer_328543758(NetworkConnection sender = null)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendServerRpc(0U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x0600050C RID: 1292 RVA: 0x000216A4 File Offset: 0x0001F8A4
	public void RpcLogic___VoteRematchServer_328543758(NetworkConnection sender = null)
	{
		ClientInstance clientInstance = null;
		foreach (ClientInstance clientInstance2 in ClientInstance.playerInstances.Values)
		{
			if (!(clientInstance2.Owner != sender))
			{
				clientInstance = clientInstance2;
				break;
			}
		}
		if (clientInstance == null)
		{
			return;
		}
		int playerId = clientInstance.PlayerId;
		if (this.VotedPlayers.Contains(playerId))
		{
			this.VotedPlayers.Remove(playerId);
			return;
		}
		this.VotedPlayers.Add(playerId);
	}

	// Token: 0x0600050D RID: 1293 RVA: 0x00021744 File Offset: 0x0001F944
	private void RpcReader___Server_VoteRematchServer_328543758(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		if (!base.IsServer)
		{
			return;
		}
		this.RpcLogic___VoteRematchServer_328543758(conn);
	}

	// Token: 0x0600050E RID: 1294 RVA: 0x00021768 File Offset: 0x0001F968
	private void RpcWriter___Server_RequestArrayData_328543758(NetworkConnection sender = null)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendServerRpc(1U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x0600050F RID: 1295 RVA: 0x00021802 File Offset: 0x0001FA02
	public void RpcLogic___RequestArrayData_328543758(NetworkConnection sender = null)
	{
		this.GetPlaylist(sender, SceneMotor.Instance.scenes.ToArray());
	}

	// Token: 0x06000510 RID: 1296 RVA: 0x0002181C File Offset: 0x0001FA1C
	private void RpcReader___Server_RequestArrayData_328543758(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		if (!base.IsServer)
		{
			return;
		}
		this.RpcLogic___RequestArrayData_328543758(conn);
	}

	// Token: 0x06000511 RID: 1297 RVA: 0x00021840 File Offset: 0x0001FA40
	private void RpcWriter___Target_GetPlaylist_2890081366(NetworkConnection conn, string[] playlist)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.Write___System.String[]FishNet.Serializing.Generated(playlist);
		base.SendTargetRpc(2U, writer, channel, DataOrderType.Default, conn, false, true);
		writer.Store();
	}

	// Token: 0x06000512 RID: 1298 RVA: 0x000218F8 File Offset: 0x0001FAF8
	private void RpcLogic___GetPlaylist_2890081366(NetworkConnection conn, string[] playlist)
	{
		if (!this.hasAskedForPlaylist)
		{
			return;
		}
		MapsManager.Instance.AddPlaylistWithoutUpdate(playlist, "Session " + DateTime.Now.ToString());
		PauseManager.Instance.WriteOfflineLog("Session " + DateTime.Now.ToString());
		base.gameObject.SetActive(false);
		this.hasAskedForPlaylist = false;
	}

	// Token: 0x06000513 RID: 1299 RVA: 0x00021964 File Offset: 0x0001FB64
	private void RpcReader___Target_GetPlaylist_2890081366(PooledReader PooledReader0, Channel channel)
	{
		string[] array = global::FishNet.Serializing.Generated.GeneratedReaders___Internal.Read___System.String[]FishNet.Serializing.Generateds(PooledReader0);
		if (!base.IsClient)
		{
			return;
		}
		this.RpcLogic___GetPlaylist_2890081366(base.LocalConnection, array);
	}

	// Token: 0x06000514 RID: 1300 RVA: 0x0002199B File Offset: 0x0001FB9B
	public override void Awake()
	{
		this.NetworkInitialize___Early();
		base.Awake();
		this.NetworkInitialize__Late();
	}

	// Token: 0x06000515 RID: 1301 RVA: 0x000023D6 File Offset: 0x000005D6
	public override void Awake___UserLogic()
	{
	}

	// Token: 0x040004F2 RID: 1266
	[SerializeField]
	private InputAction menu;

	// Token: 0x040004F3 RID: 1267
	[SerializeField]
	private InputAction menu2;

	// Token: 0x040004F4 RID: 1268
	[SerializeField]
	private bool returnMenu;

	// Token: 0x040004F5 RID: 1269
	[SerializeField]
	private bool restartGame;

	// Token: 0x040004F6 RID: 1270
	[SerializeField]
	private bool quitGame;

	// Token: 0x040004F7 RID: 1271
	[SerializeField]
	private bool savePlaylist;

	// Token: 0x040004F8 RID: 1272
	[SerializeField]
	private bool sharePlaylist;

	// Token: 0x040004F9 RID: 1273
	[SerializeField]
	private bool scrambleTeams;

	// Token: 0x040004FA RID: 1274
	private Vector3 restPos;

	// Token: 0x040004FB RID: 1275
	[SerializeField]
	private float focusOffset = 0.15f;

	// Token: 0x040004FC RID: 1276
	[SerializeField]
	private float pressOffset = 0.33f;

	// Token: 0x040004FD RID: 1277
	[SerializeField]
	private float moveSpeed = 12f;

	// Token: 0x040004FE RID: 1278
	[Space]
	[SerializeField]
	private GameObject textObject;

	// Token: 0x040004FF RID: 1279
	[SerializeField]
	private TMP_Text voteRematchText;

	// Token: 0x04000500 RID: 1280
	[SerializeField]
	private TMP_Text playersText;

	// Token: 0x04000501 RID: 1281
	private bool act;

	// Token: 0x04000502 RID: 1282
	private bool focused;

	// Token: 0x04000503 RID: 1283
	private List<NetworkObject> playersThatCanRequestARestart = new List<NetworkObject>();

	// Token: 0x04000504 RID: 1284
	private bool rematch = true;

	// Token: 0x04000505 RID: 1285
	[SyncObject]
	private readonly SyncList<int> VotedPlayers = new SyncList<int>();

	// Token: 0x04000506 RID: 1286
	public bool hasAskedForPlaylist;

	// Token: 0x04000507 RID: 1287
	private bool NetworkInitializeEarly_VictoryMenu_Assembly-CSharp.dll;

	// Token: 0x04000508 RID: 1288
	private bool NetworkInitializeLate_VictoryMenu_Assembly-CSharp.dll;
}

﻿using System;
using System.Collections.Generic;
using FishNet;
using FishNet.Object;
using UnityEngine;
using UnityEngine.Serialization;

// Token: 0x020000B9 RID: 185
[Serializable]
public abstract class Spawner : NetworkBehaviour
{
	// Token: 0x06000A4E RID: 2638 RVA: 0x0004B544 File Offset: 0x00049744
	public static void UpdateSpawnableWeapons()
	{
		if (!InstanceFinder.NetworkManager.IsServer)
		{
			return;
		}
		uint num = 0U;
		foreach (WeaponData weaponData in Spawner.weaponInfo.Values)
		{
			if (weaponData.IsSpawnable)
			{
				num += weaponData.SpawnChance;
			}
		}
		SpawnerManager.Instance.SyncRandomSettings(Spawner.weaponInfo.Values, num);
	}

	// Token: 0x06000A4F RID: 2639 RVA: 0x0004B5CC File Offset: 0x000497CC
	protected virtual void Start()
	{
		this.countdownTimer = this.weaponRespawnTimeInSeconds;
	}

	// Token: 0x06000A50 RID: 2640 RVA: 0x0004B5DC File Offset: 0x000497DC
	protected virtual void Update()
	{
		if (!InstanceFinder.NetworkManager.IsServer)
		{
			return;
		}
		if (this.SpawnedItem != null && !this.SpawnedItem.GetComponent<ItemBehaviour>().isTaken)
		{
			if (this.countdownTimer != this.weaponRespawnTimeInSeconds)
			{
				this.countdownTimer = this.weaponRespawnTimeInSeconds;
			}
			return;
		}
		if (this.countdownTimer > 0f)
		{
			this.countdownTimer -= Time.deltaTime;
			return;
		}
		this.TrySpawn();
		this.countdownTimer = this.weaponRespawnTimeInSeconds;
	}

	// Token: 0x06000A51 RID: 2641
	public abstract void TrySpawn();

	// Token: 0x06000A54 RID: 2644 RVA: 0x0004B688 File Offset: 0x00049888
	public virtual void NetworkInitialize___Early()
	{
		if (this.NetworkInitializeEarly_Spawner_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeEarly_Spawner_Assembly-CSharp.dll = true;
	}

	// Token: 0x06000A55 RID: 2645 RVA: 0x0004B69B File Offset: 0x0004989B
	public virtual void NetworkInitialize__Late()
	{
		if (this.NetworkInitializeLate_Spawner_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeLate_Spawner_Assembly-CSharp.dll = true;
	}

	// Token: 0x06000A56 RID: 2646 RVA: 0x0004B6AE File Offset: 0x000498AE
	public override void NetworkInitializeIfDisabled()
	{
		this.NetworkInitialize___Early();
		this.NetworkInitialize__Late();
	}

	// Token: 0x06000A57 RID: 2647 RVA: 0x0004B6BC File Offset: 0x000498BC
	public virtual void Awake()
	{
		this.NetworkInitialize___Early();
		this.Awake___UserLogic();
		this.NetworkInitialize__Late();
	}

	// Token: 0x06000A58 RID: 2648 RVA: 0x000023D6 File Offset: 0x000005D6
	public virtual void Awake___UserLogic()
	{
	}

	// Token: 0x04000910 RID: 2320
	public static bool IsRandomWeaponsEnabled = false;

	// Token: 0x04000911 RID: 2321
	public static Dictionary<string, WeaponData> weaponInfo = new Dictionary<string, WeaponData>();

	// Token: 0x04000912 RID: 2322
	[FormerlySerializedAs("countdown")]
	[Range(0f, 60f)]
	[SerializeField]
	protected float weaponRespawnTimeInSeconds = 2f;

	// Token: 0x04000913 RID: 2323
	protected float countdownTimer;

	// Token: 0x04000914 RID: 2324
	protected GameObject SpawnedItem;

	// Token: 0x04000915 RID: 2325
	private bool NetworkInitializeEarly_Spawner_Assembly-CSharp.dll;

	// Token: 0x04000916 RID: 2326
	private bool NetworkInitializeLate_Spawner_Assembly-CSharp.dll;
}

﻿using System;
using UnityEngine;

// Token: 0x02000071 RID: 113
public class SpawnPoint : MonoBehaviour
{
	// Token: 0x17000072 RID: 114
	// (get) Token: 0x060004E9 RID: 1257 RVA: 0x00020E3F File Offset: 0x0001F03F
	public float Radius
	{
		get
		{
			return this._radius;
		}
	}

	// Token: 0x060004EA RID: 1258 RVA: 0x00020E47 File Offset: 0x0001F047
	private void Awake()
	{
		if (this.snapToGround)
		{
			this.SnapToGround();
		}
	}

	// Token: 0x060004EB RID: 1259 RVA: 0x00020E58 File Offset: 0x0001F058
	private void SnapToGround()
	{
		RaycastHit raycastHit;
		if (Physics.Raycast(new Ray(base.transform.position, Vector3.down), out raycastHit, 10f))
		{
			base.transform.position = raycastHit.point + Vector3.up * 0.2f;
		}
	}

	// Token: 0x040004E5 RID: 1253
	[Tooltip("Radius of this spawn point.")]
	[SerializeField]
	private float _radius = 3f;

	// Token: 0x040004E6 RID: 1254
	[SerializeField]
	private bool snapToGround = true;
}

﻿using System;
using UnityEngine;

// Token: 0x0200010C RID: 268
public class SetPlayerVolume : MonoBehaviour
{
	// Token: 0x06000D40 RID: 3392 RVA: 0x00059F0F File Offset: 0x0005810F
	private void Awake()
	{
		this.source = SoundManager.Instance._effectsSource;
	}

	// Token: 0x06000D41 RID: 3393 RVA: 0x00059F21 File Offset: 0x00058121
	private void Update()
	{
		if (this.audio.volume != this.source.volume)
		{
			this.audio.volume = this.source.volume;
		}
	}

	// Token: 0x04000B7B RID: 2939
	private AudioSource source;

	// Token: 0x04000B7C RID: 2940
	[SerializeField]
	private AudioSource audio;
}

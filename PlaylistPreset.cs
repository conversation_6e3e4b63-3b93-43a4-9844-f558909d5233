﻿using System;
using UnityEngine;

// Token: 0x020000ED RID: 237
[CreateAssetMenu(fileName = "PlaylistPreset", menuName = "ComputeryStuff/PlaylistPreset", order = 1)]
public class PlaylistPreset : ScriptableObject
{
	// Token: 0x04000ABB RID: 2747
	public string Name;

	// Token: 0x04000ABC RID: 2748
	public string[] Maps;

	// Token: 0x04000ABD RID: 2749
	[Header("This will make the playlist only available if the player owns the DLC, you can include DLC exclusive maps in non-DLC playlists, but they will not show up if the player does not own the DLC.")]
	public bool IsDlcExclusive;
}

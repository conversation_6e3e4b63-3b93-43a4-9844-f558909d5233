﻿using System;
using System.Collections;
using System.Collections.Generic;
using FishNet;
using FishNet.Connection;
using FishNet.Managing;
using FishNet.Managing.Scened;
using FishNet.Object;
using FishNet.Object.Delegating;
using FishNet.Serializing;
using FishNet.Transporting;
using UnityEngine;
using UnityEngine.SceneManagement;
using UnityEngine.Serialization;

// Token: 0x02000066 RID: 102
public class PlayerManager : NetworkBehaviour
{
	// Token: 0x0600046C RID: 1132 RVA: 0x0001EB81 File Offset: 0x0001CD81
	public virtual void Awake()
	{
		this.NetworkInitialize___Early();
		this.Awake___UserLogic();
		this.NetworkInitialize__Late();
	}

	// Token: 0x0600046D RID: 1133 RVA: 0x0001EB95 File Offset: 0x0001CD95
	private void OnEnable()
	{
		InstanceFinder.SceneManager.OnLoadStart += this.OnLoadSceneStart;
		InstanceFinder.SceneManager.OnLoadEnd += this.OnLoadSceneEnd;
	}

	// Token: 0x0600046E RID: 1134 RVA: 0x0001EBC3 File Offset: 0x0001CDC3
	private void Start()
	{
		this.mapsManager = MapsManager.Instance;
	}

	// Token: 0x0600046F RID: 1135 RVA: 0x0001EBD0 File Offset: 0x0001CDD0
	private void Update()
	{
		if (!base.IsOwner)
		{
			return;
		}
		if (this.SpawnedObject && PauseManager.Instance.inMainMenu)
		{
			this.SpawnedObject.SetActive(false);
			this.DespawnServer(this.SpawnedObject);
		}
		if (this.waitForRoundStartCoroutine != null && PauseManager.Instance.inMainMenu)
		{
			base.StopCoroutine(this.waitForRoundStartCoroutine);
			GameManager.Instance.hasSetStartTime = false;
			this.waitForRoundStartCoroutine = null;
		}
	}

	// Token: 0x06000470 RID: 1136 RVA: 0x0001EC49 File Offset: 0x0001CE49
	public void WaitForRoundStartCoroutineStart()
	{
		if (this.waitForRoundStartCoroutine != null)
		{
			Debug.LogWarning("WaitForRoundStartCoroutine is already running, not starting a new one.");
			return;
		}
		this.waitForRoundStartCoroutine = base.StartCoroutine(this.WaitForRoundStart());
	}

	// Token: 0x06000471 RID: 1137 RVA: 0x0001EC70 File Offset: 0x0001CE70
	public IEnumerator WaitForRoundStart()
	{
		List<NetworkObject> playersToLoadIn = SteamLobby.Instance.players;
		if (this.ClientScript.nonSteamworksTransport || SceneMotor.Instance.testMap || PauseManager.Instance.inVictoryMenu || PauseManager.Instance.inMainMenu)
		{
			this.SetPlayerMove(true);
			PauseManager.Instance.InvokeRoundStarted();
			Debug.Log(string.Format("Skipping round start... nonSteamworksTransport: {0}, testMap: {1}, inVictoryMenu: {2}, inMainMenu: {3}", new object[]
			{
				this.ClientScript.nonSteamworksTransport,
				this.mapsManager.inExplorationMap,
				PauseManager.Instance.inVictoryMenu,
				PauseManager.Instance.inMainMenu
			}));
			this.waitForRoundStartCoroutine = null;
			yield break;
		}
		yield return new WaitForSeconds(0.5f);
		this.SetPlayerMove(false);
		this.IncrementReadyPlayers();
		if (base.IsServer)
		{
			while (GameManager.Instance.numberOfReadyPlayers < playersToLoadIn.Count)
			{
				for (int i = playersToLoadIn.Count - 1; i >= 0; i--)
				{
					if (!SteamLobby.Instance.players.Contains(playersToLoadIn[i]))
					{
						playersToLoadIn.RemoveAt(i);
					}
				}
				yield return null;
			}
			GameManager.Instance.numberOfReadyPlayers = 0;
			float num = ((ScoreManager.Instance.SyncAccessor_TakeIndex == 0) ? 3f : 1.2f);
			num += 0.25f;
			GameManager.Instance.SetStartTime(num);
		}
		else
		{
			while (!GameManager.Instance.hasSetStartTime)
			{
				yield return null;
			}
		}
		GameManager.Instance.hasSetStartTime = false;
		PauseManager.Instance.startRound = true;
		PauseManager.Instance.StartRoundDelay(GameManager.Instance.timeTillStart);
		double elapsedTime = 0.0;
		while (elapsedTime < (double)GameManager.Instance.timeTillStart)
		{
			elapsedTime += (double)Time.deltaTime;
			yield return null;
		}
		this.SetPlayerMove(true);
		PauseManager.Instance.startRound = false;
		string text = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff");
		Debug.Log("CAN MOVE at Current Time: " + text);
		this.waitForRoundStartCoroutine = null;
		yield break;
	}

	// Token: 0x06000472 RID: 1138 RVA: 0x0001EC7F File Offset: 0x0001CE7F
	[ServerRpc(RequireOwnership = false)]
	public void IncrementReadyPlayers()
	{
		this.RpcWriter___Server_IncrementReadyPlayers_2166136261();
	}

	// Token: 0x06000473 RID: 1139 RVA: 0x0001EC87 File Offset: 0x0001CE87
	[ServerRpc(RequireOwnership = false, RunLocally = true)]
	public void SetPlayerMove(bool state)
	{
		this.RpcWriter___Server_SetPlayerMove_1140765316(state);
		this.RpcLogic___SetPlayerMove_1140765316(state);
	}

	// Token: 0x06000474 RID: 1140 RVA: 0x0001ECA0 File Offset: 0x0001CEA0
	public void OnLoadSceneStart(SceneLoadStartEventArgs args)
	{
		if (this.SpawnedObject)
		{
			base.Despawn(this.SpawnedObject, null);
		}
	}

	// Token: 0x06000475 RID: 1141 RVA: 0x0001ECD0 File Offset: 0x0001CED0
	public void PopulateSpawnPoints()
	{
		GameObject gameObject = GameObject.FindGameObjectWithTag("Spawnpoints");
		this.SpawnPoint1v1 = (gameObject ? gameObject.GetComponentsInChildren<SpawnPoint>() : Array.Empty<SpawnPoint>());
		GameObject gameObject2 = GameObject.FindGameObjectWithTag("Spawnpoints4Player");
		this.SpawnPoint4v4 = (gameObject2 ? gameObject2.GetComponentsInChildren<SpawnPoint>() : Array.Empty<SpawnPoint>());
		this.SetActiveSpawnPoints();
	}

	// Token: 0x06000476 RID: 1142 RVA: 0x0001ED30 File Offset: 0x0001CF30
	public void SetActiveSpawnPoints()
	{
		if (this.ClientScript.nonSteamworksTransport)
		{
			this.CurrentSpawnPoints = this.SpawnPoint1v1;
			return;
		}
		if (SteamLobby.Instance.players.Count > 2)
		{
			if (this.SpawnPoint4v4.Length != 0)
			{
				this.CurrentSpawnPoints = this.SpawnPoint4v4;
				return;
			}
			Debug.LogError("No spawn points found for 4+ players! Falling back to 1v1 spawn points.");
		}
		this.CurrentSpawnPoints = this.SpawnPoint1v1;
	}

	// Token: 0x06000477 RID: 1143 RVA: 0x0001ED98 File Offset: 0x0001CF98
	public void OnLoadSceneEnd(SceneLoadEndEventArgs args)
	{
		if (global::UnityEngine.SceneManagement.SceneManager.GetActiveScene().name == "MainMenu" && !PauseManager.Instance.inMainMenu)
		{
			this.ClientScript.ServerSetPlayerReadyState(false);
			Debug.Log("main menu player manager");
		}
		if (this.SpawnedObject != null)
		{
			return;
		}
		this.PopulateSpawnPoints();
		if (global::UnityEngine.SceneManagement.SceneManager.GetActiveScene().name != "EmptyScene")
		{
			this.NewSceneSpawn();
		}
	}

	// Token: 0x06000478 RID: 1144 RVA: 0x0001EE14 File Offset: 0x0001D014
	public void TryRespawn()
	{
		if (global::UnityEngine.SceneManagement.SceneManager.GetActiveScene().name == "MainMenu")
		{
			return;
		}
		if (!base.IsOwner)
		{
			return;
		}
		AnimResetOnRound[] array = global::UnityEngine.Object.FindObjectsOfType<AnimResetOnRound>();
		for (int i = 0; i < array.Length; i++)
		{
			array[i].StartNewRound();
		}
		this.CmdRespawn();
		PauseManager.Instance.startRound = false;
	}

	// Token: 0x06000479 RID: 1145 RVA: 0x0001EE74 File Offset: 0x0001D074
	private Transform ReturnSpawnPoint()
	{
		if (this.CurrentSpawnPoints.Length == 0)
		{
			return null;
		}
		PlayerManager.Shuffle(this.CurrentSpawnPoints);
		foreach (SpawnPoint spawnPoint in this.CurrentSpawnPoints)
		{
			if (spawnPoint.gameObject.activeInHierarchy && Physics.OverlapSphereNonAlloc(spawnPoint.transform.position, spawnPoint.Radius, null, 5) == 0)
			{
				return spawnPoint.transform;
			}
		}
		return this.CurrentSpawnPoints[0].transform;
	}

	// Token: 0x0600047A RID: 1146 RVA: 0x0001EEEC File Offset: 0x0001D0EC
	private static void Shuffle(SpawnPoint[] spawnPoints)
	{
		for (int i = 0; i < spawnPoints.Length; i++)
		{
			SpawnPoint spawnPoint = spawnPoints[i];
			int num = global::UnityEngine.Random.Range(i, spawnPoints.Length);
			spawnPoints[i] = spawnPoints[num];
			spawnPoints[num] = spawnPoint;
		}
	}

	// Token: 0x0600047B RID: 1147 RVA: 0x0001EF20 File Offset: 0x0001D120
	[ServerRpc]
	private void CmdRespawn()
	{
		this.RpcWriter___Server_CmdRespawn_2166136261();
	}

	// Token: 0x0600047C RID: 1148 RVA: 0x0001EF34 File Offset: 0x0001D134
	public void NewSceneSpawn()
	{
		if (!base.IsOwner)
		{
			return;
		}
		SaveLoadSystem.Instance.Save();
		if (global::UnityEngine.SceneManagement.SceneManager.GetActiveScene().name == "MainMenu")
		{
			return;
		}
		this.StartTrigger = true;
		Settings.Instance.IncreaseRoundsPlayed();
		base.StartCoroutine(SceneMotor.Instance.StartGameClients());
		this.CmdNewSceneSpawn(CosmeticsManager.Instance.currentsuitIndex, CosmeticsManager.Instance.currentcigIndex);
	}

	// Token: 0x0600047D RID: 1149 RVA: 0x0001EFAC File Offset: 0x0001D1AC
	[ServerRpc]
	private void CmdNewSceneSpawn(int suitIndex, int cigIndex)
	{
		this.RpcWriter___Server_CmdNewSceneSpawn_1692629761(suitIndex, cigIndex);
	}

	// Token: 0x0600047E RID: 1150 RVA: 0x0001EFC8 File Offset: 0x0001D1C8
	public void RoundSpawn()
	{
		if (global::UnityEngine.SceneManagement.SceneManager.GetActiveScene().name == "MainMenu")
		{
			return;
		}
		if (!base.IsOwner)
		{
			return;
		}
		this.StartTrigger = true;
		this.CmdNewRoundSpawn(CosmeticsManager.Instance.currentsuitIndex, CosmeticsManager.Instance.currentcigIndex);
	}

	// Token: 0x0600047F RID: 1151 RVA: 0x0001F01C File Offset: 0x0001D21C
	[ServerRpc]
	private void CmdNewRoundSpawn(int suitIndex, int cigIndex)
	{
		this.RpcWriter___Server_CmdNewRoundSpawn_1692629761(suitIndex, cigIndex);
	}

	// Token: 0x06000480 RID: 1152 RVA: 0x0001F038 File Offset: 0x0001D238
	private void SpawnPlayer(int suitIndex, int cigIndex)
	{
		this.SetActiveSpawnPoints();
		SpawnPoint spawnPoint;
		if (SteamLobby.Instance.players.Count <= 2)
		{
			int num = ((this.ClientScript.PlayerId == 0) ? 0 : 1);
			spawnPoint = this.CurrentSpawnPoints[(ScoreManager.Instance.SyncAccessor_TakeIndex + num) % this.CurrentSpawnPoints.Length];
		}
		else
		{
			spawnPoint = this.CurrentSpawnPoints[(ScoreManager.Instance.SyncAccessor_TakeIndex + this.ClientScript.PlayerId) % this.CurrentSpawnPoints.Length];
		}
		this.SpawnPlayer(suitIndex, cigIndex, spawnPoint.transform.position, Quaternion.Euler(0f, spawnPoint.transform.eulerAngles.y, 0f));
	}

	// Token: 0x06000481 RID: 1153 RVA: 0x0001F0E8 File Offset: 0x0001D2E8
	private void SpawnPlayer(int suitIndex, int cigIndex, Vector3 position, Quaternion rotation)
	{
		this.BeforeSpawn(this.ClientScript.Owner);
		if (GameManager.Instance != null && !GameManager.Instance.alivePlayers.Contains(this.ClientScript.PlayerId))
		{
			GameManager.Instance.alivePlayers.Add(this.ClientScript.PlayerId);
		}
		GameObject gameObject = global::UnityEngine.Object.Instantiate<GameObject>(this.characterPrefab, position, rotation);
		this.SpawnedObject = gameObject;
		this.player = gameObject.GetComponent<FirstPersonController>();
		gameObject.GetComponentInChildren<VisualInfo>().name.text = this.ClientScript.PlayerName;
		gameObject.GetComponent<PlayerValues>().sync___set_value_playerClient(this.ClientScript, true);
		PlayerSetup component = gameObject.GetComponent<PlayerSetup>();
		component.sync___set_value_mat(suitIndex, true);
		component.sync___set_value_cig(cigIndex, true);
		base.Spawn(gameObject, base.Owner);
	}

	// Token: 0x06000482 RID: 1154 RVA: 0x0001F1BA File Offset: 0x0001D3BA
	[TargetRpc]
	private void BeforeSpawn(NetworkConnection conn = null)
	{
		this.RpcWriter___Target_BeforeSpawn_328543758(conn);
	}

	// Token: 0x06000483 RID: 1155 RVA: 0x0001F1C8 File Offset: 0x0001D3C8
	private void UnsubscribeFromInput()
	{
		this.player.move.Disable();
		this.player.moveUp.Disable();
		this.player.jump.Disable();
		this.player.run.Disable();
		this.player.lookY.Disable();
		this.player.lookX.Disable();
		this.player.crouch.Disable();
		this.player.leanLeft.Disable();
		this.player.leanRight.Disable();
		this.player.jump.performed -= this.player.Jump;
		this.player.crouch.performed -= this.player.Slide;
		this.player.crouch.started -= this.player.SetCrouch;
		this.player.crouch.canceled -= this.player.SetCrouch;
		this.player.crouch.canceled -= this.player.SlideEnd;
	}

	// Token: 0x06000484 RID: 1156 RVA: 0x0001F30C File Offset: 0x0001D50C
	[ServerRpc]
	private void DespawnServer(GameObject obj)
	{
		this.RpcWriter___Server_DespawnServer_1934289915(obj);
	}

	// Token: 0x06000486 RID: 1158 RVA: 0x0001F354 File Offset: 0x0001D554
	public virtual void NetworkInitialize___Early()
	{
		if (this.NetworkInitializeEarly_PlayerManager_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeEarly_PlayerManager_Assembly-CSharp.dll = true;
		base.RegisterServerRpc(0U, new ServerRpcDelegate(this.RpcReader___Server_IncrementReadyPlayers_2166136261));
		base.RegisterServerRpc(1U, new ServerRpcDelegate(this.RpcReader___Server_SetPlayerMove_1140765316));
		base.RegisterServerRpc(2U, new ServerRpcDelegate(this.RpcReader___Server_CmdRespawn_2166136261));
		base.RegisterServerRpc(3U, new ServerRpcDelegate(this.RpcReader___Server_CmdNewSceneSpawn_1692629761));
		base.RegisterServerRpc(4U, new ServerRpcDelegate(this.RpcReader___Server_CmdNewRoundSpawn_1692629761));
		base.RegisterTargetRpc(5U, new ClientRpcDelegate(this.RpcReader___Target_BeforeSpawn_328543758));
		base.RegisterServerRpc(6U, new ServerRpcDelegate(this.RpcReader___Server_DespawnServer_1934289915));
	}

	// Token: 0x06000487 RID: 1159 RVA: 0x0001F413 File Offset: 0x0001D613
	public virtual void NetworkInitialize__Late()
	{
		if (this.NetworkInitializeLate_PlayerManager_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeLate_PlayerManager_Assembly-CSharp.dll = true;
	}

	// Token: 0x06000488 RID: 1160 RVA: 0x0001F426 File Offset: 0x0001D626
	public override void NetworkInitializeIfDisabled()
	{
		this.NetworkInitialize___Early();
		this.NetworkInitialize__Late();
	}

	// Token: 0x06000489 RID: 1161 RVA: 0x0001F434 File Offset: 0x0001D634
	private void RpcWriter___Server_IncrementReadyPlayers_2166136261()
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendServerRpc(0U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x0600048A RID: 1162 RVA: 0x0001F4CE File Offset: 0x0001D6CE
	public void RpcLogic___IncrementReadyPlayers_2166136261()
	{
		GameManager.Instance.numberOfReadyPlayers++;
	}

	// Token: 0x0600048B RID: 1163 RVA: 0x0001F4E4 File Offset: 0x0001D6E4
	private void RpcReader___Server_IncrementReadyPlayers_2166136261(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		if (!base.IsServer)
		{
			return;
		}
		this.RpcLogic___IncrementReadyPlayers_2166136261();
	}

	// Token: 0x0600048C RID: 1164 RVA: 0x0001F504 File Offset: 0x0001D704
	private void RpcWriter___Server_SetPlayerMove_1140765316(bool state)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteBoolean(state);
		base.SendServerRpc(1U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x0600048D RID: 1165 RVA: 0x0001F5AB File Offset: 0x0001D7AB
	public void RpcLogic___SetPlayerMove_1140765316(bool state)
	{
		this.player.sync___set_value_canMove(state, true);
	}

	// Token: 0x0600048E RID: 1166 RVA: 0x0001F5BC File Offset: 0x0001D7BC
	private void RpcReader___Server_SetPlayerMove_1140765316(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		bool flag = PooledReader0.ReadBoolean();
		if (!base.IsServer)
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___SetPlayerMove_1140765316(flag);
	}

	// Token: 0x0600048F RID: 1167 RVA: 0x0001F5FC File Offset: 0x0001D7FC
	private void RpcWriter___Server_CmdRespawn_2166136261()
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendServerRpc(2U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000490 RID: 1168 RVA: 0x0001F6F0 File Offset: 0x0001D8F0
	private void RpcLogic___CmdRespawn_2166136261()
	{
		if (this.SpawnedObject != null)
		{
			this.UnsubscribeFromInput();
			base.Despawn(this.SpawnedObject, null);
		}
		Transform transform = this.ReturnSpawnPoint();
		if (transform)
		{
			this.SpawnPlayer(CosmeticsManager.Instance.currentsuitIndex, CosmeticsManager.Instance.currentcigIndex, transform.position, Quaternion.Euler(0f, transform.eulerAngles.y, 0f));
			return;
		}
		Debug.LogError("All spawns are occupied.");
	}

	// Token: 0x06000491 RID: 1169 RVA: 0x0001F77C File Offset: 0x0001D97C
	private void RpcReader___Server_CmdRespawn_2166136261(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		this.RpcLogic___CmdRespawn_2166136261();
	}

	// Token: 0x06000492 RID: 1170 RVA: 0x0001F7B0 File Offset: 0x0001D9B0
	private void RpcWriter___Server_CmdNewSceneSpawn_1692629761(int suitIndex, int cigIndex)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteInt32(suitIndex, AutoPackType.Packed);
		writer.WriteInt32(cigIndex, AutoPackType.Packed);
		base.SendServerRpc(3U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000493 RID: 1171 RVA: 0x0001F8C8 File Offset: 0x0001DAC8
	private void RpcLogic___CmdNewSceneSpawn_1692629761(int suitIndex, int cigIndex)
	{
		if (this.SpawnedObject)
		{
			this.UnsubscribeFromInput();
			this.SpawnedObject.SetActive(false);
			base.Despawn(this.SpawnedObject, null);
		}
		this.SpawnPlayer(suitIndex, cigIndex);
	}

	// Token: 0x06000494 RID: 1172 RVA: 0x0001F914 File Offset: 0x0001DB14
	private void RpcReader___Server_CmdNewSceneSpawn_1692629761(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		int num = PooledReader0.ReadInt32(AutoPackType.Packed);
		int num2 = PooledReader0.ReadInt32(AutoPackType.Packed);
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		this.RpcLogic___CmdNewSceneSpawn_1692629761(num, num2);
	}

	// Token: 0x06000495 RID: 1173 RVA: 0x0001F974 File Offset: 0x0001DB74
	private void RpcWriter___Server_CmdNewRoundSpawn_1692629761(int suitIndex, int cigIndex)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteInt32(suitIndex, AutoPackType.Packed);
		writer.WriteInt32(cigIndex, AutoPackType.Packed);
		base.SendServerRpc(4U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000496 RID: 1174 RVA: 0x0001FA8C File Offset: 0x0001DC8C
	private void RpcLogic___CmdNewRoundSpawn_1692629761(int suitIndex, int cigIndex)
	{
		if (this.SpawnedObject != null)
		{
			this.UnsubscribeFromInput();
			this.SpawnedObject.SetActive(false);
			base.Despawn(this.SpawnedObject, null);
		}
		this.SpawnPlayer(suitIndex, cigIndex);
	}

	// Token: 0x06000497 RID: 1175 RVA: 0x0001FAD8 File Offset: 0x0001DCD8
	private void RpcReader___Server_CmdNewRoundSpawn_1692629761(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		int num = PooledReader0.ReadInt32(AutoPackType.Packed);
		int num2 = PooledReader0.ReadInt32(AutoPackType.Packed);
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		this.RpcLogic___CmdNewRoundSpawn_1692629761(num, num2);
	}

	// Token: 0x06000498 RID: 1176 RVA: 0x0001FB38 File Offset: 0x0001DD38
	private void RpcWriter___Target_BeforeSpawn_328543758(NetworkConnection conn = null)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendTargetRpc(5U, writer, channel, DataOrderType.Default, conn, false, true);
		writer.Store();
	}

	// Token: 0x06000499 RID: 1177 RVA: 0x0001FBE0 File Offset: 0x0001DDE0
	private void RpcLogic___BeforeSpawn_328543758(NetworkConnection conn = null)
	{
		PauseManager.Instance.InvokeBeforeSpawn();
	}

	// Token: 0x0600049A RID: 1178 RVA: 0x0001FBEC File Offset: 0x0001DDEC
	private void RpcReader___Target_BeforeSpawn_328543758(PooledReader PooledReader0, Channel channel)
	{
		if (!base.IsClient)
		{
			return;
		}
		this.RpcLogic___BeforeSpawn_328543758(base.LocalConnection);
	}

	// Token: 0x0600049B RID: 1179 RVA: 0x0001FC14 File Offset: 0x0001DE14
	private void RpcWriter___Server_DespawnServer_1934289915(GameObject obj)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteGameObject(obj);
		base.SendServerRpc(6U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x0600049C RID: 1180 RVA: 0x0001FD18 File Offset: 0x0001DF18
	private void RpcLogic___DespawnServer_1934289915(GameObject obj)
	{
		if (obj)
		{
			base.Despawn(obj, null);
		}
	}

	// Token: 0x0600049D RID: 1181 RVA: 0x0001FD40 File Offset: 0x0001DF40
	private void RpcReader___Server_DespawnServer_1934289915(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		GameObject gameObject = PooledReader0.ReadGameObject();
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		this.RpcLogic___DespawnServer_1934289915(gameObject);
	}

	// Token: 0x0600049E RID: 1182 RVA: 0x0001FD82 File Offset: 0x0001DF82
	public virtual void Awake___UserLogic()
	{
		this.ClientScript = base.GetComponent<ClientInstance>();
		this.PopulateSpawnPoints();
	}

	// Token: 0x040004AF RID: 1199
	[Tooltip("Character prefab to spawn.")]
	[FormerlySerializedAs("_characterPrefab")]
	[SerializeField]
	private GameObject characterPrefab;

	// Token: 0x040004B0 RID: 1200
	private GameObject SpawnedObject;

	// Token: 0x040004B1 RID: 1201
	private SpawnPoint[] CurrentSpawnPoints = Array.Empty<SpawnPoint>();

	// Token: 0x040004B2 RID: 1202
	private SpawnPoint[] SpawnPoint1v1 = Array.Empty<SpawnPoint>();

	// Token: 0x040004B3 RID: 1203
	private SpawnPoint[] SpawnPoint4v4 = Array.Empty<SpawnPoint>();

	// Token: 0x040004B4 RID: 1204
	private ClientInstance ClientScript;

	// Token: 0x040004B5 RID: 1205
	[HideInInspector]
	public FirstPersonController player;

	// Token: 0x040004B6 RID: 1206
	private MapsManager mapsManager;

	// Token: 0x040004B7 RID: 1207
	private bool StartTrigger = true;

	// Token: 0x040004B8 RID: 1208
	public Coroutine waitForRoundStartCoroutine;

	// Token: 0x040004B9 RID: 1209
	private bool NetworkInitializeEarly_PlayerManager_Assembly-CSharp.dll;

	// Token: 0x040004BA RID: 1210
	private bool NetworkInitializeLate_PlayerManager_Assembly-CSharp.dll;
}

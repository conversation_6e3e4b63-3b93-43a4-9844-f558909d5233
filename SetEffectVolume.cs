﻿using System;
using UnityEngine;

// Token: 0x02000109 RID: 265
public class SetEffectVolume : MonoBehaviour
{
	// Token: 0x06000D30 RID: 3376 RVA: 0x0005969C File Offset: 0x0005789C
	private void Awake()
	{
		this.source = SoundManager.Instance._effectsSource;
		this.audio = base.GetComponent<AudioSource>();
	}

	// Token: 0x06000D31 RID: 3377 RVA: 0x000596BA File Offset: 0x000578BA
	private void Update()
	{
		if (this.audio.volume != this.source.volume)
		{
			this.audio.volume = this.source.volume;
		}
	}

	// Token: 0x04000B65 RID: 2917
	private AudioSource source;

	// Token: 0x04000B66 RID: 2918
	private AudioSource audio;
}

﻿using System;
using DG.Tweening;
using DG.Tweening.Core;
using DG.Tweening.Plugins.Options;
using UnityEngine;

// Token: 0x0200012C RID: 300
public class MenuHUDTween : MonoBehaviour
{
	// Token: 0x06000E53 RID: 3667 RVA: 0x0005F4CC File Offset: 0x0005D6CC
	private void Start()
	{
		if (this.displayed)
		{
			base.transform.localScale = Vector3.one;
			base.transform.position = this.displayedPosition.position;
			return;
		}
		base.transform.localScale = Vector3.zero;
		base.transform.position = this.hiddenPosition.position;
	}

	// Token: 0x06000E54 RID: 3668 RVA: 0x0005F52E File Offset: 0x0005D72E
	private void Update()
	{
		if (base.transform.position == this.hiddenPosition.position)
		{
			base.transform.localScale = Vector3.zero;
			return;
		}
		base.transform.localScale = Vector3.one;
	}

	// Token: 0x06000E55 RID: 3669 RVA: 0x0005F570 File Offset: 0x0005D770
	public void ChangeState()
	{
		base.transform.DOKill(false);
		if (this.displayed)
		{
			base.transform.DOMove(this.hiddenPosition.position, this.lerpTime, false).SetEase(this.easeType);
		}
		else
		{
			base.transform.DOMove(this.displayedPosition.position, this.lerpTime, false).SetEase(this.easeType);
		}
		this.displayed = !this.displayed;
	}

	// Token: 0x06000E56 RID: 3670 RVA: 0x0005F5F5 File Offset: 0x0005D7F5
	public void SetEnabled()
	{
		base.transform.DOKill(false);
		base.transform.DOMove(this.displayedPosition.position, this.lerpTime, false).SetEase(this.easeType);
		this.displayed = true;
	}

	// Token: 0x06000E57 RID: 3671 RVA: 0x0005F634 File Offset: 0x0005D834
	public void SetDisabled()
	{
		base.transform.DOKill(false);
		base.transform.DOMove(this.hiddenPosition.position, this.lerpTime, false).SetEase(this.easeType);
		this.displayed = false;
	}

	// Token: 0x04000CEB RID: 3307
	public bool displayed;

	// Token: 0x04000CEC RID: 3308
	[Space]
	[SerializeField]
	private Transform hiddenPosition;

	// Token: 0x04000CED RID: 3309
	[SerializeField]
	private Transform displayedPosition;

	// Token: 0x04000CEE RID: 3310
	[Space]
	[SerializeField]
	private Ease easeType;

	// Token: 0x04000CEF RID: 3311
	[SerializeField]
	private float lerpTime;
}

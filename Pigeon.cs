﻿using System;
using System.Collections;
using FishNet.Object;
using UnityEngine;

// Token: 0x02000064 RID: 100
public class Pigeon : NetworkBehaviour
{
	// Token: 0x0600045C RID: 1116 RVA: 0x0001E6C8 File Offset: 0x0001C8C8
	private void Start()
	{
		this.jumpTimer = global::UnityEngine.Random.Range(1f, 2f);
		if (this.randomRot)
		{
			base.transform.rotation = Quaternion.Euler(0f, (float)global::UnityEngine.Random.Range(0, 360), 0f);
		}
		this.initPos = base.transform.position;
		base.StartCoroutine(this.UpdatePlayer());
	}

	// Token: 0x0600045D RID: 1117 RVA: 0x0001E736 File Offset: 0x0001C936
	private IEnumerator UpdatePlayer()
	{
		foreach (GameObject gameObject in GameObject.FindGameObjectsWithTag("Player"))
		{
			if (gameObject.GetComponent<FirstPersonController>().IsOwner)
			{
				this.player = gameObject;
			}
		}
		yield return new WaitForSeconds(2f);
		base.StartCoroutine(this.UpdatePlayer());
		yield break;
	}

	// Token: 0x0600045E RID: 1118 RVA: 0x0001E745 File Offset: 0x0001C945
	public void Die()
	{
		global::UnityEngine.Object.Instantiate<GameObject>(this.dieVfx, base.transform.position, Quaternion.identity);
		global::UnityEngine.Object.Destroy(base.gameObject);
	}

	// Token: 0x0600045F RID: 1119 RVA: 0x0001E770 File Offset: 0x0001C970
	private void Update()
	{
		if (this.player == null)
		{
			return;
		}
		this.distanceFromPlayer = Vector3.Distance(base.transform.position, this.player.transform.position);
		this.flyTimer -= Time.deltaTime;
		this.walkTimer = Mathf.Sin(Time.time) * (float)global::UnityEngine.Random.Range(1, 1);
		this.anim.SetBool("fly", this.fly);
		if (this.distanceFromPlayer < this.maxDistance && !this.fly)
		{
			SoundManager.Instance.PlaySound(this.flyClip);
			this.flyDir = -(this.player.transform.position - base.transform.position).normalized;
			base.transform.rotation = Quaternion.LookRotation(this.flyDir);
			this.fly = true;
			this.flyTimer = this.flyTime;
		}
		if (this.fly)
		{
			base.transform.position += new Vector3(this.flyDir.x * Time.deltaTime * this.flySpeed, this.verticalMovement * Time.deltaTime, this.flyDir.z * Time.deltaTime * this.flySpeed);
		}
		else if (this.animate)
		{
			this.jumpTimer -= Time.deltaTime;
			if (this.jumpTimer < 0.1f && this.jumpTimer > 0f)
			{
				this.value = (float)global::UnityEngine.Random.Range(-1, 1);
				this.jumpTimer = 0f;
			}
			if (this.jumpTimer < 0f)
			{
				base.transform.Rotate(0f, (this.value > 0f) ? (290f * Time.deltaTime) : (-290f * Time.deltaTime), 0f);
				if (this.jumpTimer > -0.2f)
				{
					base.transform.position += new Vector3(0f, 2f * Time.deltaTime, 0f);
				}
				else if (this.jumpTimer > -0.35f)
				{
					base.transform.position -= new Vector3(0f, 2f * Time.deltaTime, 0f);
				}
				else
				{
					this.jumpTimer = global::UnityEngine.Random.Range(2f, 8f);
				}
			}
		}
		if (this.flyTimer < 0f && Vector3.Distance(this.initPos, this.player.transform.position) > 20f)
		{
			this.fly = false;
			base.transform.position = this.initPos;
		}
	}

	// Token: 0x06000461 RID: 1121 RVA: 0x0001EAA8 File Offset: 0x0001CCA8
	public virtual void NetworkInitialize___Early()
	{
		if (this.NetworkInitializeEarly_Pigeon_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeEarly_Pigeon_Assembly-CSharp.dll = true;
	}

	// Token: 0x06000462 RID: 1122 RVA: 0x0001EABB File Offset: 0x0001CCBB
	public virtual void NetworkInitialize__Late()
	{
		if (this.NetworkInitializeLate_Pigeon_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeLate_Pigeon_Assembly-CSharp.dll = true;
	}

	// Token: 0x06000463 RID: 1123 RVA: 0x0001EACE File Offset: 0x0001CCCE
	public override void NetworkInitializeIfDisabled()
	{
		this.NetworkInitialize___Early();
		this.NetworkInitialize__Late();
	}

	// Token: 0x06000464 RID: 1124 RVA: 0x0001EACE File Offset: 0x0001CCCE
	public virtual void Awake()
	{
		this.NetworkInitialize___Early();
		this.NetworkInitialize__Late();
	}

	// Token: 0x06000465 RID: 1125 RVA: 0x000023D6 File Offset: 0x000005D6
	public virtual void Awake___UserLogic()
	{
	}

	// Token: 0x04000495 RID: 1173
	[SerializeField]
	private GameObject dieVfx;

	// Token: 0x04000496 RID: 1174
	[SerializeField]
	private float distanceFromPlayer;

	// Token: 0x04000497 RID: 1175
	[SerializeField]
	private float maxDistance = 5f;

	// Token: 0x04000498 RID: 1176
	[SerializeField]
	private float flyTime = 20f;

	// Token: 0x04000499 RID: 1177
	[SerializeField]
	private float flySpeed = 2f;

	// Token: 0x0400049A RID: 1178
	[SerializeField]
	private float verticalMovement = 1f;

	// Token: 0x0400049B RID: 1179
	[SerializeField]
	private float flyTimer;

	// Token: 0x0400049C RID: 1180
	[SerializeField]
	private bool randomRot = true;

	// Token: 0x0400049D RID: 1181
	[SerializeField]
	private bool animate = true;

	// Token: 0x0400049E RID: 1182
	[SerializeField]
	private Animator anim;

	// Token: 0x0400049F RID: 1183
	private GameManager gameManager;

	// Token: 0x040004A0 RID: 1184
	private float walkTimer;

	// Token: 0x040004A1 RID: 1185
	private bool fly;

	// Token: 0x040004A2 RID: 1186
	private bool walk;

	// Token: 0x040004A3 RID: 1187
	private Vector3 initPos;

	// Token: 0x040004A4 RID: 1188
	private Vector3 flyDir;

	// Token: 0x040004A5 RID: 1189
	private Vector3 rotation;

	// Token: 0x040004A6 RID: 1190
	private GameObject player;

	// Token: 0x040004A7 RID: 1191
	[SerializeField]
	private float jumpTimer = 3f;

	// Token: 0x040004A8 RID: 1192
	private float value;

	// Token: 0x040004A9 RID: 1193
	[SerializeField]
	private AudioClip flyClip;

	// Token: 0x040004AA RID: 1194
	private bool NetworkInitializeEarly_Pigeon_Assembly-CSharp.dll;

	// Token: 0x040004AB RID: 1195
	private bool NetworkInitializeLate_Pigeon_Assembly-CSharp.dll;
}

﻿using System;
using UnityEngine;

// Token: 0x02000111 RID: 273
public class SetVoiceChatVolume : MonoBehaviour
{
	// Token: 0x06000D86 RID: 3462 RVA: 0x0005BFB5 File Offset: 0x0005A1B5
	private void Start()
	{
		this.settings = Settings.Instance;
		if (base.GetComponent<AudioSource>() != null)
		{
			this.source = base.GetComponent<AudioSource>();
		}
	}

	// Token: 0x06000D87 RID: 3463 RVA: 0x0005BFDC File Offset: 0x0005A1DC
	private void Update()
	{
		if (this.source.volume != this.settings.voiceChatVolume)
		{
			this.source.volume = this.settings.voiceChatVolume;
		}
	}

	// Token: 0x04000BFE RID: 3070
	private Settings settings;

	// Token: 0x04000BFF RID: 3071
	private AudioSource source;
}

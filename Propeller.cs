﻿using System;
using FishNet;
using FishNet.Connection;
using FishNet.Managing;
using FishNet.Object;
using FishNet.Object.Delegating;
using FishNet.Serializing;
using FishNet.Transporting;
using LambdaTheDev.NetworkAudioSync;
using UnityEngine;

// Token: 0x020000A3 RID: 163
public class Propeller : Weapon
{
	// Token: 0x060008E1 RID: 2273 RVA: 0x00041612 File Offset: 0x0003F812
	private void Start()
	{
		this.networkAudioSource = base.GetComponent<NetworkAudioSource>();
	}

	// Token: 0x060008E2 RID: 2274 RVA: 0x00041620 File Offset: 0x0003F820
	private void Update()
	{
		base.WeaponUpdate();
		if (base.gameObject.layer == 7)
		{
			return;
		}
		if (!this.playerController.IsOwner)
		{
			return;
		}
		if (this.playerController.isGrounded)
		{
			this.power = this.maxPower;
		}
		if (this.power <= 0f && this.active3)
		{
			this.AudioStop();
			this.active3 = false;
		}
		if (this.jump.ReadValue<float>() > 0.1f)
		{
			this.Fire();
		}
		else
		{
			this.isflying = false;
		}
		if (this.jump.ReadValue<float>() > 0.1f)
		{
			this.released = false;
		}
		else
		{
			this.pressed = false;
		}
		if (this.jump.ReadValue<float>() > 0.1f && !this.pressed)
		{
			this.pressed = true;
			this.active3 = true;
			this.AudioPlay();
		}
		if (this.jump.ReadValue<float>() < 0.1f && !this.released)
		{
			this.released = true;
			this.AudioStop();
		}
		this.rotateSpeed = Mathf.Lerp(this.rotateSpeed, this.isflying ? this.rotationSpeed : 0f, this.lerpSpeed * Time.deltaTime);
		this.rotativePart.Rotate(this.rotateSpeed, 0f, 0f);
	}

	// Token: 0x060008E3 RID: 2275 RVA: 0x00041770 File Offset: 0x0003F970
	[ServerRpc]
	private void AudioPlay()
	{
		this.RpcWriter___Server_AudioPlay_2166136261();
	}

	// Token: 0x060008E4 RID: 2276 RVA: 0x00041778 File Offset: 0x0003F978
	[ServerRpc]
	private void AudioStop()
	{
		this.RpcWriter___Server_AudioStop_2166136261();
	}

	// Token: 0x060008E5 RID: 2277 RVA: 0x00041780 File Offset: 0x0003F980
	private void Fire()
	{
		if (this.power < 0f)
		{
			this.isflying = false;
		}
		else
		{
			this.isflying = true;
		}
		if (this.power < 0f)
		{
			return;
		}
		if (this.power < 0.1f)
		{
			this.power = -1f;
			this.audio.PlayOneShot(this.propOutClip);
		}
		this.Fly();
	}

	// Token: 0x060008E6 RID: 2278 RVA: 0x000417E8 File Offset: 0x0003F9E8
	private void Fly()
	{
		this.power -= Time.deltaTime;
		if (this.playerController.moveDirection.y < 7f)
		{
			FirstPersonController playerController = this.playerController;
			playerController.moveDirection.y = playerController.moveDirection.y + this.flySpeed * Time.deltaTime;
		}
	}

	// Token: 0x060008E7 RID: 2279 RVA: 0x0004183E File Offset: 0x0003FA3E
	[ObserversRpc(RunLocally = true, ExcludeOwner = true)]
	private void PlayReleaseClipObservers()
	{
		this.RpcWriter___Observers_PlayReleaseClipObservers_2166136261();
		this.RpcLogic___PlayReleaseClipObservers_2166136261();
	}

	// Token: 0x060008E9 RID: 2281 RVA: 0x00041888 File Offset: 0x0003FA88
	public override void NetworkInitialize___Early()
	{
		if (this.NetworkInitializeEarly_Propeller_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeEarly_Propeller_Assembly-CSharp.dll = true;
		base.NetworkInitialize___Early();
		base.RegisterServerRpc(15U, new ServerRpcDelegate(this.RpcReader___Server_AudioPlay_2166136261));
		base.RegisterServerRpc(16U, new ServerRpcDelegate(this.RpcReader___Server_AudioStop_2166136261));
		base.RegisterObserversRpc(17U, new ClientRpcDelegate(this.RpcReader___Observers_PlayReleaseClipObservers_2166136261));
	}

	// Token: 0x060008EA RID: 2282 RVA: 0x000418F1 File Offset: 0x0003FAF1
	public override void NetworkInitialize__Late()
	{
		if (this.NetworkInitializeLate_Propeller_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeLate_Propeller_Assembly-CSharp.dll = true;
		base.NetworkInitialize__Late();
	}

	// Token: 0x060008EB RID: 2283 RVA: 0x0004190A File Offset: 0x0003FB0A
	public override void NetworkInitializeIfDisabled()
	{
		this.NetworkInitialize___Early();
		this.NetworkInitialize__Late();
	}

	// Token: 0x060008EC RID: 2284 RVA: 0x00041918 File Offset: 0x0003FB18
	private void RpcWriter___Server_AudioPlay_2166136261()
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendServerRpc(15U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x060008ED RID: 2285 RVA: 0x00041A0C File Offset: 0x0003FC0C
	private void RpcLogic___AudioPlay_2166136261()
	{
		this.networkAudioSource.Play(0);
	}

	// Token: 0x060008EE RID: 2286 RVA: 0x00041A1C File Offset: 0x0003FC1C
	private void RpcReader___Server_AudioPlay_2166136261(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		this.RpcLogic___AudioPlay_2166136261();
	}

	// Token: 0x060008EF RID: 2287 RVA: 0x00041A50 File Offset: 0x0003FC50
	private void RpcWriter___Server_AudioStop_2166136261()
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendServerRpc(16U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x060008F0 RID: 2288 RVA: 0x00041B44 File Offset: 0x0003FD44
	private void RpcLogic___AudioStop_2166136261()
	{
		this.networkAudioSource.Stop();
		this.PlayReleaseClipObservers();
	}

	// Token: 0x060008F1 RID: 2289 RVA: 0x00041B58 File Offset: 0x0003FD58
	private void RpcReader___Server_AudioStop_2166136261(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		this.RpcLogic___AudioStop_2166136261();
	}

	// Token: 0x060008F2 RID: 2290 RVA: 0x00041B8C File Offset: 0x0003FD8C
	private void RpcWriter___Observers_PlayReleaseClipObservers_2166136261()
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendObserversRpc(17U, writer, channel, DataOrderType.Default, false, false, true);
		writer.Store();
	}

	// Token: 0x060008F3 RID: 2291 RVA: 0x00041C35 File Offset: 0x0003FE35
	private void RpcLogic___PlayReleaseClipObservers_2166136261()
	{
		this.audio.PlayOneShot(this.propOutClip);
	}

	// Token: 0x060008F4 RID: 2292 RVA: 0x00041C48 File Offset: 0x0003FE48
	private void RpcReader___Observers_PlayReleaseClipObservers_2166136261(PooledReader PooledReader0, Channel channel)
	{
		if (!base.IsClient)
		{
			return;
		}
		if (base.IsHost)
		{
			return;
		}
		this.RpcLogic___PlayReleaseClipObservers_2166136261();
	}

	// Token: 0x060008F5 RID: 2293 RVA: 0x00041C72 File Offset: 0x0003FE72
	public override void Awake()
	{
		this.NetworkInitialize___Early();
		base.Awake();
		this.NetworkInitialize__Late();
	}

	// Token: 0x060008F6 RID: 2294 RVA: 0x000023D6 File Offset: 0x000005D6
	public override void Awake___UserLogic()
	{
	}

	// Token: 0x04000824 RID: 2084
	[Header("Weapon Specials")]
	[SerializeField]
	private float flySpeed;

	// Token: 0x04000825 RID: 2085
	[SerializeField]
	private float decelSpeed = 8f;

	// Token: 0x04000826 RID: 2086
	[SerializeField]
	private float maxPower;

	// Token: 0x04000827 RID: 2087
	[SerializeField]
	private AudioClip propOutClip;

	// Token: 0x04000828 RID: 2088
	[SerializeField]
	private Transform rotativePart;

	// Token: 0x04000829 RID: 2089
	[SerializeField]
	private float rotationSpeed = 1000f;

	// Token: 0x0400082A RID: 2090
	[SerializeField]
	private float lerpSpeed = 100f;

	// Token: 0x0400082B RID: 2091
	private float power = 4f;

	// Token: 0x0400082C RID: 2092
	private float rotateSpeed;

	// Token: 0x0400082D RID: 2093
	private NetworkAudioSource networkAudioSource;

	// Token: 0x0400082E RID: 2094
	private bool isflying;

	// Token: 0x0400082F RID: 2095
	private bool active3;

	// Token: 0x04000830 RID: 2096
	private bool active4;

	// Token: 0x04000831 RID: 2097
	private bool pressed;

	// Token: 0x04000832 RID: 2098
	private bool released = true;

	// Token: 0x04000833 RID: 2099
	private bool NetworkInitializeEarly_Propeller_Assembly-CSharp.dll;

	// Token: 0x04000834 RID: 2100
	private bool NetworkInitializeLate_Propeller_Assembly-CSharp.dll;
}

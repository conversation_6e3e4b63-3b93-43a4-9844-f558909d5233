﻿using System;
using UnityEngine;

// Token: 0x02000160 RID: 352
public class CFX_Demo_RotateCamera : MonoBehaviour
{
	// Token: 0x06000F11 RID: 3857 RVA: 0x0006340A File Offset: 0x0006160A
	private void Update()
	{
		if (CFX_Demo_RotateCamera.rotating)
		{
			base.transform.RotateAround(this.rotationCenter.position, Vector3.up, this.speed * Time.deltaTime);
		}
	}

	// Token: 0x04000DCA RID: 3530
	public static bool rotating = true;

	// Token: 0x04000DCB RID: 3531
	public float speed = 30f;

	// Token: 0x04000DCC RID: 3532
	public Transform rotationCenter;
}

﻿using System;
using FishNet;
using UnityEngine;

// Token: 0x02000095 RID: 149
public class HandGrenade : MonoBehaviour
{
	// Token: 0x060007A6 RID: 1958 RVA: 0x00033F42 File Offset: 0x00032142
	private void Awake()
	{
		this.audio = base.GetComponent<AudioSource>();
		this._gravity = this.gravityStart;
	}

	// Token: 0x060007A7 RID: 1959 RVA: 0x00033F5C File Offset: 0x0003215C
	public void Initialize(Vector3 direction, float force, float passedTime, GameObject rootObject, GameObject gun)
	{
		this._direction = direction;
		this._passedTime = passedTime;
		this._rootObject = rootObject;
		this._force = force;
		this._gun = gun;
		this.explosionTimer = this.timeBeforeExplosion;
		base.transform.rotation = Quaternion.LookRotation(-direction);
	}

	// Token: 0x060007A8 RID: 1960 RVA: 0x00033FB0 File Offset: 0x000321B0
	private void Update()
	{
		this.currentPosition = base.transform.position;
		this.safeTimer -= Time.deltaTime;
		this.explosionTimer -= Time.deltaTime;
		this.Move();
		this.HandleCollision();
		this.HandleExplosion();
		this.lastPosition = this.currentPosition;
		this.lastFrameVelocity = this.velocity;
	}

	// Token: 0x060007A9 RID: 1961 RVA: 0x0003401C File Offset: 0x0003221C
	private void Move()
	{
		this.velocity = this.lastPosition - this.currentPosition;
		base.transform.rotation = Quaternion.LookRotation(-this.velocity);
		float deltaTime = Time.deltaTime;
		float num = 0f;
		if (this._passedTime > 0f)
		{
			float num2 = this._passedTime * 0.08f;
			this._passedTime -= num2;
			if (this._passedTime <= deltaTime / 2f)
			{
				num2 += this._passedTime;
				this._passedTime = 0f;
			}
			num = num2;
		}
		if (this.useGravity)
		{
			this._gravity += this.gravity * Time.deltaTime;
		}
		if (this.usePhysics)
		{
			this._force -= this.friction * Time.deltaTime;
			this.rebondDrag -= this.rebondDecel * Time.deltaTime;
		}
		this.rebondDrag = Mathf.Clamp(this.rebondDrag, 0f, 1f);
		this._force = Mathf.Clamp(this._force, 0f, 100f);
		base.transform.position += this._direction * ((this.usePhysics ? this._force : this.MOVE_RATE) * (deltaTime + num));
		if (this.useGravity)
		{
			base.transform.position -= Vector3.up * (this._gravity * (deltaTime + num));
		}
	}

	// Token: 0x060007AA RID: 1962 RVA: 0x000341B4 File Offset: 0x000323B4
	private void HandleCollision()
	{
		RaycastHit raycastHit;
		this.backupRaycast = Physics.Raycast(base.transform.position - base.transform.forward * this.backupRayDistance, base.transform.forward, out raycastHit, this.backupRayLength, this.playerLayer);
		Collider[] array = Physics.OverlapSphere(base.transform.position, this.radius, this.playerLayer);
		if (array.Length != 0 && this.safeTimer < 0f && raycastHit.normal != this.firstNormal)
		{
			bool flag = false;
			for (int i = 0; i < array.Length; i++)
			{
				flag = array[i].gameObject == this._gun;
			}
			bool flag2 = false;
			for (int j = 0; j < array.Length; j++)
			{
				flag2 = array[j].gameObject == this._rootObject;
			}
			if (InstanceFinder.IsClient && !flag && !flag2)
			{
				this.audio.PlayOneShot(this.hitClip);
				global::UnityEngine.Object.Instantiate<GameObject>(this.hitVfx, base.transform.position, Quaternion.identity);
			}
			if (!flag && !flag2)
			{
				this.firstNormal = raycastHit.normal;
				this.secondNormal = this.firstNormal;
				this._direction += raycastHit.normal;
				this._direction = this._direction.normalized * this.rebondFactor * this.rebondDrag;
				this.rebonds -= 1f;
				this._gravity = 1f;
				this.lastVel = this.velocity;
				return;
			}
		}
		else if (this.backupRaycast && this.safeTimer < 0f && this.velocity != this.lastVel)
		{
			this.safeTimer = 0.1f;
			bool flag3 = raycastHit.transform.gameObject == this._gun;
			bool flag4 = raycastHit.transform.gameObject == this._rootObject;
			if (InstanceFinder.IsClient && !flag3 && !flag4)
			{
				SoundManager.Instance.PlaySound(this.hitClip);
				global::UnityEngine.Object.Instantiate<GameObject>(this.hitVfx, base.transform.position, Quaternion.identity);
			}
			if (!flag3 && !flag4)
			{
				this.firstNormal = raycastHit.normal;
				this.secondNormal = this.firstNormal;
				this._direction += raycastHit.normal;
				this._direction = this._direction.normalized * this.rebondFactor * this.rebondDrag;
				this.rebonds -= 1f;
				this._gravity = 0f;
			}
		}
	}

	// Token: 0x060007AB RID: 1963 RVA: 0x000344C4 File Offset: 0x000326C4
	private void HandleExplosion()
	{
		if (this.explosionTimer >= 0f || this.explosionTimer <= -2f)
		{
			return;
		}
		this.explosionTimer = -3f;
		Collider[] array = Physics.OverlapSphere(base.transform.position, this.explosionRadius, this.bodyLayer);
		for (int i = 0; i < array.Length; i++)
		{
			PlayerHealth componentInParent = array[i].GetComponentInParent<PlayerHealth>();
			if (componentInParent && !componentInParent.SyncAccessor_isKilled)
			{
				if (componentInParent.transform.gameObject == this._rootObject)
				{
					Settings.Instance.IncreaseSuicidesAmount();
				}
				else
				{
					Settings.Instance.IncreaseKillsAmount();
				}
				componentInParent.sync___set_value_isKilled(true, true);
				PlayerHealth playerHealth = componentInParent;
				playerHealth.sync___set_value_health(playerHealth.SyncAccessor_health - 10f, true);
				componentInParent.Explode(false, true, componentInParent.gameObject.name, componentInParent.transform.position - base.transform.position, this.ragdollEjectForce, base.transform.position);
				componentInParent.sync___set_value_killer(this._rootObject.transform, true);
			}
		}
		global::UnityEngine.Object.Destroy(base.gameObject);
		global::UnityEngine.Object.Instantiate<GameObject>(this.explosionVfx, base.transform.position, Quaternion.identity);
		SoundManager.Instance.PlaySound(this.explosionClip);
	}

	// Token: 0x040006C9 RID: 1737
	private Vector3 _direction;

	// Token: 0x040006CA RID: 1738
	private float _passedTime;

	// Token: 0x040006CB RID: 1739
	[SerializeField]
	private float MOVE_RATE = 5f;

	// Token: 0x040006CC RID: 1740
	[SerializeField]
	private float radius = 0.2f;

	// Token: 0x040006CD RID: 1741
	[SerializeField]
	private float explosionRadius = 3f;

	// Token: 0x040006CE RID: 1742
	[SerializeField]
	private float rebonds = 2f;

	// Token: 0x040006CF RID: 1743
	[SerializeField]
	private float rebondFactor = 0.6f;

	// Token: 0x040006D0 RID: 1744
	[SerializeField]
	private float rebondDecel = 0.2f;

	// Token: 0x040006D1 RID: 1745
	[SerializeField]
	private float rebondDrag = 1f;

	// Token: 0x040006D2 RID: 1746
	[SerializeField]
	private float ragdollEjectForce = 4f;

	// Token: 0x040006D3 RID: 1747
	[SerializeField]
	private GameObject vfx;

	// Token: 0x040006D4 RID: 1748
	[SerializeField]
	private LayerMask playerLayer;

	// Token: 0x040006D5 RID: 1749
	[SerializeField]
	private LayerMask bodyLayer;

	// Token: 0x040006D6 RID: 1750
	[SerializeField]
	private GameObject hitVfx;

	// Token: 0x040006D7 RID: 1751
	[SerializeField]
	private GameObject explosionVfx;

	// Token: 0x040006D8 RID: 1752
	[SerializeField]
	private AudioClip hitClip;

	// Token: 0x040006D9 RID: 1753
	[SerializeField]
	private AudioClip explosionClip;

	// Token: 0x040006DA RID: 1754
	private GameObject _rootObject;

	// Token: 0x040006DB RID: 1755
	private AudioSource audio;

	// Token: 0x040006DC RID: 1756
	[SerializeField]
	private bool useGravity;

	// Token: 0x040006DD RID: 1757
	[SerializeField]
	private float gravityStart;

	// Token: 0x040006DE RID: 1758
	[SerializeField]
	private float gravity;

	// Token: 0x040006DF RID: 1759
	private float _gravity;

	// Token: 0x040006E0 RID: 1760
	[SerializeField]
	private bool usePhysics;

	// Token: 0x040006E1 RID: 1761
	[SerializeField]
	private float friction;

	// Token: 0x040006E2 RID: 1762
	public float explosionTimer;

	// Token: 0x040006E3 RID: 1763
	[SerializeField]
	private float timeBeforeExplosion = 3f;

	// Token: 0x040006E4 RID: 1764
	private float _force;

	// Token: 0x040006E5 RID: 1765
	private bool backupRaycast;

	// Token: 0x040006E6 RID: 1766
	private bool forwardCast;

	// Token: 0x040006E7 RID: 1767
	private float safeTimer;

	// Token: 0x040006E8 RID: 1768
	private GameObject _gun;

	// Token: 0x040006E9 RID: 1769
	private Vector3 lastFrameVelocity;

	// Token: 0x040006EA RID: 1770
	[HideInInspector]
	public Weapon weapon;

	// Token: 0x040006EB RID: 1771
	private Vector3 currentPosition;

	// Token: 0x040006EC RID: 1772
	private Vector3 lastPosition;

	// Token: 0x040006ED RID: 1773
	private Vector3 velocity;

	// Token: 0x040006EE RID: 1774
	[SerializeField]
	private float backupRayLength = 1.5f;

	// Token: 0x040006EF RID: 1775
	[SerializeField]
	private float backupRayDistance = 1f;

	// Token: 0x040006F0 RID: 1776
	[SerializeField]
	private float forwardCastLength = 1.5f;

	// Token: 0x040006F1 RID: 1777
	private Vector3 firstNormal;

	// Token: 0x040006F2 RID: 1778
	private Vector3 secondNormal;

	// Token: 0x040006F3 RID: 1779
	private Vector3 lastVel;
}

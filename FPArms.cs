﻿using System;
using DG.Tweening;
using DG.Tweening.Core;
using DG.Tweening.Plugins.Options;
using UnityEngine;
using UnityEngine.InputSystem;

// Token: 0x02000025 RID: 37
public class FPArms : MonoBehaviour
{
	// Token: 0x060001A9 RID: 425 RVA: 0x0000F3CC File Offset: 0x0000D5CC
	private void Awake()
	{
		this.playerControls = InputManager.inputActions;
		this.playerControls.Player.MouseX.performed += delegate(InputAction.CallbackContext ctx)
		{
			this.mouseInput.x = ctx.ReadValue<float>();
		};
		this.playerControls.Player.MouseY.performed += delegate(InputAction.CallbackContext ctx)
		{
			this.mouseInput.y = ctx.ReadValue<float>();
		};
		this.pauseManager = PauseManager.Instance;
	}

	// Token: 0x060001AA RID: 426 RVA: 0x0000F438 File Offset: 0x0000D638
	private void OnEnable()
	{
		this.move = this.playerControls.Player.Move;
		this.move.Enable();
		this.lookY = this.playerControls.Player.MouseY;
		this.lookY.Enable();
		this.lookX = this.playerControls.Player.MouseX;
		this.lookX.Enable();
		this.jump = this.playerControls.Player.Jump;
		this.jump.Enable();
		this.jump.performed += this.Jump;
	}

	// Token: 0x060001AB RID: 427 RVA: 0x0000F4EC File Offset: 0x0000D6EC
	private void OnDisable()
	{
		this.move.Disable();
		this.jump.Disable();
		this.lookY.Disable();
		this.lookX.Disable();
		this.jump.performed -= this.Jump;
	}

	// Token: 0x060001AC RID: 428 RVA: 0x0000F53C File Offset: 0x0000D73C
	private void Start()
	{
		this.restPos = base.transform.localPosition;
		this.restRot = base.transform.localRotation;
	}

	// Token: 0x060001AD RID: 429 RVA: 0x0000F560 File Offset: 0x0000D760
	private void Update()
	{
		this.landTimer -= Time.deltaTime;
		if (!this.player.isGrounded && this.jumped && !this.player.isAiming)
		{
			base.transform.localPosition = Vector3.Lerp(base.transform.localPosition, this.restPos + this.jumpOffset, this.jumpBobSpeed * Time.deltaTime);
		}
		else if (!this.player.isGrounded && !Physics.Raycast(this.player.transform.position, Vector3.down, 0.5f) && !this.player.isAiming)
		{
			base.transform.localPosition = Vector3.Lerp(base.transform.localPosition, this.restPos + this.fallOffset, this.fallBobSpeed * Time.deltaTime);
		}
		else if (!this.player.isWalking && !this.player.isSprinting && !this.player.isSliding && this.player.isGrounded && this.player.playerSpeed < 0.3f)
		{
			this.IdleScaleTempTimer += Time.deltaTime;
			float num = Mathf.Sin(this.idleSinSpeed * this.IdleScaleTempTimer) * this.idleScale;
			num = Mathf.Clamp(num, -this.maxIdleScale, this.maxIdleScale);
			this.targetIdlePos = new Vector3(this.restPos.x, this.restPos.y + num, this.restPos.z);
			base.transform.localPosition = Vector3.Lerp(base.transform.localPosition, this.targetIdlePos, this.idleLerpSpeed * Time.deltaTime);
			base.transform.localRotation = Quaternion.Slerp(base.transform.localRotation, Quaternion.Euler(0f, 0f, 0f), this.resetRecoverSpeed * Time.deltaTime);
		}
		else
		{
			base.transform.localPosition = Vector3.Lerp(base.transform.localPosition, this.restPos, this.resetRecoverSpeed * Time.deltaTime);
			base.transform.localRotation = Quaternion.Slerp(base.transform.localRotation, Quaternion.Euler(0f, 0f, 0f), this.resetRecoverSpeed * Time.deltaTime);
		}
		if (!this.player.isGrounded && this.jumped && !this.player.isAiming)
		{
			this.bobPosition.transform.localPosition = Vector3.Lerp(this.bobPosition.transform.localPosition, this.restPos, this.resetRecoverSpeed * Time.deltaTime);
		}
		else if (!this.player.isGrounded && !Physics.Raycast(this.player.transform.position, Vector3.down, 0.5f) && !this.player.isAiming)
		{
			this.bobPosition.transform.localPosition = Vector3.Lerp(this.bobPosition.transform.localPosition, this.restPos, this.resetRecoverSpeed * Time.deltaTime);
		}
		else if (this.move.ReadValue<Vector2>() != Vector2.zero && !this.player.isSliding && this.player.playerSpeed > 1f && this.player.isAiming && (!(this.pauseManager != null) || (!this.pauseManager.pause && (!this.pauseManager.steamPlaying || !this.pauseManager.chatting))))
		{
			this.tempBobScaleTimer += Time.deltaTime;
			this.IdleScaleTempTimer = 0f;
			float num2 = 2f / (3f - Mathf.Cos(2f * Time.time));
			this.bobPosition.transform.localPosition = Vector3.Lerp(this.bobPosition.transform.localPosition, new Vector3(this.restPos.x, this.bobPosition.transform.localPosition.y, this.restPos.z), this.resetRecoverSpeed * Time.deltaTime);
			this.bobPosition.transform.localPosition = new Vector3(this.bobPosition.transform.localPosition.x, this.bobPosition.transform.localPosition.y + ((this.player.isCrouching || this.player.isLeaning) ? this.crouchBobScale : (this.player.isSprinting ? this.sprintBobScale : this.bobScale)) * this.aimBobScale * (num2 * Mathf.Sin(2f * Time.time * (((this.player.isCrouching || this.player.isLeaning) ? this.crouchBobSpeed : (this.player.isSprinting ? this.sprintBobSpeed : this.bobSpeed)) * this.aimBobSpeed)) / 2f * Time.deltaTime), this.bobPosition.transform.localPosition.z);
		}
		else if (this.move.ReadValue<Vector2>() != Vector2.zero && !this.player.isSliding && this.player.playerSpeed > 1f && !this.player.isAiming && (!(this.pauseManager != null) || (!this.pauseManager.pause && (!this.pauseManager.steamPlaying || !this.pauseManager.chatting))))
		{
			if (this.heavy)
			{
				this.tempBobScaleTimer += Time.deltaTime;
				this.IdleScaleTempTimer = 0f;
				float num3 = 2f / (3f - Mathf.Cos(2f * this.tempBobScaleTimer));
				this.bobPosition.transform.localPosition = new Vector3(this.bobPosition.transform.localPosition.x + ((this.player.isCrouching || this.player.isLeaning) ? this.crouchBobScale : (this.player.isSprinting ? this.sprintBobScale : this.bobScale)) / 2f * (num3 * Mathf.Sin(this.tempBobScaleTimer * ((this.player.isCrouching || this.player.isLeaning) ? this.crouchBobSpeed : (this.player.isSprinting ? this.sprintBobSpeed : this.bobSpeed))) * Time.deltaTime), this.bobPosition.transform.localPosition.y + (((this.player.isCrouching || this.player.isLeaning) ? this.crouchBobScale : (this.player.isSprinting ? this.sprintBobScale : this.bobScale)) + 2f) * (num3 * Mathf.Sin(2f * this.tempBobScaleTimer * ((this.player.isCrouching || this.player.isLeaning) ? this.crouchBobSpeed : (this.player.isSprinting ? (this.sprintBobSpeed / 3f) : (this.bobSpeed / 3f)))) / 2f * Time.deltaTime), this.bobPosition.transform.localPosition.z);
			}
			else if (this.vertical)
			{
				this.tempBobScaleTimer += Time.deltaTime * ((this.player.isCrouching || this.player.isLeaning) ? this.crouchBobSpeed : (this.player.isSprinting ? this.sprintBobSpeed : this.bobSpeed)) / 6f;
				this.IdleScaleTempTimer = 0f;
				float num4 = 2f / (3f - Mathf.Cos(2f * this.tempBobScaleTimer));
				this.bobPosition.transform.localPosition = new Vector3(this.bobPosition.transform.localPosition.x, this.bobPosition.transform.localPosition.y + (((this.player.isCrouching || this.player.isLeaning) ? this.crouchBobScale : (this.player.isSprinting ? this.sprintBobScale : (this.bobScale / 10f))) + 1.7f) * (num4 * Mathf.Sin(4f * this.tempBobScaleTimer) / 2f * Time.deltaTime), this.bobPosition.transform.localPosition.z);
			}
			else
			{
				this.tempBobScaleTimer += Time.deltaTime;
				this.IdleScaleTempTimer = 0f;
				float num5 = 2f / (3f - Mathf.Cos(2f * Time.time));
				this.bobPosition.transform.localPosition = new Vector3(this.bobPosition.transform.localPosition.x + ((this.player.isCrouching || this.player.isLeaning) ? this.crouchBobScale : (this.player.isSprinting ? this.sprintBobScale : this.bobScale)) * (num5 * Mathf.Cos(Time.time * ((this.player.isCrouching || this.player.isLeaning) ? this.crouchBobSpeed : (this.player.isSprinting ? this.sprintBobSpeed : this.bobSpeed))) * Time.deltaTime), this.bobPosition.transform.localPosition.y + ((this.player.isCrouching || this.player.isLeaning) ? this.crouchBobScale : (this.player.isSprinting ? this.sprintBobScale : this.bobScale)) * (num5 * Mathf.Sin(2f * Time.time * ((this.player.isCrouching || this.player.isLeaning) ? this.crouchBobSpeed : (this.player.isSprinting ? this.sprintBobSpeed : this.bobSpeed))) / 2f * Time.deltaTime), this.bobPosition.transform.localPosition.z);
			}
		}
		else
		{
			this.bobPosition.transform.localPosition = Vector3.Lerp(this.bobPosition.transform.localPosition, this.restPos, this.resetRecoverSpeed * Time.deltaTime);
		}
		if ((this.landTimer < 0f || this.jumped) && this.player.isGrounded)
		{
			this.IdleScaleTempTimer = 0f;
			base.transform.DOLocalMove(this.restPos, this.jumped ? 0.2f : 0.3f, false).SetEase(Ease.OutBack);
		}
		if (this.player.isGrounded && this.jumped)
		{
			this.jumped = false;
		}
		if (this.player.isGrounded)
		{
			this.landTimer = 0.15f;
		}
		if (!(this.pauseManager != null) || (!this.pauseManager.pause && !this.pauseManager.chatting))
		{
			this.TiltSway((this.player.wallSlideLean > 1f || this.player.wallSlideLean < -1f) ? Quaternion.Euler(0f, 0f, this.player.wallSlideLean) : this.restRot, base.transform);
		}
	}

	// Token: 0x060001AE RID: 430 RVA: 0x000101B9 File Offset: 0x0000E3B9
	private void Jump(InputAction.CallbackContext ctx)
	{
		if (Physics.Raycast(this.player.transform.position, Vector3.down, 1.3f))
		{
			this.jumped = true;
		}
	}

	// Token: 0x060001AF RID: 431 RVA: 0x000101E4 File Offset: 0x0000E3E4
	private void WeaponSway(Vector3 initialPosition, Transform member)
	{
		this.InputX = -Input.GetAxis("Mouse X");
		this.InputY = -Input.GetAxis("Mouse Y");
		float num = Mathf.Clamp(this.InputX * this.amount, -this.maxAmount, this.maxAmount);
		float num2 = Mathf.Clamp(this.InputY * this.amount, -this.maxAmount, this.maxAmount);
		Vector3 vector = new Vector3(num, num2, 0f);
		member.position = Vector3.Lerp(member.localPosition, initialPosition + vector, Time.deltaTime * this.smoothAmount);
	}

	// Token: 0x060001B0 RID: 432 RVA: 0x00010288 File Offset: 0x0000E488
	private void TiltSway(Quaternion initialRotation, Transform member)
	{
		float num = Mathf.Clamp(-Input.GetAxis("Mouse X") * this.rotationAmount, -this.maxRotationAmount, this.maxRotationAmount);
		float num2 = Mathf.Clamp(Input.GetAxis("Mouse Y") * this.rotationAmount, -this.maxRotationAmount, this.maxRotationAmount);
		Quaternion quaternion = Quaternion.Euler(new Vector3(this.rotationX ? num2 : 0f, this.rotationY ? num : 0f, this.rotationZ ? num : 0f));
		member.localRotation = Quaternion.Slerp(member.localRotation, quaternion * initialRotation, Time.deltaTime * this.smoothRotation);
	}

	// Token: 0x04000232 RID: 562
	[Header("Jump Animation")]
	[SerializeField]
	private Vector3 jumpOffset;

	// Token: 0x04000233 RID: 563
	[SerializeField]
	private Vector3 fallOffset;

	// Token: 0x04000234 RID: 564
	[SerializeField]
	private float jumpBobSpeed = 0.1f;

	// Token: 0x04000235 RID: 565
	[SerializeField]
	private float fallBobSpeed = 1f;

	// Token: 0x04000236 RID: 566
	[Header("Movement Animation")]
	[SerializeField]
	private Transform bobPosition;

	// Token: 0x04000237 RID: 567
	[SerializeField]
	private float bobScale = 0.3f;

	// Token: 0x04000238 RID: 568
	[SerializeField]
	private float bobSpeed = 4f;

	// Token: 0x04000239 RID: 569
	[SerializeField]
	private float crouchBobScale = 0.7f;

	// Token: 0x0400023A RID: 570
	[SerializeField]
	private float crouchBobSpeed = 10f;

	// Token: 0x0400023B RID: 571
	[SerializeField]
	private float sprintBobScale = 0.1f;

	// Token: 0x0400023C RID: 572
	[SerializeField]
	private float sprintBobSpeed = 2f;

	// Token: 0x0400023D RID: 573
	[SerializeField]
	private float aimBobScale = 0.4f;

	// Token: 0x0400023E RID: 574
	[SerializeField]
	private float aimBobSpeed = 0.5f;

	// Token: 0x0400023F RID: 575
	[SerializeField]
	private float resetRecoverSpeed = 3f;

	// Token: 0x04000240 RID: 576
	private Vector3 restPos;

	// Token: 0x04000241 RID: 577
	private Quaternion restRot;

	// Token: 0x04000242 RID: 578
	private Vector3 targetIdlePos;

	// Token: 0x04000243 RID: 579
	private float IdleScaleTempTimer;

	// Token: 0x04000244 RID: 580
	private float tempBobScaleTimer;

	// Token: 0x04000245 RID: 581
	[Header("Idle Animation")]
	[SerializeField]
	private float idleSinSpeed = 0.25f;

	// Token: 0x04000246 RID: 582
	[SerializeField]
	private float idleScale = 0.7f;

	// Token: 0x04000247 RID: 583
	[SerializeField]
	private float idleLerpSpeed = 10f;

	// Token: 0x04000248 RID: 584
	[SerializeField]
	private float maxIdleScale = 0.3f;

	// Token: 0x04000249 RID: 585
	[Header("Members")]
	[SerializeField]
	private Animator animator;

	// Token: 0x0400024A RID: 586
	public PlayerControls playerControls;

	// Token: 0x0400024B RID: 587
	private InputAction move;

	// Token: 0x0400024C RID: 588
	private InputAction jump;

	// Token: 0x0400024D RID: 589
	private InputAction run;

	// Token: 0x0400024E RID: 590
	private InputAction lookY;

	// Token: 0x0400024F RID: 591
	private InputAction lookX;

	// Token: 0x04000250 RID: 592
	private InputAction zoom;

	// Token: 0x04000251 RID: 593
	private InputAction crouch;

	// Token: 0x04000252 RID: 594
	private Vector2 mouseInput;

	// Token: 0x04000253 RID: 595
	private bool jumped;

	// Token: 0x04000254 RID: 596
	[HideInInspector]
	public bool heavy;

	// Token: 0x04000255 RID: 597
	[HideInInspector]
	public bool vertical;

	// Token: 0x04000256 RID: 598
	private float landTimer;

	// Token: 0x04000257 RID: 599
	[SerializeField]
	private FirstPersonController player;

	// Token: 0x04000258 RID: 600
	private PauseManager pauseManager;

	// Token: 0x04000259 RID: 601
	[Header("Position")]
	public float amount = 0.02f;

	// Token: 0x0400025A RID: 602
	public float maxAmount = 0.06f;

	// Token: 0x0400025B RID: 603
	public float smoothAmount = 6f;

	// Token: 0x0400025C RID: 604
	[Header("Rotation")]
	public float rotationAmount = 4f;

	// Token: 0x0400025D RID: 605
	public float maxRotationAmount = 5f;

	// Token: 0x0400025E RID: 606
	public float smoothRotation = 12f;

	// Token: 0x0400025F RID: 607
	[Space]
	public bool rotationX = true;

	// Token: 0x04000260 RID: 608
	public bool rotationY = true;

	// Token: 0x04000261 RID: 609
	public bool rotationZ = true;

	// Token: 0x04000262 RID: 610
	private float InputX;

	// Token: 0x04000263 RID: 611
	private float InputY;
}

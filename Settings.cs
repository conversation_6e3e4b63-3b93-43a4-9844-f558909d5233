﻿using System;
using System.Collections.Generic;
using HeathenEngineering.SteamworksIntegration;
using HeathenEngineering.SteamworksIntegration.API;
using Newtonsoft.Json.Linq;
using TMPro;
using UnityEngine;
using UnityEngine.SceneManagement;
using UnityEngine.UI;

// Token: 0x0200010D RID: 269
public class Settings : MonoBehaviour, ISaveable
{
	// Token: 0x06000D43 RID: 3395 RVA: 0x00059F54 File Offset: 0x00058154
	private void Awake()
	{
		if (Settings.Instance == null)
		{
			Settings.Instance = this;
			global::UnityEngine.Object.DontDestroyOnLoad(base.gameObject);
		}
		else
		{
			global::UnityEngine.Object.Destroy(base.gameObject);
		}
		if (PlayerPrefs.GetInt("firstTimeSave") != 256)
		{
			this.FirstTimeSetup();
		}
	}

	// Token: 0x06000D44 RID: 3396 RVA: 0x00059FA4 File Offset: 0x000581A4
	public void AddPlayerToHistory(ClientInstance player)
	{
		string text = string.Concat(new string[]
		{
			player.PlayerName,
			" (SteamID : ",
			player.PlayerSteamID.ToString(),
			") (Time : ",
			DateTime.Now.ToString(),
			")"
		});
		this.recentlyPlayedWithPlayers.Insert(0, text);
	}

	// Token: 0x06000D45 RID: 3397 RVA: 0x0005A00C File Offset: 0x0005820C
	public void CopyMatchHistory()
	{
		string text = "PLAYERNAME | STEAMID | DATE OF PLAY" + Environment.NewLine + Environment.NewLine;
		for (int i = 0; i < this.recentlyPlayedWithPlayers.Count; i++)
		{
			text = string.Concat(new string[]
			{
				text,
				i.ToString(),
				": ",
				this.recentlyPlayedWithPlayers[i],
				Environment.NewLine
			});
		}
		PauseManager.Instance.WriteOfflineLog("Match history copied to clipboard");
		GUIUtility.systemCopyBuffer = text;
	}

	// Token: 0x06000D46 RID: 3398 RVA: 0x0005A092 File Offset: 0x00058292
	private void Start()
	{
		this.mapsManager = MapsManager.Instance;
		this.FirstTimeSetup();
	}

	// Token: 0x06000D47 RID: 3399 RVA: 0x0005A0A8 File Offset: 0x000582A8
	private void FirstTimeSetup()
	{
		if (PlayerPrefs.HasKey("toggleTwoAxis"))
		{
			this.toggleTwoAxis = PlayerPrefs.GetInt("toggleTwoAxis") == 1;
		}
		if (PlayerPrefs.HasKey("invertMouseX"))
		{
			this.invertMouseX = PlayerPrefs.GetInt("invertMouseX") == 1;
		}
		if (PlayerPrefs.HasKey("invertMouseY"))
		{
			this.invertMouseY = PlayerPrefs.GetInt("invertMouseY") == 1;
		}
		if (PlayerPrefs.HasKey("sprintToggle"))
		{
			this.sprintToggle = PlayerPrefs.GetInt("sprintToggle") == 1;
		}
		if (PlayerPrefs.HasKey("aimToggle"))
		{
			this.aimToggle = PlayerPrefs.GetInt("aimToggle") == 1;
		}
		if (PlayerPrefs.HasKey("leanToggle"))
		{
			this.leanToggle = PlayerPrefs.GetInt("leanToggle") == 1;
		}
		if (PlayerPrefs.HasKey("crouchToggle"))
		{
			this.crouchToggle = PlayerPrefs.GetInt("crouchToggle") == 1;
		}
		if (PlayerPrefs.HasKey("reverseSprintBind"))
		{
			this.reverseSprintBind = PlayerPrefs.GetInt("reverseSprintBind") == 1;
		}
		if (PlayerPrefs.HasKey("isFullscreen"))
		{
			this.isFullscreen = PlayerPrefs.GetInt("isFullscreen") == 1;
		}
		if (PlayerPrefs.HasKey("enableVoiceChat"))
		{
			this.enableVoiceChat = PlayerPrefs.GetInt("enableVoiceChat") == 1;
		}
		if (PlayerPrefs.HasKey("minimalistUi"))
		{
			this.minimalistUi = PlayerPrefs.GetInt("minimalistUi") == 1;
		}
		if (PlayerPrefs.HasKey("motionBlur"))
		{
			this.motionBlur = PlayerPrefs.GetInt("motionBlur") == 1;
		}
		if (PlayerPrefs.HasKey("inverseFireBinding"))
		{
			this.inverseFireBinding = PlayerPrefs.GetInt("inverseFireBinding") == 1;
		}
		if (PlayerPrefs.HasKey("mouseSensitivity"))
		{
			this.mouseSensitivity = PlayerPrefs.GetFloat("mouseSensitivity");
		}
		if (PlayerPrefs.HasKey("mouseAimScopeSensitivity"))
		{
			this.mouseAimScopeSensitivity = PlayerPrefs.GetFloat("mouseAimScopeSensitivity");
		}
		if (PlayerPrefs.HasKey("mouseAimSensitivity"))
		{
			this.mouseAimSensitivity = PlayerPrefs.GetFloat("mouseAimSensitivity");
		}
		if (PlayerPrefs.HasKey("mouseXSensitivity"))
		{
			this.horizontalSensitivity = PlayerPrefs.GetFloat("mouseXSensitivity");
		}
		if (PlayerPrefs.HasKey("mouseYSensitivity"))
		{
			this.verticalSensitivity = PlayerPrefs.GetFloat("mouseYSensitivity");
		}
		if (PlayerPrefs.HasKey("fovValue"))
		{
			this.fovValue = PlayerPrefs.GetFloat("fovValue");
		}
		if (PlayerPrefs.HasKey("brightness"))
		{
			this.brightness = PlayerPrefs.GetFloat("brightness");
		}
		if (PlayerPrefs.HasKey("damageIntensity"))
		{
			this.damageIntensity = PlayerPrefs.GetFloat("damageIntensity");
		}
		if (PlayerPrefs.HasKey("voiceChatVolume"))
		{
			this.voiceChatVolume = PlayerPrefs.GetFloat("voiceChatVolume");
		}
		if (PlayerPrefs.HasKey("qualitySetting"))
		{
			this.qualitySetting = PlayerPrefs.GetInt("qualitySetting");
		}
		if (PlayerPrefs.HasKey("targetFps"))
		{
			this.targetFps = PlayerPrefs.GetInt("targetFps", 120);
		}
		if (PlayerPrefs.HasKey("targetFpsToggle"))
		{
			this.targetFpsToggle = PlayerPrefs.GetInt("targetFpsToggle") == 1;
		}
		if (PlayerPrefs.HasKey("resolution"))
		{
			this.resolution = PlayerPrefs.GetInt("resolution");
		}
		if (PlayerPrefs.HasKey("useVsync"))
		{
			this.useVsync = PlayerPrefs.GetInt("useVsync", 0);
		}
		if (PlayerPrefs.HasKey("exclusiveFullscreen"))
		{
			this.exclusiveFullscreen = PlayerPrefs.GetInt("exclusiveFullscreen") == 1;
		}
		if (PlayerPrefs.HasKey("pushToTalk"))
		{
			this.pushToTalk = PlayerPrefs.GetInt("pushToTalk") == 1;
		}
		if (PlayerPrefs.HasKey("reduceVFX"))
		{
			this.reduceVFX = PlayerPrefs.GetInt("reduceVFX", 0) == 1;
		}
		if (PlayerPrefs.HasKey("inGameMusic"))
		{
			this.inGameMusic = PlayerPrefs.GetInt("inGameMusic") == 1;
		}
		if (PlayerPrefs.HasKey("enableFixedCrosshair"))
		{
			this.enableFixedCrosshair = PlayerPrefs.GetInt("enableFixedCrosshair") == 1;
		}
		if (PlayerPrefs.HasKey("showSpeedometer"))
		{
			this.showSpeedometer = PlayerPrefs.GetInt("showSpeedometer") == 1;
		}
		if (PlayerPrefs.HasKey("disableCrosshair"))
		{
			this.disableCrosshair = PlayerPrefs.GetInt("disableCrosshair") == 1;
		}
		if (PlayerPrefs.GetInt("firstTimeSave") == 256)
		{
			AudioListener.volume = PlayerPrefs.GetFloat("masterVolume");
			SoundManager.Instance._effectsSource.volume = PlayerPrefs.GetFloat("effectsVolume");
			SoundManager.Instance._musicSource.volume = PlayerPrefs.GetFloat("musicVolume");
			SoundManager.Instance._ambientSource.volume = PlayerPrefs.GetFloat("ambientVolume");
			SoundManager.Instance.menuMusicVolume = PlayerPrefs.GetFloat("menuMusicVolume");
		}
		if (!PlayerPrefs.HasKey("toggleTwoAxis"))
		{
			PlayerPrefs.SetInt("toggleTwoAxis", this.toggleTwoAxis ? 1 : 0);
		}
		if (!PlayerPrefs.HasKey("invertMouseX"))
		{
			PlayerPrefs.SetInt("invertMouseX", this.invertMouseX ? 1 : 0);
		}
		if (!PlayerPrefs.HasKey("invertMouseY"))
		{
			PlayerPrefs.SetInt("invertMouseY", this.invertMouseY ? 1 : 0);
		}
		if (!PlayerPrefs.HasKey("sprintToggle"))
		{
			PlayerPrefs.SetInt("sprintToggle", this.sprintToggle ? 1 : 0);
		}
		if (!PlayerPrefs.HasKey("aimToggle"))
		{
			PlayerPrefs.SetInt("aimToggle", this.aimToggle ? 1 : 0);
		}
		if (!PlayerPrefs.HasKey("leanToggle"))
		{
			PlayerPrefs.SetInt("leanToggle", this.leanToggle ? 1 : 0);
		}
		if (!PlayerPrefs.HasKey("crouchToggle"))
		{
			PlayerPrefs.SetInt("crouchToggle", this.crouchToggle ? 1 : 0);
		}
		if (!PlayerPrefs.HasKey("reverseSprintBind"))
		{
			PlayerPrefs.SetInt("reverseSprintBind", this.reverseSprintBind ? 1 : 0);
		}
		if (!PlayerPrefs.HasKey("isFullscreen"))
		{
			PlayerPrefs.SetInt("isFullscreen", this.isFullscreen ? 1 : 0);
		}
		if (!PlayerPrefs.HasKey("enableVoiceChat"))
		{
			PlayerPrefs.SetInt("enableVoiceChat", this.enableVoiceChat ? 1 : 0);
		}
		if (!PlayerPrefs.HasKey("minimalistUi"))
		{
			PlayerPrefs.SetInt("minimalistUi", this.minimalistUi ? 1 : 0);
		}
		if (!PlayerPrefs.HasKey("motionBlur"))
		{
			PlayerPrefs.SetInt("motionBlur", this.motionBlur ? 1 : 0);
		}
		if (!PlayerPrefs.HasKey("inverseFireBinding"))
		{
			PlayerPrefs.SetInt("inverseFireBinding", this.inverseFireBinding ? 1 : 0);
		}
		if (!PlayerPrefs.HasKey("mouseSensitivity"))
		{
			PlayerPrefs.SetFloat("mouseSensitivity", this.mouseSensitivity);
		}
		if (!PlayerPrefs.HasKey("mouseAimScopeSensitivity"))
		{
			PlayerPrefs.SetFloat("mouseAimScopeSensitivity", this.mouseAimScopeSensitivity);
		}
		if (!PlayerPrefs.HasKey("mouseAimSensitivity"))
		{
			PlayerPrefs.SetFloat("mouseAimSensitivity", this.mouseSensitivity);
		}
		if (!PlayerPrefs.HasKey("mouseXSensitivity"))
		{
			PlayerPrefs.SetFloat("mouseXSensitivity", this.horizontalSensitivity);
		}
		if (!PlayerPrefs.HasKey("mouseYSensitivity"))
		{
			PlayerPrefs.SetFloat("mouseYSensitivity", this.verticalSensitivity);
		}
		if (!PlayerPrefs.HasKey("fovValue"))
		{
			PlayerPrefs.SetFloat("fovValue", this.fovValue);
		}
		if (!PlayerPrefs.HasKey("brightness"))
		{
			PlayerPrefs.SetFloat("brightness", this.brightness);
		}
		if (!PlayerPrefs.HasKey("damageIntensity"))
		{
			PlayerPrefs.SetFloat("damageIntensity", this.damageIntensity);
		}
		if (!PlayerPrefs.HasKey("voiceChatVolume"))
		{
			PlayerPrefs.SetFloat("voiceChatVolume", this.voiceChatVolume);
		}
		if (!PlayerPrefs.HasKey("qualitySetting"))
		{
			PlayerPrefs.SetInt("qualitySetting", 3);
		}
		if (!PlayerPrefs.HasKey("targetFps"))
		{
			PlayerPrefs.SetInt("targetFps", 120);
		}
		if (!PlayerPrefs.HasKey("targetFpsToggle"))
		{
			PlayerPrefs.SetInt("targetFpsToggle", this.targetFpsToggle ? 1 : 0);
		}
		if (!PlayerPrefs.HasKey("resolution"))
		{
			PlayerPrefs.SetInt("resolution", 0);
		}
		if (!PlayerPrefs.HasKey("useVsync"))
		{
			PlayerPrefs.SetInt("useVsync", 0);
		}
		if (!PlayerPrefs.HasKey("exclusiveFullscreen"))
		{
			PlayerPrefs.SetInt("exclusiveFullscreen", this.exclusiveFullscreen ? 1 : 0);
		}
		if (!PlayerPrefs.HasKey("pushToTalk"))
		{
			PlayerPrefs.SetInt("pushToTalk", this.pushToTalk ? 1 : 0);
		}
		if (!PlayerPrefs.HasKey("reduceVFX"))
		{
			PlayerPrefs.SetInt("reduceVFX", this.reduceVFX ? 1 : 0);
		}
		if (!PlayerPrefs.HasKey("inGameMusic"))
		{
			PlayerPrefs.SetInt("inGameMusic", this.inGameMusic ? 1 : 0);
		}
		if (!PlayerPrefs.HasKey("enableFixedCrosshair"))
		{
			PlayerPrefs.SetInt("enableFixedCrosshair", this.enableFixedCrosshair ? 1 : 0);
		}
		if (!PlayerPrefs.HasKey("showSpeedometer"))
		{
			PlayerPrefs.SetInt("showSpeedometer", this.showSpeedometer ? 1 : 0);
		}
		if (!PlayerPrefs.HasKey("disableCrosshair"))
		{
			PlayerPrefs.SetInt("disableCrosshair", this.disableCrosshair ? 1 : 0);
		}
		if (PlayerPrefs.GetInt("firstTimeSave") != 256)
		{
			PlayerPrefs.SetInt("firstTimeSave", 256);
			PlayerPrefs.SetFloat("masterVolume", 1f);
			PlayerPrefs.SetFloat("effectsVolume", 1f);
			PlayerPrefs.SetFloat("musicVolume", 1f);
			PlayerPrefs.SetFloat("ambientVolume", 1f);
			PlayerPrefs.SetFloat("menuMusicVolume", 0.6f);
		}
		PlayerPrefs.Save();
		Screen.fullScreenMode = (this.exclusiveFullscreen ? FullScreenMode.ExclusiveFullScreen : FullScreenMode.FullScreenWindow);
		if (this.targetFpsToggle)
		{
			if (Application.targetFrameRate != this.targetFps && this.targetFps > 30)
			{
				Application.targetFrameRate = this.targetFps;
				return;
			}
		}
		else
		{
			Application.targetFrameRate = -1;
		}
	}

	// Token: 0x06000D48 RID: 3400 RVA: 0x0005AA54 File Offset: 0x00058C54
	private void Update()
	{
		if (Screen.fullScreen != this.isFullscreen)
		{
			Screen.fullScreen = this.isFullscreen;
		}
		if (QualitySettings.vSyncCount != this.useVsync)
		{
			QualitySettings.vSyncCount = this.useVsync;
		}
		this.SteamAchievementsCheck();
		if (SceneManager.GetActiveScene().name == "MainMenu")
		{
			this.tauntsAmountText.text = "taunts : " + this.tauntsAmount.ToString();
			this.killsAmountText.text = "kills : " + this.killsAmount.ToString();
			this.deathsAmountText.text = "deaths : " + this.deathsAmount.ToString();
			this.suicidesAmountText.text = "suicides : " + this.suicidesAmount.ToString();
			this.bodyshotsAmountText.text = "bodyshots : " + this.bodyshotsAmount.ToString();
			this.headshotsAmountText.text = "headshots : " + this.headshotsAmount.ToString();
			this.timeSpentInGameText.text = "time spent in game : " + string.Concat(new string[]
			{
				Mathf.Floor(this.timeSpentInGame / 3600f).ToString(),
				":",
				((int)(this.timeSpentInGame / 60f % 60f)).ToString(),
				":",
				((int)(this.timeSpentInGame % 60f)).ToString()
			}).ToString() + " hours";
			this.timeSpentInAirText.text = "time spent in air : " + string.Concat(new string[]
			{
				((int)Mathf.Floor(this.timeSpentInAir / 3600f)).ToString(),
				":",
				((int)(this.timeSpentInAir / 60f % 60f)).ToString(),
				":",
				((int)(this.timeSpentInAir % 60f)).ToString()
			}).ToString() + " hours";
			this.timeSpentOnGroundText.text = "time spent on ground : " + string.Concat(new string[]
			{
				((int)Mathf.Floor(this.timeSpentOnGround / 3600f)).ToString(),
				":",
				((int)(this.timeSpentOnGround / 60f % 60f)).ToString(),
				":",
				((int)(this.timeSpentOnGround % 60f)).ToString()
			}).ToString() + " hours";
			this.roundsWonText.text = "rounds won : " + this.roundsWon.ToString();
			this.roundsLostText.text = "rounds lost : " + this.roundsLost.ToString();
			this.roundsPlayedText.text = "rounds played : " + this.roundsPlayed.ToString();
			this.gamesLostText.text = "games lost : " + this.gamesLost.ToString();
			this.gamesWonText.text = "games won : " + this.gamesWon.ToString();
			this.gamesPlayedText.text = "games played : " + this.gamesPlayed.ToString();
			this.mapsUnlockedText.text = "maps unlocked : " + this.mapsManager.unlockedMaps.Length.ToString() + "/" + this.mapsManager.allMaps.Length.ToString();
		}
		if (this.localPlayer == null)
		{
			return;
		}
		if (this.toggleTwoAxis)
		{
			this.localPlayer.lookSpeedX = this.horizontalSensitivity;
			this.localPlayer.lookSpeedY = this.verticalSensitivity;
		}
		else
		{
			this.localPlayer.lookSpeedX = this.mouseSensitivity;
			this.localPlayer.lookSpeedY = this.mouseSensitivity;
			this.localPlayer.lookSpeedAim = this.mouseAimScopeSensitivity;
			this.localPlayer.lookSpeedAimNoScope = this.mouseAimSensitivity;
		}
		this.localPlayer.sprintToggle = this.sprintToggle;
		this.localPlayer.aimToggle = this.aimToggle;
		this.localPlayer.leanToggle = this.leanToggle;
		this.localPlayer.invertX = this.invertMouseX;
		this.localPlayer.invertY = this.invertMouseY;
		this.localPlayer.crouchToggle = this.crouchToggle;
		this.localPlayer.reverseSprintBind = this.reverseSprintBind;
	}

	// Token: 0x06000D49 RID: 3401 RVA: 0x0005AF14 File Offset: 0x00059114
	public void UpdateElo()
	{
		int num = (int)(this.gamesWon * 4f + this.roundsWon);
		if (num > 150000)
		{
			this.leaderboardManager.ForceScore(0);
			return;
		}
		this.leaderboardManager.UploadScore(num);
	}

	// Token: 0x06000D4A RID: 3402 RVA: 0x0005AF57 File Offset: 0x00059157
	public void ChangeVoiceChatVolume(Slider _slider)
	{
		this.voiceChatVolume = _slider.value;
		PlayerPrefs.SetFloat("voiceChatVolume", this.voiceChatVolume);
		PlayerPrefs.Save();
	}

	// Token: 0x06000D4B RID: 3403 RVA: 0x0005AF7A File Offset: 0x0005917A
	public void ChangeMouseSensitivity(Slider _slider)
	{
		this.mouseSensitivity = _slider.value;
		PlayerPrefs.SetFloat("mouseSensitivity", this.mouseSensitivity);
		PlayerPrefs.Save();
	}

	// Token: 0x06000D4C RID: 3404 RVA: 0x0005AF9D File Offset: 0x0005919D
	public void ChangeAimScopeMouseSensitivity(Slider _slider)
	{
		this.mouseAimScopeSensitivity = _slider.value;
		PlayerPrefs.SetFloat("mouseAimScopeSensitivity", this.mouseAimScopeSensitivity);
		PlayerPrefs.Save();
	}

	// Token: 0x06000D4D RID: 3405 RVA: 0x0005AFC0 File Offset: 0x000591C0
	public void ChangeAimMouseSensitivity(Slider _slider)
	{
		this.mouseAimSensitivity = _slider.value;
		PlayerPrefs.SetFloat("mouseAimSensitivity", this.mouseAimSensitivity);
		PlayerPrefs.Save();
	}

	// Token: 0x06000D4E RID: 3406 RVA: 0x0005AFE3 File Offset: 0x000591E3
	public void ChangeMouseXSensitivity(Slider _slider)
	{
		this.horizontalSensitivity = _slider.value;
		PlayerPrefs.SetFloat("mouseXSensitivity", this.horizontalSensitivity);
		PlayerPrefs.Save();
	}

	// Token: 0x06000D4F RID: 3407 RVA: 0x0005B006 File Offset: 0x00059206
	public void ChangeMouseYSensitivity(Slider _slider)
	{
		this.verticalSensitivity = _slider.value;
		PlayerPrefs.SetFloat("mouseYSensitivity", this.verticalSensitivity);
		PlayerPrefs.Save();
	}

	// Token: 0x06000D50 RID: 3408 RVA: 0x0005B029 File Offset: 0x00059229
	public void ChangeFov(Slider _slider)
	{
		this.fovValue = _slider.value;
		PlayerPrefs.SetFloat("fovValue", this.fovValue);
		PlayerPrefs.Save();
	}

	// Token: 0x06000D51 RID: 3409 RVA: 0x0005B04C File Offset: 0x0005924C
	public void ChangeBrightness(Slider _slider)
	{
		this.brightness = _slider.value;
		PlayerPrefs.SetFloat("brightness", this.brightness);
		PlayerPrefs.Save();
	}

	// Token: 0x06000D52 RID: 3410 RVA: 0x0005B06F File Offset: 0x0005926F
	public void ChangeDamageIntensity(Slider _slider)
	{
		this.damageIntensity = _slider.value;
		PlayerPrefs.SetFloat("damageIntensity", this.damageIntensity);
		PlayerPrefs.Save();
	}

	// Token: 0x06000D53 RID: 3411 RVA: 0x0005B092 File Offset: 0x00059292
	public void ToggleTwoAxisMouse(Toggle _toggle)
	{
		this.toggleTwoAxis = _toggle.isOn;
		PlayerPrefs.SetInt("toggleTwoAxis", this.toggleTwoAxis ? 1 : 0);
		PlayerPrefs.Save();
	}

	// Token: 0x06000D54 RID: 3412 RVA: 0x0005B0BB File Offset: 0x000592BB
	public void ToggleVerticalInvert(Toggle _toggle)
	{
		this.invertMouseY = _toggle.isOn;
		PlayerPrefs.SetInt("invertMouseY", this.invertMouseY ? 1 : 0);
		PlayerPrefs.Save();
	}

	// Token: 0x06000D55 RID: 3413 RVA: 0x0005B0E4 File Offset: 0x000592E4
	public void ToggleHorizontalInvert(Toggle _toggle)
	{
		this.invertMouseX = _toggle.isOn;
		PlayerPrefs.SetInt("invertMouseX", this.invertMouseX ? 1 : 0);
		PlayerPrefs.Save();
	}

	// Token: 0x06000D56 RID: 3414 RVA: 0x0005B10D File Offset: 0x0005930D
	public void ToggleSprint(Toggle _toggle)
	{
		this.sprintToggle = _toggle.isOn;
		PlayerPrefs.SetInt("sprintToggle", this.sprintToggle ? 1 : 0);
		PlayerPrefs.Save();
	}

	// Token: 0x06000D57 RID: 3415 RVA: 0x0005B136 File Offset: 0x00059336
	public void ToggleAim(Toggle _toggle)
	{
		this.aimToggle = _toggle.isOn;
		PlayerPrefs.SetInt("aimToggle", this.aimToggle ? 1 : 0);
		PlayerPrefs.Save();
	}

	// Token: 0x06000D58 RID: 3416 RVA: 0x0005B15F File Offset: 0x0005935F
	public void ToggleLean(Toggle _toggle)
	{
		this.leanToggle = _toggle.isOn;
		PlayerPrefs.SetInt("leanToggle", this.leanToggle ? 1 : 0);
		PlayerPrefs.Save();
	}

	// Token: 0x06000D59 RID: 3417 RVA: 0x0005B188 File Offset: 0x00059388
	public void ToggleCrouch(Toggle _toggle)
	{
		this.crouchToggle = _toggle.isOn;
		PlayerPrefs.SetInt("crouchToggle", this.crouchToggle ? 1 : 0);
		PlayerPrefs.Save();
	}

	// Token: 0x06000D5A RID: 3418 RVA: 0x0005B1B1 File Offset: 0x000593B1
	public void ToggleReverseSprintBind(Toggle _toggle)
	{
		this.reverseSprintBind = _toggle.isOn;
		PlayerPrefs.SetInt("reverseSprintBind", this.reverseSprintBind ? 1 : 0);
		PlayerPrefs.Save();
	}

	// Token: 0x06000D5B RID: 3419 RVA: 0x0005B1DC File Offset: 0x000593DC
	public void ToggleMinimalistUi(Toggle _toggle)
	{
		this.minimalistUi = _toggle.isOn;
		PlayerPrefs.SetInt("minimalistUi", this.minimalistUi ? 1 : 0);
		if (this.localPlayer && this.qualitySetting != 0)
		{
			this.localPlayer.transform.GetComponent<PlayerSetup>().HideHUD(this.minimalistUi);
		}
		PlayerPrefs.Save();
	}

	// Token: 0x06000D5C RID: 3420 RVA: 0x0005B240 File Offset: 0x00059440
	public void ToggleMotionBlur(Toggle _toggle)
	{
		this.motionBlur = _toggle.isOn;
		PlayerPrefs.SetInt("motionBlur", this.motionBlur ? 1 : 0);
		if (this.localPlayer)
		{
			this.localPlayer.transform.GetComponent<PlayerSetup>().ChangeMotionBlur(this.motionBlur);
		}
		PlayerPrefs.Save();
	}

	// Token: 0x06000D5D RID: 3421 RVA: 0x0005B29C File Offset: 0x0005949C
	public void InverseFireBinding(Toggle _toggle)
	{
		this.inverseFireBinding = _toggle.isOn;
		PlayerPrefs.SetInt("inverseFireBinding", this.inverseFireBinding ? 1 : 0);
		PlayerPrefs.Save();
	}

	// Token: 0x06000D5E RID: 3422 RVA: 0x0005B2C5 File Offset: 0x000594C5
	public void ChangeFullscreen(Toggle _toggle)
	{
		this.isFullscreen = _toggle.isOn;
		Screen.fullScreen = this.isFullscreen;
		PlayerPrefs.SetInt("isFullscreen", this.isFullscreen ? 1 : 0);
		PlayerPrefs.Save();
	}

	// Token: 0x06000D5F RID: 3423 RVA: 0x0005B2F9 File Offset: 0x000594F9
	public void ChangeVSync(Toggle _toggle)
	{
		this.useVsync = (_toggle.isOn ? 1 : 0);
		PlayerPrefs.SetInt("useVsync", this.useVsync);
		PlayerPrefs.Save();
	}

	// Token: 0x06000D60 RID: 3424 RVA: 0x0005B322 File Offset: 0x00059522
	public void ChangeVoiceChat(Toggle _toggle)
	{
		this.enableVoiceChat = _toggle.isOn;
		PlayerPrefs.SetInt("enableVoiceChat", this.enableVoiceChat ? 1 : 0);
		PlayerPrefs.Save();
	}

	// Token: 0x06000D61 RID: 3425 RVA: 0x0005B34B File Offset: 0x0005954B
	public void ChangePushToTalk(Toggle _toggle)
	{
		this.pushToTalk = _toggle.isOn;
		PlayerPrefs.SetInt("pushToTalk", this.pushToTalk ? 1 : 0);
		PlayerPrefs.Save();
	}

	// Token: 0x06000D62 RID: 3426 RVA: 0x0005B374 File Offset: 0x00059574
	public void EnableFixedCrosshair(Toggle _toggle)
	{
		this.enableFixedCrosshair = _toggle.isOn;
		PlayerPrefs.SetInt("enableFixedCrosshair", this.enableFixedCrosshair ? 1 : 0);
		PlayerPrefs.Save();
	}

	// Token: 0x06000D63 RID: 3427 RVA: 0x0005B39D File Offset: 0x0005959D
	public void EnableSpeedometer(Toggle _toggle)
	{
		this.showSpeedometer = _toggle.isOn;
		PlayerPrefs.SetInt("showSpeedometer", this.showSpeedometer ? 1 : 0);
		PlayerPrefs.Save();
	}

	// Token: 0x06000D64 RID: 3428 RVA: 0x0005B3C6 File Offset: 0x000595C6
	public void DisableCrosshair(Toggle _toggle)
	{
		this.disableCrosshair = _toggle.isOn;
		PlayerPrefs.SetInt("disableCrosshair", this.disableCrosshair ? 1 : 0);
		PlayerPrefs.Save();
	}

	// Token: 0x06000D65 RID: 3429 RVA: 0x0005B3EF File Offset: 0x000595EF
	public void InGameMusic(Toggle _toggle)
	{
		this.inGameMusic = _toggle.isOn;
		PlayerPrefs.SetInt("inGameMusic", this.inGameMusic ? 1 : 0);
		PlayerPrefs.Save();
	}

	// Token: 0x06000D66 RID: 3430 RVA: 0x0005B418 File Offset: 0x00059618
	public void ChangeExclusiveFullscreen(Toggle _toggle)
	{
		this.exclusiveFullscreen = _toggle.isOn;
		PlayerPrefs.SetInt("exclusiveFullscreen", this.exclusiveFullscreen ? 1 : 0);
		Screen.fullScreenMode = (this.exclusiveFullscreen ? FullScreenMode.ExclusiveFullScreen : FullScreenMode.FullScreenWindow);
		PlayerPrefs.Save();
	}

	// Token: 0x06000D67 RID: 3431 RVA: 0x0005B452 File Offset: 0x00059652
	public void ChangeResolution(int index)
	{
		this.resolution = index;
		PlayerPrefs.SetInt("resolution", this.resolution);
		PlayerPrefs.Save();
	}

	// Token: 0x06000D68 RID: 3432 RVA: 0x0005B470 File Offset: 0x00059670
	public void ChangeFrameRate(TMP_InputField _inputField)
	{
		int num;
		try
		{
			num = int.Parse(_inputField.text);
		}
		catch
		{
			num = 120;
			_inputField.text = "120";
		}
		if (num < 30)
		{
			num = 30;
			_inputField.text = "30";
		}
		PlayerPrefs.SetInt("targetFps", num);
		PlayerPrefs.Save();
		this.targetFps = num;
		if (this.targetFpsToggle)
		{
			Application.targetFrameRate = num;
			return;
		}
		Application.targetFrameRate = -1;
	}

	// Token: 0x06000D69 RID: 3433 RVA: 0x0005B4EC File Offset: 0x000596EC
	public void ChangeTargetFpsToggle(Toggle _toggle)
	{
		this.targetFpsToggle = _toggle.isOn;
		PlayerPrefs.SetInt("targetFpsToggle", this.targetFpsToggle ? 1 : 0);
		PlayerPrefs.Save();
		if (this.targetFpsToggle)
		{
			Application.targetFrameRate = this.targetFps;
			return;
		}
		Application.targetFrameRate = -1;
	}

	// Token: 0x06000D6A RID: 3434 RVA: 0x0005B53A File Offset: 0x0005973A
	public void ReduceVFX(Toggle _toggle)
	{
		this.reduceVFX = _toggle.isOn;
		PlayerPrefs.SetInt("reduceVFX", this.reduceVFX ? 1 : 0);
		PlayerPrefs.Save();
	}

	// Token: 0x06000D6B RID: 3435 RVA: 0x0005B564 File Offset: 0x00059764
	public void SetQualityLevelDropdown()
	{
		int value = this.qualityDropdown.value;
		QualitySettings.SetQualityLevel(value, true);
		this.qualitySetting = this.qualityDropdown.value;
		if (this.qualitySetting == 0 && this.localPlayer)
		{
			this.localPlayer.transform.GetComponent<PlayerSetup>().HideHUD(true);
		}
		else if (this.localPlayer)
		{
			this.localPlayer.transform.GetComponent<PlayerSetup>().HideHUD(false);
		}
		PlayerPrefs.SetInt("qualitySetting", value);
		PlayerPrefs.Save();
	}

	// Token: 0x06000D6C RID: 3436 RVA: 0x0005B5F8 File Offset: 0x000597F8
	public void SteamAchievementsCheck()
	{
		if (this.rocketJumps > 0f && !this.rocketJumpsHat.acquired)
		{
			if (!this.rocketJumpsHatAch.IsAchieved)
			{
				this.rocketJumpsHatAch.Unlock();
				StatsAndAchievements.Client.StoreStats();
			}
			this.rocketJumpsHat.acquired = true;
		}
		if (this.windowsBroken > 19f && !this.windowsBrokenHat.acquired)
		{
			if (!this.windowsBrokenHatAch.IsAchieved)
			{
				this.windowsBrokenHatAch.Unlock();
				StatsAndAchievements.Client.StoreStats();
			}
			this.windowsBrokenHat.acquired = true;
		}
		if (this.headshotsAmount > 0f && !this.headshotHat.acquired)
		{
			if (!this.headshotHatAch.IsAchieved)
			{
				this.headshotHatAch.Unlock();
				StatsAndAchievements.Client.StoreStats();
			}
			this.headshotHat.acquired = true;
		}
		if (this.ragdollsThrownAway > 0f && !this.ragdollsThrownAwayHat.acquired)
		{
			if (!this.ragdollsThrownAwayHatAch.IsAchieved)
			{
				this.ragdollsThrownAwayHatAch.Unlock();
				StatsAndAchievements.Client.StoreStats();
			}
			this.ragdollsThrownAwayHat.acquired = true;
		}
		if (this.noscope > 0f && !this.noscopeHat.acquired)
		{
			if (!this.noscopeHatAch.IsAchieved)
			{
				this.noscopeHatAch.Unlock();
				StatsAndAchievements.Client.StoreStats();
			}
			this.noscopeHat.acquired = true;
		}
		if (this.propKills > 0f && !this.propKillsHat.acquired)
		{
			if (!this.propKillsHatAch.IsAchieved)
			{
				this.propKillsHatAch.Unlock();
				StatsAndAchievements.Client.StoreStats();
			}
			this.propKillsHat.acquired = true;
		}
		if (this.taserShots > 0f && !this.taserShotsHat.acquired)
		{
			if (!this.taserShotsHatAch.IsAchieved)
			{
				this.taserShotsHatAch.Unlock();
				StatsAndAchievements.Client.StoreStats();
			}
			this.taserShotsHat.acquired = true;
		}
		if (this.potsBroken > 49f && !this.potsBrokenHat.acquired)
		{
			if (!this.potsBrokenHatAch.IsAchieved)
			{
				this.potsBrokenHatAch.Unlock();
				StatsAndAchievements.Client.StoreStats();
			}
			this.potsBrokenHat.acquired = true;
		}
		if (this.gamesPlayed > 4f && !this.fiveGamesHat.acquired)
		{
			if (!this.fiveGamesHatAch.IsAchieved)
			{
				this.fiveGamesHatAch.Unlock();
				StatsAndAchievements.Client.StoreStats();
			}
			this.fiveGamesHat.acquired = true;
		}
		if (this.killsAmount > 42f && !this.killsHat.acquired)
		{
			if (!this.killsHatAch.IsAchieved)
			{
				this.killsHatAch.Unlock();
				StatsAndAchievements.Client.StoreStats();
			}
			this.killsHat.acquired = true;
		}
	}

	// Token: 0x06000D6D RID: 3437 RVA: 0x0005B8AD File Offset: 0x00059AAD
	public void IncreaseRoundsWon()
	{
		this.roundsWon += 1f;
	}

	// Token: 0x06000D6E RID: 3438 RVA: 0x0005B8C1 File Offset: 0x00059AC1
	public void IncreaseRoundsPlayed()
	{
		this.roundsPlayed += 1f;
	}

	// Token: 0x06000D6F RID: 3439 RVA: 0x0005B8D5 File Offset: 0x00059AD5
	public void IncreaseGamesPlayed()
	{
		this.gamesPlayed += 1f;
	}

	// Token: 0x06000D70 RID: 3440 RVA: 0x0005B8E9 File Offset: 0x00059AE9
	public void IncreaseGamesWon()
	{
		this.gamesWon += 1f;
	}

	// Token: 0x06000D71 RID: 3441 RVA: 0x0005B8FD File Offset: 0x00059AFD
	public void IncreaseMapsUnlocked()
	{
		this.mapsUnlocked += 1f;
	}

	// Token: 0x06000D72 RID: 3442 RVA: 0x0005B911 File Offset: 0x00059B11
	public void IncreaseTauntsAmount()
	{
		this.tauntsAmount += 1f;
	}

	// Token: 0x06000D73 RID: 3443 RVA: 0x0005B925 File Offset: 0x00059B25
	public void IncreaseKillsAmount()
	{
		this.killsAmount += 1f;
	}

	// Token: 0x06000D74 RID: 3444 RVA: 0x0005B939 File Offset: 0x00059B39
	public void IncreaseDeathsAmount()
	{
		this.deathsAmount += 1f;
	}

	// Token: 0x06000D75 RID: 3445 RVA: 0x0005B94D File Offset: 0x00059B4D
	public void IncreaseSuicidesAmount()
	{
		this.suicidesAmount += 1f;
	}

	// Token: 0x06000D76 RID: 3446 RVA: 0x0005B961 File Offset: 0x00059B61
	public void IncreaseBodyshotsAmount()
	{
		this.bodyshotsAmount += 1f;
	}

	// Token: 0x06000D77 RID: 3447 RVA: 0x0005B975 File Offset: 0x00059B75
	public void IncreaseHeadshotsAmount()
	{
		this.headshotsAmount += 1f;
	}

	// Token: 0x06000D78 RID: 3448 RVA: 0x0005B989 File Offset: 0x00059B89
	public void IncreaseGamesLost()
	{
		this.gamesLost += 1f;
	}

	// Token: 0x06000D79 RID: 3449 RVA: 0x0005B99D File Offset: 0x00059B9D
	public void IncreaseRoundsLost()
	{
		this.roundsLost += 1f;
	}

	// Token: 0x06000D7A RID: 3450 RVA: 0x0005B9B4 File Offset: 0x00059BB4
	public object SaveState()
	{
		return new Settings.SaveData
		{
			tauntsAmount = this.tauntsAmount,
			killsAmount = this.killsAmount,
			deathsAmount = this.deathsAmount,
			suicidesAmount = this.suicidesAmount,
			bodyshotsAmount = this.bodyshotsAmount,
			headshotsAmount = this.headshotsAmount,
			roundsWon = this.roundsWon,
			roundsLost = this.roundsLost,
			roundsPlayed = this.roundsPlayed,
			gamesWon = this.gamesWon,
			gamesLost = this.gamesLost,
			gamesPlayed = this.gamesPlayed,
			timeSpentInGame = this.timeSpentInGame,
			timeSpentInAir = this.timeSpentInAir,
			timeSpentOnGround = this.timeSpentOnGround,
			mapsUnlocked = this.mapsUnlocked,
			rocketJumps = this.rocketJumps,
			windowsBroken = this.windowsBroken,
			ragdollsThrownAway = this.ragdollsThrownAway,
			taserShots = this.taserShots,
			propKills = this.propKills,
			potsBroken = this.potsBroken,
			noscope = this.noscope,
			recentlyPlayedWithPlayers = this.recentlyPlayedWithPlayers
		};
	}

	// Token: 0x06000D7B RID: 3451 RVA: 0x0005BB08 File Offset: 0x00059D08
	public void LoadState(JObject state)
	{
		Settings.SaveData saveData = state.ToObject<Settings.SaveData>();
		this.tauntsAmount = saveData.tauntsAmount;
		this.killsAmount = saveData.killsAmount;
		this.deathsAmount = saveData.deathsAmount;
		this.suicidesAmount = saveData.suicidesAmount;
		this.bodyshotsAmount = saveData.bodyshotsAmount;
		this.headshotsAmount = saveData.headshotsAmount;
		this.roundsWon = saveData.roundsWon;
		this.roundsLost = saveData.roundsLost;
		this.roundsPlayed = saveData.roundsPlayed;
		this.gamesWon = saveData.gamesWon;
		this.gamesLost = saveData.gamesLost;
		this.gamesPlayed = saveData.gamesPlayed;
		this.timeSpentInGame = saveData.timeSpentInGame;
		this.timeSpentInAir = saveData.timeSpentInAir;
		this.timeSpentOnGround = saveData.timeSpentOnGround;
		this.mapsUnlocked = saveData.mapsUnlocked;
		this.rocketJumps = saveData.rocketJumps;
		this.windowsBroken = saveData.windowsBroken;
		this.ragdollsThrownAway = saveData.ragdollsThrownAway;
		this.taserShots = saveData.taserShots;
		this.propKills = saveData.propKills;
		this.potsBroken = saveData.potsBroken;
		this.noscope = saveData.noscope;
		this.recentlyPlayedWithPlayers = saveData.recentlyPlayedWithPlayers;
		this.UpdateElo();
	}

	// Token: 0x04000B7D RID: 2941
	public static Settings Instance;

	// Token: 0x04000B7E RID: 2942
	public FirstPersonController localPlayer;

	// Token: 0x04000B7F RID: 2943
	private MapsManager mapsManager;

	// Token: 0x04000B80 RID: 2944
	public List<string> recentlyPlayedWithPlayers = new List<string>();

	// Token: 0x04000B81 RID: 2945
	[Header("Player settings")]
	public bool toggleTwoAxis;

	// Token: 0x04000B82 RID: 2946
	public bool invertMouseX;

	// Token: 0x04000B83 RID: 2947
	public bool invertMouseY;

	// Token: 0x04000B84 RID: 2948
	public bool sprintToggle;

	// Token: 0x04000B85 RID: 2949
	public bool aimToggle;

	// Token: 0x04000B86 RID: 2950
	public bool leanToggle;

	// Token: 0x04000B87 RID: 2951
	public bool crouchToggle;

	// Token: 0x04000B88 RID: 2952
	public bool reverseSprintBind;

	// Token: 0x04000B89 RID: 2953
	public bool inverseFireBinding;

	// Token: 0x04000B8A RID: 2954
	public float mouseSensitivity = 2f;

	// Token: 0x04000B8B RID: 2955
	public float mouseAimScopeSensitivity = 0.8f;

	// Token: 0x04000B8C RID: 2956
	public float mouseAimSensitivity = 2f;

	// Token: 0x04000B8D RID: 2957
	public float horizontalSensitivity = 2f;

	// Token: 0x04000B8E RID: 2958
	public float verticalSensitivity = 2f;

	// Token: 0x04000B8F RID: 2959
	public float fovValue = 2f;

	// Token: 0x04000B90 RID: 2960
	public float brightness = 1f;

	// Token: 0x04000B91 RID: 2961
	public float damageIntensity = 1f;

	// Token: 0x04000B92 RID: 2962
	public bool disableCrosshair;

	// Token: 0x04000B93 RID: 2963
	public bool enableFixedCrosshair;

	// Token: 0x04000B94 RID: 2964
	public bool showSpeedometer;

	// Token: 0x04000B95 RID: 2965
	[Header("Audio settings")]
	public float voiceChatVolume = 1f;

	// Token: 0x04000B96 RID: 2966
	public bool enableVoiceChat = true;

	// Token: 0x04000B97 RID: 2967
	public bool pushToTalk = true;

	// Token: 0x04000B98 RID: 2968
	public bool inGameMusic;

	// Token: 0x04000B99 RID: 2969
	[Header("Application settings")]
	public int useVsync;

	// Token: 0x04000B9A RID: 2970
	public bool isFullscreen = true;

	// Token: 0x04000B9B RID: 2971
	public bool exclusiveFullscreen;

	// Token: 0x04000B9C RID: 2972
	public bool minimalistUi;

	// Token: 0x04000B9D RID: 2973
	public bool motionBlur;

	// Token: 0x04000B9E RID: 2974
	public int targetFps = 120;

	// Token: 0x04000B9F RID: 2975
	public bool targetFpsToggle;

	// Token: 0x04000BA0 RID: 2976
	public bool reduceVFX;

	// Token: 0x04000BA1 RID: 2977
	[Space]
	[SerializeField]
	private LeaderboardManager leaderboardManager;

	// Token: 0x04000BA2 RID: 2978
	[Space]
	[SerializeField]
	private TMP_Dropdown qualityDropdown;

	// Token: 0x04000BA3 RID: 2979
	public int resolution = 3;

	// Token: 0x04000BA4 RID: 2980
	public int qualitySetting = 3;

	// Token: 0x04000BA5 RID: 2981
	[Space]
	[Header("Stats")]
	public float tauntsAmount;

	// Token: 0x04000BA6 RID: 2982
	public float killsAmount;

	// Token: 0x04000BA7 RID: 2983
	public float deathsAmount;

	// Token: 0x04000BA8 RID: 2984
	public float suicidesAmount;

	// Token: 0x04000BA9 RID: 2985
	public float bodyshotsAmount;

	// Token: 0x04000BAA RID: 2986
	public float headshotsAmount;

	// Token: 0x04000BAB RID: 2987
	public float roundsWon;

	// Token: 0x04000BAC RID: 2988
	public float roundsLost;

	// Token: 0x04000BAD RID: 2989
	public float roundsPlayed;

	// Token: 0x04000BAE RID: 2990
	public float gamesWon;

	// Token: 0x04000BAF RID: 2991
	public float gamesLost;

	// Token: 0x04000BB0 RID: 2992
	public float gamesPlayed;

	// Token: 0x04000BB1 RID: 2993
	public float timeSpentInGame;

	// Token: 0x04000BB2 RID: 2994
	public float timeSpentInAir;

	// Token: 0x04000BB3 RID: 2995
	public float timeSpentOnGround;

	// Token: 0x04000BB4 RID: 2996
	public float mapsUnlocked;

	// Token: 0x04000BB5 RID: 2997
	[Header("Challenges Stats")]
	public float rocketJumps;

	// Token: 0x04000BB6 RID: 2998
	public float windowsBroken;

	// Token: 0x04000BB7 RID: 2999
	public float ragdollsThrownAway;

	// Token: 0x04000BB8 RID: 3000
	public float taserShots;

	// Token: 0x04000BB9 RID: 3001
	public float propKills;

	// Token: 0x04000BBA RID: 3002
	public float potsBroken;

	// Token: 0x04000BBB RID: 3003
	public float noscope;

	// Token: 0x04000BBC RID: 3004
	[Header("Texts")]
	[SerializeField]
	private TextMeshProUGUI tauntsAmountText;

	// Token: 0x04000BBD RID: 3005
	[SerializeField]
	private TextMeshProUGUI killsAmountText;

	// Token: 0x04000BBE RID: 3006
	[SerializeField]
	private TextMeshProUGUI deathsAmountText;

	// Token: 0x04000BBF RID: 3007
	[SerializeField]
	private TextMeshProUGUI suicidesAmountText;

	// Token: 0x04000BC0 RID: 3008
	[SerializeField]
	private TextMeshProUGUI bodyshotsAmountText;

	// Token: 0x04000BC1 RID: 3009
	[SerializeField]
	private TextMeshProUGUI headshotsAmountText;

	// Token: 0x04000BC2 RID: 3010
	[SerializeField]
	private TextMeshProUGUI timeSpentInGameText;

	// Token: 0x04000BC3 RID: 3011
	[SerializeField]
	private TextMeshProUGUI timeSpentInAirText;

	// Token: 0x04000BC4 RID: 3012
	[SerializeField]
	private TextMeshProUGUI timeSpentOnGroundText;

	// Token: 0x04000BC5 RID: 3013
	[SerializeField]
	private TextMeshProUGUI roundsWonText;

	// Token: 0x04000BC6 RID: 3014
	[SerializeField]
	private TextMeshProUGUI roundsLostText;

	// Token: 0x04000BC7 RID: 3015
	[SerializeField]
	private TextMeshProUGUI roundsPlayedText;

	// Token: 0x04000BC8 RID: 3016
	[SerializeField]
	private TextMeshProUGUI gamesWonText;

	// Token: 0x04000BC9 RID: 3017
	[SerializeField]
	private TextMeshProUGUI gamesLostText;

	// Token: 0x04000BCA RID: 3018
	[SerializeField]
	private TextMeshProUGUI gamesPlayedText;

	// Token: 0x04000BCB RID: 3019
	[SerializeField]
	private TextMeshProUGUI mapsUnlockedText;

	// Token: 0x04000BCC RID: 3020
	[Header("Challenges Hats")]
	public CosmeticInstance rocketJumpsHat;

	// Token: 0x04000BCD RID: 3021
	public CosmeticInstance windowsBrokenHat;

	// Token: 0x04000BCE RID: 3022
	public CosmeticInstance headshotHat;

	// Token: 0x04000BCF RID: 3023
	public CosmeticInstance ragdollsThrownAwayHat;

	// Token: 0x04000BD0 RID: 3024
	public CosmeticInstance taserShotsHat;

	// Token: 0x04000BD1 RID: 3025
	public CosmeticInstance noscopeHat;

	// Token: 0x04000BD2 RID: 3026
	public CosmeticInstance propKillsHat;

	// Token: 0x04000BD3 RID: 3027
	public CosmeticInstance potsBrokenHat;

	// Token: 0x04000BD4 RID: 3028
	public CosmeticInstance fiveGamesHat;

	// Token: 0x04000BD5 RID: 3029
	public CosmeticInstance killsHat;

	// Token: 0x04000BD6 RID: 3030
	public AchievementObject rocketJumpsHatAch;

	// Token: 0x04000BD7 RID: 3031
	public AchievementObject windowsBrokenHatAch;

	// Token: 0x04000BD8 RID: 3032
	public AchievementObject headshotHatAch;

	// Token: 0x04000BD9 RID: 3033
	public AchievementObject ragdollsThrownAwayHatAch;

	// Token: 0x04000BDA RID: 3034
	public AchievementObject taserShotsHatAch;

	// Token: 0x04000BDB RID: 3035
	public AchievementObject noscopeHatAch;

	// Token: 0x04000BDC RID: 3036
	public AchievementObject propKillsHatAch;

	// Token: 0x04000BDD RID: 3037
	public AchievementObject potsBrokenHatAch;

	// Token: 0x04000BDE RID: 3038
	public AchievementObject fiveGamesHatAch;

	// Token: 0x04000BDF RID: 3039
	public AchievementObject killsHatAch;

	// Token: 0x0200010E RID: 270
	[Serializable]
	public struct SaveData
	{
		// Token: 0x04000BE0 RID: 3040
		public float tauntsAmount;

		// Token: 0x04000BE1 RID: 3041
		public float killsAmount;

		// Token: 0x04000BE2 RID: 3042
		public float deathsAmount;

		// Token: 0x04000BE3 RID: 3043
		public float suicidesAmount;

		// Token: 0x04000BE4 RID: 3044
		public float bodyshotsAmount;

		// Token: 0x04000BE5 RID: 3045
		public float headshotsAmount;

		// Token: 0x04000BE6 RID: 3046
		public float roundsWon;

		// Token: 0x04000BE7 RID: 3047
		public float roundsLost;

		// Token: 0x04000BE8 RID: 3048
		public float roundsPlayed;

		// Token: 0x04000BE9 RID: 3049
		public float gamesWon;

		// Token: 0x04000BEA RID: 3050
		public float gamesLost;

		// Token: 0x04000BEB RID: 3051
		public float gamesPlayed;

		// Token: 0x04000BEC RID: 3052
		public float timeSpentInGame;

		// Token: 0x04000BED RID: 3053
		public float timeSpentInAir;

		// Token: 0x04000BEE RID: 3054
		public float timeSpentOnGround;

		// Token: 0x04000BEF RID: 3055
		public float mapsUnlocked;

		// Token: 0x04000BF0 RID: 3056
		public float rocketJumps;

		// Token: 0x04000BF1 RID: 3057
		public float windowsBroken;

		// Token: 0x04000BF2 RID: 3058
		public float ragdollsThrownAway;

		// Token: 0x04000BF3 RID: 3059
		public float taserShots;

		// Token: 0x04000BF4 RID: 3060
		public float propKills;

		// Token: 0x04000BF5 RID: 3061
		public float potsBroken;

		// Token: 0x04000BF6 RID: 3062
		public float noscope;

		// Token: 0x04000BF7 RID: 3063
		public List<string> recentlyPlayedWithPlayers;
	}
}

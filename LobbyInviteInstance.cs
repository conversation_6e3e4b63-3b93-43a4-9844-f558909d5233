﻿using System;
using FishNet;
using HeathenEngineering.SteamworksIntegration;
using TMPro;
using UnityEngine;

// Token: 0x020000D4 RID: 212
public class LobbyInviteInstance : MonoBehaviour
{
	// Token: 0x06000B9A RID: 2970 RVA: 0x000519C8 File Offset: 0x0004FBC8
	private void Start()
	{
		this.text.text = this.lobbyName;
	}

	// Token: 0x06000B9B RID: 2971 RVA: 0x000519DC File Offset: 0x0004FBDC
	public void Join()
	{
		if (InstanceFinder.NetworkManager.ServerManager.Started)
		{
			SteamLobby.Instance.LeaveLobby();
		}
		SteamLobby.Instance.DestroyInviteCards();
		if (this.lobbyID == 0UL)
		{
			return;
		}
		GameObject.Find("LobbyController").GetComponent<LobbyManager>().Join(this.lobbyID);
	}

	// Token: 0x06000B9C RID: 2972 RVA: 0x00002E03 File Offset: 0x00001003
	public void Refuse()
	{
		global::UnityEngine.Object.Destroy(base.gameObject);
	}

	// Token: 0x040009BF RID: 2495
	public string lobbyName;

	// Token: 0x040009C0 RID: 2496
	public ulong lobbyID;

	// Token: 0x040009C1 RID: 2497
	[SerializeField]
	private TextMeshProUGUI text;
}

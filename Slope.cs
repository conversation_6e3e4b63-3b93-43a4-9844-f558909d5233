﻿using System;
using UnityEngine;

// Token: 0x02000036 RID: 54
public class Slope : MonoBehaviour
{
	// Token: 0x060002E3 RID: 739 RVA: 0x000184D3 File Offset: 0x000166D3
	private void Awake()
	{
		this.controller = base.GetComponent<FirstPersonController>();
	}

	// Token: 0x060002E4 RID: 740 RVA: 0x000184E4 File Offset: 0x000166E4
	private void Update()
	{
		this.rearCast = Physics.Raycast(this.rearRayPos.position, -Vector3.up, out this.rearHit, float.PositiveInfinity, this.layerMask);
		this.frontCast = Physics.Raycast(this.frontRayPos.position, -Vector3.up, out this.frontHit, float.PositiveInfinity, this.layerMask);
		this.middleCast = Physics.Raycast(base.transform.position, -Vector3.up, out this.middleHit, float.PositiveInfinity, this.layerMask);
		this.surfaceAngle = Vector3.Angle(this.middleHit.normal, Vector3.up);
		if (this.frontHit.distance < this.rearHit.distance)
		{
			this.flatSurface = false;
			this.uphill = true;
			this.downhill = false;
			return;
		}
		if (this.frontHit.distance > this.rearHit.distance)
		{
			this.flatSurface = false;
			this.downhill = true;
			this.uphill = false;
			return;
		}
		if (this.frontHit.distance == this.rearHit.distance)
		{
			this.flatSurface = true;
			this.uphill = false;
			this.downhill = false;
		}
	}

	// Token: 0x04000333 RID: 819
	public Transform rearRayPos;

	// Token: 0x04000334 RID: 820
	public Transform frontRayPos;

	// Token: 0x04000335 RID: 821
	public LayerMask layerMask;

	// Token: 0x04000336 RID: 822
	private FirstPersonController controller;

	// Token: 0x04000337 RID: 823
	public float surfaceAngle;

	// Token: 0x04000338 RID: 824
	public bool uphill;

	// Token: 0x04000339 RID: 825
	public bool downhill;

	// Token: 0x0400033A RID: 826
	public bool flatSurface;

	// Token: 0x0400033B RID: 827
	private RaycastHit rearHit;

	// Token: 0x0400033C RID: 828
	private bool rearCast;

	// Token: 0x0400033D RID: 829
	private RaycastHit frontHit;

	// Token: 0x0400033E RID: 830
	private bool frontCast;

	// Token: 0x0400033F RID: 831
	private RaycastHit middleHit;

	// Token: 0x04000340 RID: 832
	private bool middleCast;
}

﻿using System;
using UnityEngine;

// Token: 0x0200006D RID: 109
public class RagdollSound : MonoBehaviour
{
	// Token: 0x060004C1 RID: 1217 RVA: 0x00020472 File Offset: 0x0001E672
	private void Update()
	{
		this.timer -= Time.deltaTime;
	}

	// Token: 0x060004C2 RID: 1218 RVA: 0x00020488 File Offset: 0x0001E688
	private void OnTriggerEnter(Collider col)
	{
		if (col.transform.tag == "Killz")
		{
			Settings.Instance.ragdollsThrownAway += 1f;
		}
		if (this.trigger)
		{
			return;
		}
		if (col.gameObject.layer != 0)
		{
			return;
		}
		if (this.timer < 0f)
		{
			this.timer = 1f;
			this.audio.pitch = global::UnityEngine.Random.Range(0.9f, 1.1f);
			this.audio.PlayOneShot(this.groundHitClip);
			this.trigger = true;
		}
	}

	// Token: 0x040004D4 RID: 1236
	private float timer;

	// Token: 0x040004D5 RID: 1237
	private bool trigger;

	// Token: 0x040004D6 RID: 1238
	[SerializeField]
	private AudioClip groundHitClip;

	// Token: 0x040004D7 RID: 1239
	[SerializeField]
	private AudioSource audio;
}

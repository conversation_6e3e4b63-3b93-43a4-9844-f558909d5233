﻿using System;
using UnityEngine;

// Token: 0x02000017 RID: 23
[RequireComponent(typeof(Line<PERSON>enderer))]
[ExecuteAlways]
public class CableProceduralMultipoint : MonoBehaviour
{
	// Token: 0x060000D1 RID: 209 RVA: 0x00005F58 File Offset: 0x00004158
	private void Start()
	{
		this.line = base.GetComponent<LineRenderer>();
		this.line.positionCount = 0;
		if (this.cableSections.Length == 0)
		{
			Debug.LogError("No sections assigned to Cable_Procedural component attached to " + base.gameObject.name);
			return;
		}
		this.sagDirection = Physics.gravity.normalized;
		foreach (CableProceduralMultipoint.CableSection cableSection in this.cableSections)
		{
			this.Draw(cableSection);
		}
	}

	// Token: 0x060000D2 RID: 210 RVA: 0x00005FD4 File Offset: 0x000041D4
	private void Draw(CableProceduralMultipoint.CableSection section)
	{
		Vector3 vector = section.end.position - section.start.position;
		section.start.forward = vector.normalized;
		int num = Mathf.FloorToInt(this.pointDensity * vector.magnitude);
		for (int i = 0; i < num; i++)
		{
			float num2 = (float)i / (float)((num - 1 > 0) ? (num - 1) : 1);
			float num3 = Mathf.Sin(num2 * 3.1415927f);
			Vector3 vector2 = vector * num2;
			Vector3 vector3 = this.sagDirection * section.sag;
			Vector3 vector4 = section.start.position + vector2 + vector3 * num3;
			this.line.positionCount++;
			this.line.SetPosition(this.line.positionCount - 1, vector4);
		}
	}

	// Token: 0x040000AB RID: 171
	private LineRenderer line;

	// Token: 0x040000AC RID: 172
	[SerializeField]
	[Tooltip("Series of points in cable.  If this transform is the desired start, add it to this array.")]
	private CableProceduralMultipoint.CableSection[] cableSections;

	// Token: 0x040000AD RID: 173
	[SerializeField]
	[Tooltip("Number of points per unit length, using the straight line from the start to the end transform.")]
	private float pointDensity = 3f;

	// Token: 0x040000AE RID: 174
	private Vector3 sagDirection;

	// Token: 0x02000018 RID: 24
	[Serializable]
	private class CableSection
	{
		// Token: 0x040000AF RID: 175
		public Transform start;

		// Token: 0x040000B0 RID: 176
		public Transform end;

		// Token: 0x040000B1 RID: 177
		public float sag;
	}
}

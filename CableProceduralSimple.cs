﻿using System;
using UnityEngine;

// Token: 0x02000019 RID: 25
[RequireComponent(typeof(LineRenderer))]
[ExecuteAlways]
public class CableProceduralSimple : MonoBehaviour
{
	// Token: 0x060000D5 RID: 213 RVA: 0x000060DC File Offset: 0x000042DC
	private void Start()
	{
		this.line = base.GetComponent<LineRenderer>();
		if (!this.endPointTransform)
		{
			Debug.LogError("No Endpoint Transform assigned to Cable_Procedural component attached to " + base.gameObject.name);
			return;
		}
		this.vectorFromStartToEnd = this.endPointTransform.position - base.transform.position;
		base.transform.forward = this.vectorFromStartToEnd.normalized;
		this.pointsInLineRenderer = Mathf.FloorToInt(this.pointDensity * this.vectorFromStartToEnd.magnitude);
		this.line.positionCount = this.pointsInLineRenderer;
		this.sagDirection = Physics.gravity.normalized;
	}

	// Token: 0x060000D6 RID: 214 RVA: 0x00006195 File Offset: 0x00004395
	private void Update()
	{
		this.Draw();
	}

	// Token: 0x060000D7 RID: 215 RVA: 0x000061A0 File Offset: 0x000043A0
	private void Draw()
	{
		if (!this.endPointTransform)
		{
			return;
		}
		int i = 0;
		this.swayValue += this.swayFrequency * Time.deltaTime;
		if (this.swayValue > 6.2831855f)
		{
			this.swayValue = 0f;
		}
		if (this.swayValue < 0f)
		{
			this.swayValue = 6.2831855f;
		}
		while (i < this.pointsInLineRenderer)
		{
			float num = (float)i / (float)(this.pointsInLineRenderer - 1);
			float num2 = Mathf.Sin(num * 3.1415927f);
			Vector3 vector = this.vectorFromStartToEnd * num;
			Vector3 vector2 = this.sagDirection * this.sagAmplitude;
			Vector3 vector3 = this.swayMultiplier * base.transform.TransformDirection(new Vector3(Mathf.Sin(this.swayValue) * this.swayXMultiplier, Mathf.Cos(2f * this.swayValue + 3.1415927f) * 0.5f * this.swayYMultiplier, 0f));
			Vector3 vector4 = base.transform.position + vector + (vector3 + Vector3.ClampMagnitude(vector2, this.sagAmplitude)) * num2;
			this.line.SetPosition(i, vector4);
			i++;
		}
	}

	// Token: 0x040000B2 RID: 178
	private LineRenderer line;

	// Token: 0x040000B3 RID: 179
	[SerializeField]
	private Transform endPointTransform;

	// Token: 0x040000B4 RID: 180
	[SerializeField]
	[Tooltip("Number of points per unit length, using the straight line from the start to the end transform.")]
	private float pointDensity = 3f;

	// Token: 0x040000B5 RID: 181
	[SerializeField]
	private float sagAmplitude = 1f;

	// Token: 0x040000B6 RID: 182
	[SerializeField]
	private float swayMultiplier = 1f;

	// Token: 0x040000B7 RID: 183
	[SerializeField]
	private float swayXMultiplier = 1f;

	// Token: 0x040000B8 RID: 184
	[SerializeField]
	private float swayYMultiplier = 0.5f;

	// Token: 0x040000B9 RID: 185
	[SerializeField]
	private float swayFrequency = 1f;

	// Token: 0x040000BA RID: 186
	private int pointsInLineRenderer;

	// Token: 0x040000BB RID: 187
	private Vector3 vectorFromStartToEnd;

	// Token: 0x040000BC RID: 188
	private Vector3 sagDirection;

	// Token: 0x040000BD RID: 189
	private float swayValue;
}

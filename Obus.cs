﻿using System;
using DG.Tweening;
using UnityEngine;

// Token: 0x020000A0 RID: 160
public class Obus : MonoBehaviour
{
	// Token: 0x060008BA RID: 2234 RVA: 0x0003E835 File Offset: 0x0003CA35
	private void OnEnable()
	{
		PauseManager.OnBeforeSpawn += this.StartNewRound;
	}

	// Token: 0x060008BB RID: 2235 RVA: 0x0003E848 File Offset: 0x0003CA48
	private void OnDisable()
	{
		PauseManager.OnBeforeSpawn -= this.StartNewRound;
	}

	// Token: 0x060008BC RID: 2236 RVA: 0x00002E03 File Offset: 0x00001003
	private void StartNewRound()
	{
		global::UnityEngine.Object.Destroy(base.gameObject);
	}

	// Token: 0x060008BD RID: 2237 RVA: 0x0003E85B File Offset: 0x0003CA5B
	private void Awake()
	{
		this.audio = base.GetComponent<AudioSource>();
	}

	// Token: 0x060008BE RID: 2238 RVA: 0x0003E869 File Offset: 0x0003CA69
	public void Initialize(GameObject rootObject, GameObject gun, float passedTime)
	{
		this._rootObject = rootObject;
		this._passedTime = passedTime;
		this._gun = gun;
		this.explosionTimer = this.timeBeforeExplosion;
	}

	// Token: 0x060008BF RID: 2239 RVA: 0x0003E88C File Offset: 0x0003CA8C
	private void OnCollisionEnter()
	{
		this.hit = true;
	}

	// Token: 0x060008C0 RID: 2240 RVA: 0x0003E898 File Offset: 0x0003CA98
	private void Update()
	{
		float deltaTime = Time.deltaTime;
		float num = 0f;
		this.velocity = this.lastPosition - this.currentPosition;
		base.transform.rotation = Quaternion.LookRotation(-this.velocity);
		if (this.hit)
		{
			this.explosionTimer -= deltaTime + num;
		}
		this.HandleExplosion();
		this.lastPosition = this.currentPosition;
	}

	// Token: 0x060008C1 RID: 2241 RVA: 0x0003E910 File Offset: 0x0003CB10
	private void HandleExplosion()
	{
		if (this.explosionTimer < 0f && this.explosionTimer > -2f)
		{
			Collider[] array = Physics.OverlapSphere(base.transform.position, this.explosionRadius, this.bodyLayer);
			if (array.Length != 0 && this.isOwner)
			{
				this.ph2 = new PlayerHealth[array.Length];
				for (int i = 0; i < array.Length; i++)
				{
					if (array[i].transform.tag == "ShatterableGlass" && array[i].gameObject.GetComponent<ShatterableGlass>() != null)
					{
						array[i].gameObject.GetComponent<ShatterableGlass>().Shatter3D(array[i].transform.position, array[i].transform.position - base.transform.position);
					}
					if (array[i].GetComponentInParent<PlayerHealth>() != null)
					{
						this.ph2[i] = array[i].GetComponentInParent<PlayerHealth>();
					}
				}
				for (int j = 0; j < this.ph2.Length; j++)
				{
					if (this.ph2[j] != null && !this.ph2[j].SyncAccessor_isKilled)
					{
						if (this.ph2[j].transform.gameObject == this._rootObject)
						{
							Settings.Instance.IncreaseSuicidesAmount();
							this.ph2[j].ChangeKilledState(true);
							this.ph2[j].RemoveHealth(10f);
							this.ph2[j].Explode(false, true, this.ph2[j].gameObject.name, this.ph2[j].transform.position - base.transform.position, this.ragdollEjectForce, base.transform.position);
							this.ph2[j].SetKiller(this._rootObject.transform);
							this.touched = true;
						}
						else if (this.ph2[j].transform.gameObject != this._rootObject)
						{
							this.ph2[j].ChangeKilledState(true);
							this.ph2[j].RemoveHealth(10f);
							this.KillShockWave();
							this.SendKillLog(this.ph2[j]);
							this.ph2[j].Explode(false, true, this.ph2[j].gameObject.name, this.ph2[j].transform.position - base.transform.position, this.ragdollEjectForce, base.transform.position);
							this.ph2[j].SetKiller(this._rootObject.transform);
							this.touched2 = true;
						}
					}
				}
			}
			GameObject[] array2 = GameObject.FindGameObjectsWithTag("Player");
			for (int k = 0; k < array2.Length; k++)
			{
				float num = Vector3.Distance(base.transform.position, array2[k].transform.position);
				array2[k].GetComponent<PlayerHealth>().LocalScreenshake(this.duration, Mathf.Lerp(this.maxStrength, this.minStrength, Mathf.Clamp(num / this.maxDistance, 0f, 1f)), this.vibrato, this.randomness, this.shakeEase);
			}
			global::UnityEngine.Object.Destroy(base.gameObject, 3f);
			base.enabled = false;
			this.graph.gameObject.SetActive(false);
			global::UnityEngine.Object.Instantiate<GameObject>(this.explosionVfx, base.transform.position, Quaternion.identity);
			global::UnityEngine.Object.Instantiate<GameObject>(this.explosionDecal, base.transform.position, Quaternion.identity);
			this.audio.Play();
		}
	}

	// Token: 0x060008C2 RID: 2242 RVA: 0x0003ECE0 File Offset: 0x0003CEE0
	public void KillShockWave()
	{
		Settings.Instance.IncreaseKillsAmount();
		this.increaseKillAmount = true;
		this._rootObject.GetComponent<FirstPersonController>().lensDistortion.intensity.value = this._rootObject.GetComponent<FirstPersonController>().killShockWaveStrength;
		this._rootObject.GetComponent<FirstPersonController>().colorGrading.saturation.value = -100f;
	}

	// Token: 0x060008C3 RID: 2243 RVA: 0x0003ED48 File Offset: 0x0003CF48
	private void SendKillLog(PlayerHealth enemyHealth)
	{
		this.sendKillLog = true;
		PauseManager.Instance.WriteLog(string.Concat(new string[]
		{
			"<b><color=#",
			PauseManager.Instance.selfNameLogColor,
			">",
			enemyHealth.SyncAccessor_playerValues.SyncAccessor_playerClient.PlayerName,
			"</color></b> was killed with a <color=white>grenade launcher</color> by <b><color=#",
			PauseManager.Instance.enemyNameLogColor,
			">",
			ClientInstance.Instance.PlayerName,
			"</color></b>"
		}));
	}

	// Token: 0x04000776 RID: 1910
	public bool isOwner;

	// Token: 0x04000777 RID: 1911
	private Vector3 impact = Vector3.zero;

	// Token: 0x04000778 RID: 1912
	private CharacterController character;

	// Token: 0x04000779 RID: 1913
	[SerializeField]
	private float ragdollEjectForce;

	// Token: 0x0400077A RID: 1914
	[SerializeField]
	private LayerMask playerLayer;

	// Token: 0x0400077B RID: 1915
	[SerializeField]
	private LayerMask bodyLayer;

	// Token: 0x0400077C RID: 1916
	[SerializeField]
	private GameObject hitVfx;

	// Token: 0x0400077D RID: 1917
	[SerializeField]
	private GameObject explosionVfx;

	// Token: 0x0400077E RID: 1918
	[SerializeField]
	private AudioClip hitClip;

	// Token: 0x0400077F RID: 1919
	[SerializeField]
	private AudioClip explosionClip;

	// Token: 0x04000780 RID: 1920
	[SerializeField]
	private float rotateSpeed;

	// Token: 0x04000781 RID: 1921
	[SerializeField]
	private Vector3 rotateAxis;

	// Token: 0x04000782 RID: 1922
	[SerializeField]
	private Transform graph;

	// Token: 0x04000783 RID: 1923
	[SerializeField]
	private GameObject explosionDecal;

	// Token: 0x04000784 RID: 1924
	[SerializeField]
	private float timeBeforeExplosion = 2f;

	// Token: 0x04000785 RID: 1925
	[SerializeField]
	private float explosionRadius = 3f;

	// Token: 0x04000786 RID: 1926
	private float explosionTimer;

	// Token: 0x04000787 RID: 1927
	[SerializeField]
	private float rebondForce;

	// Token: 0x04000788 RID: 1928
	private bool isGrounded;

	// Token: 0x04000789 RID: 1929
	[Header("Screenshake values")]
	[SerializeField]
	private float duration;

	// Token: 0x0400078A RID: 1930
	[SerializeField]
	private float minStrength;

	// Token: 0x0400078B RID: 1931
	[SerializeField]
	private float maxStrength;

	// Token: 0x0400078C RID: 1932
	[SerializeField]
	private int vibrato;

	// Token: 0x0400078D RID: 1933
	[SerializeField]
	private float randomness;

	// Token: 0x0400078E RID: 1934
	[SerializeField]
	private Ease shakeEase;

	// Token: 0x0400078F RID: 1935
	[SerializeField]
	private float maxDistance;

	// Token: 0x04000790 RID: 1936
	private bool touched;

	// Token: 0x04000791 RID: 1937
	private bool touched2;

	// Token: 0x04000792 RID: 1938
	private GameObject _gun;

	// Token: 0x04000793 RID: 1939
	private PlayerHealth[] ph2;

	// Token: 0x04000794 RID: 1940
	private float _passedTime;

	// Token: 0x04000795 RID: 1941
	private GameObject _rootObject;

	// Token: 0x04000796 RID: 1942
	private AudioSource audio;

	// Token: 0x04000797 RID: 1943
	[HideInInspector]
	public Weapon weapon;

	// Token: 0x04000798 RID: 1944
	private Vector3 currentPosition;

	// Token: 0x04000799 RID: 1945
	private Vector3 lastPosition;

	// Token: 0x0400079A RID: 1946
	private Vector3 velocity;

	// Token: 0x0400079B RID: 1947
	private float safeTimer;

	// Token: 0x0400079C RID: 1948
	private bool hit;

	// Token: 0x0400079D RID: 1949
	private bool increaseKillAmount;

	// Token: 0x0400079E RID: 1950
	private bool sendKillLog;
}

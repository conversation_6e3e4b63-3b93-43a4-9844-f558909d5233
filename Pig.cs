﻿using System;
using UnityEngine;

// Token: 0x02000004 RID: 4
public class Pig : MonoBehaviour
{
	// Token: 0x06000010 RID: 16 RVA: 0x000023D6 File Offset: 0x000005D6
	private void OnDisable()
	{
	}

	// Token: 0x06000011 RID: 17 RVA: 0x00002870 File Offset: 0x00000A70
	private void OnEnable()
	{
		this.StopWalking();
		this.Grunt();
	}

	// Token: 0x06000012 RID: 18 RVA: 0x0000287E File Offset: 0x00000A7E
	private void Awake()
	{
		this.rb = base.GetComponent<Rigidbody>();
		this.anim = base.GetComponent<Animator>();
		this.sound = base.GetComponent<AudioSource>();
	}

	// Token: 0x06000013 RID: 19 RVA: 0x000028A4 File Offset: 0x00000AA4
	private void Start()
	{
		this.StartWalking();
		if (global::UnityEngine.Random.Range(0f, 1000f) < 1f)
		{
			this.hat.SetActive(true);
			this.hatHeld.SetActive(true);
		}
	}

	// Token: 0x06000014 RID: 20 RVA: 0x000023D6 File Offset: 0x000005D6
	public void Interact()
	{
	}

	// Token: 0x06000015 RID: 21 RVA: 0x000028DC File Offset: 0x00000ADC
	public void Fling()
	{
		this.StopWalking();
		this.sound.pitch = 1f + global::UnityEngine.Random.Range(-0.1f, 0.1f);
		this.sound.PlayOneShot(this.squeal);
		this.anim.SetTrigger("Fling");
	}

	// Token: 0x06000016 RID: 22 RVA: 0x00002930 File Offset: 0x00000B30
	public void Grunt()
	{
		float num = global::UnityEngine.Random.Range(0f, 1f);
		this.sound.pitch = 1f + global::UnityEngine.Random.Range(-0.1f, 0.1f);
		if (num < 0.5f)
		{
			this.sound.PlayOneShot(this.grunt);
			return;
		}
		this.sound.PlayOneShot(this.grunt2);
	}

	// Token: 0x06000017 RID: 23 RVA: 0x00002996 File Offset: 0x00000B96
	public void Sniff()
	{
		this.sound.pitch = 1f + global::UnityEngine.Random.Range(-0.1f, 0.1f);
		this.sound.PlayOneShot(this.sniff);
	}

	// Token: 0x06000018 RID: 24 RVA: 0x000029CC File Offset: 0x00000BCC
	private void FixedUpdate()
	{
		if (this.rb.velocity.y < -20f && !this.fellFar)
		{
			this.FallFar();
		}
		if (!this.stopped)
		{
			this.walkTimer += Time.deltaTime;
			if ((double)this.walkTimer > (double)this.walkTime * 0.5 && !this.walkGrunted)
			{
				if (global::UnityEngine.Random.Range(0f, 1f) < this.walkGruntChance)
				{
					this.Grunt();
				}
				this.walkGrunted = true;
			}
			if (this.walkTimer > this.walkTime)
			{
				this.StopWalking();
			}
			this.stepTimer += Time.deltaTime;
			if (this.stepTimer > this.stepTime)
			{
				this.sound.pitch = 1f + global::UnityEngine.Random.Range(-0.1f, 0.1f);
				this.sound.PlayOneShot(this.step);
				this.stepTimer = 0f;
			}
			this.rb.AddForce(this.walkDir * -this.speed, ForceMode.Impulse);
			if (this.rb.velocity.magnitude > this.speedLimit)
			{
				Vector3 vector = this.rb.velocity.normalized * this.speedLimit;
				vector.y = this.rb.velocity.y;
				this.rb.velocity = vector;
			}
			if (this.rb.velocity.magnitude > 0.1f)
			{
				this.Orient(this.rb.velocity * -1f);
			}
			if ((double)this.rb.velocity.magnitude > 0.5)
			{
				this.lastTrueVel = this.rb.velocity * -1f;
				return;
			}
		}
		else
		{
			this.stoppedTimer += Time.deltaTime;
			if (this.stoppedTimer > this.stoppedTime)
			{
				this.StartWalking();
			}
			this.Orient(this.lastTrueVel);
		}
	}

	// Token: 0x06000019 RID: 25 RVA: 0x00002BF0 File Offset: 0x00000DF0
	public void FallFar()
	{
		this.sound.spatialBlend = 0.6f;
		this.sound.PlayOneShot(this.fallFar);
		this.anim.SetTrigger("Fling");
		this.fellFar = true;
		base.enabled = false;
		base.Invoke("Die", 4f);
	}

	// Token: 0x0600001A RID: 26 RVA: 0x00002C4C File Offset: 0x00000E4C
	private void Orient(Vector3 travelDir)
	{
		Quaternion quaternion = Quaternion.LookRotation(travelDir);
		base.transform.rotation = Quaternion.Slerp(base.transform.rotation, quaternion, Time.deltaTime * 2.5f);
	}

	// Token: 0x0600001B RID: 27 RVA: 0x00002C88 File Offset: 0x00000E88
	private void StartWalking()
	{
		this.walkDir = new Vector3(global::UnityEngine.Random.Range(-1f, 1f), 0f, global::UnityEngine.Random.Range(-1f, 1f));
		this.walkTime = global::UnityEngine.Random.Range(this.walkTimeMin, this.walkTimeMax);
		this.walkTimer = 0f;
		this.stopped = false;
		this.anim.ResetTrigger("Sniff");
		this.anim.SetBool("Walking", true);
		this.walkGrunted = false;
		this.stepTimer = 0f;
	}

	// Token: 0x0600001C RID: 28 RVA: 0x00002D20 File Offset: 0x00000F20
	private void StopWalking()
	{
		this.walkTimer = 999f;
		Vector3 velocity = this.rb.velocity;
		velocity.x = (velocity.z = 0f);
		this.rb.velocity = velocity;
		this.stoppedTime = global::UnityEngine.Random.Range(this.stoppedTimeMin, this.stoppedTimeMax);
		this.stoppedTimer = 0f;
		this.stopped = true;
		this.anim.SetBool("Walking", false);
		float num = global::UnityEngine.Random.Range(0f, 1f);
		if (num < this.gruntChance)
		{
			if (num < this.sniffChance)
			{
				this.anim.SetTrigger("Sniff");
				base.Invoke("Sniff", 0.3f);
				return;
			}
			this.anim.SetTrigger("Greet");
			base.Invoke("Grunt", 1f);
		}
	}

	// Token: 0x0600001D RID: 29 RVA: 0x00002E03 File Offset: 0x00001003
	public void Die()
	{
		global::UnityEngine.Object.Destroy(base.gameObject);
	}

	// Token: 0x0400001B RID: 27
	private Rigidbody rb;

	// Token: 0x0400001C RID: 28
	private Animator anim;

	// Token: 0x0400001D RID: 29
	private float speed = 1f;

	// Token: 0x0400001E RID: 30
	private float speedLimit = 2f;

	// Token: 0x0400001F RID: 31
	private float walkTimeMin = 1f;

	// Token: 0x04000020 RID: 32
	private float walkTimeMax = 2f;

	// Token: 0x04000021 RID: 33
	private float walkTime;

	// Token: 0x04000022 RID: 34
	private float walkTimer;

	// Token: 0x04000023 RID: 35
	private Vector3 walkDir;

	// Token: 0x04000024 RID: 36
	private bool stopped;

	// Token: 0x04000025 RID: 37
	private float stoppedTimeMin = 2f;

	// Token: 0x04000026 RID: 38
	private float stoppedTimeMax = 5f;

	// Token: 0x04000027 RID: 39
	private float stoppedTime;

	// Token: 0x04000028 RID: 40
	private float stoppedTimer;

	// Token: 0x04000029 RID: 41
	private float sniffChance = 0.2f;

	// Token: 0x0400002A RID: 42
	private float gruntChance = 0.4f;

	// Token: 0x0400002B RID: 43
	private float walkGruntChance = 0.2f;

	// Token: 0x0400002C RID: 44
	private bool walkGrunted;

	// Token: 0x0400002D RID: 45
	private AudioSource sound;

	// Token: 0x0400002E RID: 46
	public AudioClip squeal;

	// Token: 0x0400002F RID: 47
	public AudioClip grunt;

	// Token: 0x04000030 RID: 48
	public AudioClip grunt2;

	// Token: 0x04000031 RID: 49
	public AudioClip sniff;

	// Token: 0x04000032 RID: 50
	public AudioClip fallFar;

	// Token: 0x04000033 RID: 51
	public AudioClip step;

	// Token: 0x04000034 RID: 52
	private bool fellFar;

	// Token: 0x04000035 RID: 53
	private Vector3 lastTrueVel;

	// Token: 0x04000036 RID: 54
	private float stepTimer;

	// Token: 0x04000037 RID: 55
	private float stepTime = 0.5f;

	// Token: 0x04000038 RID: 56
	public GameObject hat;

	// Token: 0x04000039 RID: 57
	public GameObject hatHeld;
}

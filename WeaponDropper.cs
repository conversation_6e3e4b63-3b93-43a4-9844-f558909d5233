﻿using System;
using FishNet;
using FishNet.Connection;
using FishNet.Managing;
using FishNet.Object;
using FishNet.Object.Delegating;
using FishNet.Serializing;
using FishNet.Transporting;
using UnityEngine;

// Token: 0x02000077 RID: 119
public class WeaponDropper : Spawner
{
	// Token: 0x0600051D RID: 1309 RVA: 0x00021A61 File Offset: 0x0001FC61
	private void OnEnable()
	{
		PauseManager.OnRoundStarted += this.StartNewRound;
	}

	// Token: 0x0600051E RID: 1310 RVA: 0x00021A74 File Offset: 0x0001FC74
	private void OnDisable()
	{
		PauseManager.OnRoundStarted -= this.StartNewRound;
	}

	// Token: 0x0600051F RID: 1311 RVA: 0x00021A87 File Offset: 0x0001FC87
	private void StartNewRound()
	{
		this.readyToSpawn = true;
	}

	// Token: 0x06000520 RID: 1312 RVA: 0x00021A90 File Offset: 0x0001FC90
	[ServerRpc(RunLocally = true, RequireOwnership = false)]
	private void Drop(GameObject spawned)
	{
		this.RpcWriter___Server_Drop_1934289915(spawned);
		this.RpcLogic___Drop_1934289915(spawned);
	}

	// Token: 0x06000521 RID: 1313 RVA: 0x00021AA6 File Offset: 0x0001FCA6
	protected override void Update()
	{
		if (!this.readyToSpawn)
		{
			return;
		}
		base.Update();
	}

	// Token: 0x06000522 RID: 1314 RVA: 0x00021AB8 File Offset: 0x0001FCB8
	public override void TrySpawn()
	{
		GameObject gameObject;
		if (SpawnerManager.Instance.SyncAccessor_randomiseWeapons)
		{
			gameObject = global::UnityEngine.Object.Instantiate<GameObject>(SpawnerManager.Instance.GetRandomSpawnableWeapon(), base.transform.position + Vector3.up / 2f, Quaternion.identity);
		}
		else
		{
			Vector3 vector = base.transform.position + Vector3.up / 2f;
			gameObject = global::UnityEngine.Object.Instantiate<GameObject>(this.itemsToSpawn[global::UnityEngine.Random.Range(0, this.itemsToSpawn.Length)], vector, Quaternion.identity);
		}
		base.ServerManager.Spawn(gameObject, null);
		this.Drop(gameObject);
	}

	// Token: 0x06000524 RID: 1316 RVA: 0x00021B6F File Offset: 0x0001FD6F
	public override void NetworkInitialize___Early()
	{
		if (this.NetworkInitializeEarly_WeaponDropper_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeEarly_WeaponDropper_Assembly-CSharp.dll = true;
		base.NetworkInitialize___Early();
		base.RegisterServerRpc(0U, new ServerRpcDelegate(this.RpcReader___Server_Drop_1934289915));
	}

	// Token: 0x06000525 RID: 1317 RVA: 0x00021B9F File Offset: 0x0001FD9F
	public override void NetworkInitialize__Late()
	{
		if (this.NetworkInitializeLate_WeaponDropper_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeLate_WeaponDropper_Assembly-CSharp.dll = true;
		base.NetworkInitialize__Late();
	}

	// Token: 0x06000526 RID: 1318 RVA: 0x00021BB8 File Offset: 0x0001FDB8
	public override void NetworkInitializeIfDisabled()
	{
		this.NetworkInitialize___Early();
		this.NetworkInitialize__Late();
	}

	// Token: 0x06000527 RID: 1319 RVA: 0x00021BC8 File Offset: 0x0001FDC8
	private void RpcWriter___Server_Drop_1934289915(GameObject spawned)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteGameObject(spawned);
		base.SendServerRpc(0U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000528 RID: 1320 RVA: 0x00021C6F File Offset: 0x0001FE6F
	private void RpcLogic___Drop_1934289915(GameObject spawned)
	{
		spawned.GetComponent<ItemBehaviour>().DispenserDrop(this.ejectDirection);
	}

	// Token: 0x06000529 RID: 1321 RVA: 0x00021C84 File Offset: 0x0001FE84
	private void RpcReader___Server_Drop_1934289915(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		GameObject gameObject = PooledReader0.ReadGameObject();
		if (!base.IsServer)
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___Drop_1934289915(gameObject);
	}

	// Token: 0x0600052A RID: 1322 RVA: 0x00021CC2 File Offset: 0x0001FEC2
	public override void Awake()
	{
		this.NetworkInitialize___Early();
		base.Awake();
		this.NetworkInitialize__Late();
	}

	// Token: 0x0600052B RID: 1323 RVA: 0x000023D6 File Offset: 0x000005D6
	public override void Awake___UserLogic()
	{
	}

	// Token: 0x04000510 RID: 1296
	public GameObject[] itemsToSpawn;

	// Token: 0x04000511 RID: 1297
	private bool readyToSpawn;

	// Token: 0x04000512 RID: 1298
	[SerializeField]
	private Vector3 ejectDirection = Vector3.zero;

	// Token: 0x04000513 RID: 1299
	private bool NetworkInitializeEarly_WeaponDropper_Assembly-CSharp.dll;

	// Token: 0x04000514 RID: 1300
	private bool NetworkInitializeLate_WeaponDropper_Assembly-CSharp.dll;
}

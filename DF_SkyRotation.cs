﻿using System;
using UnityEngine;

// Token: 0x0200001B RID: 27
public class DF_SkyRotation : MonoBehaviour
{
	// Token: 0x060000DC RID: 220 RVA: 0x000064B7 File Offset: 0x000046B7
	private void Update()
	{
		RenderSettings.skybox.SetFloat("_Rotation", Time.time * this.skyboxRotationX);
		base.transform.Rotate(0f, -(Time.deltaTime * this.skyboxRotationX), 0f);
	}

	// Token: 0x040000C5 RID: 197
	public float skyboxRotationX = 1.5f;
}

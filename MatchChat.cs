﻿using System;
using System.Collections.Generic;
using FishNet;
using FishNet.Connection;
using FishNet.Managing;
using FishNet.Object;
using FishNet.Object.Delegating;
using FishNet.Serializing;
using FishNet.Transporting;
using Goodgulf.Graphics;
using TMPro;
using UnityEngine;

// Token: 0x020000DE RID: 222
public class MatchChat : NetworkBehaviour
{
	// Token: 0x170000AF RID: 175
	// (get) Token: 0x06000BE1 RID: 3041 RVA: 0x000533AD File Offset: 0x000515AD
	// (set) Token: 0x06000BE2 RID: 3042 RVA: 0x000533B4 File Offset: 0x000515B4
	public static MatchChat Instance { get; private set; }

	// Token: 0x06000BE3 RID: 3043 RVA: 0x000533BC File Offset: 0x000515BC
	public virtual void Awake()
	{
		this.NetworkInitialize___Early();
		this.Awake___UserLogic();
		this.NetworkInitialize__Late();
	}

	// Token: 0x06000BE4 RID: 3044 RVA: 0x000533DB File Offset: 0x000515DB
	public override void OnStartClient()
	{
		base.OnStartClient();
	}

	// Token: 0x06000BE5 RID: 3045 RVA: 0x000533E4 File Offset: 0x000515E4
	[Client]
	public void Update()
	{
		if (base.IsNetworked && !base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (this.ChatBox.activeSelf)
		{
			this.inputLine.ActivateInputField();
		}
		if (Input.GetKeyDown(KeyCode.Return))
		{
			this.ChatBox.SetActive(!this.ChatBox.activeSelf);
		}
		if (Input.GetKeyDown(KeyCode.Escape))
		{
			this.ChatBox.SetActive(false);
		}
	}

	// Token: 0x06000BE6 RID: 3046 RVA: 0x000534A5 File Offset: 0x000516A5
	public void OnInputEnterShout()
	{
		this.RpcSendChatLine(this.localPlayer.PlayerName + " : " + this.inputLine.text);
		this.inputLine.text = "";
	}

	// Token: 0x06000BE7 RID: 3047 RVA: 0x000534DD File Offset: 0x000516DD
	[ServerRpc(RequireOwnership = false)]
	public void RpcSendChatLine(string line)
	{
		this.RpcWriter___Server_RpcSendChatLine_3615296227(line);
	}

	// Token: 0x06000BE8 RID: 3048 RVA: 0x000534EC File Offset: 0x000516EC
	[ObserversRpc]
	private void RpcSendChatLineToAllObservers(string line)
	{
		this.RpcWriter___Observers_RpcSendChatLineToAllObservers_3615296227(line);
	}

	// Token: 0x06000BE9 RID: 3049 RVA: 0x00053503 File Offset: 0x00051703
	private void OnDisable()
	{
		this.ChatBox.transform.localScale = Vector3.zero;
		this.ChatBox.SetActive(true);
	}

	// Token: 0x06000BEB RID: 3051 RVA: 0x00053528 File Offset: 0x00051728
	public virtual void NetworkInitialize___Early()
	{
		if (this.NetworkInitializeEarly_MatchChat_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeEarly_MatchChat_Assembly-CSharp.dll = true;
		base.RegisterServerRpc(0U, new ServerRpcDelegate(this.RpcReader___Server_RpcSendChatLine_3615296227));
		base.RegisterObserversRpc(1U, new ClientRpcDelegate(this.RpcReader___Observers_RpcSendChatLineToAllObservers_3615296227));
	}

	// Token: 0x06000BEC RID: 3052 RVA: 0x00053574 File Offset: 0x00051774
	public virtual void NetworkInitialize__Late()
	{
		if (this.NetworkInitializeLate_MatchChat_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeLate_MatchChat_Assembly-CSharp.dll = true;
	}

	// Token: 0x06000BED RID: 3053 RVA: 0x00053587 File Offset: 0x00051787
	public override void NetworkInitializeIfDisabled()
	{
		this.NetworkInitialize___Early();
		this.NetworkInitialize__Late();
	}

	// Token: 0x06000BEE RID: 3054 RVA: 0x00053598 File Offset: 0x00051798
	private void RpcWriter___Server_RpcSendChatLine_3615296227(string line)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteString(line);
		base.SendServerRpc(0U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000BEF RID: 3055 RVA: 0x0005363F File Offset: 0x0005183F
	public void RpcLogic___RpcSendChatLine_3615296227(string line)
	{
		this.RpcSendChatLineToAllObservers(line);
	}

	// Token: 0x06000BF0 RID: 3056 RVA: 0x00053648 File Offset: 0x00051848
	private void RpcReader___Server_RpcSendChatLine_3615296227(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		string text = PooledReader0.ReadString();
		if (!base.IsServer)
		{
			return;
		}
		this.RpcLogic___RpcSendChatLine_3615296227(text);
	}

	// Token: 0x06000BF1 RID: 3057 RVA: 0x0005367C File Offset: 0x0005187C
	private void RpcWriter___Observers_RpcSendChatLineToAllObservers_3615296227(string line)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteString(line);
		base.SendObserversRpc(1U, writer, channel, DataOrderType.Default, false, false, false);
		writer.Store();
	}

	// Token: 0x06000BF2 RID: 3058 RVA: 0x00053734 File Offset: 0x00051934
	private void RpcLogic___RpcSendChatLineToAllObservers_3615296227(string line)
	{
		int childCount = this.parentForChatLines.childCount;
		List<GameObject> list = new List<GameObject>();
		for (int i = 0; i < childCount; i++)
		{
			GameObject gameObject = this.parentForChatLines.GetChild(i).gameObject;
			if (gameObject.GetComponent<TMP_Text>() != null)
			{
				list.Add(gameObject);
			}
		}
		for (int j = list.Count - 1; j >= 0; j--)
		{
			GameObject gameObject2 = list[j];
			if (gameObject2.GetComponent<MatchChatLine>().deleteMe)
			{
				list.RemoveAt(j);
				global::UnityEngine.Object.Destroy(gameObject2);
			}
			else
			{
				RectTransform component = gameObject2.GetComponent<RectTransform>();
				Vector2 anchoredPosition = component.anchoredPosition;
				component.anchoredPosition = new Vector2(anchoredPosition.x, anchoredPosition.y + ((this.previousLine.Length > 32) ? 80.5f : 35f));
			}
		}
		GameObject gameObject3 = global::UnityEngine.Object.Instantiate<GameObject>(this.chatLinePrefab, this.parentForChatLines);
		gameObject3.GetComponent<TMP_Text>().text = line;
		MatchChatLine component2 = gameObject3.GetComponent<MatchChatLine>();
		component2.duration = this.duration;
		component2.fadeDuration = this.fadeDuration;
		component2.StartDuration();
		this.previousLine = line;
	}

	// Token: 0x06000BF3 RID: 3059 RVA: 0x00053850 File Offset: 0x00051A50
	private void RpcReader___Observers_RpcSendChatLineToAllObservers_3615296227(PooledReader PooledReader0, Channel channel)
	{
		string text = PooledReader0.ReadString();
		if (!base.IsClient)
		{
			return;
		}
		this.RpcLogic___RpcSendChatLineToAllObservers_3615296227(text);
	}

	// Token: 0x06000BF4 RID: 3060 RVA: 0x00053884 File Offset: 0x00051A84
	public virtual void Awake___UserLogic()
	{
		if (MatchChat.Instance != null && MatchChat.Instance != this)
		{
			global::UnityEngine.Object.Destroy(this);
			return;
		}
		MatchChat.Instance = this;
		this.inputLine = GameObject.Find("ChatInputField2").GetComponent<TMP_InputField>();
		this.parentForChatLines = GameObject.Find("ChatPosition").transform;
		this.ChatBox = GameObject.Find("ChatInputField2");
		this.ChatBox.transform.localScale = Vector3.one;
		this.ChatBox.SetActive(false);
	}

	// Token: 0x04000A0A RID: 2570
	private GameObject ChatBox;

	// Token: 0x04000A0B RID: 2571
	public TMP_InputField inputLine;

	// Token: 0x04000A0C RID: 2572
	public GameObject chatLinePrefab;

	// Token: 0x04000A0D RID: 2573
	public Transform parentForChatLines;

	// Token: 0x04000A0E RID: 2574
	public float duration;

	// Token: 0x04000A0F RID: 2575
	public float fadeDuration;

	// Token: 0x04000A10 RID: 2576
	public ClientInstance localPlayer;

	// Token: 0x04000A11 RID: 2577
	private string previousLine;

	// Token: 0x04000A12 RID: 2578
	private bool NetworkInitializeEarly_MatchChat_Assembly-CSharp.dll;

	// Token: 0x04000A13 RID: 2579
	private bool NetworkInitializeLate_MatchChat_Assembly-CSharp.dll;
}

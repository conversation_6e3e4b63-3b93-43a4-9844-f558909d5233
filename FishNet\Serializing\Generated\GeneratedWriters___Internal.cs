﻿using System;
using System.Collections.Generic;
using System.Runtime.InteropServices;
using UnityEngine;

namespace FishNet.Serializing.Generated
{
	// Token: 0x020001A8 RID: 424
	[StructLayout(LayoutKind.Auto, CharSet = CharSet.Auto)]
	public static class GeneratedWriters___Internal
	{
		// Token: 0x06001069 RID: 4201 RVA: 0x0006AEB4 File Offset: 0x000690B4
		[RuntimeInitializeOnLoadMethod]
		private static void InitializeOnce()
		{
			GenericWriter<ChatBroadcast.Message>.Write = new Action<Writer, ChatBroadcast.Message>(GeneratedWriters___Internal.Write___ChatBroadcast/MessageFishNet.Serializing.Generated);
			GenericWriter<PlayerHealth>.Write = new Action<Writer, PlayerHealth>(GeneratedWriters___Internal.Write___PlayerHealthFishNet.Serializing.Generated);
			GenericWriter<PlayerValues>.Write = new Action<Writer, PlayerValues>(GeneratedWriters___Internal.Write___PlayerValuesFishNet.Serializing.Generated);
			GenericWriter<PlayerSpawnObject>.Write = new Action<Writer, PlayerSpawnObject>(GeneratedWriters___Internal.Write___PlayerSpawnObjectFishNet.Serializing.Generated);
			GenericWriter<ClientInstance>.Write = new Action<Writer, ClientInstance>(GeneratedWriters___Internal.Write___ClientInstanceFishNet.Serializing.Generated);
			GenericWriter<List<int>>.Write = new Action<Writer, List<int>>(GeneratedWriters___Internal.Write___System.Collections.Generic.List`1<System.Int32>FishNet.Serializing.Generated);
			GenericWriter<string[]>.Write = new Action<Writer, string[]>(GeneratedWriters___Internal.Write___System.String[]FishNet.Serializing.Generated);
			GenericWriter<Weapon>.Write = new Action<Writer, Weapon>(GeneratedWriters___Internal.Write___WeaponFishNet.Serializing.Generated);
			GenericWriter<Dictionary<int, int>>.Write = new Action<Writer, Dictionary<int, int>>(GeneratedWriters___Internal.Write___System.Collections.Generic.Dictionary`2<System.Int32,System.Int32>FishNet.Serializing.Generated);
			GenericWriter<WeaponData>.Write = new Action<Writer, WeaponData>(GeneratedWriters___Internal.Write___WeaponDataFishNet.Serializing.Generated);
		}

		// Token: 0x0600106A RID: 4202 RVA: 0x0006AF6C File Offset: 0x0006916C
		public static void Write___ChatBroadcast/MessageFishNet.Serializing.Generated(this Writer writer, ChatBroadcast.Message value)
		{
			writer.WriteString(value.username);
			writer.WriteString(value.message);
		}

		// Token: 0x0600106B RID: 4203 RVA: 0x0006AFA0 File Offset: 0x000691A0
		public static void Write___PlayerHealthFishNet.Serializing.Generated(this Writer writer, PlayerHealth value)
		{
			writer.WriteNetworkBehaviour(value);
		}

		// Token: 0x0600106C RID: 4204 RVA: 0x0006AFBC File Offset: 0x000691BC
		public static void Write___PlayerValuesFishNet.Serializing.Generated(this Writer writer, PlayerValues value)
		{
			writer.WriteNetworkBehaviour(value);
		}

		// Token: 0x0600106D RID: 4205 RVA: 0x0006AFD8 File Offset: 0x000691D8
		public static void Write___PlayerSpawnObjectFishNet.Serializing.Generated(this Writer writer, PlayerSpawnObject value)
		{
			writer.WriteNetworkBehaviour(value);
		}

		// Token: 0x0600106E RID: 4206 RVA: 0x0006AFF4 File Offset: 0x000691F4
		public static void Write___ClientInstanceFishNet.Serializing.Generated(this Writer writer, ClientInstance value)
		{
			writer.WriteNetworkBehaviour(value);
		}

		// Token: 0x0600106F RID: 4207 RVA: 0x0006B010 File Offset: 0x00069210
		public static void Write___System.Collections.Generic.List`1<System.Int32>FishNet.Serializing.Generated(this Writer writer, List<int> value)
		{
			writer.WriteList<int>(value);
		}

		// Token: 0x06001070 RID: 4208 RVA: 0x0006B02C File Offset: 0x0006922C
		public static void Write___System.String[]FishNet.Serializing.Generated(this Writer writer, string[] value)
		{
			writer.WriteArray<string>(value);
		}

		// Token: 0x06001071 RID: 4209 RVA: 0x0006B048 File Offset: 0x00069248
		public static void Write___WeaponFishNet.Serializing.Generated(this Writer writer, Weapon value)
		{
			writer.WriteNetworkBehaviour(value);
		}

		// Token: 0x06001072 RID: 4210 RVA: 0x0006B064 File Offset: 0x00069264
		public static void Write___System.Collections.Generic.Dictionary`2<System.Int32,System.Int32>FishNet.Serializing.Generated(this Writer writer, Dictionary<int, int> value)
		{
			writer.WriteDictionary<int, int>(value);
		}

		// Token: 0x06001073 RID: 4211 RVA: 0x0006B080 File Offset: 0x00069280
		public static void Write___WeaponDataFishNet.Serializing.Generated(this Writer writer, WeaponData value)
		{
			writer.WriteString(value.WeaponName);
			writer.WriteUInt32(value.SpawnChance, AutoPackType.Packed);
			writer.WriteBoolean(value.IsSpawnable);
		}
	}
}

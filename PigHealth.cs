﻿using System;
using UnityEngine;

// Token: 0x02000005 RID: 5
public class PigHealth : Pigeon
{
	// Token: 0x0600001F RID: 31 RVA: 0x000023D6 File Offset: 0x000005D6
	private void Start()
	{
	}

	// Token: 0x06000020 RID: 32 RVA: 0x00002E03 File Offset: 0x00001003
	public new void Die()
	{
		global::UnityEngine.Object.Destroy(base.gameObject);
	}

	// Token: 0x06000021 RID: 33 RVA: 0x000023D6 File Offset: 0x000005D6
	private void Update()
	{
	}

	// Token: 0x06000023 RID: 35 RVA: 0x00002E99 File Offset: 0x00001099
	public override void NetworkInitialize___Early()
	{
		if (this.NetworkInitializeEarly_PigHealth_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeEarly_PigHealth_Assembly-CSharp.dll = true;
		base.NetworkInitialize___Early();
	}

	// Token: 0x06000024 RID: 36 RVA: 0x00002EB2 File Offset: 0x000010B2
	public override void NetworkInitialize__Late()
	{
		if (this.NetworkInitializeLate_PigHealth_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeLate_PigHealth_Assembly-CSharp.dll = true;
		base.NetworkInitialize__Late();
	}

	// Token: 0x06000025 RID: 37 RVA: 0x00002ECB File Offset: 0x000010CB
	public override void NetworkInitializeIfDisabled()
	{
		this.NetworkInitialize___Early();
		this.NetworkInitialize__Late();
	}

	// Token: 0x06000026 RID: 38 RVA: 0x00002ED9 File Offset: 0x000010D9
	public override void Awake()
	{
		this.NetworkInitialize___Early();
		base.Awake();
		this.NetworkInitialize__Late();
	}

	// Token: 0x06000027 RID: 39 RVA: 0x000023D6 File Offset: 0x000005D6
	public override void Awake___UserLogic()
	{
	}

	// Token: 0x0400003A RID: 58
	private bool NetworkInitializeEarly_PigHealth_Assembly-CSharp.dll;

	// Token: 0x0400003B RID: 59
	private bool NetworkInitializeLate_PigHealth_Assembly-CSharp.dll;
}

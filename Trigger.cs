﻿using System;
using UnityEngine;

// Token: 0x0200014B RID: 331
public class Trigger : MonoBehaviour
{
	// Token: 0x06000ED4 RID: 3796 RVA: 0x0006182C File Offset: 0x0005FA2C
	private void OnTriggerEnter(Collider Intruder)
	{
		if (Intruder.tag == "Player")
		{
			if (this.Glass)
			{
				this.Glass.Shatter(Vector2.zero, this.Glass.transform.forward);
			}
			global::UnityEngine.Object.Destroy(base.gameObject);
		}
	}

	// Token: 0x04000D6E RID: 3438
	public ShatterableGlass Glass;
}

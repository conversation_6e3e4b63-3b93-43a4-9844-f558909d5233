﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Diagnostics;
using DG.Tweening;
using DG.Tweening.Core;
using DG.Tweening.Plugins.Options;
using FishNet;
using FishySteamworks;
using HeathenEngineering.SteamworksIntegration.UI;
using TMPro;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.InputSystem;
using UnityEngine.SceneManagement;
using UnityEngine.UI;

// Token: 0x020000E1 RID: 225
public class PauseManager : MonoBehaviour
{
	// Token: 0x06000C0F RID: 3087 RVA: 0x00053F4F File Offset: 0x0005214F
	public void ShowInfoPopup(string text)
	{
		if (this.TextToShow.Count > 0)
		{
			this.TextToShow.Add(text);
			return;
		}
		this.infoPopup.SetActive(true);
		this.infoPopup.GetComponentInChildren<TextMeshProUGUI>().text = text;
	}

	// Token: 0x06000C10 RID: 3088 RVA: 0x00053F8C File Offset: 0x0005218C
	public void ShowNextInfoPopup()
	{
		if (this.TextToShow.Count == 0)
		{
			this.infoPopup.SetActive(false);
			return;
		}
		this.infoPopup.GetComponentInChildren<TextMeshProUGUI>().text = this.TextToShow[0];
		this.TextToShow.RemoveAt(0);
	}

	// Token: 0x14000001 RID: 1
	// (add) Token: 0x06000C11 RID: 3089 RVA: 0x00053FDC File Offset: 0x000521DC
	// (remove) Token: 0x06000C12 RID: 3090 RVA: 0x00054010 File Offset: 0x00052210
	public static event PauseManager.StartRoundAction OnRoundStarted;

	// Token: 0x06000C13 RID: 3091 RVA: 0x00054043 File Offset: 0x00052243
	public void InvokeRoundStarted()
	{
		PauseManager.StartRoundAction onRoundStarted = PauseManager.OnRoundStarted;
		if (onRoundStarted == null)
		{
			return;
		}
		onRoundStarted();
	}

	// Token: 0x14000002 RID: 2
	// (add) Token: 0x06000C14 RID: 3092 RVA: 0x00054054 File Offset: 0x00052254
	// (remove) Token: 0x06000C15 RID: 3093 RVA: 0x00054088 File Offset: 0x00052288
	public static event PauseManager.BeforeSpawnAction OnBeforeSpawn;

	// Token: 0x06000C16 RID: 3094 RVA: 0x000540BB File Offset: 0x000522BB
	public void InvokeBeforeSpawn()
	{
		PauseManager.BeforeSpawnAction onBeforeSpawn = PauseManager.OnBeforeSpawn;
		if (onBeforeSpawn == null)
		{
			return;
		}
		onBeforeSpawn();
	}

	// Token: 0x06000C17 RID: 3095 RVA: 0x000540CC File Offset: 0x000522CC
	private void Awake()
	{
		if (PauseManager.Instance == null)
		{
			PauseManager.Instance = this;
			global::UnityEngine.Object.DontDestroyOnLoad(base.gameObject);
		}
		else
		{
			global::UnityEngine.Object.Destroy(base.gameObject);
		}
		if (this.optionsMenu != null)
		{
			this.optionsMenu.SetActive(true);
			this.optionsMenu.transform.localScale = Vector3.zero;
		}
		this.ChatBox = GameObject.Find("ChatInputField2");
		this.nonSteamworksTransport = InstanceFinder.TransportManager.Transport != InstanceFinder.NetworkManager.gameObject.GetComponent<global::FishySteamworks.FishySteamworks>();
		this._playerInput = InputManager.inputActions;
	}

	// Token: 0x06000C18 RID: 3096 RVA: 0x00054174 File Offset: 0x00052374
	private void OnEnable()
	{
		this.menu.Enable();
		this.menu.performed += this.Menu;
		this.gamepadAny = this._playerInput.Player.AnyGamepad;
		this.gamepadAny.Enable();
		this.gamepadAny.performed += this.ChangeControlSchemeGamepad;
		this.keyboardAny = this._playerInput.Player.Any;
		this.keyboardAny.Enable();
		this.keyboardAny.performed += this.ChangeControlSchemeKeyboard;
	}

	// Token: 0x06000C19 RID: 3097 RVA: 0x0005421C File Offset: 0x0005241C
	private void OnDisable()
	{
		this.menu.Disable();
		this.menu.performed -= this.Menu;
		this.gamepadAny.Disable();
		this.gamepadAny.performed -= this.ChangeControlSchemeGamepad;
		this.keyboardAny.Disable();
		this.keyboardAny.performed -= this.ChangeControlSchemeKeyboard;
	}

	// Token: 0x06000C1A RID: 3098 RVA: 0x0005428F File Offset: 0x0005248F
	public void WriteLog(string text)
	{
		if (!InstanceFinder.NetworkManager.IsOffline)
		{
			MatchLogs.Instance.WriteLog(text);
		}
	}

	// Token: 0x06000C1B RID: 3099 RVA: 0x000542A8 File Offset: 0x000524A8
	public void WriteOfflineLog(string text)
	{
		MatchLogsOffline.Instance.WriteLog(text);
	}

	// Token: 0x06000C1C RID: 3100 RVA: 0x000542B5 File Offset: 0x000524B5
	public void CopyText(TextMeshProUGUI text)
	{
		this.WriteOfflineLog("Successfully copied lobby id");
		GUIUtility.systemCopyBuffer = text.text;
	}

	// Token: 0x06000C1D RID: 3101 RVA: 0x000542D0 File Offset: 0x000524D0
	private void Update()
	{
		if (this.gamepad && this.interactPromptTextGamepad)
		{
			this.InteractPromptLetter = this.interactPromptTextGamepad.text;
		}
		else if (this.interactPromptText)
		{
			this.InteractPromptLetter = this.interactPromptText.text;
		}
		else
		{
			this.InteractPromptLetter = "?";
		}
		this.HandleServerState();
		this.HandleServerStateWhenOnePlayerIsLeft();
		this.HandleInputDetection();
		if (this.onEndRoundScreen)
		{
			this.deadText.text = "";
		}
		if (this.ChatBox)
		{
			this.chatting = this.ChatBox.activeSelf && this.canChat;
		}
		if (!this.inMainMenu && SceneManager.GetActiveScene().name == "MainMenu")
		{
			this.gameStarted = false;
			Debug.Log("Returned to main menu");
			SceneMotor.Instance.testMap = false;
			this.friendListScript.UpdateDisplay();
			this.StoppingMapCoroutine();
			ProgressManager.Instance.ReturnToMenu();
			Screen.fullScreen = Settings.Instance.isFullscreen;
			if (RoundManager.Instance != null)
			{
				RoundManager.Instance.StopCoroutine();
			}
			Settings.Instance.UpdateElo();
			MapsManager.Instance.inExplorationMap = false;
			this.enemyHealthText.text = "";
			this.deadText.text = "";
		}
		this.inMainMenu = SceneManager.GetActiveScene().name == "MainMenu";
		this.inVictoryMenu = SceneManager.GetActiveScene().name == "VictoryScene";
		if (this.inMainMenu || this.inVictoryMenu)
		{
			this.setIndex = 0;
		}
		if (this.serverDownPopup)
		{
			this.otherPauseBools = this.serverDownPopup.activeSelf || this.tabScreen.activeSelf || this.onePlayerLeftPopup.activeSelf;
		}
		if (SceneManager.GetActiveScene().name == "MainMenu")
		{
			if (!this.mainMenu.activeSelf && !this.optionsMenu.activeSelf)
			{
				this.mainMenu.SetActive(true);
			}
			else if (this.optionsMenu.activeSelf)
			{
				this.mainMenu.SetActive(false);
			}
			Cursor.lockState = CursorLockMode.None;
			Cursor.visible = true;
			this.steamPlaying = true;
		}
		if (this.tabScreen)
		{
			if (!this.inMainMenu)
			{
				this.tabScreen.SetActive(Input.GetKey(KeyCode.Tab));
			}
			else
			{
				this.tabScreen.SetActive(false);
			}
		}
		this.VoiceChat();
		if (SceneManager.GetActiveScene().name == "MainMenu")
		{
			return;
		}
		if (!this.nonSteamworksTransport)
		{
			this.pause = this.pauseMenu.activeSelf;
		}
		this.mainMenu.SetActive(false);
		Crosshair.Instance.image.enabled = !this.pauseMenu.activeSelf;
		if (this.pause || this.otherPauseBools)
		{
			Cursor.lockState = CursorLockMode.None;
			Cursor.visible = true;
			return;
		}
		Cursor.lockState = CursorLockMode.Locked;
		Cursor.visible = false;
	}

	// Token: 0x06000C1E RID: 3102 RVA: 0x000545F8 File Offset: 0x000527F8
	private void LateUpdate()
	{
		if (this.gamepad)
		{
			if (EventSystem.current.currentSelectedGameObject == null)
			{
				if (global::UnityEngine.Object.FindObjectsOfType<Button>().Length != 0)
				{
					EventSystem.current.SetSelectedGameObject(global::UnityEngine.Object.FindObjectsOfType<Button>()[0].gameObject);
					return;
				}
			}
			else if (!EventSystem.current.currentSelectedGameObject.activeInHierarchy && global::UnityEngine.Object.FindObjectsOfType<Button>().Length != 0)
			{
				EventSystem.current.SetSelectedGameObject(global::UnityEngine.Object.FindObjectsOfType<Button>()[0].gameObject);
				Debug.Log(EventSystem.current.currentSelectedGameObject.activeSelf);
			}
		}
	}

	// Token: 0x06000C1F RID: 3103 RVA: 0x000023D6 File Offset: 0x000005D6
	private void HandleInputDetection()
	{
	}

	// Token: 0x06000C20 RID: 3104 RVA: 0x00054688 File Offset: 0x00052888
	private void ChangeControlSchemeGamepad(InputAction.CallbackContext ctx)
	{
		if (Gamepad.current != null)
		{
			if (MenuController.Instance != null && MenuController.Instance.startMenu.activeSelf)
			{
				MenuController.Instance.OpenGame();
			}
			this.gamepad = true;
			if (EventSystem.current.currentSelectedGameObject == null)
			{
				if (global::UnityEngine.Object.FindObjectsOfType<Button>().Length != 0)
				{
					EventSystem.current.SetSelectedGameObject(global::UnityEngine.Object.FindObjectsOfType<Button>()[0].gameObject);
					return;
				}
			}
			else if (!EventSystem.current.currentSelectedGameObject.activeSelf && global::UnityEngine.Object.FindObjectsOfType<Button>().Length != 0)
			{
				EventSystem.current.SetSelectedGameObject(global::UnityEngine.Object.FindObjectsOfType<Button>()[0].gameObject);
			}
		}
	}

	// Token: 0x06000C21 RID: 3105 RVA: 0x0005472E File Offset: 0x0005292E
	private void ChangeControlSchemeKeyboard(InputAction.CallbackContext ctx)
	{
		if (MenuController.Instance != null && MenuController.Instance.startMenu.activeSelf)
		{
			MenuController.Instance.OpenGame();
		}
		this.gamepad = false;
	}

	// Token: 0x06000C22 RID: 3106 RVA: 0x0005475F File Offset: 0x0005295F
	public void ChangeSelectedItem(GameObject obj)
	{
		if (!this.gamepad)
		{
			return;
		}
		EventSystem.current.firstSelectedGameObject = obj;
		EventSystem.current.SetSelectedGameObject(obj);
	}

	// Token: 0x06000C23 RID: 3107 RVA: 0x00054780 File Offset: 0x00052980
	private void Menu(InputAction.CallbackContext ctx)
	{
		if (this.nonSteamworksTransport)
		{
			this.pause = !this.pause;
			return;
		}
		if (this.optionsMenu.activeSelf)
		{
			this.optionsMenu.SetActive(false);
		}
		if (SceneManager.GetActiveScene().name == "MainMenu")
		{
			return;
		}
		this.pauseMenu.SetActive(!this.pauseMenu.activeSelf);
		if (this.pauseMenu.activeSelf)
		{
			this.ChangeSelectedItem(this.resumeButton3D);
		}
		this.firstInterface3D.ChooseState(true);
		this.firstInterface.SetActive(true);
		this.secondInterface.SetActive(false);
		if (this.pause)
		{
			Cursor.lockState = CursorLockMode.Locked;
			Cursor.visible = false;
		}
	}

	// Token: 0x06000C24 RID: 3108 RVA: 0x00054842 File Offset: 0x00052A42
	public void StartGameSteam()
	{
		this.gameStarted = true;
		SceneMotor.Instance.ServerStartGameScene();
	}

	// Token: 0x06000C25 RID: 3109 RVA: 0x00054855 File Offset: 0x00052A55
	public void SetActiveOpposite(GameObject obj)
	{
		obj.SetActive(!obj.activeSelf);
	}

	// Token: 0x06000C26 RID: 3110 RVA: 0x00054866 File Offset: 0x00052A66
	public void SetActiveOppositeScale(Transform obj)
	{
		obj.localScale = ((obj.localScale.x == 0f) ? Vector3.one : Vector3.zero);
	}

	// Token: 0x06000C27 RID: 3111 RVA: 0x0005488C File Offset: 0x00052A8C
	public void QuitGame()
	{
		Process.GetCurrentProcess().Kill();
	}

	// Token: 0x06000C28 RID: 3112 RVA: 0x00054898 File Offset: 0x00052A98
	public void MoveAmmoDisplay(bool up, bool right)
	{
		if (right)
		{
			this.rightGunAmmo.transform.DOMove(up ? new Vector3(this.rightGunAmmo.transform.position.x, this.posRightUp.position.y, this.rightGunAmmo.transform.position.z) : new Vector3(this.rightGunAmmo.transform.position.x, this.posRightDown.position.y, this.rightGunAmmo.transform.position.z), 0.3f, false).SetEase(Ease.OutSine);
			return;
		}
		this.leftGunAmmo.transform.DOMove(up ? new Vector3(this.leftGunAmmo.transform.position.x, this.posLeftUp.position.y, this.leftGunAmmo.transform.position.z) : new Vector3(this.leftGunAmmo.transform.position.x, this.posLeftDown.position.y, this.leftGunAmmo.transform.position.z), 0.3f, false).SetEase(Ease.OutSine);
	}

	// Token: 0x06000C29 RID: 3113 RVA: 0x000549EC File Offset: 0x00052BEC
	public void ChangeAmmoText(string text, string reloadText, bool right)
	{
		if (right)
		{
			this.rightGunAmmo.text = reloadText + text;
			return;
		}
		this.leftGunAmmo.text = reloadText + text;
	}

	// Token: 0x06000C2A RID: 3114 RVA: 0x00054A16 File Offset: 0x00052C16
	public void PlayMenuClip(AudioClip clip)
	{
		SoundManager.Instance.PlaySound(clip);
	}

	// Token: 0x06000C2B RID: 3115 RVA: 0x00054A23 File Offset: 0x00052C23
	private void HandleServerState()
	{
		if (InstanceFinder.ClientManager && !InstanceFinder.ClientManager.Started && this.serverStarted)
		{
			this.serverStarted = false;
			this.serverTimer = -2f;
			this.DisplayServerDownPopup();
		}
	}

	// Token: 0x06000C2C RID: 3116 RVA: 0x00054A60 File Offset: 0x00052C60
	private void HandleServerStateWhenOnePlayerIsLeft()
	{
		if (this.inMainMenu)
		{
			return;
		}
		if (SceneMotor.Instance != null && SceneMotor.Instance.testMap)
		{
			return;
		}
		if (!SteamLobby.Instance)
		{
			return;
		}
		if (InstanceFinder.NetworkManager.IsServer && SteamLobby.Instance.players.Count == 1 && !Application.isEditor && this.serverStarted)
		{
			this.serverStarted = false;
			SteamLobby.Instance.LeaveMatch();
			this.WriteOfflineLog("Player 2 left your lobby.");
		}
	}

	// Token: 0x06000C2D RID: 3117 RVA: 0x00054AE8 File Offset: 0x00052CE8
	private void StoppingMapCoroutine()
	{
		if (this.RoundDelayCoroutine == null)
		{
			return;
		}
		base.StopCoroutine(this.RoundDelayCoroutine);
		this.startRoundBackDrop.GetComponent<Image>().DOColor(new Color(this.startRoundBackDrop.GetComponent<Image>().color.r, this.startRoundBackDrop.GetComponent<Image>().color.g, this.startRoundBackDrop.GetComponent<Image>().color.b, 0f), 0f);
		this.startRoundImageOne.DOMove(this.startRoundImageOneRestPos.position, 0f, false).SetEase(Ease.OutSine);
		this.startRoundImageTwo.DOMove(this.startRoundImageTwoRestPos.position, 0f, false).SetEase(Ease.OutSine);
		this.startRoundImageThree.DOMove(this.startRoundImageThreeRestPos.position, 0f, false).SetEase(Ease.OutSine);
		this.startRoundText.text = "";
		this.startRoundTextTitle.text = "";
		this.container.localScale = Vector3.zero;
		base.StartCoroutine(this.ChangeBoolStartRound());
		this.canShow = true;
		this.startRound = false;
		this.RoundDelayCoroutine = null;
	}

	// Token: 0x06000C2E RID: 3118 RVA: 0x00054C24 File Offset: 0x00052E24
	public void StartRoundDelay(float timeTillMovementStarts)
	{
		if (!this.canShow || this.inMainMenu || this.inVictoryMenu)
		{
			Debug.Log(string.Format("Cannot start round delay animation, canShow: {0}, inMainMenu: {1}, inVictoryMenu: {2}", this.canShow, this.inMainMenu, this.inVictoryMenu));
			return;
		}
		this.RoundDelayCoroutine = base.StartCoroutine(this.StartRoundDelayCoroutine(timeTillMovementStarts));
	}

	// Token: 0x06000C2F RID: 3119 RVA: 0x00054C8D File Offset: 0x00052E8D
	private IEnumerator StartRoundDelayCoroutine(float timeTillMovementStarts)
	{
		yield return null;
		this.canShow = false;
		this.onStartRoundScreen = false;
		if (ScoreManager.Instance)
		{
			this.roundIndexText = ScoreManager.Instance.SyncAccessor_TakeIndex;
		}
		float num = 1.2f;
		float num2 = 1.8f;
		float num3 = num;
		if (this.roundIndexText == 0)
		{
			num3 += num2;
		}
		float num4 = timeTillMovementStarts - num3;
		if (num4 < 0f)
		{
			Debug.Log("Not enough time to play the the full animation!!");
			num4 = 0f;
		}
		yield return new WaitForSeconds(num4);
		if (this.roundIndexText == 0)
		{
			this.roundIndex = 1;
			this.setIndex++;
			this.container.localScale = Vector3.one;
			this.setText.text = "round " + this.setIndex.ToString();
			this.mapText.text = SceneManager.GetActiveScene().name;
			this.startRoundBackDrop.GetComponent<Image>().color = new Color(this.startRoundBackDrop.GetComponent<Image>().color.r, this.startRoundBackDrop.GetComponent<Image>().color.g, this.startRoundBackDrop.GetComponent<Image>().color.b, 0.92f);
			SoundManager.Instance.PlaySound(RoundManager.Instance.swooshClip[0]);
			this.startRoundImageOne.DOMove(this.startRoundImageOneActivePos.position, 0.4f, false).SetEase(Ease.OutSine);
			this.startRoundImageTwo.DOMove(this.startRoundImageTwoActivePos.position, 0.4f, false).SetEase(Ease.OutSine);
			this.startRoundImageThree.DOMove(this.startRoundImageThreeActivePos.position, 0.4f, false).SetEase(Ease.OutSine);
			yield return new WaitForSeconds(1.8f);
			this.startRoundBackDrop.GetComponent<Image>().DOColor(new Color(this.startRoundBackDrop.GetComponent<Image>().color.r, this.startRoundBackDrop.GetComponent<Image>().color.g, this.startRoundBackDrop.GetComponent<Image>().color.b, 0f), 0.4f);
			this.startRoundImageOne.DOMove(this.startRoundImageOneRestPos.position, 0.4f, false).SetEase(Ease.OutSine);
			this.startRoundImageTwo.DOMove(this.startRoundImageTwoRestPos.position, 0.4f, false).SetEase(Ease.OutSine);
			this.startRoundImageThree.DOMove(this.startRoundImageThreeRestPos.position, 0.4f, false).SetEase(Ease.OutSine);
			SoundManager.Instance.PlaySound(RoundManager.Instance.swooshClip[1]);
		}
		this.InvokeRoundStarted();
		yield return new WaitForSeconds(0.4f);
		this.startRoundTextTitle.text = "TAKE " + this.roundIndex.ToString();
		this.roundIndex++;
		SoundManager.Instance.PlaySound(this.startMatchClip);
		this.crosshair.SetActive(false);
		this.startRoundText.text = "GET";
		yield return new WaitForSeconds(0.4f);
		this.startRoundText.text = "READY";
		yield return new WaitForSeconds(0.4f);
		this.startRoundText.text = "GO!";
		this.crosshair.SetActive(true);
		this.onStartRoundScreen = true;
		yield return new WaitForSeconds(0.4f);
		this.startRoundText.text = "";
		this.startRoundTextTitle.text = "";
		this.container.localScale = Vector3.zero;
		base.StartCoroutine(this.ChangeBoolStartRound());
		this.canShow = true;
		yield break;
	}

	// Token: 0x06000C30 RID: 3120 RVA: 0x00054CA3 File Offset: 0x00052EA3
	private IEnumerator ChangeBoolStartRound()
	{
		yield return new WaitForSeconds(0.4f);
		this.onStartRoundScreen = false;
		yield break;
	}

	// Token: 0x06000C31 RID: 3121 RVA: 0x00054CB2 File Offset: 0x00052EB2
	public void ShowEnemyHealth(float h, PlayerHealth ph)
	{
		base.StartCoroutine(this.ShowEnemyHealthCoroutine(h, ph));
	}

	// Token: 0x06000C32 RID: 3122 RVA: 0x00054CC3 File Offset: 0x00052EC3
	private IEnumerator ShowEnemyHealthCoroutine(float h, PlayerHealth ph)
	{
		yield return new WaitForSeconds(0.2f);
		if (ph.SyncAccessor_health <= 0f)
		{
			if (!GameManager.Instance.SyncAccessor_roundWasWon)
			{
				this.enemyHealthText.text = "ENEMY HEALTH : " + Mathf.Ceil(h / 4f * 100f).ToString() + " HP";
			}
			if (!GameManager.Instance.SyncAccessor_roundWasWon)
			{
				this.deadText.text = "DEAD";
			}
		}
		yield return new WaitForSeconds(2.7f);
		this.enemyHealthText.text = "";
		this.deadText.text = "";
		yield break;
	}

	// Token: 0x06000C33 RID: 3123 RVA: 0x00054CE0 File Offset: 0x00052EE0
	public void SetActiveAfterSecond(GameObject obj)
	{
		base.StartCoroutine(this.SetActiveAfterSecondCoroutine(obj));
	}

	// Token: 0x06000C34 RID: 3124 RVA: 0x00054CF0 File Offset: 0x00052EF0
	private IEnumerator SetActiveAfterSecondCoroutine(GameObject obj)
	{
		yield return new WaitForSeconds(this.time);
		obj.SetActive(true);
		yield break;
	}

	// Token: 0x06000C35 RID: 3125 RVA: 0x00054D06 File Offset: 0x00052F06
	public void SetActiveOppositeAfterSecond(GameObject obj)
	{
		base.StartCoroutine(this.SetActiveOppositeAfterSecondCoroutine(obj));
	}

	// Token: 0x06000C36 RID: 3126 RVA: 0x00054D16 File Offset: 0x00052F16
	private IEnumerator SetActiveOppositeAfterSecondCoroutine(GameObject obj)
	{
		obj.SetActive(!obj.activeSelf);
		yield return new WaitForSeconds(this.time2);
		obj.SetActive(!obj.activeSelf);
		yield break;
	}

	// Token: 0x06000C37 RID: 3127 RVA: 0x00054D2C File Offset: 0x00052F2C
	public void DisplayServerDownPopup()
	{
		this.serverDownPopup.SetActive(true);
	}

	// Token: 0x06000C38 RID: 3128 RVA: 0x00054D3A File Offset: 0x00052F3A
	private void VoiceChat()
	{
		if (this.chatting)
		{
			return;
		}
		if (!this.voiceChatText)
		{
			return;
		}
		if (this.isRecording)
		{
			this.voiceChatText.SetActive(true);
			return;
		}
		this.voiceChatText.SetActive(false);
	}

	// Token: 0x06000C39 RID: 3129 RVA: 0x00054D74 File Offset: 0x00052F74
	public void ShowInviteViewport(bool show, Transform viewport)
	{
		if (show)
		{
			viewport.DOMove(this.shownInvitePos.position, 0.3f, false);
			return;
		}
		viewport.DOMove(this.hiddenInvitePos.position, 0.3f, false);
	}

	// Token: 0x06000C3A RID: 3130 RVA: 0x00054DAA File Offset: 0x00052FAA
	public void PlaySoundWithGamepad()
	{
		if (!this.gamepad)
		{
			return;
		}
		SoundManager.Instance.PlaySound(this.genericMenuClip);
	}

	// Token: 0x04000A26 RID: 2598
	public bool pause;

	// Token: 0x04000A27 RID: 2599
	public bool gamepad;

	// Token: 0x04000A28 RID: 2600
	public bool chatting;

	// Token: 0x04000A29 RID: 2601
	public bool startRound;

	// Token: 0x04000A2A RID: 2602
	public bool rebinding;

	// Token: 0x04000A2B RID: 2603
	public bool serverStarted;

	// Token: 0x04000A2C RID: 2604
	public bool inMainMenu;

	// Token: 0x04000A2D RID: 2605
	public bool inVictoryMenu;

	// Token: 0x04000A2E RID: 2606
	public bool otherPauseBools;

	// Token: 0x04000A2F RID: 2607
	public bool gameStarted;

	// Token: 0x04000A30 RID: 2608
	[SerializeField]
	private bool canChat;

	// Token: 0x04000A31 RID: 2609
	[Space]
	[SerializeField]
	private FriendList friendListScript;

	// Token: 0x04000A32 RID: 2610
	[Space]
	[HideInInspector]
	public bool steamPlaying;

	// Token: 0x04000A33 RID: 2611
	private GameObject ChatBox;

	// Token: 0x04000A34 RID: 2612
	public GameObject sequenceDisplayGameObject;

	// Token: 0x04000A35 RID: 2613
	public static PauseManager Instance;

	// Token: 0x04000A36 RID: 2614
	[SerializeField]
	private InputAction menu;

	// Token: 0x04000A37 RID: 2615
	[SerializeField]
	private InputAction gamepadAny;

	// Token: 0x04000A38 RID: 2616
	[SerializeField]
	private InputAction keyboardAny;

	// Token: 0x04000A39 RID: 2617
	private PlayerControls _playerInput;

	// Token: 0x04000A3A RID: 2618
	[SerializeField]
	private GameObject pauseMenu;

	// Token: 0x04000A3B RID: 2619
	public GameObject tabScreen;

	// Token: 0x04000A3C RID: 2620
	[SerializeField]
	private GameObject firstInterface;

	// Token: 0x04000A3D RID: 2621
	[SerializeField]
	private GameObject secondInterface;

	// Token: 0x04000A3E RID: 2622
	[SerializeField]
	private GameObject mainMenu;

	// Token: 0x04000A3F RID: 2623
	[SerializeField]
	private GameObject resumeButton3D;

	// Token: 0x04000A40 RID: 2624
	[SerializeField]
	private GameObject optionsMenu;

	// Token: 0x04000A41 RID: 2625
	public GameObject minimalistUi;

	// Token: 0x04000A42 RID: 2626
	public TextMeshProUGUI minimalistHealthText;

	// Token: 0x04000A43 RID: 2627
	public TextMeshProUGUI minimalFpsText;

	// Token: 0x04000A44 RID: 2628
	public TextMeshProUGUI minimalPingText;

	// Token: 0x04000A45 RID: 2629
	[SerializeField]
	private MoveUIObject firstInterface3D;

	// Token: 0x04000A46 RID: 2630
	[SerializeField]
	private GameObject serverDownPopup;

	// Token: 0x04000A47 RID: 2631
	[SerializeField]
	private GameObject onePlayerLeftPopup;

	// Token: 0x04000A48 RID: 2632
	[SerializeField]
	private GameObject infoPopup;

	// Token: 0x04000A49 RID: 2633
	private List<string> TextToShow = new List<string>();

	// Token: 0x04000A4A RID: 2634
	public TextMeshProUGUI rightGunAmmo;

	// Token: 0x04000A4B RID: 2635
	public TextMeshProUGUI leftGunAmmo;

	// Token: 0x04000A4C RID: 2636
	public TextMeshProUGUI rightGunAmmoReload;

	// Token: 0x04000A4D RID: 2637
	public TextMeshProUGUI leftGunAmmoReload;

	// Token: 0x04000A4E RID: 2638
	public TextMeshProUGUI grabPopup;

	// Token: 0x04000A4F RID: 2639
	public TextMeshProUGUI interactPopup;

	// Token: 0x04000A50 RID: 2640
	[SerializeField]
	private Transform posRightDown;

	// Token: 0x04000A51 RID: 2641
	[SerializeField]
	private Transform posRightUp;

	// Token: 0x04000A52 RID: 2642
	[SerializeField]
	private Transform posLeftDown;

	// Token: 0x04000A53 RID: 2643
	[SerializeField]
	private Transform posLeftUp;

	// Token: 0x04000A54 RID: 2644
	public AudioClip matchChatClip;

	// Token: 0x04000A55 RID: 2645
	public AudioClip genericMenuClip;

	// Token: 0x04000A56 RID: 2646
	public AudioClip pressMenuClip;

	// Token: 0x04000A57 RID: 2647
	public AudioClip releaseMenuClip;

	// Token: 0x04000A58 RID: 2648
	public AudioClip closeMenuClip;

	// Token: 0x04000A59 RID: 2649
	public AudioClip[] deathAudioClip;

	// Token: 0x04000A5A RID: 2650
	public bool nonSteamworksTransport;

	// Token: 0x04000A5B RID: 2651
	[Space]
	public string selfNameLogColor;

	// Token: 0x04000A5C RID: 2652
	public string enemyNameLogColor;

	// Token: 0x04000A5F RID: 2655
	[SerializeField]
	private TextMeshProUGUI interactPromptText;

	// Token: 0x04000A60 RID: 2656
	[SerializeField]
	private TextMeshProUGUI interactPromptTextGamepad;

	// Token: 0x04000A61 RID: 2657
	public string InteractPromptLetter;

	// Token: 0x04000A62 RID: 2658
	private float serverTimer = -2f;

	// Token: 0x04000A63 RID: 2659
	[Space]
	[SerializeField]
	private TextMeshProUGUI startRoundText;

	// Token: 0x04000A64 RID: 2660
	[SerializeField]
	private TextMeshProUGUI startRoundTextTitle;

	// Token: 0x04000A65 RID: 2661
	[SerializeField]
	private TextMeshProUGUI enemyHealthText;

	// Token: 0x04000A66 RID: 2662
	[SerializeField]
	private TextMeshProUGUI deadText;

	// Token: 0x04000A67 RID: 2663
	[SerializeField]
	private AudioClip startMatchClip;

	// Token: 0x04000A68 RID: 2664
	[SerializeField]
	private GameObject crosshair;

	// Token: 0x04000A69 RID: 2665
	public int roundIndexText;

	// Token: 0x04000A6A RID: 2666
	public int roundIndex;

	// Token: 0x04000A6B RID: 2667
	public int setIndex;

	// Token: 0x04000A6C RID: 2668
	[Space]
	[Header("Start Round Screens")]
	[SerializeField]
	private TextMeshProUGUI setText;

	// Token: 0x04000A6D RID: 2669
	[SerializeField]
	private TextMeshProUGUI mapText;

	// Token: 0x04000A6E RID: 2670
	[Space]
	[SerializeField]
	private Transform container;

	// Token: 0x04000A6F RID: 2671
	[SerializeField]
	private Transform startRoundBackDrop;

	// Token: 0x04000A70 RID: 2672
	[SerializeField]
	private Transform startRoundImageOne;

	// Token: 0x04000A71 RID: 2673
	[SerializeField]
	private Transform startRoundImageTwo;

	// Token: 0x04000A72 RID: 2674
	[SerializeField]
	private Transform startRoundImageThree;

	// Token: 0x04000A73 RID: 2675
	[Space]
	[SerializeField]
	private Transform startRoundImageOneRestPos;

	// Token: 0x04000A74 RID: 2676
	[SerializeField]
	private Transform startRoundImageOneActivePos;

	// Token: 0x04000A75 RID: 2677
	[Space]
	[SerializeField]
	private Transform startRoundImageTwoRestPos;

	// Token: 0x04000A76 RID: 2678
	[SerializeField]
	private Transform startRoundImageTwoActivePos;

	// Token: 0x04000A77 RID: 2679
	[Space]
	[SerializeField]
	private Transform startRoundImageThreeRestPos;

	// Token: 0x04000A78 RID: 2680
	[SerializeField]
	private Transform startRoundImageThreeActivePos;

	// Token: 0x04000A79 RID: 2681
	public bool onStartRoundScreen;

	// Token: 0x04000A7A RID: 2682
	public bool onEndRoundScreen;

	// Token: 0x04000A7B RID: 2683
	private Coroutine RoundDelayCoroutine;

	// Token: 0x04000A7C RID: 2684
	private bool canShow = true;

	// Token: 0x04000A7D RID: 2685
	[SerializeField]
	private float time;

	// Token: 0x04000A7E RID: 2686
	[SerializeField]
	private float time2 = 1f;

	// Token: 0x04000A7F RID: 2687
	[Space]
	[SerializeField]
	private GameObject voiceChatText;

	// Token: 0x04000A80 RID: 2688
	public bool isRecording;

	// Token: 0x04000A81 RID: 2689
	[Space]
	[SerializeField]
	private Transform hiddenInvitePos;

	// Token: 0x04000A82 RID: 2690
	[SerializeField]
	private Transform shownInvitePos;

	// Token: 0x020000E2 RID: 226
	// (Invoke) Token: 0x06000C3D RID: 3133
	public delegate void StartRoundAction();

	// Token: 0x020000E3 RID: 227
	// (Invoke) Token: 0x06000C41 RID: 3137
	public delegate void BeforeSpawnAction();
}

﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Runtime.Serialization.Formatters.Binary;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Steamworks;
using UnityEngine;

// Token: 0x02000102 RID: 258
public class SaveLoadSystem : MonoBehaviour
{
	// Token: 0x06000CFF RID: 3327 RVA: 0x00058964 File Offset: 0x00056B64
	private static Dictionary<string, object> UpgradeV2Save(string path)
	{
		Dictionary<string, object> dictionary;
		using (FileStream fileStream = File.Open(path, FileMode.Open))
		{
			dictionary = (Dictionary<string, object>)new BinaryFormatter().Deserialize(fileStream);
		}
		return dictionary;
	}

	// Token: 0x06000D00 RID: 3328 RVA: 0x000589A8 File Offset: 0x00056BA8
	private bool UpgradeSaveFile()
	{
		string text = SteamUser.GetSteamID().ToString();
		foreach (string text2 in Directory.GetFiles(Application.persistentDataPath, "*.sav"))
		{
			string fileNameWithoutExtension = Path.GetFileNameWithoutExtension(text2);
			if (fileNameWithoutExtension.Contains(text))
			{
				string[] array = fileNameWithoutExtension.Split(text, StringSplitOptions.None);
				if (array.Length >= 2)
				{
					string text3 = array[1];
					Debug.Log("SaveLoadSystem: Upgrading save file from version " + text3 + "...");
					SaveLoadSystem.SaveFileUpgradeDelegate saveFileUpgradeDelegate;
					if (SaveLoadSystem.OldSaveFileUpgradeMap.TryGetValue(text3, out saveFileUpgradeDelegate))
					{
						try
						{
							Dictionary<string, object> dictionary = saveFileUpgradeDelegate(text2);
							this.SaveFile(dictionary);
							return true;
						}
						catch (Exception ex)
						{
							Debug.LogError("Error upgrading save file from version " + text3 + ": " + ex.Message);
						}
					}
				}
			}
		}
		return false;
	}

	// Token: 0x170000C7 RID: 199
	// (get) Token: 0x06000D01 RID: 3329 RVA: 0x00058A94 File Offset: 0x00056C94
	private static string SavePath
	{
		get
		{
			return Application.persistentDataPath + "/" + SteamUser.GetSteamID().ToString() + "Version3.sav";
		}
	}

	// Token: 0x170000C8 RID: 200
	// (get) Token: 0x06000D02 RID: 3330 RVA: 0x00058AC8 File Offset: 0x00056CC8
	private static string PreviousSavePath
	{
		get
		{
			return SaveLoadSystem.SavePath + ".prev";
		}
	}

	// Token: 0x06000D03 RID: 3331 RVA: 0x00058AD9 File Offset: 0x00056CD9
	private void Awake()
	{
		if (SaveLoadSystem.Instance == null)
		{
			SaveLoadSystem.Instance = this;
			if (!SteamManager.Initialized)
			{
				Debug.LogError("SaveLoadSystem: SteamManager not initialized, cannot save or load file.");
				SaveLoadSystem.DisableSavingDueToSteam = true;
			}
			return;
		}
		global::UnityEngine.Object.Destroy(base.gameObject);
	}

	// Token: 0x06000D04 RID: 3332 RVA: 0x00058B13 File Offset: 0x00056D13
	private void Start()
	{
		if (SaveLoadSystem.DisableSavingDueToSteam)
		{
			PauseManager.Instance.ShowInfoPopup("Steam is not initialized, please restart the game!");
		}
	}

	// Token: 0x06000D05 RID: 3333 RVA: 0x00058B2C File Offset: 0x00056D2C
	[ContextMenu("Save")]
	public void Save()
	{
		if (SaveLoadSystem.DisableSavingDueToSteam)
		{
			Debug.LogError("SaveLoadSystem: SteamManager was not initialized on Awake, cannot save file.");
			return;
		}
		Dictionary<string, object> dictionary = this.LoadFile();
		if (this.SaveState(dictionary))
		{
			this.SaveFile(dictionary);
		}
	}

	// Token: 0x06000D06 RID: 3334 RVA: 0x00058B64 File Offset: 0x00056D64
	[ContextMenu("Load")]
	public void Load()
	{
		Dictionary<string, object> dictionary;
		if (SaveLoadSystem.DisableSavingDueToSteam)
		{
			Debug.LogError("SaveLoadSystem: SteamManager was not initialized on Awake, loading blank file.");
			dictionary = new Dictionary<string, object>();
		}
		else
		{
			dictionary = this.LoadFile();
		}
		this.LoadState(dictionary);
		Debug.Log("SaveLoadSystem: Loaded save file successfully.");
	}

	// Token: 0x06000D07 RID: 3335 RVA: 0x00058BA4 File Offset: 0x00056DA4
	private void SaveFile(Dictionary<string, object> state)
	{
		string text = JsonConvert.SerializeObject(state);
		if (string.IsNullOrEmpty(text))
		{
			Debug.LogError("SaveLoadSystem: Save file is empty, not saving.");
			return;
		}
		string text2 = SaveLoadSystem.SavePath + ".tmp";
		try
		{
			File.WriteAllText(text2, text);
			if (File.Exists(SaveLoadSystem.SavePath))
			{
				File.Replace(text2, SaveLoadSystem.SavePath, SaveLoadSystem.PreviousSavePath);
			}
			else
			{
				File.Move(text2, SaveLoadSystem.SavePath);
			}
		}
		catch (Exception ex)
		{
			Debug.LogError("Error saving file: " + ex.Message);
		}
		finally
		{
			if (File.Exists(text2))
			{
				File.Delete(text2);
			}
		}
	}

	// Token: 0x06000D08 RID: 3336 RVA: 0x00058C50 File Offset: 0x00056E50
	private Dictionary<string, object> LoadFile()
	{
		if (File.Exists(SaveLoadSystem.SavePath))
		{
			try
			{
				Dictionary<string, object> dictionary = JsonConvert.DeserializeObject<Dictionary<string, object>>(File.ReadAllText(SaveLoadSystem.SavePath));
				if (dictionary != null)
				{
					return dictionary;
				}
				throw new Exception("Save file is null!?");
			}
			catch (Exception ex)
			{
				Debug.LogError(string.Concat(new string[]
				{
					"Error loading save file at ",
					SaveLoadSystem.SavePath,
					": ",
					ex.Message,
					", trying previous save file..."
				}));
				goto IL_0095;
			}
		}
		if (this.UpgradeSaveFile())
		{
			return this.LoadFile();
		}
		Debug.LogError("SaveLoadSystem: No save file found at " + SaveLoadSystem.SavePath + ", trying previous save file...");
		IL_0095:
		if (File.Exists(SaveLoadSystem.PreviousSavePath))
		{
			try
			{
				Dictionary<string, object> dictionary2 = JsonConvert.DeserializeObject<Dictionary<string, object>>(File.ReadAllText(SaveLoadSystem.PreviousSavePath));
				if (dictionary2 != null)
				{
					return dictionary2;
				}
				throw new Exception("Save file is null!?");
			}
			catch (Exception ex2)
			{
				Debug.LogError(string.Concat(new string[]
				{
					"Error loading previous save file at ",
					SaveLoadSystem.PreviousSavePath,
					": ",
					ex2.Message,
					", creating new save file..."
				}));
				return new Dictionary<string, object>();
			}
		}
		Debug.LogError("SaveLoadSystem: No temporary save file found at " + SaveLoadSystem.PreviousSavePath + ", creating new save file...");
		return new Dictionary<string, object>();
	}

	// Token: 0x06000D09 RID: 3337 RVA: 0x00058DA0 File Offset: 0x00056FA0
	private bool SaveState(Dictionary<string, object> state)
	{
		float num = 0f;
		object obj;
		if (state.TryGetValue("7ec1c0a0-f12f-4b9f-a6b6-38f4c4e6519b", out obj))
		{
			JObject jobject = obj as JObject;
			if (jobject != null)
			{
				try
				{
					num = jobject["Settings"]["killsAmount"].ToObject<float>();
				}
				catch (Exception ex)
				{
					Debug.LogError("Error parsing settings state: " + ex.Message);
				}
			}
		}
		foreach (SaveableEntity saveableEntity in global::UnityEngine.Object.FindObjectsOfType<SaveableEntity>())
		{
			object obj2 = saveableEntity.SaveState();
			if (saveableEntity.Id == "7ec1c0a0-f12f-4b9f-a6b6-38f4c4e6519b")
			{
				Dictionary<string, object> dictionary = obj2 as Dictionary<string, object>;
				object obj3;
				if (dictionary != null && dictionary.TryGetValue("Settings", out obj3))
				{
					if (!(obj3 is Settings.SaveData))
					{
						Debug.LogError("SaveLoadSystem: Settings state is not a valid SaveData object? not saving...");
						return false;
					}
					Settings.SaveData saveData = (Settings.SaveData)obj3;
					if (num > saveData.killsAmount)
					{
						Debug.Log("SaveLoadSystem: Old kill count is greater than new kill count, not saving.");
						return false;
					}
				}
			}
			state[saveableEntity.Id] = obj2;
		}
		return true;
	}

	// Token: 0x06000D0A RID: 3338 RVA: 0x00058EB8 File Offset: 0x000570B8
	private void LoadState(Dictionary<string, object> state)
	{
		foreach (SaveableEntity saveableEntity in global::UnityEngine.Object.FindObjectsOfType<SaveableEntity>(true))
		{
			object obj;
			if (state.TryGetValue(saveableEntity.Id, out obj))
			{
				try
				{
					saveableEntity.LoadState((JObject)obj);
				}
				catch (Exception ex)
				{
					Debug.LogError(string.Concat(new string[] { "Error loading part of save state for ", saveableEntity.Id, ": ", ex.Message, " ", ex.StackTrace }));
				}
			}
		}
	}

	// Token: 0x04000B44 RID: 2884
	private static bool DisableSavingDueToSteam = false;

	// Token: 0x04000B45 RID: 2885
	private const string SettingsSaveId = "7ec1c0a0-f12f-4b9f-a6b6-38f4c4e6519b";

	// Token: 0x04000B46 RID: 2886
	private static readonly Dictionary<string, SaveLoadSystem.SaveFileUpgradeDelegate> OldSaveFileUpgradeMap = new Dictionary<string, SaveLoadSystem.SaveFileUpgradeDelegate> { 
	{
		"Version2",
		new SaveLoadSystem.SaveFileUpgradeDelegate(SaveLoadSystem.UpgradeV2Save)
	} };

	// Token: 0x04000B47 RID: 2887
	public static SaveLoadSystem Instance;

	// Token: 0x02000103 RID: 259
	// (Invoke) Token: 0x06000D0E RID: 3342
	private delegate Dictionary<string, object> SaveFileUpgradeDelegate(string path);
}

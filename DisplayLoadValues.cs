﻿using System;
using System.Collections;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

// Token: 0x02000124 RID: 292
public class DisplayLoadValues : MonoBehaviour
{
	// Token: 0x06000E2B RID: 3627 RVA: 0x0005E93F File Offset: 0x0005CB3F
	private void Start()
	{
		base.StartCoroutine(this.LoadValues());
	}

	// Token: 0x06000E2C RID: 3628 RVA: 0x0005E94E File Offset: 0x0005CB4E
	private IEnumerator LoadValues()
	{
		yield return new WaitForSeconds(0.1f);
		if (this.toggleTwoAxis)
		{
			base.GetComponent<Toggle>().isOn = Settings.Instance.toggleTwoAxis;
		}
		if (this.invertMouseX)
		{
			base.GetComponent<Toggle>().isOn = Settings.Instance.invertMouseX;
		}
		if (this.invertMouseY)
		{
			base.GetComponent<Toggle>().isOn = Settings.Instance.invertMouseY;
		}
		if (this.sprintToggle)
		{
			base.GetComponent<Toggle>().isOn = Settings.Instance.sprintToggle;
		}
		if (this.aimToggle)
		{
			base.GetComponent<Toggle>().isOn = Settings.Instance.aimToggle;
		}
		if (this.leanToggle)
		{
			base.GetComponent<Toggle>().isOn = Settings.Instance.leanToggle;
		}
		if (this.crouchToggle)
		{
			base.GetComponent<Toggle>().isOn = Settings.Instance.crouchToggle;
		}
		if (this.reverseSprintBind)
		{
			base.GetComponent<Toggle>().isOn = Settings.Instance.reverseSprintBind;
		}
		if (this.enableVoiceChat)
		{
			base.GetComponent<Toggle>().isOn = Settings.Instance.enableVoiceChat;
		}
		if (this.isFullscreen)
		{
			base.GetComponent<Toggle>().isOn = Settings.Instance.isFullscreen;
		}
		if (this.minimalistUi)
		{
			base.GetComponent<Toggle>().isOn = Settings.Instance.minimalistUi;
		}
		if (this.motionBlur)
		{
			base.GetComponent<Toggle>().isOn = Settings.Instance.motionBlur;
		}
		if (this.inverseFireBinding)
		{
			base.GetComponent<Toggle>().isOn = Settings.Instance.inverseFireBinding;
		}
		if (this.pushToTalk)
		{
			base.GetComponent<Toggle>().isOn = Settings.Instance.pushToTalk;
		}
		if (this.inGameMusic)
		{
			base.GetComponent<Toggle>().isOn = Settings.Instance.inGameMusic;
		}
		if (this.enableFixedCrosshair)
		{
			base.GetComponent<Toggle>().isOn = Settings.Instance.enableFixedCrosshair;
		}
		if (this.showSpeedometer)
		{
			base.GetComponent<Toggle>().isOn = Settings.Instance.showSpeedometer;
		}
		if (this.disableCrosshair)
		{
			base.GetComponent<Toggle>().isOn = Settings.Instance.disableCrosshair;
		}
		if (this.exclusiveFullscreen)
		{
			base.GetComponent<Toggle>().isOn = Settings.Instance.exclusiveFullscreen;
		}
		if (this.mouseSensitivity)
		{
			base.GetComponent<Slider>().value = PlayerPrefs.GetFloat("mouseSensitivity");
		}
		if (this.mouseAimScopeSensitivity)
		{
			base.GetComponent<Slider>().value = PlayerPrefs.GetFloat("mouseAimScopeSensitivity");
		}
		if (this.mouseAimSensitivity)
		{
			base.GetComponent<Slider>().value = PlayerPrefs.GetFloat("mouseAimSensitivity");
		}
		if (this.mouseXSensitivity)
		{
			base.GetComponent<Slider>().value = PlayerPrefs.GetFloat("mouseXSensitivity");
		}
		if (this.mouseYSensitivity)
		{
			base.GetComponent<Slider>().value = PlayerPrefs.GetFloat("mouseYSensitivity");
		}
		if (this.fovValue)
		{
			base.GetComponent<Slider>().value = PlayerPrefs.GetFloat("fovValue");
		}
		if (this.brightness)
		{
			base.GetComponent<Slider>().value = PlayerPrefs.GetFloat("brightness");
		}
		if (this.damageIntensity)
		{
			base.GetComponent<Slider>().value = PlayerPrefs.GetFloat("damageIntensity");
		}
		if (this.voiceChatVolume)
		{
			base.GetComponent<Slider>().value = PlayerPrefs.GetFloat("voiceChatVolume");
		}
		if (this.effectsVolume)
		{
			base.GetComponent<Slider>().value = PlayerPrefs.GetFloat("effectsVolume");
		}
		if (this.musicVolume)
		{
			base.GetComponent<Slider>().value = PlayerPrefs.GetFloat("musicVolume");
		}
		if (this.masterVolume)
		{
			base.GetComponent<Slider>().value = PlayerPrefs.GetFloat("masterVolume");
		}
		if (this.menuMusicVolume)
		{
			base.GetComponent<Slider>().value = PlayerPrefs.GetFloat("menuMusicVolume");
		}
		if (this.ambientVolume)
		{
			base.GetComponent<Slider>().value = PlayerPrefs.GetFloat("ambientVolume");
		}
		if (this.targetFps)
		{
			int num = PlayerPrefs.GetInt("targetFps");
			if (num < 30)
			{
				num = 30;
			}
			base.GetComponent<TMP_InputField>().text = num.ToString();
		}
		if (this.targetFpsToggle)
		{
			base.GetComponent<Toggle>().isOn = Settings.Instance.targetFpsToggle;
		}
		if (this.graphics)
		{
			base.GetComponent<TMP_Dropdown>().value = PlayerPrefs.GetInt("qualitySetting");
		}
		if (this.useVsync)
		{
			base.GetComponent<Toggle>().isOn = PlayerPrefs.GetInt("useVsync") == 1;
		}
		if (this.reduceVFX)
		{
			base.GetComponent<Toggle>().isOn = Settings.Instance.reduceVFX;
		}
		yield break;
	}

	// Token: 0x04000C99 RID: 3225
	[SerializeField]
	private bool toggleTwoAxis;

	// Token: 0x04000C9A RID: 3226
	[SerializeField]
	private bool invertMouseX;

	// Token: 0x04000C9B RID: 3227
	[SerializeField]
	private bool invertMouseY;

	// Token: 0x04000C9C RID: 3228
	[SerializeField]
	private bool sprintToggle;

	// Token: 0x04000C9D RID: 3229
	[SerializeField]
	private bool aimToggle;

	// Token: 0x04000C9E RID: 3230
	[SerializeField]
	private bool leanToggle;

	// Token: 0x04000C9F RID: 3231
	[SerializeField]
	private bool crouchToggle;

	// Token: 0x04000CA0 RID: 3232
	[SerializeField]
	private bool enableVoiceChat;

	// Token: 0x04000CA1 RID: 3233
	[SerializeField]
	private bool voiceChatVolume;

	// Token: 0x04000CA2 RID: 3234
	[SerializeField]
	private bool reverseSprintBind;

	// Token: 0x04000CA3 RID: 3235
	[SerializeField]
	private bool inverseFireBinding;

	// Token: 0x04000CA4 RID: 3236
	[SerializeField]
	private bool mouseSensitivity;

	// Token: 0x04000CA5 RID: 3237
	[SerializeField]
	private bool mouseAimScopeSensitivity;

	// Token: 0x04000CA6 RID: 3238
	[SerializeField]
	private bool mouseAimSensitivity;

	// Token: 0x04000CA7 RID: 3239
	[SerializeField]
	private bool mouseXSensitivity;

	// Token: 0x04000CA8 RID: 3240
	[SerializeField]
	private bool mouseYSensitivity;

	// Token: 0x04000CA9 RID: 3241
	[SerializeField]
	private bool fovValue;

	// Token: 0x04000CAA RID: 3242
	[SerializeField]
	private bool brightness;

	// Token: 0x04000CAB RID: 3243
	[SerializeField]
	private bool damageIntensity;

	// Token: 0x04000CAC RID: 3244
	[SerializeField]
	private bool effectsVolume;

	// Token: 0x04000CAD RID: 3245
	[SerializeField]
	private bool ambientVolume;

	// Token: 0x04000CAE RID: 3246
	[SerializeField]
	private bool musicVolume;

	// Token: 0x04000CAF RID: 3247
	[SerializeField]
	private bool masterVolume;

	// Token: 0x04000CB0 RID: 3248
	[SerializeField]
	private bool menuMusicVolume;

	// Token: 0x04000CB1 RID: 3249
	[SerializeField]
	private bool targetFps;

	// Token: 0x04000CB2 RID: 3250
	[SerializeField]
	private bool targetFpsToggle;

	// Token: 0x04000CB3 RID: 3251
	[SerializeField]
	private bool useVsync;

	// Token: 0x04000CB4 RID: 3252
	[SerializeField]
	private bool graphics;

	// Token: 0x04000CB5 RID: 3253
	[SerializeField]
	private bool isFullscreen;

	// Token: 0x04000CB6 RID: 3254
	[SerializeField]
	private bool minimalistUi;

	// Token: 0x04000CB7 RID: 3255
	[SerializeField]
	private bool motionBlur;

	// Token: 0x04000CB8 RID: 3256
	[SerializeField]
	private bool pushToTalk;

	// Token: 0x04000CB9 RID: 3257
	[SerializeField]
	private bool exclusiveFullscreen;

	// Token: 0x04000CBA RID: 3258
	[SerializeField]
	private bool enableFixedCrosshair;

	// Token: 0x04000CBB RID: 3259
	[SerializeField]
	private bool showSpeedometer;

	// Token: 0x04000CBC RID: 3260
	[SerializeField]
	private bool disableCrosshair;

	// Token: 0x04000CBD RID: 3261
	[SerializeField]
	private bool inGameMusic;

	// Token: 0x04000CBE RID: 3262
	[SerializeField]
	private bool reduceVFX;
}

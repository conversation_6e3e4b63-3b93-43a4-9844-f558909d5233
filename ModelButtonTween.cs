﻿using System;
using DG.Tweening;
using UnityEngine;

// Token: 0x0200012E RID: 302
public class ModelButtonTween : MonoBehaviour
{
	// Token: 0x06000E60 RID: 3680 RVA: 0x0005F6D9 File Offset: 0x0005D8D9
	private void Start()
	{
		this.initialPosition = base.transform.localPosition;
		if (this.direction == Vector3.zero)
		{
			this.direction = Vector3.forward;
		}
	}

	// Token: 0x06000E61 RID: 3681 RVA: 0x0005F709 File Offset: 0x0005D909
	public void Hover()
	{
		base.transform.DOLocalMove(this.initialPosition - this.direction * this.hoverAmount, this.tweenDuration, false);
	}

	// Token: 0x06000E62 RID: 3682 RVA: 0x0005F73A File Offset: 0x0005D93A
	public void Leave()
	{
		base.transform.DOLocalMove(this.initialPosition, this.tweenDuration, false);
	}

	// Token: 0x06000E63 RID: 3683 RVA: 0x0005F755 File Offset: 0x0005D955
	public void Press()
	{
		base.transform.DOLocalMove(this.initialPosition - this.direction * this.pressAmount, this.tweenDuration, false);
	}

	// Token: 0x06000E64 RID: 3684 RVA: 0x0005F709 File Offset: 0x0005D909
	public void Release()
	{
		base.transform.DOLocalMove(this.initialPosition - this.direction * this.hoverAmount, this.tweenDuration, false);
	}

	// Token: 0x06000E65 RID: 3685 RVA: 0x0005F786 File Offset: 0x0005D986
	private void OnDisable()
	{
		if (this.initialPosition != Vector3.zero)
		{
			base.transform.localPosition = this.initialPosition;
		}
	}

	// Token: 0x04000CF1 RID: 3313
	[SerializeField]
	private float hoverAmount = 0.15f;

	// Token: 0x04000CF2 RID: 3314
	[SerializeField]
	private float pressAmount = 0.3f;

	// Token: 0x04000CF3 RID: 3315
	[SerializeField]
	private float tweenDuration = 0.04f;

	// Token: 0x04000CF4 RID: 3316
	[SerializeField]
	private Ease ease = Ease.Linear;

	// Token: 0x04000CF5 RID: 3317
	[SerializeField]
	private Vector3 direction;

	// Token: 0x04000CF6 RID: 3318
	private Vector3 initialPosition;
}

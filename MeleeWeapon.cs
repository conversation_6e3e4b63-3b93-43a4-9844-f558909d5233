﻿using System;
using System.Collections;
using DG.Tweening;
using FishNet;
using FishNet.Component.Animating;
using FishNet.Connection;
using FishNet.Managing;
using FishNet.Object;
using FishNet.Object.Delegating;
using FishNet.Serializing;
using FishNet.Serializing.Generated;
using FishNet.Transporting;
using UnityEngine;
using UnityEngine.UI;

// Token: 0x0200009A RID: 154
public class MeleeWeapon : Weapon
{
	// Token: 0x0600080F RID: 2063 RVA: 0x00038C10 File Offset: 0x00036E10
	private void Update()
	{
		base.WeaponUpdate();
		if (this.fireTimer > 0f)
		{
			this.fireTimer -= Time.deltaTime;
		}
		if (this.behaviour.outofhandsAnim != "")
		{
			this.attackAnimator.Animator.SetBool(this.behaviour.outofhandsAnim, base.gameObject.layer == 7);
		}
		if (base.gameObject.layer == 7)
		{
			return;
		}
		if (this.minusScale && this.inLeftHand)
		{
			base.transform.localScale = new Vector3(-1f, 1f, 1f);
		}
		if (this.fireTimer < this.timeBetweenBaseAttack - this.baseAttackDuration && !this.playerController.isAiming)
		{
			this.collisionScript.canHit = false;
		}
		else if (this.fireTimer < this.timeBetweenSecondAttack - this.secondAttackDuration && this.playerController.isAiming)
		{
			this.collisionScript.canHit = false;
			this.secondAttackPlaying = false;
		}
		if (this.fireTimer < this.timeBetweenBaseAttack - this.baseAttackDuration && !this.playerController.isAiming)
		{
			this.collisionScript.canHitEnvi = false;
		}
		else if (this.fireTimer < this.timeBetweenSecondAttack - this.secondAttackDuration && this.playerController.isAiming)
		{
			this.collisionScript.canHitEnvi = false;
		}
		if (!this.onePressShoot)
		{
			if ((this.invertFire ? this.fire2 : this.fire1).ReadValue<float>() > 0.1f && this.inRightHand && (this.reloadWeapon || base.SyncAccessor_currentAmmo > 0))
			{
				this.Fire();
			}
			if ((this.invertFire ? this.fire1 : this.fire2).ReadValue<float>() > 0.1f && this.inLeftHand && (this.reloadWeapon || base.SyncAccessor_currentAmmo > 0))
			{
				this.Fire();
			}
			if ((this.invertFire ? this.fire2 : this.fire1).ReadValue<float>() > 0.1f && this.inRightHand && this.akAnim && base.SyncAccessor_currentAmmo > 0)
			{
				this.camAnimScript.rotateBack = false;
			}
			else if ((this.invertFire ? this.fire1 : this.fire2).ReadValue<float>() > 0.1f && this.inLeftHand && this.akAnim && base.SyncAccessor_currentAmmo > 0)
			{
				this.camAnimScript.rotateBack = false;
			}
			else
			{
				this.camAnimScript.rotateBack = true;
			}
			if (!this.reloadWeapon && base.SyncAccessor_currentAmmo <= 0)
			{
				if ((this.invertFire ? this.fire2 : this.fire1).ReadValue<float>() < 0.1f && this.inRightHand)
				{
					this.isClicked = true;
				}
				if ((this.invertFire ? this.fire1 : this.fire2).ReadValue<float>() < 0.1f && this.inLeftHand)
				{
					this.isClicked = true;
				}
			}
		}
		else
		{
			if ((this.invertFire ? this.fire2 : this.fire1).ReadValue<float>() < 0.1f && this.inRightHand)
			{
				this.isClicked = true;
			}
			if ((this.invertFire ? this.fire1 : this.fire2).ReadValue<float>() < 0.1f && this.inLeftHand)
			{
				this.isClicked = true;
			}
			this.camAnimScript.rotateBack = true;
		}
		if (this.isClicked)
		{
			if ((this.invertFire ? this.fire2 : this.fire1).ReadValue<float>() > 0.1f && this.inRightHand)
			{
				this.Fire();
				this.isClicked = false;
			}
			if ((this.invertFire ? this.fire1 : this.fire2).ReadValue<float>() > 0.1f && this.inLeftHand)
			{
				this.Fire();
				this.isClicked = false;
			}
		}
		if (this.shot && this.timeBetweenFire < 0.2f && this.onePressShoot)
		{
			this.Fire();
		}
		if (this.fireTimer > 0.1f && this.secondAttackDelay > 0f && this.secondAttackPlaying)
		{
			this.playerController.isZooming = true;
			this.zoomTrigger = true;
			return;
		}
		if (this.zoomTrigger && !this.collisionScript.canHit && this.secondAttackDelay > 0f)
		{
			this.playerController.isZooming = false;
			this.zoomTrigger = false;
		}
	}

	// Token: 0x06000810 RID: 2064 RVA: 0x000390A8 File Offset: 0x000372A8
	private void Fire()
	{
		if (PauseManager.Instance.pause || !this.playerController.SyncAccessor_canMove)
		{
			return;
		}
		if (!this.playerController.IsOwner)
		{
			return;
		}
		if (this.fireTimer > 0f)
		{
			return;
		}
		base.StartCoroutine(this.FireCoroutine());
		if (this.firstAttackStartClip != null && (!this.behaviour.aimWeapon || !this.playerController.isAiming))
		{
			this.ShootServerEffect(2);
		}
		else if (this.secondAttackStartClip != null && this.behaviour.aimWeapon && this.playerController.isAiming)
		{
			this.ShootServerEffect(3);
		}
		this.fireTimer = ((this.behaviour.aimWeapon && this.playerController.isAiming) ? this.timeBetweenSecondAttack : this.timeBetweenBaseAttack);
		this.secondAttackPlaying = this.behaviour.aimWeapon && this.playerController.isAiming;
		this.attackAnimator.SetTrigger((this.behaviour.aimWeapon && this.playerController.isAiming) ? this.secondAttackAnim : (this.inLeftHand ? this.baseAttackLeftAnim : this.baseAttackAnim));
	}

	// Token: 0x06000811 RID: 2065 RVA: 0x000391F0 File Offset: 0x000373F0
	private IEnumerator FireCoroutine()
	{
		yield return new WaitForSeconds((this.behaviour.aimWeapon && this.playerController.isAiming) ? this.secondAttackDelay : this.firstAttackDelay);
		this.TriggerAttack();
		if (this.revolverShake)
		{
			base.CameraRevolverAnimation();
		}
		else
		{
			base.CameraAnimation();
		}
		yield break;
	}

	// Token: 0x06000812 RID: 2066 RVA: 0x00039200 File Offset: 0x00037400
	private void TriggerAttack()
	{
		this.ShootServerEffect((this.behaviour.aimWeapon && this.playerController.isAiming) ? 1 : 0);
		this.collisionScript.canHit = true;
		this.collisionScript.canHitEnvi = true;
		if (this.bforce && this.propulsion)
		{
			this.playerController.BForce(this.cam.transform.forward, (this.behaviour.aimWeapon && this.playerController.isAiming) ? this.secondPropulsionAmount : this.basePropulsionAmount, true, false, this.bforceDecel, true);
			return;
		}
		if (this.propulsion)
		{
			this.playerController.CustomAddForce(this.cam.transform.forward, (this.behaviour.aimWeapon && this.playerController.isAiming) ? this.secondPropulsionAmount : this.basePropulsionAmount);
		}
	}

	// Token: 0x06000813 RID: 2067 RVA: 0x000392F1 File Offset: 0x000374F1
	public void BounceHolder()
	{
		if (!this.bounceHolder)
		{
			return;
		}
		if (this.playerKnockback != 0f)
		{
			this.playerController.AddForce(-this.cam.transform.forward, this.playerKnockback);
		}
	}

	// Token: 0x06000814 RID: 2068 RVA: 0x00039330 File Offset: 0x00037530
	public void HitServer(PlayerHealth enemyHealth, Vector3 hitPosition, Vector3 hitNormal, string hitName)
	{
		this.BumpPlayerServer(this.cam.transform.forward + Vector3.up * 2f, this.repulseForce, enemyHealth);
		float num = (this.secondAttackPlaying ? this.secondAttackDamage : this.baseAttackDamage);
		if (enemyHealth.gameObject == base.transform.root.gameObject)
		{
			return;
		}
		if (base.FriendlyFireCheck(enemyHealth))
		{
			return;
		}
		if (hitName == "Head_Col")
		{
			num *= this.headMultiplier;
			Settings.Instance.IncreaseHeadshotsAmount();
		}
		else
		{
			Settings.Instance.IncreaseBodyshotsAmount();
		}
		global::UnityEngine.Object.Instantiate<GameObject>(this.bodyImpact, hitPosition, Quaternion.LookRotation(hitNormal));
		global::UnityEngine.Object.Instantiate<GameObject>(this.bloodSplatter, hitPosition, Quaternion.identity);
		if (this.behaviour.aimWeapon && this.playerController.isAiming)
		{
			global::UnityEngine.Object.Instantiate<GameObject>(this.headImpact, hitPosition, Quaternion.LookRotation(hitNormal));
			global::UnityEngine.Object.Instantiate<GameObject>(this.genericBodyImpact, hitPosition, Quaternion.LookRotation(hitNormal));
		}
		if (this.marker == null)
		{
			this.marker = global::UnityEngine.Object.Instantiate<GameObject>(this.hitMarker, Crosshair.Instance.transform.position, Quaternion.identity, PauseManager.Instance.transform);
			this.marker.transform.DOPunchScale((hitName == "Head_Col") ? new Vector3(2.5f, 2.5f, 2.5f) : Vector3.one, 0.3f, 8, 2f);
			if (hitName == "Head_Col")
			{
				this.marker.GetComponent<Image>().color = Color.red;
			}
			global::UnityEngine.Object.Destroy(this.marker, 0.3f);
		}
		else
		{
			global::UnityEngine.Object.Destroy(this.marker);
			this.marker = global::UnityEngine.Object.Instantiate<GameObject>(this.hitMarker, Crosshair.Instance.transform.position, Quaternion.identity, PauseManager.Instance.transform);
			this.marker.transform.DOPunchScale((hitName == "Head_Col") ? new Vector3(2.5f, 2.5f, 2.5f) : Vector3.one, 0.3f, 8, 2f);
			if (hitName == "Head_Col")
			{
				this.marker.GetComponent<Image>().color = Color.red;
			}
			global::UnityEngine.Object.Destroy(this.marker, 0.3f);
		}
		if (hitName == "Head_Col")
		{
			this.LocalSound(0);
			this.LocalSound(1);
		}
		else
		{
			this.LocalSound(1);
		}
		if (enemyHealth.SyncAccessor_health - num <= 0f)
		{
			this.LocalSound(2);
			enemyHealth.Explode(false, false, hitName, this.cam.transform.forward, this.ragdollEjectForce, hitPosition);
			enemyHealth.graphics.SetActive(false);
			enemyHealth.GetComponent<CharacterController>().enabled = false;
			PauseManager.Instance.WriteLog(string.Concat(new string[]
			{
				"<b><color=#",
				PauseManager.Instance.selfNameLogColor,
				">",
				enemyHealth.SyncAccessor_playerValues.SyncAccessor_playerClient.PlayerName,
				"</color></b> was ",
				(hitName == "Head_Col") ? "beheaded" : "slain",
				" with ",
				base.StartsWithVowel(this.behaviour.weaponName) ? "an" : "a",
				" <b><color=white>",
				this.behaviour.weaponName,
				"</color></b> by <b><color=#",
				PauseManager.Instance.enemyNameLogColor,
				">",
				ClientInstance.Instance.PlayerName,
				"</color></b>"
			}));
			base.KillShockWave();
			this.KillServer(enemyHealth);
			enemyHealth.DisablePlayerObjectWhenKilled();
			return;
		}
		this.GiveDamage(num, enemyHealth, hitName);
	}

	// Token: 0x06000815 RID: 2069 RVA: 0x00039726 File Offset: 0x00037926
	[ServerRpc(RunLocally = true)]
	private void RemoveAmmo()
	{
		this.RpcWriter___Server_RemoveAmmo_2166136261();
		this.RpcLogic___RemoveAmmo_2166136261();
	}

	// Token: 0x06000816 RID: 2070 RVA: 0x00039734 File Offset: 0x00037934
	[ServerRpc(RunLocally = true)]
	private void GiveDamage(float damageToGive, PlayerHealth enemyHealth, string name)
	{
		this.RpcWriter___Server_GiveDamage_324487999(damageToGive, enemyHealth, name);
		this.RpcLogic___GiveDamage_324487999(damageToGive, enemyHealth, name);
	}

	// Token: 0x06000817 RID: 2071 RVA: 0x0003975C File Offset: 0x0003795C
	[ServerRpc]
	private void KillServer(PlayerHealth enemyHealth)
	{
		this.RpcWriter___Server_KillServer_1722911636(enemyHealth);
	}

	// Token: 0x06000818 RID: 2072 RVA: 0x00039773 File Offset: 0x00037973
	[TargetRpc]
	private void KillObserver(NetworkConnection conn, ClientInstance client, PlayerHealth enemyHealth)
	{
		this.RpcWriter___Target_KillObserver_123853379(conn, client, enemyHealth);
	}

	// Token: 0x06000819 RID: 2073 RVA: 0x00039787 File Offset: 0x00037987
	[ObserversRpc]
	private void HitFeeback(PlayerHealth enemyHealth)
	{
		this.RpcWriter___Observers_HitFeeback_1722911636(enemyHealth);
	}

	// Token: 0x0600081A RID: 2074 RVA: 0x00039793 File Offset: 0x00037993
	[ObserversRpc]
	private void NextRound()
	{
		this.RpcWriter___Observers_NextRound_2166136261();
	}

	// Token: 0x0600081B RID: 2075 RVA: 0x0003979B File Offset: 0x0003799B
	[ServerRpc(RunLocally = true)]
	private void ShootServerEffect(int x)
	{
		this.RpcWriter___Server_ShootServerEffect_3316948804(x);
		this.RpcLogic___ShootServerEffect_3316948804(x);
	}

	// Token: 0x0600081C RID: 2076 RVA: 0x000397B1 File Offset: 0x000379B1
	[ServerRpc]
	private void BumpPlayerServer(Vector3 direction, float force, PlayerHealth ph)
	{
		this.RpcWriter___Server_BumpPlayerServer_1076951378(direction, force, ph);
	}

	// Token: 0x0600081D RID: 2077 RVA: 0x000397C5 File Offset: 0x000379C5
	[TargetRpc]
	private void BumpPlayer(NetworkConnection conn, PlayerHealth enemyHealth, float force, Vector3 direction)
	{
		this.RpcWriter___Target_BumpPlayer_2429708885(conn, enemyHealth, force, direction);
	}

	// Token: 0x0600081E RID: 2078 RVA: 0x000397E0 File Offset: 0x000379E0
	[ObserversRpc(RunLocally = true, ExcludeOwner = true)]
	private void ShootObserversEffect(int x)
	{
		this.RpcWriter___Observers_ShootObserversEffect_3316948804(x);
		this.RpcLogic___ShootObserversEffect_3316948804(x);
	}

	// Token: 0x0600081F RID: 2079 RVA: 0x00039801 File Offset: 0x00037A01
	[ServerRpc(RunLocally = true)]
	private void SpawnVFXServer(int index, Vector3 hitPoint, Vector3 hitNormal, string surface, Transform parent)
	{
		this.RpcWriter___Server_SpawnVFXServer_606331033(index, hitPoint, hitNormal, surface, parent);
		this.RpcLogic___SpawnVFXServer_606331033(index, hitPoint, hitNormal, surface, parent);
	}

	// Token: 0x06000820 RID: 2080 RVA: 0x00039838 File Offset: 0x00037A38
	[ObserversRpc(RunLocally = true, ExcludeOwner = true)]
	private void SpawnVFX(int index, Vector3 hitPoint, Vector3 hitNormal, string surface, Transform parent)
	{
		this.RpcWriter___Observers_SpawnVFX_606331033(index, hitPoint, hitNormal, surface, parent);
		this.RpcLogic___SpawnVFX_606331033(index, hitPoint, hitNormal, surface, parent);
	}

	// Token: 0x06000821 RID: 2081 RVA: 0x0003987C File Offset: 0x00037A7C
	private void LocalSound(int index)
	{
		if (index == 0)
		{
			this.audio.PlayOneShot(this.headHitClip);
		}
		if (index == 1)
		{
			this.audio.PlayOneShot(this.bodyHitClip);
		}
		if (index == 2)
		{
			this.audio.PlayOneShot(this.deathClip);
		}
		if (index == 3)
		{
			this.audio.PlayOneShot(this.secondAttackClip);
		}
	}

	// Token: 0x06000823 RID: 2083 RVA: 0x00039930 File Offset: 0x00037B30
	public override void NetworkInitialize___Early()
	{
		if (this.NetworkInitializeEarly_MeleeWeapon_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeEarly_MeleeWeapon_Assembly-CSharp.dll = true;
		base.NetworkInitialize___Early();
		base.RegisterServerRpc(15U, new ServerRpcDelegate(this.RpcReader___Server_RemoveAmmo_2166136261));
		base.RegisterServerRpc(16U, new ServerRpcDelegate(this.RpcReader___Server_GiveDamage_324487999));
		base.RegisterServerRpc(17U, new ServerRpcDelegate(this.RpcReader___Server_KillServer_1722911636));
		base.RegisterTargetRpc(18U, new ClientRpcDelegate(this.RpcReader___Target_KillObserver_123853379));
		base.RegisterObserversRpc(19U, new ClientRpcDelegate(this.RpcReader___Observers_HitFeeback_1722911636));
		base.RegisterObserversRpc(20U, new ClientRpcDelegate(this.RpcReader___Observers_NextRound_2166136261));
		base.RegisterServerRpc(21U, new ServerRpcDelegate(this.RpcReader___Server_ShootServerEffect_3316948804));
		base.RegisterServerRpc(22U, new ServerRpcDelegate(this.RpcReader___Server_BumpPlayerServer_1076951378));
		base.RegisterTargetRpc(23U, new ClientRpcDelegate(this.RpcReader___Target_BumpPlayer_2429708885));
		base.RegisterObserversRpc(24U, new ClientRpcDelegate(this.RpcReader___Observers_ShootObserversEffect_3316948804));
		base.RegisterServerRpc(25U, new ServerRpcDelegate(this.RpcReader___Server_SpawnVFXServer_606331033));
		base.RegisterObserversRpc(26U, new ClientRpcDelegate(this.RpcReader___Observers_SpawnVFX_606331033));
	}

	// Token: 0x06000824 RID: 2084 RVA: 0x00039A68 File Offset: 0x00037C68
	public override void NetworkInitialize__Late()
	{
		if (this.NetworkInitializeLate_MeleeWeapon_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeLate_MeleeWeapon_Assembly-CSharp.dll = true;
		base.NetworkInitialize__Late();
	}

	// Token: 0x06000825 RID: 2085 RVA: 0x00039A81 File Offset: 0x00037C81
	public override void NetworkInitializeIfDisabled()
	{
		this.NetworkInitialize___Early();
		this.NetworkInitialize__Late();
	}

	// Token: 0x06000826 RID: 2086 RVA: 0x00039A90 File Offset: 0x00037C90
	private void RpcWriter___Server_RemoveAmmo_2166136261()
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendServerRpc(15U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000827 RID: 2087 RVA: 0x0002BFDC File Offset: 0x0002A1DC
	private void RpcLogic___RemoveAmmo_2166136261()
	{
		if (this.reloadWeapon)
		{
			this.chargedBullets -= 1f;
			return;
		}
		base.sync___set_value_currentAmmo(base.SyncAccessor_currentAmmo - 1, true);
	}

	// Token: 0x06000828 RID: 2088 RVA: 0x00039B84 File Offset: 0x00037D84
	private void RpcReader___Server_RemoveAmmo_2166136261(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___RemoveAmmo_2166136261();
	}

	// Token: 0x06000829 RID: 2089 RVA: 0x00039BC4 File Offset: 0x00037DC4
	private void RpcWriter___Server_GiveDamage_324487999(float damageToGive, PlayerHealth enemyHealth, string name)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteSingle(damageToGive, AutoPackType.Unpacked);
		writer.Write___PlayerHealthFishNet.Serializing.Generated(enemyHealth);
		writer.WriteString(name);
		base.SendServerRpc(16U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x0600082A RID: 2090 RVA: 0x00039CE4 File Offset: 0x00037EE4
	private void RpcLogic___GiveDamage_324487999(float damageToGive, PlayerHealth enemyHealth, string name)
	{
		enemyHealth.sync___set_value_killer(this.rootObject.transform, true);
		enemyHealth.sync___set_value_health(enemyHealth.SyncAccessor_health - damageToGive, true);
		enemyHealth.KillCam();
		this.HitFeeback(enemyHealth);
		enemyHealth.Dismemberment(name);
	}

	// Token: 0x0600082B RID: 2091 RVA: 0x00039D1C File Offset: 0x00037F1C
	private void RpcReader___Server_GiveDamage_324487999(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		float num = PooledReader0.ReadSingle(AutoPackType.Unpacked);
		PlayerHealth playerHealth = global::FishNet.Serializing.Generated.GeneratedReaders___Internal.Read___PlayerHealthFishNet.Serializing.Generateds(PooledReader0);
		string text = PooledReader0.ReadString();
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___GiveDamage_324487999(num, playerHealth, text);
	}

	// Token: 0x0600082C RID: 2092 RVA: 0x00039D94 File Offset: 0x00037F94
	private void RpcWriter___Server_KillServer_1722911636(PlayerHealth enemyHealth)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.Write___PlayerHealthFishNet.Serializing.Generated(enemyHealth);
		base.SendServerRpc(17U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x0600082D RID: 2093 RVA: 0x00039E98 File Offset: 0x00038098
	private void RpcLogic___KillServer_1722911636(PlayerHealth enemyHealth)
	{
		enemyHealth.sync___set_value_isShot(true, true);
		enemyHealth.sync___set_value_health(-8f, true);
		GameManager.Instance.PlayerDied(enemyHealth.SyncAccessor_playerValues.SyncAccessor_playerClient.PlayerId);
		if (this.rootObject != null)
		{
			enemyHealth.sync___set_value_killer(this.rootObject.transform, true);
		}
		this.KillObserver(enemyHealth.SyncAccessor_playerValues.SyncAccessor_playerClient.transform.GetComponent<NetworkObject>().Owner, enemyHealth.SyncAccessor_playerValues.SyncAccessor_playerClient, enemyHealth);
		this.NextRound();
	}

	// Token: 0x0600082E RID: 2094 RVA: 0x00039F28 File Offset: 0x00038128
	private void RpcReader___Server_KillServer_1722911636(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		PlayerHealth playerHealth = global::FishNet.Serializing.Generated.GeneratedReaders___Internal.Read___PlayerHealthFishNet.Serializing.Generateds(PooledReader0);
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		this.RpcLogic___KillServer_1722911636(playerHealth);
	}

	// Token: 0x0600082F RID: 2095 RVA: 0x00039F6C File Offset: 0x0003816C
	private void RpcWriter___Target_KillObserver_123853379(NetworkConnection conn, ClientInstance client, PlayerHealth enemyHealth)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.Write___ClientInstanceFishNet.Serializing.Generated(client);
		writer.Write___PlayerHealthFishNet.Serializing.Generated(enemyHealth);
		base.SendTargetRpc(18U, writer, channel, DataOrderType.Default, conn, false, true);
		writer.Store();
	}

	// Token: 0x06000830 RID: 2096 RVA: 0x000275DA File Offset: 0x000257DA
	private void RpcLogic___KillObserver_123853379(NetworkConnection conn, ClientInstance client, PlayerHealth enemyHealth)
	{
		enemyHealth.shouldDropWeapon = true;
		enemyHealth.isDeadFromTargetRpc = true;
		if (this.rootObject != null)
		{
			GameObject.Find("Main Camera").GetComponent<KillCam>().enemy = this.rootObject.transform;
		}
	}

	// Token: 0x06000831 RID: 2097 RVA: 0x0003A030 File Offset: 0x00038230
	private void RpcReader___Target_KillObserver_123853379(PooledReader PooledReader0, Channel channel)
	{
		ClientInstance clientInstance = global::FishNet.Serializing.Generated.GeneratedReaders___Internal.Read___ClientInstanceFishNet.Serializing.Generateds(PooledReader0);
		PlayerHealth playerHealth = global::FishNet.Serializing.Generated.GeneratedReaders___Internal.Read___PlayerHealthFishNet.Serializing.Generateds(PooledReader0);
		if (!base.IsClient)
		{
			return;
		}
		this.RpcLogic___KillObserver_123853379(base.LocalConnection, clientInstance, playerHealth);
	}

	// Token: 0x06000832 RID: 2098 RVA: 0x0003A078 File Offset: 0x00038278
	private void RpcWriter___Observers_HitFeeback_1722911636(PlayerHealth enemyHealth)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.Write___PlayerHealthFishNet.Serializing.Generated(enemyHealth);
		base.SendObserversRpc(19U, writer, channel, DataOrderType.Default, false, false, false);
		writer.Store();
	}

	// Token: 0x06000833 RID: 2099 RVA: 0x00027716 File Offset: 0x00025916
	private void RpcLogic___HitFeeback_1722911636(PlayerHealth enemyHealth)
	{
		enemyHealth.HitFeeback();
	}

	// Token: 0x06000834 RID: 2100 RVA: 0x0003A130 File Offset: 0x00038330
	private void RpcReader___Observers_HitFeeback_1722911636(PooledReader PooledReader0, Channel channel)
	{
		PlayerHealth playerHealth = global::FishNet.Serializing.Generated.GeneratedReaders___Internal.Read___PlayerHealthFishNet.Serializing.Generateds(PooledReader0);
		if (!base.IsClient)
		{
			return;
		}
		this.RpcLogic___HitFeeback_1722911636(playerHealth);
	}

	// Token: 0x06000835 RID: 2101 RVA: 0x0003A164 File Offset: 0x00038364
	private void RpcWriter___Observers_NextRound_2166136261()
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendObserversRpc(20U, writer, channel, DataOrderType.Default, false, false, false);
		writer.Store();
	}

	// Token: 0x06000836 RID: 2102 RVA: 0x000023D6 File Offset: 0x000005D6
	private void RpcLogic___NextRound_2166136261()
	{
	}

	// Token: 0x06000837 RID: 2103 RVA: 0x0003A210 File Offset: 0x00038410
	private void RpcReader___Observers_NextRound_2166136261(PooledReader PooledReader0, Channel channel)
	{
		if (!base.IsClient)
		{
			return;
		}
		this.RpcLogic___NextRound_2166136261();
	}

	// Token: 0x06000838 RID: 2104 RVA: 0x0003A230 File Offset: 0x00038430
	private void RpcWriter___Server_ShootServerEffect_3316948804(int x)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteInt32(x, AutoPackType.Packed);
		base.SendServerRpc(21U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000839 RID: 2105 RVA: 0x0003A336 File Offset: 0x00038536
	private void RpcLogic___ShootServerEffect_3316948804(int x)
	{
		this.ShootObserversEffect(x);
	}

	// Token: 0x0600083A RID: 2106 RVA: 0x0003A340 File Offset: 0x00038540
	private void RpcReader___Server_ShootServerEffect_3316948804(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		int num = PooledReader0.ReadInt32(AutoPackType.Packed);
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___ShootServerEffect_3316948804(num);
	}

	// Token: 0x0600083B RID: 2107 RVA: 0x0003A394 File Offset: 0x00038594
	private void RpcWriter___Server_BumpPlayerServer_1076951378(Vector3 direction, float force, PlayerHealth ph)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteVector3(direction);
		writer.WriteSingle(force, AutoPackType.Unpacked);
		writer.Write___PlayerHealthFishNet.Serializing.Generated(ph);
		base.SendServerRpc(22U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x0600083C RID: 2108 RVA: 0x0003A4B4 File Offset: 0x000386B4
	private void RpcLogic___BumpPlayerServer_1076951378(Vector3 direction, float force, PlayerHealth ph)
	{
		this.BumpPlayer(ph.SyncAccessor_playerValues.SyncAccessor_playerClient.transform.GetComponent<NetworkObject>().Owner, ph, force, direction);
	}

	// Token: 0x0600083D RID: 2109 RVA: 0x0003A4DC File Offset: 0x000386DC
	private void RpcReader___Server_BumpPlayerServer_1076951378(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		Vector3 vector = PooledReader0.ReadVector3();
		float num = PooledReader0.ReadSingle(AutoPackType.Unpacked);
		PlayerHealth playerHealth = global::FishNet.Serializing.Generated.GeneratedReaders___Internal.Read___PlayerHealthFishNet.Serializing.Generateds(PooledReader0);
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		this.RpcLogic___BumpPlayerServer_1076951378(vector, num, playerHealth);
	}

	// Token: 0x0600083E RID: 2110 RVA: 0x0003A548 File Offset: 0x00038748
	private void RpcWriter___Target_BumpPlayer_2429708885(NetworkConnection conn, PlayerHealth enemyHealth, float force, Vector3 direction)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.Write___PlayerHealthFishNet.Serializing.Generated(enemyHealth);
		writer.WriteSingle(force, AutoPackType.Unpacked);
		writer.WriteVector3(direction);
		base.SendTargetRpc(23U, writer, channel, DataOrderType.Default, conn, false, true);
		writer.Store();
	}

	// Token: 0x0600083F RID: 2111 RVA: 0x0003A61C File Offset: 0x0003881C
	private void RpcLogic___BumpPlayer_2429708885(NetworkConnection conn, PlayerHealth enemyHealth, float force, Vector3 direction)
	{
		enemyHealth.bounceDirection = direction;
		enemyHealth.bounceForce = force;
		enemyHealth.shouldBounce = true;
	}

	// Token: 0x06000840 RID: 2112 RVA: 0x0003A634 File Offset: 0x00038834
	private void RpcReader___Target_BumpPlayer_2429708885(PooledReader PooledReader0, Channel channel)
	{
		PlayerHealth playerHealth = global::FishNet.Serializing.Generated.GeneratedReaders___Internal.Read___PlayerHealthFishNet.Serializing.Generateds(PooledReader0);
		float num = PooledReader0.ReadSingle(AutoPackType.Unpacked);
		Vector3 vector = PooledReader0.ReadVector3();
		if (!base.IsClient)
		{
			return;
		}
		this.RpcLogic___BumpPlayer_2429708885(base.LocalConnection, playerHealth, num, vector);
	}

	// Token: 0x06000841 RID: 2113 RVA: 0x0003A694 File Offset: 0x00038894
	private void RpcWriter___Observers_ShootObserversEffect_3316948804(int x)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteInt32(x, AutoPackType.Packed);
		base.SendObserversRpc(24U, writer, channel, DataOrderType.Default, false, false, true);
		writer.Store();
	}

	// Token: 0x06000842 RID: 2114 RVA: 0x0003A750 File Offset: 0x00038950
	private void RpcLogic___ShootObserversEffect_3316948804(int x)
	{
		if (x == 0)
		{
			this.audio.PlayOneShot(this.fireClip);
			return;
		}
		if (x == 1)
		{
			this.audio.PlayOneShot(this.secondAttackClip);
			return;
		}
		if (x == 2)
		{
			this.audio.PlayOneShot(this.firstAttackStartClip);
			return;
		}
		if (x == 3)
		{
			this.audio.PlayOneShot(this.secondAttackStartClip);
		}
	}

	// Token: 0x06000843 RID: 2115 RVA: 0x0003A7B4 File Offset: 0x000389B4
	private void RpcReader___Observers_ShootObserversEffect_3316948804(PooledReader PooledReader0, Channel channel)
	{
		int num = PooledReader0.ReadInt32(AutoPackType.Packed);
		if (!base.IsClient)
		{
			return;
		}
		if (base.IsHost)
		{
			return;
		}
		this.RpcLogic___ShootObserversEffect_3316948804(num);
	}

	// Token: 0x06000844 RID: 2116 RVA: 0x0003A7F4 File Offset: 0x000389F4
	private void RpcWriter___Server_SpawnVFXServer_606331033(int index, Vector3 hitPoint, Vector3 hitNormal, string surface, Transform parent)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteInt32(index, AutoPackType.Packed);
		writer.WriteVector3(hitPoint);
		writer.WriteVector3(hitNormal);
		writer.WriteString(surface);
		writer.WriteTransform(parent);
		base.SendServerRpc(25U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000845 RID: 2117 RVA: 0x0003A92E File Offset: 0x00038B2E
	private void RpcLogic___SpawnVFXServer_606331033(int index, Vector3 hitPoint, Vector3 hitNormal, string surface, Transform parent)
	{
		this.SpawnVFX(index, hitPoint, hitNormal, surface, parent);
	}

	// Token: 0x06000846 RID: 2118 RVA: 0x0003A940 File Offset: 0x00038B40
	private void RpcReader___Server_SpawnVFXServer_606331033(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		int num = PooledReader0.ReadInt32(AutoPackType.Packed);
		Vector3 vector = PooledReader0.ReadVector3();
		Vector3 vector2 = PooledReader0.ReadVector3();
		string text = PooledReader0.ReadString();
		Transform transform = PooledReader0.ReadTransform();
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___SpawnVFXServer_606331033(num, vector, vector2, text, transform);
	}

	// Token: 0x06000847 RID: 2119 RVA: 0x0003A9D8 File Offset: 0x00038BD8
	private void RpcWriter___Observers_SpawnVFX_606331033(int index, Vector3 hitPoint, Vector3 hitNormal, string surface, Transform parent)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteInt32(index, AutoPackType.Packed);
		writer.WriteVector3(hitPoint);
		writer.WriteVector3(hitNormal);
		writer.WriteString(surface);
		writer.WriteTransform(parent);
		base.SendObserversRpc(26U, writer, channel, DataOrderType.Default, false, false, true);
		writer.Store();
	}

	// Token: 0x06000848 RID: 2120 RVA: 0x0003AAC8 File Offset: 0x00038CC8
	private void RpcLogic___SpawnVFX_606331033(int index, Vector3 hitPoint, Vector3 hitNormal, string surface, Transform parent)
	{
		if (Settings.Instance.reduceVFX)
		{
			return;
		}
		uint num = <PrivateImplementationDetails>.ComputeStringHash(surface);
		if (num <= 1825421690U)
		{
			if (num <= 977466297U)
			{
				if (num <= 464173256U)
				{
					if (num != 456440475U)
					{
						if (num == 464173256U)
						{
							if (surface == "Footsteps/Concrete/Dalles")
							{
								if (index == 0)
								{
									global::UnityEngine.Object.Instantiate<GameObject>(this.genericImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
								}
								if (index == 1)
								{
									global::UnityEngine.Object.Instantiate<GameObject>(this.bulletHole, hitPoint, Quaternion.LookRotation(hitNormal), parent);
									goto IL_06A0;
								}
								goto IL_06A0;
							}
						}
					}
					else if (surface == "Footsteps/Sand")
					{
						if (index == 0)
						{
							global::UnityEngine.Object.Instantiate<GameObject>(this.genericImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
						}
						if (index == 1)
						{
							global::UnityEngine.Object.Instantiate<GameObject>(this.sandHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
							goto IL_06A0;
						}
						goto IL_06A0;
					}
				}
				else if (num != 913360285U)
				{
					if (num == 977466297U)
					{
						if (surface == "Footsteps/Concrete/Solide")
						{
							if (index == 0)
							{
								global::UnityEngine.Object.Instantiate<GameObject>(this.genericImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
							}
							if (index == 1)
							{
								global::UnityEngine.Object.Instantiate<GameObject>(this.bulletHole, hitPoint, Quaternion.LookRotation(hitNormal), parent);
								goto IL_06A0;
							}
							goto IL_06A0;
						}
					}
				}
				else if (surface == "Grenade")
				{
					goto IL_06A0;
				}
			}
			else if (num <= 1429664136U)
			{
				if (num != 1378315797U)
				{
					if (num == 1429664136U)
					{
						if (surface == "Footsteps/Water")
						{
							if (index == 0)
							{
								global::UnityEngine.Object.Instantiate<GameObject>(this.genericImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
							}
							if (index == 1)
							{
								global::UnityEngine.Object.Instantiate<GameObject>(this.waterHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
								goto IL_06A0;
							}
							goto IL_06A0;
						}
					}
				}
				else if (surface == "Footsteps/Moquette")
				{
					if (index == 0)
					{
						global::UnityEngine.Object.Instantiate<GameObject>(this.genericImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
					}
					if (index == 1)
					{
						global::UnityEngine.Object.Instantiate<GameObject>(this.softbodyHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
						goto IL_06A0;
					}
					goto IL_06A0;
				}
			}
			else if (num != 1610969363U)
			{
				if (num != 1624496828U)
				{
					if (num == 1825421690U)
					{
						if (surface == "Footsteps/Wood/Creux")
						{
							if (index == 0)
							{
								global::UnityEngine.Object.Instantiate<GameObject>(this.genericImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
							}
							if (index == 1)
							{
								global::UnityEngine.Object.Instantiate<GameObject>(this.woodHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
								goto IL_06A0;
							}
							goto IL_06A0;
						}
					}
				}
				else if (surface == "Footsteps/Concrete/Default")
				{
					if (index == 0)
					{
						global::UnityEngine.Object.Instantiate<GameObject>(this.genericImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
					}
					if (index == 1)
					{
						global::UnityEngine.Object.Instantiate<GameObject>(this.bulletHole, hitPoint, Quaternion.LookRotation(hitNormal), parent);
						goto IL_06A0;
					}
					goto IL_06A0;
				}
			}
			else if (surface == "Footsteps/Concrete/PleinAir")
			{
				if (index == 0)
				{
					global::UnityEngine.Object.Instantiate<GameObject>(this.genericImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
				}
				if (index == 1)
				{
					global::UnityEngine.Object.Instantiate<GameObject>(this.bulletHole, hitPoint, Quaternion.LookRotation(hitNormal), parent);
					goto IL_06A0;
				}
				goto IL_06A0;
			}
		}
		else if (num <= 2833365437U)
		{
			if (num <= 2180110808U)
			{
				if (num != 1954265137U)
				{
					if (num == 2180110808U)
					{
						if (surface == "Footsteps/Dirt")
						{
							if (index == 0)
							{
								global::UnityEngine.Object.Instantiate<GameObject>(this.genericImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
							}
							if (index == 1)
							{
								global::UnityEngine.Object.Instantiate<GameObject>(this.dirtHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
								goto IL_06A0;
							}
							goto IL_06A0;
						}
					}
				}
				else if (surface == "NoSound")
				{
					goto IL_06A0;
				}
			}
			else if (num != 2251139421U)
			{
				if (num != 2400559108U)
				{
					if (num == 2833365437U)
					{
						if (surface == "Footsteps/Metal/Pipe2")
						{
							if (index == 0)
							{
								global::UnityEngine.Object.Instantiate<GameObject>(this.genericImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
							}
							if (index == 1)
							{
								global::UnityEngine.Object.Instantiate<GameObject>(this.metalHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
								goto IL_06A0;
							}
							goto IL_06A0;
						}
					}
				}
				else if (surface == "Footsteps/Matelas")
				{
					if (index == 0)
					{
						global::UnityEngine.Object.Instantiate<GameObject>(this.genericImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
					}
					if (index == 1)
					{
						global::UnityEngine.Object.Instantiate<GameObject>(this.softbodyHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
						goto IL_06A0;
					}
					goto IL_06A0;
				}
			}
			else if (surface == "Footsteps/Metal/Pipe")
			{
				if (index == 0)
				{
					global::UnityEngine.Object.Instantiate<GameObject>(this.genericImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
				}
				if (index == 1)
				{
					global::UnityEngine.Object.Instantiate<GameObject>(this.tauleHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
					goto IL_06A0;
				}
				goto IL_06A0;
			}
		}
		else if (num <= 3455850406U)
		{
			if (num != 3075599786U)
			{
				if (num == 3455850406U)
				{
					if (surface == "Footsteps/Graviers")
					{
						if (index == 0)
						{
							global::UnityEngine.Object.Instantiate<GameObject>(this.genericImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
						}
						if (index == 1)
						{
							global::UnityEngine.Object.Instantiate<GameObject>(this.bulletHole, hitPoint, Quaternion.LookRotation(hitNormal), parent);
							goto IL_06A0;
						}
						goto IL_06A0;
					}
				}
			}
			else if (surface == "Footsteps/Wood/Sec")
			{
				if (index == 0)
				{
					global::UnityEngine.Object.Instantiate<GameObject>(this.genericImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
				}
				if (index == 1)
				{
					global::UnityEngine.Object.Instantiate<GameObject>(this.woodHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
					goto IL_06A0;
				}
				goto IL_06A0;
			}
		}
		else if (num != 3492154005U)
		{
			if (num != 3807801418U)
			{
				if (num == 3848897750U)
				{
					if (surface == "Mine")
					{
						goto IL_06A0;
					}
				}
			}
			else if (surface == "Footsteps/Metal/Grille")
			{
				if (index == 0)
				{
					global::UnityEngine.Object.Instantiate<GameObject>(this.genericImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
				}
				if (index == 1)
				{
					global::UnityEngine.Object.Instantiate<GameObject>(this.tauleHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
					goto IL_06A0;
				}
				goto IL_06A0;
			}
		}
		else if (surface == "Footsteps/Grass")
		{
			if (index == 0)
			{
				global::UnityEngine.Object.Instantiate<GameObject>(this.genericImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
			}
			if (index == 1)
			{
				global::UnityEngine.Object.Instantiate<GameObject>(this.dirtHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
				goto IL_06A0;
			}
			goto IL_06A0;
		}
		if (index == 0)
		{
			global::UnityEngine.Object.Instantiate<GameObject>(this.genericImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
		}
		if (index == 1)
		{
			global::UnityEngine.Object.Instantiate<GameObject>(this.bulletHole, hitPoint, Quaternion.LookRotation(hitNormal), parent);
		}
		IL_06A0:
		if (index == 2)
		{
			global::UnityEngine.Object.Instantiate<GameObject>(this.bodyImpact, hitPoint, Quaternion.identity, parent);
		}
	}

	// Token: 0x06000849 RID: 2121 RVA: 0x0003B190 File Offset: 0x00039390
	private void RpcReader___Observers_SpawnVFX_606331033(PooledReader PooledReader0, Channel channel)
	{
		int num = PooledReader0.ReadInt32(AutoPackType.Packed);
		Vector3 vector = PooledReader0.ReadVector3();
		Vector3 vector2 = PooledReader0.ReadVector3();
		string text = PooledReader0.ReadString();
		Transform transform = PooledReader0.ReadTransform();
		if (!base.IsClient)
		{
			return;
		}
		if (base.IsHost)
		{
			return;
		}
		this.RpcLogic___SpawnVFX_606331033(num, vector, vector2, text, transform);
	}

	// Token: 0x0600084A RID: 2122 RVA: 0x0003B214 File Offset: 0x00039414
	public override void Awake()
	{
		this.NetworkInitialize___Early();
		base.Awake();
		this.NetworkInitialize__Late();
	}

	// Token: 0x0600084B RID: 2123 RVA: 0x000023D6 File Offset: 0x000005D6
	public override void Awake___UserLogic()
	{
	}

	// Token: 0x04000736 RID: 1846
	[Header("Weapon Specials")]
	[SerializeField]
	private GameObject collisionObj;

	// Token: 0x04000737 RID: 1847
	[SerializeField]
	private MeleeChildCollision collisionScript;

	// Token: 0x04000738 RID: 1848
	[SerializeField]
	private NetworkAnimator attackAnimator;

	// Token: 0x04000739 RID: 1849
	[SerializeField]
	private string baseAttackAnim;

	// Token: 0x0400073A RID: 1850
	[SerializeField]
	private string baseAttackLeftAnim;

	// Token: 0x0400073B RID: 1851
	[SerializeField]
	private string secondAttackAnim;

	// Token: 0x0400073C RID: 1852
	[SerializeField]
	private float timeBetweenBaseAttack = 0.7f;

	// Token: 0x0400073D RID: 1853
	[SerializeField]
	private float timeBetweenSecondAttack = 1f;

	// Token: 0x0400073E RID: 1854
	[SerializeField]
	private float baseAttackDuration = 0.2f;

	// Token: 0x0400073F RID: 1855
	[SerializeField]
	private float secondAttackDuration = 0.4f;

	// Token: 0x04000740 RID: 1856
	[SerializeField]
	private bool propulsion;

	// Token: 0x04000741 RID: 1857
	[SerializeField]
	private float basePropulsionAmount;

	// Token: 0x04000742 RID: 1858
	[SerializeField]
	private float secondPropulsionAmount;

	// Token: 0x04000743 RID: 1859
	[SerializeField]
	private bool bforce;

	// Token: 0x04000744 RID: 1860
	[SerializeField]
	private float bforceDecel = 9f;

	// Token: 0x04000745 RID: 1861
	[SerializeField]
	private float baseAttackDamage;

	// Token: 0x04000746 RID: 1862
	[SerializeField]
	private float secondAttackDamage;

	// Token: 0x04000747 RID: 1863
	[SerializeField]
	private AudioClip secondAttackClip;

	// Token: 0x04000748 RID: 1864
	[SerializeField]
	private AudioClip firstAttackStartClip;

	// Token: 0x04000749 RID: 1865
	[SerializeField]
	private AudioClip secondAttackStartClip;

	// Token: 0x0400074A RID: 1866
	[Space]
	[SerializeField]
	private bool bounceHolder;

	// Token: 0x0400074B RID: 1867
	[SerializeField]
	private float repulseForce;

	// Token: 0x0400074C RID: 1868
	[SerializeField]
	private float playerKnockback;

	// Token: 0x0400074D RID: 1869
	public int hitsAmount = 1;

	// Token: 0x0400074E RID: 1870
	[Space]
	[SerializeField]
	private float firstAttackDelay;

	// Token: 0x0400074F RID: 1871
	[SerializeField]
	private float secondAttackDelay;

	// Token: 0x04000750 RID: 1872
	[SerializeField]
	private bool minusScale;

	// Token: 0x04000751 RID: 1873
	private float fireTimer;

	// Token: 0x04000752 RID: 1874
	private bool touched;

	// Token: 0x04000753 RID: 1875
	private bool zoomTrigger;

	// Token: 0x04000754 RID: 1876
	private bool secondAttackPlaying;

	// Token: 0x04000755 RID: 1877
	private bool hitOK;

	// Token: 0x04000756 RID: 1878
	private bool NetworkInitializeEarly_MeleeWeapon_Assembly-CSharp.dll;

	// Token: 0x04000757 RID: 1879
	private bool NetworkInitializeLate_MeleeWeapon_Assembly-CSharp.dll;
}

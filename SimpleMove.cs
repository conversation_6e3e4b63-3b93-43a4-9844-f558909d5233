﻿using System;
using UnityEngine;

// Token: 0x02000155 RID: 341
public class SimpleMove : MonoBehaviour
{
	// Token: 0x06000EF4 RID: 3828 RVA: 0x00062B28 File Offset: 0x00060D28
	private void Update()
	{
		base.transform.RotateAround(Vector3.zero, Vector3.up, Time.deltaTime * this.AngularSpeed);
		RaycastHit raycastHit;
		if (Physics.Raycast(new Ray(base.transform.position + Vector3.up, Vector3.down), out raycastHit))
		{
			base.transform.position = raycastHit.point;
		}
	}

	// Token: 0x04000D94 RID: 3476
	public float AngularSpeed = 1f;
}

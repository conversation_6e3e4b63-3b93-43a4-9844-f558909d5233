﻿using System;
using System.Collections.Generic;
using UnityEngine;

namespace ch.sycoforge.Decal.Demo
{
	// Token: 0x02000180 RID: 384
	public static class BezierUtil
	{
		// Token: 0x06000FBD RID: 4029 RVA: 0x00067218 File Offset: 0x00065418
		public static List<Vector3> InterpolatePath(List<Vector3> path, int segments, float radius, float angleThreshold)
		{
			if (path.Count >= 3)
			{
				List<Vector3> list = new List<Vector3>();
				int num = path.Count - 1;
				list.Add(path[0]);
				int num2 = 0;
				for (int i = 2; i < path.Count; i++)
				{
					Vector3 vector = path[i - 2];
					Vector3 vector2 = path[i - 1];
					Vector3 vector3 = path[i];
					Vector3 vector4 = vector2 - vector;
					Vector3 vector5 = vector3 - vector2;
					if (Mathf.Abs(Vector3.Angle(vector4, vector5)) > angleThreshold)
					{
						float num3 = vector4.magnitude;
						float num4 = vector5.magnitude;
						vector4.Normalize();
						vector5.Normalize();
						num3 = Mathf.Min(num3 * 0.5f, radius);
						num4 = Mathf.Min(num4 * 0.5f, radius);
						Vector3 vector6 = vector2 - vector4 * num3;
						Vector3 vector7 = vector2;
						Vector3 vector8 = vector2 + vector5 * num4;
						for (int j = 0; j < segments; j++)
						{
							float num5 = (float)j / ((float)segments - 1f);
							float num6 = 1f - num5;
							Vector3 vector9 = num6 * num6 * vector6 + 2f * num6 * num5 * vector7 + num5 * num5 * vector8;
							list.Add(vector9);
						}
						num2 = i;
					}
				}
				if (num2 <= num)
				{
					list.Add(path[num]);
				}
				return list;
			}
			return path;
		}

		// Token: 0x06000FBE RID: 4030 RVA: 0x00067390 File Offset: 0x00065590
		public static Vector3[] GetBezierApproximation(Vector3[] controlPoints, int outputSegmentCount)
		{
			Vector3[] array = new Vector3[outputSegmentCount + 1];
			for (int i = 0; i < outputSegmentCount; i++)
			{
				float num = (float)i / (float)outputSegmentCount;
				array[i] = BezierUtil.GetBezierPoint(num, controlPoints, 0, controlPoints.Length);
			}
			return array;
		}

		// Token: 0x06000FBF RID: 4031 RVA: 0x000673CC File Offset: 0x000655CC
		public static Vector3 GetBezierPoint(float t, Vector3[] controlPoints, int index, int count)
		{
			if (count == 1)
			{
				return controlPoints[index];
			}
			Vector3 bezierPoint = BezierUtil.GetBezierPoint(t, controlPoints, index - 1, count - 1);
			Vector3 bezierPoint2 = BezierUtil.GetBezierPoint(t, controlPoints, index, count - 1);
			Vector3 bezierPoint3 = BezierUtil.GetBezierPoint(t, controlPoints, index + 1, count - 1);
			return (1f - t) * (1f - t) * bezierPoint + 2f * (1f - t) * t * bezierPoint2 + t * t * bezierPoint3;
		}
	}
}

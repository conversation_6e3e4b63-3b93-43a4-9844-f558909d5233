using System;
using System.Collections;
using FishNet;
using FishNet.Connection;
using FishNet.Managing;
using FishNet.Object;
using FishNet.Object.Delegating;
using FishNet.Object.Synchronizing;
using FishNet.Serializing;
using FishNet.Transporting;
using UnityEngine;
using UnityEngine.Rendering.PostProcessing;

// Token: 0x0200002E RID: 46
public class PlayerSetup : NetworkBehaviour
{
	// Token: 0x06000276 RID: 630 RVA: 0x00016241 File Offset: 0x00014441
	private IEnumerator GetPlayersName()
	{
		yield return new WaitForSeconds(0.5f);
		yield break;
	}

	// Token: 0x06000277 RID: 631 RVA: 0x0001624C File Offset: 0x0001444C
	public override void OnStartClient()
	{
		base.OnStartClient();
		base.StartCoroutine(this.TaserDelay());
		if (!base.IsOwner)
		{
			for (int i = 0; i < this.componentsToDisableForEnemy.Length; i++)
			{
				this.componentsToDisableForEnemy[i].enabled = false;
			}
			for (int j = 0; j < this.gameObjectToDisableForEnemy.Length; j++)
			{
				this.gameObjectToDisableForEnemy[j].SetActive(false);
			}
			base.enabled = false;
		}
		else
		{
			for (int k = 0; k < this.gameObjectsToDisableForMe.Length; k++)
			{
				this.gameObjectsToDisableForMe[k].gameObject.SetActive(false);
			}
			for (int l = 0; l < this.componentsToDisableForMe.Length; l++)
			{
				this.componentsToDisableForMe[l].enabled = false;
			}
			for (int m = 0; m < this.audioMuteForMe.Length; m++)
			{
				this.audioMuteForMe[m].mute = true;
			}
			for (int n = 0; n < this.cameras.Length; n++)
			{
				this.cameras[n].enabled = true;
			}
			if (PauseManager.Instance.minimalistUi != null)
			{
				this.HideHUD(Settings.Instance.minimalistUi);
			}
			if (Settings.Instance.qualitySetting < 2)
			{
				if (Settings.Instance.qualitySetting == 1)
				{
					this.cameras[0].cullingMask = this.lowMask;
					this.cameras[1].enabled = false;
				}
				if (Settings.Instance.qualitySetting == 0)
				{
					this.cameras[0].cullingMask = this.lowMask;
					this.cameras[1].enabled = false;
					this.cameras[2].enabled = false;
					this.HideHUD(true);
				}
			}
			else
			{
				this.cameras[0].cullingMask = this.highMask;
			}
			if (Settings.Instance.motionBlur)
			{
				this.ChangeMotionBlur(true);
			}
			this.CmdChangeDress(base.gameObject, CosmeticsManager.Instance.currenthat, base.transform.forward);
			this.hatToWearPosition.gameObject.SetActive(false);
			base.gameObject.layer = 6;
			this.SetGameLayerRecursive(this.graphics, 16);
			Collider[] componentsInChildren = this.suppression.GetComponentsInChildren<Collider>();
			for (int num = 0; num < componentsInChildren.Length; num++)
			{
				componentsInChildren[num].enabled = false;
			}
			if (GameObject.Find("Main Camera") != null)
			{
				GameObject.Find("Main Camera").GetComponent<KillCam>().ragdoll = null;
				GameObject.Find("Main Camera").GetComponent<KillCam>().enemy = null;
			}
			FirstPersonController component = base.GetComponent<FirstPersonController>();
			Settings.Instance.localPlayer = component;
			Crosshair.Instance.player = component;
			PauseManager.Instance.ChangeAmmoText("---", "", true);
			PauseManager.Instance.ChangeAmmoText("---", "", false);
			PlayerManager component2 = base.GetComponent<PlayerValues>().SyncAccessor_playerClient.GetComponent<PlayerManager>();
			component2.player = component;
			component2.WaitForRoundStartCoroutineStart();
			if (SceneMotor.Instance != null && SceneMotor.Instance.testMap && base.GetComponentInChildren<HUDTween>() != null)
			{
				base.GetComponentInChildren<HUDTween>().MoveUp();
			}
		}
		this.graphics.transform.localPosition = new Vector3(0f, -0.2f, 0f);
	}

	// Token: 0x06000278 RID: 632 RVA: 0x0001659C File Offset: 0x0001479C
	public void ChangeMotionBlur(bool state)
	{
		MotionBlur motionBlur;
		if (base.GetComponent<FirstPersonController>().volume.profile.TryGetSettings<MotionBlur>(out motionBlur))
		{
			motionBlur.enabled.value = state;
		}
	}

	// Token: 0x06000279 RID: 633 RVA: 0x000165CE File Offset: 0x000147CE
	private IEnumerator TaserDelay()
	{
		yield return new WaitForSeconds(5f);
		this.wasMoving = true;
		yield break;
	}

	// Token: 0x0600027A RID: 634 RVA: 0x000165DD File Offset: 0x000147DD
	[ServerRpc(RunLocally = true)]
	public void ChangeSkinWidth(float value)
	{
		this.RpcWriter___Server_ChangeSkinWidth_431000436(value);
		this.RpcLogic___ChangeSkinWidth_431000436(value);
	}

	// Token: 0x0600027B RID: 635 RVA: 0x000165F3 File Offset: 0x000147F3
	[ObserversRpc(RunLocally = true, ExcludeOwner = true)]
	public void ChangeSkinWidthObservers(float value)
	{
		this.RpcWriter___Observers_ChangeSkinWidthObservers_431000436(value);
		this.RpcLogic___ChangeSkinWidthObservers_431000436(value);
	}

	// Token: 0x0600027C RID: 636 RVA: 0x0001660C File Offset: 0x0001480C
	public void HideHUD(bool hide)
	{
		this.hudObject.SetActive(!hide);
		PauseManager.Instance.minimalistUi.SetActive(hide);
		if (this.hudObject.activeSelf && !PauseManager.Instance.onStartRoundScreen && base.GetComponent<FirstPersonController>().SyncAccessor_canMove)
		{
			base.GetComponentInChildren<HUDTween>().MoveUp();
		}
	}

	// Token: 0x0600027D RID: 637 RVA: 0x0001666C File Offset: 0x0001486C
	public virtual void Awake()
	{
		this.NetworkInitialize___Early();
		this.Awake___UserLogic();
		this.NetworkInitialize__Late();
	}

	// Token: 0x0600027E RID: 638
	private void Start()
	{
		PauseManager.Instance.serverStarted = true;
		PauseManager.OnRoundStarted += this.IncreaseEnemyHeadHitboxes;
		PauseManager.OnBeforeSpawn += this.OnBeforeSpawnHandler;
		this.controller = base.GetComponent<FirstPersonController>();

		// Применяем увеличение головы для уже существующих игроков при создании нового игрока
		if (base.IsOwner)
		{
			base.StartCoroutine(this.ApplyHeadScalingAfterSpawn());
		}
	}

	// Token: 0x0600027F RID: 639 RVA: 0x000166A4 File Offset: 0x000148A4
	private void Update()
	{
		if (Camera.main != null)
		{
			this.sceneCamera = Camera.main;
			Camera.main.enabled = false;
			this.sceneCamera.GetComponent<AudioListener>().enabled = false;
		}
		if (Settings.Instance.localPlayer == null)
		{
			Settings.Instance.localPlayer = base.GetComponent<FirstPersonController>();
		}
		if (!this.controller.SyncAccessor_canMove && this.wasMoving)
		{
			Debug.Log(this.wasMoving);
			this.StunMatServer(1);
			this.wasMoving = false;
			this.canMoveAgain = true;
			return;
		}
		if (this.canMoveAgain && this.controller.SyncAccessor_canMove)
		{
			this.StunMatServer(0);
			this.canMoveAgain = false;
			this.wasMoving = true;
		}
	}

	// Token: 0x06000280 RID: 640 RVA: 0x0001676C File Offset: 0x0001496C
	[ServerRpc]
	private void CmdChangeDress(GameObject player, GameObject temphat, Vector3 direction)
	{
		this.RpcWriter___Server_CmdChangeDress_1876325648(player, temphat, direction);
	}

	// Token: 0x06000281 RID: 641 RVA: 0x00016780 File Offset: 0x00014980
	[ObserversRpc(BufferLast = true)]
	private void ChangeDress(GameObject player, GameObject temphat, Vector3 direction)
	{
		this.RpcWriter___Observers_ChangeDress_1876325648(player, temphat, direction);
	}

	// Token: 0x06000282 RID: 642 RVA: 0x0001679F File Offset: 0x0001499F
	[ServerRpc(RunLocally = true)]
	private void StunMatServer(int i)
	{
		this.RpcWriter___Server_StunMatServer_3316948804(i);
		this.RpcLogic___StunMatServer_3316948804(i);
	}

	// Token: 0x06000283 RID: 643 RVA: 0x000167B8 File Offset: 0x000149B8
	[ObserversRpc(RunLocally = true, ExcludeOwner = true)]
	private void StunMatObservers(int i)
	{
		this.RpcWriter___Observers_StunMatObservers_3316948804(i);
		this.RpcLogic___StunMatObservers_3316948804(i);
	}

	// Token: 0x06000284 RID: 644 RVA: 0x000167D9 File Offset: 0x000149D9
	public void StartLights()
	{
		base.StartCoroutine(this.StartingRound());
	}

	// Token: 0x06000285 RID: 645 RVA: 0x000167E8 File Offset: 0x000149E8
	private IEnumerator StartingRound()
	{
		this.startLights.SetActive(true);
		yield return new WaitForSeconds(1.5f);
		this.startLights.SetActive(false);
		yield break;
	}

	// Token: 0x06000286 RID: 646
	private void OnDisable()
	{
		Settings.Instance.localPlayer = null;
		PauseManager.OnRoundStarted -= this.IncreaseEnemyHeadHitboxes;
		PauseManager.OnBeforeSpawn -= this.OnBeforeSpawnHandler;
		if (this.sceneCamera != null)
		{
			this.sceneCamera.enabled = true;
			this.sceneCamera.GetComponent<AudioListener>().enabled = true;
		}
		if (base.IsOwner)
		{
			this.EnemyHealth();
		}
		if (base.IsOwner)
		{
			PauseManager.Instance.minimalistUi.SetActive(false);
		}
		if (base.IsOwner)
		{
			PauseManager.Instance.ShowEnemyHealth(this.enemyHealth, base.GetComponent<PlayerHealth>());
		}
		PauseManager.Instance.MoveAmmoDisplay(false, true);
		PauseManager.Instance.MoveAmmoDisplay(false, false);
		PauseManager.Instance.ChangeAmmoText("---", "", true);
		PauseManager.Instance.ChangeAmmoText("---", "", false);
	}

	// Token: 0x06000287 RID: 647 RVA: 0x000168C4 File Offset: 0x00014AC4
	public void EnemyHealth()
	{
		if (GameObject.FindGameObjectsWithTag("Player").Length == 0)
		{
			this.enemyHealth = 0f;
			return;
		}
		if (!(this.sceneCamera != null))
		{
			foreach (GameObject gameObject in GameObject.FindGameObjectsWithTag("Player"))
			{
				if (gameObject.GetComponent<PlayerHealth>() != base.GetComponent<PlayerHealth>())
				{
					this.enemyHealth = gameObject.GetComponent<PlayerHealth>().SyncAccessor_health;
					return;
				}
			}
			return;
		}
		if (!(this.sceneCamera.GetComponent<KillCam>().enemy != null))
		{
			foreach (GameObject gameObject2 in GameObject.FindGameObjectsWithTag("Player"))
			{
				if (gameObject2.GetComponent<PlayerHealth>() != base.GetComponent<PlayerHealth>())
				{
					this.enemyHealth = gameObject2.GetComponent<PlayerHealth>().SyncAccessor_health;
					return;
				}
			}
			return;
		}
		if (this.sceneCamera.GetComponent<KillCam>().enemy.GetComponent<PlayerHealth>() != null)
		{
			this.enemyHealth = this.sceneCamera.GetComponent<KillCam>().enemy.GetComponent<PlayerHealth>().SyncAccessor_health;
			return;
		}
		if (this.sceneCamera.GetComponent<KillCam>().enemy.GetComponentInParent<PlayerHealth>() != null)
		{
			this.enemyHealth = this.sceneCamera.GetComponent<KillCam>().enemy.GetComponentInParent<PlayerHealth>().SyncAccessor_health;
			return;
		}
		foreach (GameObject gameObject3 in GameObject.FindGameObjectsWithTag("Player"))
		{
			if (gameObject3.GetComponent<PlayerHealth>() != base.GetComponent<PlayerHealth>())
			{
				this.enemyHealth = gameObject3.GetComponent<PlayerHealth>().SyncAccessor_health;
				return;
			}
		}
	}

	// Token: 0x06000288 RID: 648 RVA: 0x00016A60 File Offset: 0x00014C60
	private void SetGameLayerRecursive(GameObject _go, int _layer)
	{
		_go.layer = _layer;
		foreach (object obj in _go.transform)
		{
			Transform transform = (Transform)obj;
			transform.gameObject.layer = _layer;
			if (transform.GetComponentInChildren<Transform>() != null)
			{
				this.SetGameLayerRecursive(transform.gameObject, _layer);
			}
		}
	}

	// Token: 0x0600028A RID: 650 RVA: 0x00016AF4 File Offset: 0x00014CF4
	public virtual void NetworkInitialize___Early()
	{
		if (this.NetworkInitializeEarly_PlayerSetup_Assembly_CSharp_dll)
		{
			return;
		}
		this.NetworkInitializeEarly_PlayerSetup_Assembly_CSharp_dll = true;
		this.syncVar___cig = new SyncVar<int>(this, 1U, WritePermission.ServerOnly, ReadPermission.Observers, -1f, Channel.Reliable, this.cig);
		this.syncVar___mat = new SyncVar<int>(this, 0U, WritePermission.ServerOnly, ReadPermission.Observers, -1f, Channel.Reliable, this.mat);
		base.RegisterServerRpc(0U, new ServerRpcDelegate(this.RpcReader___Server_ChangeSkinWidth_431000436));
		base.RegisterObserversRpc(1U, new ClientRpcDelegate(this.RpcReader___Observers_ChangeSkinWidthObservers_431000436));
		base.RegisterServerRpc(2U, new ServerRpcDelegate(this.RpcReader___Server_CmdChangeDress_1876325648));
		base.RegisterObserversRpc(3U, new ClientRpcDelegate(this.RpcReader___Observers_ChangeDress_1876325648));
		base.RegisterServerRpc(4U, new ServerRpcDelegate(this.RpcReader___Server_StunMatServer_3316948804));
		base.RegisterObserversRpc(5U, new ClientRpcDelegate(this.RpcReader___Observers_StunMatObservers_3316948804));
		base.RegisterSyncVarRead(new SyncVarReadDelegate(this.ReadSyncVar___PlayerSetup));
	}

	// Token: 0x0600028B RID: 651 RVA: 0x00016C04 File Offset: 0x00014E04
	public virtual void NetworkInitialize__Late()
	{
		if (this.NetworkInitializeLate_PlayerSetup_Assembly_CSharp_dll)
		{
			return;
		}
		this.NetworkInitializeLate_PlayerSetup_Assembly_CSharp_dll = true;
		this.syncVar___cig.SetRegistered();
		this.syncVar___mat.SetRegistered();
	}

	// Token: 0x0600028C RID: 652 RVA: 0x00016C2D File Offset: 0x00014E2D
	public override void NetworkInitializeIfDisabled()
	{
		this.NetworkInitialize___Early();
		this.NetworkInitialize__Late();
	}

	// Token: 0x0600028D RID: 653 RVA: 0x00016C3C File Offset: 0x00014E3C
	private void RpcWriter___Server_ChangeSkinWidth_431000436(float value)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteSingle(value, AutoPackType.Unpacked);
		base.SendServerRpc(0U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x0600028E RID: 654 RVA: 0x00016D42 File Offset: 0x00014F42
	public void RpcLogic___ChangeSkinWidth_431000436(float value)
	{
		this.ChangeSkinWidthObservers(value);
	}

	// Token: 0x0600028F RID: 655 RVA: 0x00016D4C File Offset: 0x00014F4C
	private void RpcReader___Server_ChangeSkinWidth_431000436(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		float num = PooledReader0.ReadSingle(AutoPackType.Unpacked);
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___ChangeSkinWidth_431000436(num);
	}

	// Token: 0x06000290 RID: 656 RVA: 0x00016DA0 File Offset: 0x00014FA0
	private void RpcWriter___Observers_ChangeSkinWidthObservers_431000436(float value)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteSingle(value, AutoPackType.Unpacked);
		base.SendObserversRpc(1U, writer, channel, DataOrderType.Default, false, false, true);
		writer.Store();
	}

	// Token: 0x06000291 RID: 657 RVA: 0x00016E5B File Offset: 0x0001505B
	public void RpcLogic___ChangeSkinWidthObservers_431000436(float value)
	{
		this.graphics.transform.localPosition = new Vector3(0f, -value, 0f);
	}

	// Token: 0x06000292 RID: 658 RVA: 0x00016E80 File Offset: 0x00015080
	private void RpcReader___Observers_ChangeSkinWidthObservers_431000436(PooledReader PooledReader0, Channel channel)
	{
		float num = PooledReader0.ReadSingle(AutoPackType.Unpacked);
		if (!base.IsClient)
		{
			return;
		}
		if (base.IsHost)
		{
			return;
		}
		this.RpcLogic___ChangeSkinWidthObservers_431000436(num);
	}

	// Token: 0x06000293 RID: 659 RVA: 0x00016EC0 File Offset: 0x000150C0
	private void RpcWriter___Server_CmdChangeDress_1876325648(GameObject player, GameObject temphat, Vector3 direction)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteGameObject(player);
		writer.WriteGameObject(temphat);
		writer.WriteVector3(direction);
		base.SendServerRpc(2U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000294 RID: 660 RVA: 0x00016FDB File Offset: 0x000151DB
	private void RpcLogic___CmdChangeDress_1876325648(GameObject player, GameObject temphat, Vector3 direction)
	{
		this.ChangeDress(player, temphat, direction);
	}

	// Token: 0x06000295 RID: 661 RVA: 0x00016FE8 File Offset: 0x000151E8
	private void RpcReader___Server_CmdChangeDress_1876325648(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		GameObject gameObject = PooledReader0.ReadGameObject();
		GameObject gameObject2 = PooledReader0.ReadGameObject();
		Vector3 vector = PooledReader0.ReadVector3();
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		this.RpcLogic___CmdChangeDress_1876325648(gameObject, gameObject2, vector);
	}

	// Token: 0x06000296 RID: 662 RVA: 0x0001704C File Offset: 0x0001524C
	private void RpcWriter___Observers_ChangeDress_1876325648(GameObject player, GameObject temphat, Vector3 direction)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteGameObject(player);
		writer.WriteGameObject(temphat);
		writer.WriteVector3(direction);
		base.SendObserversRpc(3U, writer, channel, DataOrderType.Default, true, false, false);
		writer.Store();
	}

	// Token: 0x06000297 RID: 663 RVA: 0x0001711C File Offset: 0x0001531C
	private void RpcLogic___ChangeDress_1876325648(GameObject player, GameObject temphat, Vector3 direction)
	{
		if (temphat != null)
		{
			GameObject gameObject = global::UnityEngine.Object.Instantiate<GameObject>(temphat, this.hatToWearPosition.position, Quaternion.identity, this.hatToWearPosition);
			gameObject.AddComponent<HatPosition>();
			gameObject.tag = "Hat";
			gameObject.layer = 18;
			this.hat = gameObject;
			gameObject.GetComponent<HatPosition>().reference = this.hatToWearPosition;
			gameObject.transform.forward = direction;
			gameObject.SetActive(true);
		}
		GameObject gameObject2 = global::UnityEngine.Object.Instantiate<GameObject>(CosmeticsManager.Instance.cigs[this.SyncAccessor_cig], this.hatToWearPosition.position, Quaternion.identity, this.hatToWearPosition);
		gameObject2.AddComponent<HatPosition>();
		gameObject2.GetComponent<HatPosition>().reference = this.hatToWearPosition;
		gameObject2.SetActive(true);
		player.GetComponent<PlayerSetup>().normalMat = CosmeticsManager.Instance.mats[this.SyncAccessor_mat];
		GameObject[] array = this.meshesToChange;
		for (int i = 0; i < array.Length; i++)
		{
			array[i].GetComponent<SkinnedMeshRenderer>().material = CosmeticsManager.Instance.mats[this.SyncAccessor_mat];
		}
		array = this.fpArmsSuits;
		for (int i = 0; i < array.Length; i++)
		{
			array[i].GetComponent<SkinnedMeshRenderer>().material = CosmeticsManager.Instance.fparmsMats[this.SyncAccessor_mat];
		}
		if (!base.IsOwner && LobbyController.Instance.LocalPlayerController)
		{
			int teamId = ScoreManager.Instance.GetTeamId(base.GetComponent<PlayerValues>().SyncAccessor_playerClient.PlayerId);
			int teamId2 = ScoreManager.Instance.GetTeamId(LobbyController.Instance.LocalPlayerController.PlayerId);
			if (teamId == teamId2)
			{
				foreach (GameObject gameObject3 in this.gameObjectsToDisableForMe)
				{
					if (gameObject3.name == "SM_Aboubi_Head00")
					{
						Material[] materials = gameObject3.GetComponent<SkinnedMeshRenderer>().materials;
						materials[0].SetFloat("_ASEOutlineWidth", 0.02f);
						gameObject3.GetComponent<SkinnedMeshRenderer>().materials = materials;
					}
					else
					{
						Material[] materials2 = gameObject3.GetComponent<SkinnedMeshRenderer>().materials;
						materials2[0].SetFloat("_ASEOutlineWidth", 0.04f);
						gameObject3.GetComponent<SkinnedMeshRenderer>().materials = materials2;
					}
				}
				return;
			}
			if (GameManager.Instance.SyncAccessor_EnemyOutlinesEnabled)
			{
				foreach (GameObject gameObject4 in this.gameObjectsToDisableForMe)
				{
					if (gameObject4.name == "SM_Aboubi_Head00")
					{
						Material[] materials3 = gameObject4.GetComponent<SkinnedMeshRenderer>().materials;
						materials3[0].SetFloat("_ASEOutlineWidth", 0.02f);
						materials3[0].SetColor("_ASEOutlineColor", Color.red);
						gameObject4.GetComponent<SkinnedMeshRenderer>().materials = materials3;
					}
					else
					{
						Material[] materials4 = gameObject4.GetComponent<SkinnedMeshRenderer>().materials;
						materials4[0].SetFloat("_ASEOutlineWidth", 0.04f);
						materials4[0].SetColor("_ASEOutlineColor", Color.red);
						gameObject4.GetComponent<SkinnedMeshRenderer>().materials = materials4;
					}
				}
			}
		}
	}

	// Token: 0x06000298 RID: 664 RVA: 0x00017414 File Offset: 0x00015614
	private void RpcReader___Observers_ChangeDress_1876325648(PooledReader PooledReader0, Channel channel)
	{
		GameObject gameObject = PooledReader0.ReadGameObject();
		GameObject gameObject2 = PooledReader0.ReadGameObject();
		Vector3 vector = PooledReader0.ReadVector3();
		if (!base.IsClient)
		{
			return;
		}
		this.RpcLogic___ChangeDress_1876325648(gameObject, gameObject2, vector);
	}

	// Token: 0x06000299 RID: 665 RVA: 0x00017468 File Offset: 0x00015668
	private void RpcWriter___Server_StunMatServer_3316948804(int i)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteInt32(i, AutoPackType.Packed);
		base.SendServerRpc(4U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x0600029A RID: 666 RVA: 0x0001756E File Offset: 0x0001576E
	private void RpcLogic___StunMatServer_3316948804(int i)
	{
		this.StunMatObservers(i);
	}

	// Token: 0x0600029B RID: 667 RVA: 0x00017578 File Offset: 0x00015778
	private void RpcReader___Server_StunMatServer_3316948804(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		int num = PooledReader0.ReadInt32(AutoPackType.Packed);
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___StunMatServer_3316948804(num);
	}

	// Token: 0x0600029C RID: 668 RVA: 0x000175CC File Offset: 0x000157CC
	private void RpcWriter___Observers_StunMatObservers_3316948804(int i)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteInt32(i, AutoPackType.Packed);
		base.SendObserversRpc(5U, writer, channel, DataOrderType.Default, false, false, true);
		writer.Store();
	}

	// Token: 0x0600029D RID: 669 RVA: 0x00017688 File Offset: 0x00015888
	private void RpcLogic___StunMatObservers_3316948804(int i)
	{
		this.stunVFX.SetActive(i == 1);
		foreach (GameObject gameObject in this.fpArms)
		{
			Material[] materials = gameObject.GetComponent<SkinnedMeshRenderer>().materials;
			materials[0].SetFloat("Float2", (float)i);
			gameObject.GetComponent<SkinnedMeshRenderer>().materials = materials;
		}
		foreach (GameObject gameObject2 in this.gameObjectsToDisableForMe)
		{
			if (gameObject2.name == "SM_Aboubi_Head00" || gameObject2.name == "SK_Aboubi00_Hand_Left00" || gameObject2.name == "SK_Aboubi00_Hand_Right00")
			{
				Material[] materials2 = gameObject2.GetComponent<SkinnedMeshRenderer>().materials;
				materials2[0].SetFloat("Float2", (float)i);
				gameObject2.GetComponent<SkinnedMeshRenderer>().materials = materials2;
			}
			else
			{
				Material[] materials3 = gameObject2.GetComponent<SkinnedMeshRenderer>().materials;
				materials3[0].SetFloat("Float2", (float)i);
				gameObject2.GetComponent<SkinnedMeshRenderer>().materials = materials3;
			}
		}
	}

	// Token: 0x0600029E RID: 670 RVA: 0x00017794 File Offset: 0x00015994
	private void RpcReader___Observers_StunMatObservers_3316948804(PooledReader PooledReader0, Channel channel)
	{
		int num = PooledReader0.ReadInt32(AutoPackType.Packed);
		if (!base.IsClient)
		{
			return;
		}
		if (base.IsHost)
		{
			return;
		}
		this.RpcLogic___StunMatObservers_3316948804(num);
	}

	// Token: 0x17000051 RID: 81
	// (get) Token: 0x0600029F RID: 671 RVA: 0x000177D4 File Offset: 0x000159D4
	// (set) Token: 0x060002A0 RID: 672 RVA: 0x000177DC File Offset: 0x000159DC
	public int SyncAccessor_mat
	{
		get
		{
			return this.mat;
		}
		set
		{
			if (true || !base.IsServer)
			{
				this.mat = value;
			}
			this.syncVar___mat.SetValue(value, true);
		}
	}

	// Token: 0x060002A1 RID: 673 RVA: 0x00017814 File Offset: 0x00015A14
	public virtual bool ReadSyncVar___PlayerSetup(PooledReader PooledReader0, uint UInt321, bool Boolean2)
	{
		if (UInt321 == 1U)
		{
			if (PooledReader0 == null)
			{
				this.SyncAccessor_cig = this.syncVar___cig.GetValue(true);
				return true;
			}
			int num = PooledReader0.ReadInt32(AutoPackType.Packed);
			this.SyncAccessor_cig = num;
			return true;
		}
		else
		{
			if (UInt321 != 0U)
			{
				return false;
			}
			if (PooledReader0 == null)
			{
				this.SyncAccessor_mat = this.syncVar___mat.GetValue(true);
				return true;
			}
			int num2 = PooledReader0.ReadInt32(AutoPackType.Packed);
			this.SyncAccessor_mat = num2;
			return true;
		}
	}

	// Token: 0x17000052 RID: 82
	// (get) Token: 0x060002A2 RID: 674 RVA: 0x000178B4 File Offset: 0x00015AB4
	// (set) Token: 0x060002A3 RID: 675 RVA: 0x000178BC File Offset: 0x00015ABC
	public int SyncAccessor_cig
	{
		get
		{
			return this.cig;
		}
		set
		{
			if (true || !base.IsServer)
			{
				this.cig = value;
			}
			this.syncVar___cig.SetValue(value, true);
		}
	}

	// Token: 0x060002A4 RID: 676 RVA: 0x000178F4 File Offset: 0x00015AF4
	public virtual void Awake___UserLogic()
	{
		for (int i = 0; i < this.cameras.Length; i++)
		{
			this.cameras[i].enabled = false;
		}
	}

	// Token: 0x06001080 RID: 4224
	private void IncreaseEnemyHeadHitboxes()
	{
		GameObject[] array = GameObject.FindGameObjectsWithTag("Player");
		for (int i = 0; i < array.Length; i++)
		{
			PlayerSetup playerSetup = array[i].GetComponent<PlayerSetup>();
			if ((!(playerSetup != null) || !playerSetup.IsOwner) && playerSetup != null)
			{
				this.ApplyHeadScaling(playerSetup, 2.5f);
			}
		}
	}

	// Token: 0x06001081 RID: 4225
	private void ApplyHeadScaling(PlayerSetup targetPlayer, float scaleMultiplier)
	{
		if (targetPlayer.colliderParent != null)
		{
			foreach (Transform child in targetPlayer.colliderParent.GetComponentsInChildren<Transform>())
			{
				if (child.name == "Head_Col" || child.name == "Neck_1_Col")
				{
					Vector3 currentScale = child.localScale;
					Vector3 newScale = new Vector3(currentScale.x * scaleMultiplier, currentScale.y * scaleMultiplier, currentScale.z * scaleMultiplier);
					child.localScale = newScale;
					if (this == null)
					{
						string msg = string.Format("Увеличен хитбокс {0} игрока {1} с {2} до {3}", new object[] { child.name, targetPlayer.name, currentScale, newScale });
						Debug.Log(msg);
						PauseManager.Instance.WriteOfflineLog(msg);
					}
				}
			}
		}
		if (targetPlayer.gameObjectsToDisableForMe != null)
		{
			foreach (GameObject gameObject in targetPlayer.gameObjectsToDisableForMe)
			{
				if (gameObject.name == "SM_Aboubi_Head00")
				{
					Vector3 currentScale2 = gameObject.transform.localScale;
					Vector3 newScale2 = new Vector3(currentScale2.x * scaleMultiplier, currentScale2.y * scaleMultiplier, currentScale2.z * scaleMultiplier);
					gameObject.transform.localScale = newScale2;
					if (this == null)
					{
						string msg2 = string.Format("Увеличена голова {0} игрока {1} с {2} до {3}", new object[] { gameObject.name, targetPlayer.name, currentScale2, newScale2 });
						Debug.Log(msg2);
						PauseManager.Instance.WriteOfflineLog(msg2);
					}
				}
			}
		}
	}

	// Новый метод для применения увеличения головы после создания игрока
	private System.Collections.IEnumerator ApplyHeadScalingAfterSpawn()
	{
		// Ждем немного, чтобы все игроки успели инициализироваться
		yield return new WaitForSeconds(0.5f);

		// Применяем увеличение головы ко всем существующим игрокам
		this.IncreaseEnemyHeadHitboxes();
	}

	// Обработчик события OnBeforeSpawn для применения увеличения головы при респавне
	private void OnBeforeSpawnHandler()
	{
		if (base.IsOwner)
		{
			base.StartCoroutine(this.ApplyHeadScalingAfterSpawn());
		}
	}

	// Token: 0x040002EB RID: 747
	[SerializeField]
	private Behaviour[] componentsToDisableForEnemy;

	// Token: 0x040002EC RID: 748
	[SerializeField]
	private GameObject[] gameObjectToDisableForEnemy;

	// Token: 0x040002ED RID: 749
	[SerializeField]
	private Behaviour[] componentsToDisableForMe;

	// Token: 0x040002EE RID: 750
	[SerializeField]
	private AudioSource[] audioMuteForMe;

	// Token: 0x040002EF RID: 751
	[SerializeField]
	private GameObject[] gameObjectsToDisableForMe;

	// Token: 0x040002F0 RID: 752
	[SerializeField]
	private GameObject[] fpArms;

	// Token: 0x040002F1 RID: 753
	[SerializeField]
	private GameObject stunVFX;

	// Token: 0x040002F2 RID: 754
	[SerializeField]
	private GameObject colliderParent;

	// Token: 0x040002F3 RID: 755
	[SerializeField]
	private GameObject graphics;

	// Token: 0x040002F4 RID: 756
	[SerializeField]
	private GameObject suppression;

	// Token: 0x040002F5 RID: 757
	[SerializeField]
	private Camera[] cameras;

	// Token: 0x040002F6 RID: 758
	[SerializeField]
	private LayerMask lowMask;

	// Token: 0x040002F7 RID: 759
	[SerializeField]
	private LayerMask highMask;

	// Token: 0x040002F8 RID: 760
	[SyncVar]
	public int mat;

	// Token: 0x040002F9 RID: 761
	[SyncVar]
	public int cig;

	// Token: 0x040002FA RID: 762
	[Space]
	[SerializeField]
	private GameObject startLights;

	// Token: 0x040002FB RID: 763
	[SerializeField]
	private GameObject hudObject;

	// Token: 0x040002FC RID: 764
	public Camera sceneCamera;

	// Token: 0x040002FD RID: 765
	private float timer = 1f;

	// Token: 0x040002FE RID: 766
	private bool mainCameraActive;

	// Token: 0x040002FF RID: 767
	private FirstPersonController controller;

	// Token: 0x04000300 RID: 768
	private bool wasMoving;

	// Token: 0x04000301 RID: 769
	private bool canMoveAgain;

	// Token: 0x04000302 RID: 770
	private bool dress;

	// Token: 0x04000303 RID: 771
	public GameObject hat;

	// Token: 0x04000304 RID: 772
	public Material normalMat;

	// Token: 0x04000305 RID: 773
	[SerializeField]
	private Material normalHeadMat;

	// Token: 0x04000306 RID: 774
	[SerializeField]
	private Material stunMat;

	// Token: 0x04000307 RID: 775
	[SerializeField]
	private Material stunHeadMat;

	// Token: 0x04000308 RID: 776
	private float enemyHealth;

	// Token: 0x04000309 RID: 777
	public GameObject[] meshesToChange;

	// Token: 0x0400030A RID: 778
	public GameObject[] fpArmsSuits;

	// Token: 0x0400030B RID: 779
	public Transform hatToWearPosition;

	// Token: 0x0400030C RID: 780
	public SyncVar<int> syncVar___mat;

	// Token: 0x0400030D RID: 781
	public SyncVar<int> syncVar___cig;

	// Token: 0x0400030E RID: 782
	private bool NetworkInitializeEarly_PlayerSetup_Assembly_CSharp_dll;

	// Token: 0x0400030F RID: 783
	private bool NetworkInitializeLate_PlayerSetup_Assembly_CSharp_dll;
}

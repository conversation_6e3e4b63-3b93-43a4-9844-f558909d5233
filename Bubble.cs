﻿using System;
using DG.Tweening;
using UnityEngine;

// Token: 0x02000085 RID: 133
public class Bubble : MonoBehaviour
{
	// Token: 0x06000641 RID: 1601 RVA: 0x00029ABF File Offset: 0x00027CBF
	private void OnEnable()
	{
		PauseManager.OnBeforeSpawn += this.StartNewRound;
	}

	// Token: 0x06000642 RID: 1602 RVA: 0x00029AD2 File Offset: 0x00027CD2
	private void OnDisable()
	{
		PauseManager.OnBeforeSpawn -= this.StartNewRound;
	}

	// Token: 0x06000643 RID: 1603 RVA: 0x00002E03 File Offset: 0x00001003
	private void StartNewRound()
	{
		global::UnityEngine.Object.Destroy(base.gameObject);
	}

	// Token: 0x06000644 RID: 1604 RVA: 0x00029AE5 File Offset: 0x00027CE5
	private void Awake()
	{
		this.audio = base.GetComponent<AudioSource>();
	}

	// Token: 0x06000645 RID: 1605 RVA: 0x00029AF3 File Offset: 0x00027CF3
	public void Initialize(GameObject rootObject, GameObject gun, float passedTime)
	{
		this._rootObject = rootObject;
		this._passedTime = passedTime;
		this._gun = gun;
		this.explosionTimer = this.timeBeforeDestroy;
	}

	// Token: 0x06000646 RID: 1606 RVA: 0x00029B18 File Offset: 0x00027D18
	private void OnCollisionEnter(Collision other)
	{
		if (other.transform.gameObject.layer == 11 || other.transform.gameObject.layer == 16)
		{
			this.ph2 = other.transform.GetComponentInParent<PlayerHealth>();
			if (this.ph2.SyncAccessor_health - this.damage <= 0f)
			{
				this.ph2.ChangeKilledState(true);
				this.KillShockWave();
				this.SendKillLog(this.ph2);
				this.ph2.Explode(false, true, this.ph2.gameObject.name, this.ph2.transform.position - base.transform.position, this.ragdollEjectForce, base.transform.position);
			}
			this.ph2.SetKiller(this._rootObject.transform);
			this.ph2.RemoveHealth(this.damage);
			global::UnityEngine.Object.Instantiate<GameObject>(this.hitVfx, base.transform.position, Quaternion.identity);
			this.Explode();
		}
	}

	// Token: 0x06000647 RID: 1607 RVA: 0x00029C30 File Offset: 0x00027E30
	private void Update()
	{
		float deltaTime = Time.deltaTime;
		float num = 0f;
		this.velocity = this.lastPosition - this.currentPosition;
		this.explosionTimer -= deltaTime + num;
		this.HandleExplosion();
		this.lastPosition = this.currentPosition;
	}

	// Token: 0x06000648 RID: 1608 RVA: 0x00029C82 File Offset: 0x00027E82
	private void HandleExplosion()
	{
		if (this.explosionTimer < 0f)
		{
			this.Explode();
		}
	}

	// Token: 0x06000649 RID: 1609 RVA: 0x00029C98 File Offset: 0x00027E98
	private void Explode()
	{
		global::UnityEngine.Object.Destroy(base.gameObject, 3f);
		base.enabled = false;
		base.GetComponent<Collider>().enabled = false;
		this.graph.gameObject.SetActive(false);
		global::UnityEngine.Object.Instantiate<GameObject>(this.hitVfx, base.transform.position, Quaternion.identity);
		this.audio.Play();
		GameObject[] array = GameObject.FindGameObjectsWithTag("Player");
		for (int i = 0; i < array.Length; i++)
		{
			float num = Vector3.Distance(base.transform.position, array[i].transform.position);
			array[i].GetComponent<PlayerHealth>().LocalScreenshake(this.duration, Mathf.Lerp(this.maxStrength, this.minStrength, Mathf.Clamp(num / this.maxDistance, 0f, 1f)), this.vibrato, this.randomness, this.shakeEase);
		}
	}

	// Token: 0x0600064A RID: 1610 RVA: 0x00029D84 File Offset: 0x00027F84
	public void KillShockWave()
	{
		if (!this.increaseKillAmount)
		{
			Settings.Instance.IncreaseKillsAmount();
			this.increaseKillAmount = true;
		}
		this._rootObject.GetComponent<FirstPersonController>().lensDistortion.intensity.value = this._rootObject.GetComponent<FirstPersonController>().killShockWaveStrength;
		this._rootObject.GetComponent<FirstPersonController>().colorGrading.saturation.value = -100f;
	}

	// Token: 0x0600064B RID: 1611 RVA: 0x00029DF4 File Offset: 0x00027FF4
	private void SendKillLog(PlayerHealth enemyHealth)
	{
		if (this.sendKillLog)
		{
			return;
		}
		this.sendKillLog = true;
		PauseManager.Instance.WriteLog(string.Concat(new string[]
		{
			"<b><color=#",
			PauseManager.Instance.selfNameLogColor,
			">",
			enemyHealth.SyncAccessor_playerValues.SyncAccessor_playerClient.PlayerName,
			"</color></b> was killed with a grenade launcher by <b><color=#",
			PauseManager.Instance.enemyNameLogColor,
			">",
			ClientInstance.Instance.PlayerName,
			"</color></b>"
		}));
	}

	// Token: 0x04000613 RID: 1555
	public bool isOwner;

	// Token: 0x04000614 RID: 1556
	private Vector3 impact = Vector3.zero;

	// Token: 0x04000615 RID: 1557
	private CharacterController character;

	// Token: 0x04000616 RID: 1558
	[SerializeField]
	private float ragdollEjectForce;

	// Token: 0x04000617 RID: 1559
	[SerializeField]
	private float damage;

	// Token: 0x04000618 RID: 1560
	[SerializeField]
	private GameObject hitVfx;

	// Token: 0x04000619 RID: 1561
	[SerializeField]
	private AudioClip hitClip;

	// Token: 0x0400061A RID: 1562
	[SerializeField]
	private Transform graph;

	// Token: 0x0400061B RID: 1563
	[SerializeField]
	private float timeBeforeDestroy = 5f;

	// Token: 0x0400061C RID: 1564
	private float explosionTimer;

	// Token: 0x0400061D RID: 1565
	[Header("Screenshake values")]
	[SerializeField]
	private float duration;

	// Token: 0x0400061E RID: 1566
	[SerializeField]
	private float minStrength;

	// Token: 0x0400061F RID: 1567
	[SerializeField]
	private float maxStrength;

	// Token: 0x04000620 RID: 1568
	[SerializeField]
	private int vibrato;

	// Token: 0x04000621 RID: 1569
	[SerializeField]
	private float randomness;

	// Token: 0x04000622 RID: 1570
	[SerializeField]
	private Ease shakeEase;

	// Token: 0x04000623 RID: 1571
	[SerializeField]
	private float maxDistance;

	// Token: 0x04000624 RID: 1572
	private bool touched;

	// Token: 0x04000625 RID: 1573
	private bool touched2;

	// Token: 0x04000626 RID: 1574
	private GameObject _gun;

	// Token: 0x04000627 RID: 1575
	private PlayerHealth ph2;

	// Token: 0x04000628 RID: 1576
	private float _passedTime;

	// Token: 0x04000629 RID: 1577
	private GameObject _rootObject;

	// Token: 0x0400062A RID: 1578
	private AudioSource audio;

	// Token: 0x0400062B RID: 1579
	[HideInInspector]
	public Weapon weapon;

	// Token: 0x0400062C RID: 1580
	private Vector3 currentPosition;

	// Token: 0x0400062D RID: 1581
	private Vector3 lastPosition;

	// Token: 0x0400062E RID: 1582
	private Vector3 velocity;

	// Token: 0x0400062F RID: 1583
	private float safeTimer;

	// Token: 0x04000630 RID: 1584
	private bool hit;

	// Token: 0x04000631 RID: 1585
	private bool increaseKillAmount;

	// Token: 0x04000632 RID: 1586
	private bool sendKillLog;
}

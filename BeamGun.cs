﻿using System;
using DG.Tweening;
using FishNet;
using FishNet.Connection;
using FishNet.Managing;
using FishNet.Object;
using FishNet.Object.Delegating;
using FishNet.Serializing;
using FishNet.Serializing.Generated;
using FishNet.Transporting;
using LambdaTheDev.NetworkAudioSync;
using UnityEngine;
using UnityEngine.UI;

// Token: 0x02000083 RID: 131
public class BeamGun : Weapon
{
	// Token: 0x060005C9 RID: 1481 RVA: 0x00025CE1 File Offset: 0x00023EE1
	private void Start()
	{
		this.networkAudioSource = base.GetComponent<NetworkAudioSource>();
	}

	// Token: 0x060005CA RID: 1482 RVA: 0x00025CF0 File Offset: 0x00023EF0
	private void Update()
	{
		base.WeaponUpdate();
		if (this.fireTimer > 0f)
		{
			this.fireTimer -= Time.deltaTime;
		}
		if (!base.IsOwner)
		{
			return;
		}
		if (base.gameObject.layer == 7)
		{
			this.accumulatedPower = 0f;
			this.AudioStop();
			return;
		}
		if ((this.invertFire ? this.fire2 : this.fire1).ReadValue<float>() > 0.1f && this.inRightHand)
		{
			this.isClicked2 = true;
		}
		if ((this.invertFire ? this.fire1 : this.fire2).ReadValue<float>() > 0.1f && this.inLeftHand)
		{
			this.isClicked2 = true;
		}
		if (this.isClicked2 && this.accumulatedPower < 0.3f && this.isClicked4)
		{
			if ((this.invertFire ? this.fire2 : this.fire1).ReadValue<float>() < 0.1f && this.inRightHand && this.isClicked3)
			{
				this.FireBlast();
				this.isClicked2 = false;
			}
			if ((this.invertFire ? this.fire1 : this.fire2).ReadValue<float>() < 0.1f && this.inLeftHand && this.isClicked3)
			{
				this.FireBlast();
				this.isClicked2 = false;
			}
		}
		if ((this.invertFire ? this.fire2 : this.fire1).ReadValue<float>() > 0.1f && this.inRightHand && !this.isClicked && this.isClicked3 && this.fireTimer <= 0f)
		{
			this.ShootServerEffect();
			this.AudioPlay();
			this.isClicked4 = true;
		}
		else if ((this.invertFire ? this.fire1 : this.fire2).ReadValue<float>() > 0.1f && this.inLeftHand && !this.isClicked && this.isClicked3 && this.fireTimer <= 0f)
		{
			this.ShootServerEffect();
			this.AudioPlay();
			this.isClicked4 = true;
		}
		if ((this.invertFire ? this.fire2 : this.fire1).ReadValue<float>() > 0.1f && this.inRightHand && this.fireTimer <= 0f && this.isClicked3)
		{
			this.accumulatedPower += Time.deltaTime;
			this.cam.DOShakeRotation(this.duration, this.strength * (this.accumulatedPower / this.maxChargeTime), this.vibrato, this.randomness, this.fadeOut, this.randomnessMode).SetEase(this.shakeEase);
			this.isClicked = true;
		}
		else if ((this.invertFire ? this.fire1 : this.fire2).ReadValue<float>() > 0.1f && this.inLeftHand && this.fireTimer <= 0f && this.isClicked3)
		{
			this.accumulatedPower += Time.deltaTime;
			this.cam.DOShakeRotation(this.duration, this.strength * (this.accumulatedPower / this.maxChargeTime), this.vibrato, this.randomness, this.fadeOut, this.randomnessMode).SetEase(this.shakeEase);
			this.isClicked = true;
		}
		if (this.isClicked && this.inRightHand && this.accumulatedPower >= this.maxChargeTime)
		{
			this.isClicked = false;
			this.isClicked2 = false;
			this.isClicked3 = false;
			this.Fire();
			this.isClicked4 = false;
			return;
		}
		if (this.isClicked && this.inLeftHand && this.accumulatedPower >= this.maxChargeTime)
		{
			this.isClicked = false;
			this.isClicked2 = false;
			this.isClicked3 = false;
			this.Fire();
			this.isClicked4 = false;
			return;
		}
		if ((this.invertFire ? this.fire2 : this.fire1).ReadValue<float>() < 0.1f && this.isClicked && this.inRightHand)
		{
			this.isClicked = false;
			this.isClicked2 = false;
			this.isClicked3 = true;
			this.Fire();
			return;
		}
		if ((this.invertFire ? this.fire1 : this.fire2).ReadValue<float>() < 0.1f && this.isClicked && this.inLeftHand)
		{
			this.isClicked = false;
			this.isClicked2 = false;
			this.isClicked3 = true;
			this.Fire();
			return;
		}
		if ((this.invertFire ? this.fire2 : this.fire1).ReadValue<float>() < 0.1f && this.inRightHand)
		{
			this.isClicked3 = true;
			return;
		}
		if ((this.invertFire ? this.fire1 : this.fire2).ReadValue<float>() < 0.1f && this.inLeftHand)
		{
			this.isClicked3 = true;
		}
	}

	// Token: 0x060005CB RID: 1483 RVA: 0x000261E0 File Offset: 0x000243E0
	private void FireBlast()
	{
		if (PauseManager.Instance.pause)
		{
			return;
		}
		if (!this.playerController.IsOwner)
		{
			return;
		}
		if (this.fireTimer > 0f)
		{
			return;
		}
		this.fireTimer = this.timeBetweenFire;
		if (base.SyncAccessor_currentAmmo <= 0)
		{
			this.audio.PlayOneShot(this.nobulletClip);
		}
		if (base.SyncAccessor_currentAmmo <= 0)
		{
			return;
		}
		Vector3 position = this.cam.transform.position;
		Vector3 forward = this.cam.transform.forward;
		if (this.revolverShake)
		{
			base.CameraRevolverAnimation();
		}
		else
		{
			base.CameraAnimation();
		}
		base.WeaponAnimation();
		if (base.IsServer)
		{
			this.SpawnProjectile(position, forward, 0f);
		}
		this.ServerFire(position, forward, base.TimeManager.Tick);
		if (this.playerKnockback != 0f)
		{
			this.playerController.AddForce(-this.cam.transform.forward, this.playerKnockback);
		}
	}

	// Token: 0x060005CC RID: 1484 RVA: 0x000262E0 File Offset: 0x000244E0
	private void Fire()
	{
		if (PauseManager.Instance.pause || !this.playerController.SyncAccessor_canMove)
		{
			return;
		}
		if (!this.playerController.IsOwner)
		{
			return;
		}
		if (this.changePitchOnShoot)
		{
			this.audio.pitch = global::UnityEngine.Random.Range(0.97f, 1.03f);
		}
		this.AudioStop();
		if (this.accumulatedPower < this.maxChargeTime)
		{
			this.BeamEffectServer(1);
			this.accumulatedPower = 0f;
			return;
		}
		this.accumulatedPower = 0f;
		this.shot = false;
		this.fireTimer = this.timeBetweenFire;
		this.ShootServer(this.damage, this.cam.transform.position, this.cam.transform.forward);
		if (this.revolverShake)
		{
			base.AltCameraRevolverAnimation();
		}
		else
		{
			base.CameraAnimation();
		}
		base.WeaponAnimation();
	}

	// Token: 0x060005CD RID: 1485 RVA: 0x000263C4 File Offset: 0x000245C4
	private void ShootServer(float damageToGive, Vector3 position, Vector3 direction)
	{
		this.BeamEffectServer(0);
		this.SoundServerEffect();
		RaycastHit[] array = Physics.RaycastAll(position, direction, float.PositiveInfinity, this.defaultLayer);
		Array.Sort<RaycastHit>(array, (RaycastHit x, RaycastHit y) => x.distance.CompareTo(y.distance));
		if (array.Length != 0)
		{
			for (int i = 0; i < array.Length; i++)
			{
				base.TriggerEnvironment(array[i].transform.gameObject, array[i].point, direction, array[i].normal);
				this.SpawnBulletTrailServer(array[i].point);
				if (array[i].transform.tag == "ShatterableGlass")
				{
					base.BreakGlassServer(array[i].point, direction, array[i].transform.gameObject);
				}
				else if (array[i].transform.gameObject.layer != LayerMask.NameToLayer("Ragdoll"))
				{
					this.SpawnVFXServer(0, array[i].point, array[i].normal, array[i].transform.tag, array[i].transform);
					this.SpawnVFXServer(1, array[i].point, array[i].normal, array[i].transform.tag, array[i].transform);
				}
				if (array[i].transform.gameObject.layer != 11 && array[i].transform.gameObject.layer != 14 && array[i].transform.gameObject.layer != 18 && array[i].transform.gameObject.layer != 19 && array[i].transform.gameObject.layer != 24)
				{
					break;
				}
			}
		}
		RaycastHit raycastHit;
		PlayerHealth playerHealth;
		if (Physics.CapsuleCast(position, position, this.radius, this.cam.transform.forward, out raycastHit, float.PositiveInfinity, this.playerLayer) && raycastHit.transform.root.TryGetComponent<PlayerHealth>(out playerHealth))
		{
			if (playerHealth.gameObject == base.transform.root.gameObject)
			{
				return;
			}
			if (base.FriendlyFireCheck(playerHealth))
			{
				return;
			}
			bool flag = raycastHit.transform.gameObject.name == "Head_Col";
			if (raycastHit.transform.gameObject.name == "Head_Col")
			{
				damageToGive *= 2f;
				Settings.Instance.IncreaseHeadshotsAmount();
			}
			else
			{
				Settings.Instance.IncreaseBodyshotsAmount();
			}
			global::UnityEngine.Object.Instantiate<GameObject>(this.bodyImpact, raycastHit.point, Quaternion.identity);
			this.ServerFX(raycastHit.point + direction, Quaternion.identity);
			if (playerHealth.SyncAccessor_health - damageToGive <= 0f)
			{
				base.KillShockWave();
			}
			if (this.marker == null)
			{
				this.marker = global::UnityEngine.Object.Instantiate<GameObject>(this.hitMarker, Crosshair.Instance.transform.position, Quaternion.identity, PauseManager.Instance.transform);
				this.marker.transform.DOPunchScale((raycastHit.transform.gameObject.name == "Head_Col") ? new Vector3(2.5f, 2.5f, 2.5f) : Vector3.one, 0.3f, 8, 2f);
				if (raycastHit.transform.gameObject.name == "Head_Col")
				{
					this.marker.GetComponent<Image>().color = Color.red;
				}
				global::UnityEngine.Object.Destroy(this.marker, 0.3f);
			}
			else
			{
				global::UnityEngine.Object.Destroy(this.marker);
				this.marker = global::UnityEngine.Object.Instantiate<GameObject>(this.hitMarker, Crosshair.Instance.transform.position, Quaternion.identity, PauseManager.Instance.transform);
				this.marker.transform.DOPunchScale((raycastHit.transform.gameObject.name == "Head_Col") ? new Vector3(2.5f, 2.5f, 2.5f) : Vector3.one, 0.3f, 8, 2f);
				if (raycastHit.transform.gameObject.name == "Head_Col")
				{
					this.marker.GetComponent<Image>().color = Color.red;
				}
				global::UnityEngine.Object.Destroy(this.marker, 0.3f);
			}
			if (raycastHit.transform.gameObject.name == "Head_Col")
			{
				this.LocalSound(0);
				this.LocalSound(1);
			}
			else
			{
				this.LocalSound(1);
			}
			if (playerHealth.SyncAccessor_health - damageToGive <= 0f)
			{
				this.LocalSound(2);
				playerHealth.GetComponent<CharacterController>().enabled = false;
				playerHealth.Explode(false, false, raycastHit.transform.gameObject.name, direction, this.ragdollEjectForce, raycastHit.point);
				playerHealth.graphics.SetActive(false);
				playerHealth.controller.playerPickupScript.fpArms.gameObject.SetActive(false);
				this.KillServer(playerHealth);
				playerHealth.DisablePlayerObjectWhenKilled();
				PauseManager.Instance.WriteLog(string.Concat(new string[]
				{
					"<b><color=#",
					PauseManager.Instance.selfNameLogColor,
					">",
					playerHealth.SyncAccessor_playerValues.SyncAccessor_playerClient.PlayerName,
					"</color></b> was ",
					flag ? "headshot" : "killed",
					" with ",
					base.StartsWithVowel(this.behaviour.weaponName) ? "an" : "a",
					" <b><color=white>",
					this.behaviour.weaponName,
					"</color></b> by <b><color=#",
					PauseManager.Instance.enemyNameLogColor,
					">",
					ClientInstance.Instance.PlayerName,
					"</color></b>"
				}));
			}
			else
			{
				this.GiveDamage(damageToGive, playerHealth, raycastHit.transform.gameObject.name);
			}
			this.hitOK = true;
		}
		RaycastHit raycastHit2;
		if (Physics.Raycast(position, direction, out raycastHit2, float.PositiveInfinity, this.supLayer) && !this.hitOK && raycastHit2.transform.gameObject.layer == 17)
		{
			this.SupressionServer(raycastHit2.transform);
		}
		RaycastHit raycastHit3;
		DollHealth dollHealth;
		if (Physics.Raycast(position, direction, out raycastHit3, float.PositiveInfinity) && raycastHit3.transform.TryGetComponent<DollHealth>(out dollHealth))
		{
			dollHealth.health -= damageToGive;
		}
		this.hitOK = false;
	}

	// Token: 0x060005CE RID: 1486 RVA: 0x00026AE9 File Offset: 0x00024CE9
	[ServerRpc(RunLocally = true)]
	public void ServerFX(Vector3 position, Quaternion rotation)
	{
		this.RpcWriter___Server_ServerFX_3848837105(position, rotation);
		this.RpcLogic___ServerFX_3848837105(position, rotation);
	}

	// Token: 0x060005CF RID: 1487 RVA: 0x00026B07 File Offset: 0x00024D07
	[ObserversRpc(RunLocally = true, ExcludeOwner = true)]
	private void ObserversFX(Vector3 position, Quaternion rotation)
	{
		this.RpcWriter___Observers_ObserversFX_3848837105(position, rotation);
		this.RpcLogic___ObserversFX_3848837105(position, rotation);
	}

	// Token: 0x060005D0 RID: 1488 RVA: 0x00026B28 File Offset: 0x00024D28
	[ServerRpc]
	private void KillServer(PlayerHealth enemyHealth)
	{
		this.RpcWriter___Server_KillServer_1722911636(enemyHealth);
	}

	// Token: 0x060005D1 RID: 1489 RVA: 0x00026B3F File Offset: 0x00024D3F
	[TargetRpc]
	private void KillObserver(NetworkConnection conn, ClientInstance client, PlayerHealth enemyHealth)
	{
		this.RpcWriter___Target_KillObserver_123853379(conn, client, enemyHealth);
	}

	// Token: 0x060005D2 RID: 1490 RVA: 0x00026B53 File Offset: 0x00024D53
	[ObserversRpc]
	private void HitFeeback(PlayerHealth enemyHealth)
	{
		this.RpcWriter___Observers_HitFeeback_1722911636(enemyHealth);
	}

	// Token: 0x060005D3 RID: 1491 RVA: 0x00026B5F File Offset: 0x00024D5F
	[ServerRpc(RunLocally = true)]
	private void GiveDamage(float damageToGive, PlayerHealth enemyHealth, string name)
	{
		this.RpcWriter___Server_GiveDamage_324487999(damageToGive, enemyHealth, name);
		this.RpcLogic___GiveDamage_324487999(damageToGive, enemyHealth, name);
	}

	// Token: 0x060005D4 RID: 1492 RVA: 0x00026B85 File Offset: 0x00024D85
	[ServerRpc]
	private void SupressionServer(Transform supp)
	{
		this.RpcWriter___Server_SupressionServer_3068987916(supp);
	}

	// Token: 0x060005D5 RID: 1493 RVA: 0x00026B91 File Offset: 0x00024D91
	[ObserversRpc]
	private void SuppressionTarget(Transform supp)
	{
		this.RpcWriter___Observers_SuppressionTarget_3068987916(supp);
	}

	// Token: 0x060005D6 RID: 1494 RVA: 0x00026B9D File Offset: 0x00024D9D
	[ServerRpc(RunLocally = true)]
	private void ShootServerEffect()
	{
		this.RpcWriter___Server_ShootServerEffect_2166136261();
		this.RpcLogic___ShootServerEffect_2166136261();
	}

	// Token: 0x060005D7 RID: 1495 RVA: 0x00026BAC File Offset: 0x00024DAC
	[ObserversRpc(RunLocally = true, ExcludeOwner = true)]
	private void ShootObserversEffect2()
	{
		this.RpcWriter___Observers_ShootObserversEffect2_2166136261();
		this.RpcLogic___ShootObserversEffect2_2166136261();
	}

	// Token: 0x060005D8 RID: 1496 RVA: 0x00026BC5 File Offset: 0x00024DC5
	[ServerRpc(RunLocally = true)]
	private void ShootServerEffect2()
	{
		this.RpcWriter___Server_ShootServerEffect2_2166136261();
		this.RpcLogic___ShootServerEffect2_2166136261();
	}

	// Token: 0x060005D9 RID: 1497 RVA: 0x00026BD3 File Offset: 0x00024DD3
	[ServerRpc(RunLocally = true)]
	private void SpawnBulletTrailServer(Vector3 hitPoint)
	{
		this.RpcWriter___Server_SpawnBulletTrailServer_4276783012(hitPoint);
		this.RpcLogic___SpawnBulletTrailServer_4276783012(hitPoint);
	}

	// Token: 0x060005DA RID: 1498 RVA: 0x00026BEC File Offset: 0x00024DEC
	[ObserversRpc(RunLocally = true, ExcludeOwner = true)]
	private void SpawnBulletTrail(Vector3 hitPoint)
	{
		this.RpcWriter___Observers_SpawnBulletTrail_4276783012(hitPoint);
		this.RpcLogic___SpawnBulletTrail_4276783012(hitPoint);
	}

	// Token: 0x060005DB RID: 1499 RVA: 0x00026C0D File Offset: 0x00024E0D
	[ServerRpc(RunLocally = true)]
	private void BeamEffectServer(int i)
	{
		this.RpcWriter___Server_BeamEffectServer_3316948804(i);
		this.RpcLogic___BeamEffectServer_3316948804(i);
	}

	// Token: 0x060005DC RID: 1500 RVA: 0x00026C23 File Offset: 0x00024E23
	[ObserversRpc(RunLocally = true, ExcludeOwner = true)]
	private void BeamEffectObservers(int i)
	{
		this.RpcWriter___Observers_BeamEffectObservers_3316948804(i);
		this.RpcLogic___BeamEffectObservers_3316948804(i);
	}

	// Token: 0x060005DD RID: 1501 RVA: 0x00026C39 File Offset: 0x00024E39
	[ServerRpc(RunLocally = true)]
	private void SoundServerEffect()
	{
		this.RpcWriter___Server_SoundServerEffect_2166136261();
		this.RpcLogic___SoundServerEffect_2166136261();
	}

	// Token: 0x060005DE RID: 1502 RVA: 0x00026C47 File Offset: 0x00024E47
	[ObserversRpc(RunLocally = true, ExcludeOwner = true)]
	private void SoundObserversEffect()
	{
		this.RpcWriter___Observers_SoundObserversEffect_2166136261();
		this.RpcLogic___SoundObserversEffect_2166136261();
	}

	// Token: 0x060005DF RID: 1503 RVA: 0x00026C55 File Offset: 0x00024E55
	[ServerRpc(RunLocally = true)]
	private void SoundServerEffect2()
	{
		this.RpcWriter___Server_SoundServerEffect2_2166136261();
		this.RpcLogic___SoundServerEffect2_2166136261();
	}

	// Token: 0x060005E0 RID: 1504 RVA: 0x00026C63 File Offset: 0x00024E63
	[ObserversRpc(RunLocally = true, ExcludeOwner = true)]
	private void SoundObserversEffect2()
	{
		this.RpcWriter___Observers_SoundObserversEffect2_2166136261();
		this.RpcLogic___SoundObserversEffect2_2166136261();
	}

	// Token: 0x060005E1 RID: 1505 RVA: 0x00026C71 File Offset: 0x00024E71
	private void SpawnProjectile(Vector3 position, Vector3 direction, float passedTime)
	{
		PredictedProjectile predictedProjectile = global::UnityEngine.Object.Instantiate<PredictedProjectile>(this._projectile, position, Quaternion.identity);
		predictedProjectile.Initialize(direction, this.launchForce, passedTime, this.rootObject, base.gameObject);
		predictedProjectile.isOwner = base.IsOwner;
		predictedProjectile.weapon = this;
	}

	// Token: 0x060005E2 RID: 1506 RVA: 0x00026CB0 File Offset: 0x00024EB0
	[ServerRpc(RunLocally = true)]
	private void ServerFire(Vector3 position, Vector3 direction, uint tick)
	{
		this.RpcWriter___Server_ServerFire_2754081237(position, direction, tick);
		this.RpcLogic___ServerFire_2754081237(position, direction, tick);
	}

	// Token: 0x060005E3 RID: 1507 RVA: 0x00026CE4 File Offset: 0x00024EE4
	[ObserversRpc(ExcludeOwner = true)]
	private void ObserversFire(Vector3 position, Vector3 direction, uint tick)
	{
		this.RpcWriter___Observers_ObserversFire_2754081237(position, direction, tick);
	}

	// Token: 0x060005E4 RID: 1508 RVA: 0x00026D04 File Offset: 0x00024F04
	[ObserversRpc(RunLocally = true, ExcludeOwner = true)]
	private void ShootObserversEffect()
	{
		this.RpcWriter___Observers_ShootObserversEffect_2166136261();
		this.RpcLogic___ShootObserversEffect_2166136261();
	}

	// Token: 0x060005E5 RID: 1509 RVA: 0x00026D1D File Offset: 0x00024F1D
	private void LocalSound(int index)
	{
		if (index == 0)
		{
			this.audio.PlayOneShot(this.headHitClip);
		}
	}

	// Token: 0x060005E6 RID: 1510 RVA: 0x00026D33 File Offset: 0x00024F33
	[ServerRpc]
	private void AudioPlay()
	{
		this.RpcWriter___Server_AudioPlay_2166136261();
	}

	// Token: 0x060005E7 RID: 1511 RVA: 0x00026D3B File Offset: 0x00024F3B
	[ServerRpc]
	private void AudioStop()
	{
		this.RpcWriter___Server_AudioStop_2166136261();
	}

	// Token: 0x060005E8 RID: 1512 RVA: 0x00026D43 File Offset: 0x00024F43
	[ServerRpc(RunLocally = true)]
	private void SpawnVFXServer(int index, Vector3 hitPoint, Vector3 hitNormal, string surface, Transform parent)
	{
		this.RpcWriter___Server_SpawnVFXServer_606331033(index, hitPoint, hitNormal, surface, parent);
		this.RpcLogic___SpawnVFXServer_606331033(index, hitPoint, hitNormal, surface, parent);
	}

	// Token: 0x060005E9 RID: 1513 RVA: 0x00026D7C File Offset: 0x00024F7C
	[ObserversRpc(RunLocally = true, ExcludeOwner = true)]
	private void SpawnVFX(int index, Vector3 hitPoint, Vector3 hitNormal, string surface, Transform parent)
	{
		this.RpcWriter___Observers_SpawnVFX_606331033(index, hitPoint, hitNormal, surface, parent);
		this.RpcLogic___SpawnVFX_606331033(index, hitPoint, hitNormal, surface, parent);
	}

	// Token: 0x060005EB RID: 1515 RVA: 0x00026DF0 File Offset: 0x00024FF0
	public override void NetworkInitialize___Early()
	{
		if (this.NetworkInitializeEarly_BeamGun_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeEarly_BeamGun_Assembly-CSharp.dll = true;
		base.NetworkInitialize___Early();
		base.RegisterServerRpc(15U, new ServerRpcDelegate(this.RpcReader___Server_ServerFX_3848837105));
		base.RegisterObserversRpc(16U, new ClientRpcDelegate(this.RpcReader___Observers_ObserversFX_3848837105));
		base.RegisterServerRpc(17U, new ServerRpcDelegate(this.RpcReader___Server_KillServer_1722911636));
		base.RegisterTargetRpc(18U, new ClientRpcDelegate(this.RpcReader___Target_KillObserver_123853379));
		base.RegisterObserversRpc(19U, new ClientRpcDelegate(this.RpcReader___Observers_HitFeeback_1722911636));
		base.RegisterServerRpc(20U, new ServerRpcDelegate(this.RpcReader___Server_GiveDamage_324487999));
		base.RegisterServerRpc(21U, new ServerRpcDelegate(this.RpcReader___Server_SupressionServer_3068987916));
		base.RegisterObserversRpc(22U, new ClientRpcDelegate(this.RpcReader___Observers_SuppressionTarget_3068987916));
		base.RegisterServerRpc(23U, new ServerRpcDelegate(this.RpcReader___Server_ShootServerEffect_2166136261));
		base.RegisterObserversRpc(24U, new ClientRpcDelegate(this.RpcReader___Observers_ShootObserversEffect2_2166136261));
		base.RegisterServerRpc(25U, new ServerRpcDelegate(this.RpcReader___Server_ShootServerEffect2_2166136261));
		base.RegisterServerRpc(26U, new ServerRpcDelegate(this.RpcReader___Server_SpawnBulletTrailServer_4276783012));
		base.RegisterObserversRpc(27U, new ClientRpcDelegate(this.RpcReader___Observers_SpawnBulletTrail_4276783012));
		base.RegisterServerRpc(28U, new ServerRpcDelegate(this.RpcReader___Server_BeamEffectServer_3316948804));
		base.RegisterObserversRpc(29U, new ClientRpcDelegate(this.RpcReader___Observers_BeamEffectObservers_3316948804));
		base.RegisterServerRpc(30U, new ServerRpcDelegate(this.RpcReader___Server_SoundServerEffect_2166136261));
		base.RegisterObserversRpc(31U, new ClientRpcDelegate(this.RpcReader___Observers_SoundObserversEffect_2166136261));
		base.RegisterServerRpc(32U, new ServerRpcDelegate(this.RpcReader___Server_SoundServerEffect2_2166136261));
		base.RegisterObserversRpc(33U, new ClientRpcDelegate(this.RpcReader___Observers_SoundObserversEffect2_2166136261));
		base.RegisterServerRpc(34U, new ServerRpcDelegate(this.RpcReader___Server_ServerFire_2754081237));
		base.RegisterObserversRpc(35U, new ClientRpcDelegate(this.RpcReader___Observers_ObserversFire_2754081237));
		base.RegisterObserversRpc(36U, new ClientRpcDelegate(this.RpcReader___Observers_ShootObserversEffect_2166136261));
		base.RegisterServerRpc(37U, new ServerRpcDelegate(this.RpcReader___Server_AudioPlay_2166136261));
		base.RegisterServerRpc(38U, new ServerRpcDelegate(this.RpcReader___Server_AudioStop_2166136261));
		base.RegisterServerRpc(39U, new ServerRpcDelegate(this.RpcReader___Server_SpawnVFXServer_606331033));
		base.RegisterObserversRpc(40U, new ClientRpcDelegate(this.RpcReader___Observers_SpawnVFX_606331033));
	}

	// Token: 0x060005EC RID: 1516 RVA: 0x0002706A File Offset: 0x0002526A
	public override void NetworkInitialize__Late()
	{
		if (this.NetworkInitializeLate_BeamGun_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeLate_BeamGun_Assembly-CSharp.dll = true;
		base.NetworkInitialize__Late();
	}

	// Token: 0x060005ED RID: 1517 RVA: 0x00027083 File Offset: 0x00025283
	public override void NetworkInitializeIfDisabled()
	{
		this.NetworkInitialize___Early();
		this.NetworkInitialize__Late();
	}

	// Token: 0x060005EE RID: 1518 RVA: 0x00027094 File Offset: 0x00025294
	private void RpcWriter___Server_ServerFX_3848837105(Vector3 position, Quaternion rotation)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteVector3(position);
		writer.WriteQuaternion(rotation, AutoPackType.Packed);
		base.SendServerRpc(15U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x060005EF RID: 1519 RVA: 0x000271A7 File Offset: 0x000253A7
	public void RpcLogic___ServerFX_3848837105(Vector3 position, Quaternion rotation)
	{
		this.ObserversFX(position, rotation);
	}

	// Token: 0x060005F0 RID: 1520 RVA: 0x000271B4 File Offset: 0x000253B4
	private void RpcReader___Server_ServerFX_3848837105(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		Vector3 vector = PooledReader0.ReadVector3();
		Quaternion quaternion = PooledReader0.ReadQuaternion(AutoPackType.Packed);
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___ServerFX_3848837105(vector, quaternion);
	}

	// Token: 0x060005F1 RID: 1521 RVA: 0x0002721C File Offset: 0x0002541C
	private void RpcWriter___Observers_ObserversFX_3848837105(Vector3 position, Quaternion rotation)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteVector3(position);
		writer.WriteQuaternion(rotation, AutoPackType.Packed);
		base.SendObserversRpc(16U, writer, channel, DataOrderType.Default, false, false, true);
		writer.Store();
	}

	// Token: 0x060005F2 RID: 1522 RVA: 0x000272E4 File Offset: 0x000254E4
	private void RpcLogic___ObserversFX_3848837105(Vector3 position, Quaternion rotation)
	{
		global::UnityEngine.Object.Instantiate<GameObject>(this.bloodSplatter, position, rotation);
	}

	// Token: 0x060005F3 RID: 1523 RVA: 0x000272F4 File Offset: 0x000254F4
	private void RpcReader___Observers_ObserversFX_3848837105(PooledReader PooledReader0, Channel channel)
	{
		Vector3 vector = PooledReader0.ReadVector3();
		Quaternion quaternion = PooledReader0.ReadQuaternion(AutoPackType.Packed);
		if (!base.IsClient)
		{
			return;
		}
		if (base.IsHost)
		{
			return;
		}
		this.RpcLogic___ObserversFX_3848837105(vector, quaternion);
	}

	// Token: 0x060005F4 RID: 1524 RVA: 0x00027348 File Offset: 0x00025548
	private void RpcWriter___Server_KillServer_1722911636(PlayerHealth enemyHealth)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.Write___PlayerHealthFishNet.Serializing.Generated(enemyHealth);
		base.SendServerRpc(17U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x060005F5 RID: 1525 RVA: 0x0002744C File Offset: 0x0002564C
	private void RpcLogic___KillServer_1722911636(PlayerHealth enemyHealth)
	{
		enemyHealth.sync___set_value_isShot(true, true);
		enemyHealth.sync___set_value_health(-8f, true);
		GameManager.Instance.PlayerDied(enemyHealth.SyncAccessor_playerValues.SyncAccessor_playerClient.PlayerId);
		if (this.rootObject != null)
		{
			enemyHealth.sync___set_value_killer(this.rootObject.transform, true);
		}
		this.KillObserver(enemyHealth.SyncAccessor_playerValues.SyncAccessor_playerClient.transform.GetComponent<NetworkObject>().Owner, enemyHealth.SyncAccessor_playerValues.SyncAccessor_playerClient, enemyHealth);
	}

	// Token: 0x060005F6 RID: 1526 RVA: 0x000274D4 File Offset: 0x000256D4
	private void RpcReader___Server_KillServer_1722911636(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		PlayerHealth playerHealth = global::FishNet.Serializing.Generated.GeneratedReaders___Internal.Read___PlayerHealthFishNet.Serializing.Generateds(PooledReader0);
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		this.RpcLogic___KillServer_1722911636(playerHealth);
	}

	// Token: 0x060005F7 RID: 1527 RVA: 0x00027518 File Offset: 0x00025718
	private void RpcWriter___Target_KillObserver_123853379(NetworkConnection conn, ClientInstance client, PlayerHealth enemyHealth)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.Write___ClientInstanceFishNet.Serializing.Generated(client);
		writer.Write___PlayerHealthFishNet.Serializing.Generated(enemyHealth);
		base.SendTargetRpc(18U, writer, channel, DataOrderType.Default, conn, false, true);
		writer.Store();
	}

	// Token: 0x060005F8 RID: 1528 RVA: 0x000275DA File Offset: 0x000257DA
	private void RpcLogic___KillObserver_123853379(NetworkConnection conn, ClientInstance client, PlayerHealth enemyHealth)
	{
		enemyHealth.shouldDropWeapon = true;
		enemyHealth.isDeadFromTargetRpc = true;
		if (this.rootObject != null)
		{
			GameObject.Find("Main Camera").GetComponent<KillCam>().enemy = this.rootObject.transform;
		}
	}

	// Token: 0x060005F9 RID: 1529 RVA: 0x00027618 File Offset: 0x00025818
	private void RpcReader___Target_KillObserver_123853379(PooledReader PooledReader0, Channel channel)
	{
		ClientInstance clientInstance = global::FishNet.Serializing.Generated.GeneratedReaders___Internal.Read___ClientInstanceFishNet.Serializing.Generateds(PooledReader0);
		PlayerHealth playerHealth = global::FishNet.Serializing.Generated.GeneratedReaders___Internal.Read___PlayerHealthFishNet.Serializing.Generateds(PooledReader0);
		if (!base.IsClient)
		{
			return;
		}
		this.RpcLogic___KillObserver_123853379(base.LocalConnection, clientInstance, playerHealth);
	}

	// Token: 0x060005FA RID: 1530 RVA: 0x00027660 File Offset: 0x00025860
	private void RpcWriter___Observers_HitFeeback_1722911636(PlayerHealth enemyHealth)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.Write___PlayerHealthFishNet.Serializing.Generated(enemyHealth);
		base.SendObserversRpc(19U, writer, channel, DataOrderType.Default, false, false, false);
		writer.Store();
	}

	// Token: 0x060005FB RID: 1531 RVA: 0x00027716 File Offset: 0x00025916
	private void RpcLogic___HitFeeback_1722911636(PlayerHealth enemyHealth)
	{
		enemyHealth.HitFeeback();
	}

	// Token: 0x060005FC RID: 1532 RVA: 0x00027720 File Offset: 0x00025920
	private void RpcReader___Observers_HitFeeback_1722911636(PooledReader PooledReader0, Channel channel)
	{
		PlayerHealth playerHealth = global::FishNet.Serializing.Generated.GeneratedReaders___Internal.Read___PlayerHealthFishNet.Serializing.Generateds(PooledReader0);
		if (!base.IsClient)
		{
			return;
		}
		this.RpcLogic___HitFeeback_1722911636(playerHealth);
	}

	// Token: 0x060005FD RID: 1533 RVA: 0x00027754 File Offset: 0x00025954
	private void RpcWriter___Server_GiveDamage_324487999(float damageToGive, PlayerHealth enemyHealth, string name)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteSingle(damageToGive, AutoPackType.Unpacked);
		writer.Write___PlayerHealthFishNet.Serializing.Generated(enemyHealth);
		writer.WriteString(name);
		base.SendServerRpc(20U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x060005FE RID: 1534 RVA: 0x00027874 File Offset: 0x00025A74
	private void RpcLogic___GiveDamage_324487999(float damageToGive, PlayerHealth enemyHealth, string name)
	{
		enemyHealth.sync___set_value_killer(this.rootObject.transform, true);
		enemyHealth.sync___set_value_health(enemyHealth.SyncAccessor_health - damageToGive, true);
		enemyHealth.KillCam();
		this.HitFeeback(enemyHealth);
		enemyHealth.Dismemberment(name);
	}

	// Token: 0x060005FF RID: 1535 RVA: 0x000278AC File Offset: 0x00025AAC
	private void RpcReader___Server_GiveDamage_324487999(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		float num = PooledReader0.ReadSingle(AutoPackType.Unpacked);
		PlayerHealth playerHealth = global::FishNet.Serializing.Generated.GeneratedReaders___Internal.Read___PlayerHealthFishNet.Serializing.Generateds(PooledReader0);
		string text = PooledReader0.ReadString();
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___GiveDamage_324487999(num, playerHealth, text);
	}

	// Token: 0x06000600 RID: 1536 RVA: 0x00027924 File Offset: 0x00025B24
	private void RpcWriter___Server_SupressionServer_3068987916(Transform supp)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteTransform(supp);
		base.SendServerRpc(21U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000601 RID: 1537 RVA: 0x00027A25 File Offset: 0x00025C25
	private void RpcLogic___SupressionServer_3068987916(Transform supp)
	{
		this.SuppressionTarget(supp);
	}

	// Token: 0x06000602 RID: 1538 RVA: 0x00027A30 File Offset: 0x00025C30
	private void RpcReader___Server_SupressionServer_3068987916(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		Transform transform = PooledReader0.ReadTransform();
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		this.RpcLogic___SupressionServer_3068987916(transform);
	}

	// Token: 0x06000603 RID: 1539 RVA: 0x00027A74 File Offset: 0x00025C74
	private void RpcWriter___Observers_SuppressionTarget_3068987916(Transform supp)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteTransform(supp);
		base.SendObserversRpc(22U, writer, channel, DataOrderType.Default, false, false, false);
		writer.Store();
	}

	// Token: 0x06000604 RID: 1540 RVA: 0x00027B2A File Offset: 0x00025D2A
	private void RpcLogic___SuppressionTarget_3068987916(Transform supp)
	{
		supp.GetComponent<Suppression>().SuppressionTrigger();
	}

	// Token: 0x06000605 RID: 1541 RVA: 0x00027B38 File Offset: 0x00025D38
	private void RpcReader___Observers_SuppressionTarget_3068987916(PooledReader PooledReader0, Channel channel)
	{
		Transform transform = PooledReader0.ReadTransform();
		if (!base.IsClient)
		{
			return;
		}
		this.RpcLogic___SuppressionTarget_3068987916(transform);
	}

	// Token: 0x06000606 RID: 1542 RVA: 0x00027B6C File Offset: 0x00025D6C
	private void RpcWriter___Server_ShootServerEffect_2166136261()
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendServerRpc(23U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000607 RID: 1543 RVA: 0x00027C60 File Offset: 0x00025E60
	private void RpcLogic___ShootServerEffect_2166136261()
	{
		this.ShootObserversEffect2();
	}

	// Token: 0x06000608 RID: 1544 RVA: 0x00027C68 File Offset: 0x00025E68
	private void RpcReader___Server_ShootServerEffect_2166136261(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___ShootServerEffect_2166136261();
	}

	// Token: 0x06000609 RID: 1545 RVA: 0x00027CA8 File Offset: 0x00025EA8
	private void RpcWriter___Observers_ShootObserversEffect2_2166136261()
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendObserversRpc(24U, writer, channel, DataOrderType.Default, false, false, true);
		writer.Store();
	}

	// Token: 0x0600060A RID: 1546 RVA: 0x00027D54 File Offset: 0x00025F54
	private void RpcLogic___ShootObserversEffect2_2166136261()
	{
		if (Settings.Instance.reduceVFX)
		{
			return;
		}
		GameObject gameObject = global::UnityEngine.Object.Instantiate<GameObject>(this.muzzleFlash, this.muzzleFlashPoint.position, this.shootPoint.rotation, this.muzzleFlashPoint);
		this.tempBeam = gameObject.GetComponent<ParticleSystem>();
		this.tempBeam.Play();
	}

	// Token: 0x0600060B RID: 1547 RVA: 0x00027DB0 File Offset: 0x00025FB0
	private void RpcReader___Observers_ShootObserversEffect2_2166136261(PooledReader PooledReader0, Channel channel)
	{
		if (!base.IsClient)
		{
			return;
		}
		if (base.IsHost)
		{
			return;
		}
		this.RpcLogic___ShootObserversEffect2_2166136261();
	}

	// Token: 0x0600060C RID: 1548 RVA: 0x00027DDC File Offset: 0x00025FDC
	private void RpcWriter___Server_ShootServerEffect2_2166136261()
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendServerRpc(25U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x0600060D RID: 1549 RVA: 0x00027ED0 File Offset: 0x000260D0
	private void RpcLogic___ShootServerEffect2_2166136261()
	{
		this.ShootObserversEffect();
	}

	// Token: 0x0600060E RID: 1550 RVA: 0x00027ED8 File Offset: 0x000260D8
	private void RpcReader___Server_ShootServerEffect2_2166136261(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___ShootServerEffect2_2166136261();
	}

	// Token: 0x0600060F RID: 1551 RVA: 0x00027F18 File Offset: 0x00026118
	private void RpcWriter___Server_SpawnBulletTrailServer_4276783012(Vector3 hitPoint)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteVector3(hitPoint);
		base.SendServerRpc(26U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000610 RID: 1552 RVA: 0x00028019 File Offset: 0x00026219
	private void RpcLogic___SpawnBulletTrailServer_4276783012(Vector3 hitPoint)
	{
		this.SpawnBulletTrail(hitPoint);
	}

	// Token: 0x06000611 RID: 1553 RVA: 0x00028024 File Offset: 0x00026224
	private void RpcReader___Server_SpawnBulletTrailServer_4276783012(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		Vector3 vector = PooledReader0.ReadVector3();
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___SpawnBulletTrailServer_4276783012(vector);
	}

	// Token: 0x06000612 RID: 1554 RVA: 0x00028074 File Offset: 0x00026274
	private void RpcWriter___Observers_SpawnBulletTrail_4276783012(Vector3 hitPoint)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteVector3(hitPoint);
		base.SendObserversRpc(27U, writer, channel, DataOrderType.Default, false, false, true);
		writer.Store();
	}

	// Token: 0x06000613 RID: 1555 RVA: 0x0002812C File Offset: 0x0002632C
	private void RpcLogic___SpawnBulletTrail_4276783012(Vector3 hitPoint)
	{
		GameObject gameObject = global::UnityEngine.Object.Instantiate<GameObject>(this.bulletTrailLocal.gameObject, this.shootPoint.position, Quaternion.identity);
		LineRenderer component = gameObject.GetComponent<LineRenderer>();
		component.SetPosition(0, this.shootPoint.position);
		component.SetPosition(1, hitPoint);
		global::UnityEngine.Object.Destroy(gameObject, 1.2f);
	}

	// Token: 0x06000614 RID: 1556 RVA: 0x00028184 File Offset: 0x00026384
	private void RpcReader___Observers_SpawnBulletTrail_4276783012(PooledReader PooledReader0, Channel channel)
	{
		Vector3 vector = PooledReader0.ReadVector3();
		if (!base.IsClient)
		{
			return;
		}
		if (base.IsHost)
		{
			return;
		}
		this.RpcLogic___SpawnBulletTrail_4276783012(vector);
	}

	// Token: 0x06000615 RID: 1557 RVA: 0x000281C0 File Offset: 0x000263C0
	private void RpcWriter___Server_BeamEffectServer_3316948804(int i)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteInt32(i, AutoPackType.Packed);
		base.SendServerRpc(28U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000616 RID: 1558 RVA: 0x000282C6 File Offset: 0x000264C6
	private void RpcLogic___BeamEffectServer_3316948804(int i)
	{
		this.BeamEffectObservers(i);
	}

	// Token: 0x06000617 RID: 1559 RVA: 0x000282D0 File Offset: 0x000264D0
	private void RpcReader___Server_BeamEffectServer_3316948804(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		int num = PooledReader0.ReadInt32(AutoPackType.Packed);
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___BeamEffectServer_3316948804(num);
	}

	// Token: 0x06000618 RID: 1560 RVA: 0x00028324 File Offset: 0x00026524
	private void RpcWriter___Observers_BeamEffectObservers_3316948804(int i)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteInt32(i, AutoPackType.Packed);
		base.SendObserversRpc(29U, writer, channel, DataOrderType.Default, false, false, true);
		writer.Store();
	}

	// Token: 0x06000619 RID: 1561 RVA: 0x000283DF File Offset: 0x000265DF
	private void RpcLogic___BeamEffectObservers_3316948804(int i)
	{
		if (i == 0)
		{
			this.tempBeam.transform.SetParent(null);
		}
		if (i == 1)
		{
			this.tempBeam.gameObject.SetActive(false);
		}
	}

	// Token: 0x0600061A RID: 1562 RVA: 0x0002840C File Offset: 0x0002660C
	private void RpcReader___Observers_BeamEffectObservers_3316948804(PooledReader PooledReader0, Channel channel)
	{
		int num = PooledReader0.ReadInt32(AutoPackType.Packed);
		if (!base.IsClient)
		{
			return;
		}
		if (base.IsHost)
		{
			return;
		}
		this.RpcLogic___BeamEffectObservers_3316948804(num);
	}

	// Token: 0x0600061B RID: 1563 RVA: 0x0002844C File Offset: 0x0002664C
	private void RpcWriter___Server_SoundServerEffect_2166136261()
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendServerRpc(30U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x0600061C RID: 1564 RVA: 0x00028540 File Offset: 0x00026740
	private void RpcLogic___SoundServerEffect_2166136261()
	{
		this.SoundObserversEffect();
	}

	// Token: 0x0600061D RID: 1565 RVA: 0x00028548 File Offset: 0x00026748
	private void RpcReader___Server_SoundServerEffect_2166136261(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___SoundServerEffect_2166136261();
	}

	// Token: 0x0600061E RID: 1566 RVA: 0x00028588 File Offset: 0x00026788
	private void RpcWriter___Observers_SoundObserversEffect_2166136261()
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendObserversRpc(31U, writer, channel, DataOrderType.Default, false, false, true);
		writer.Store();
	}

	// Token: 0x0600061F RID: 1567 RVA: 0x00028631 File Offset: 0x00026831
	private void RpcLogic___SoundObserversEffect_2166136261()
	{
		this.newSource.PlayOneShot(this.fireClip);
	}

	// Token: 0x06000620 RID: 1568 RVA: 0x00028644 File Offset: 0x00026844
	private void RpcReader___Observers_SoundObserversEffect_2166136261(PooledReader PooledReader0, Channel channel)
	{
		if (!base.IsClient)
		{
			return;
		}
		if (base.IsHost)
		{
			return;
		}
		this.RpcLogic___SoundObserversEffect_2166136261();
	}

	// Token: 0x06000621 RID: 1569 RVA: 0x00028670 File Offset: 0x00026870
	private void RpcWriter___Server_SoundServerEffect2_2166136261()
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendServerRpc(32U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000622 RID: 1570 RVA: 0x00028764 File Offset: 0x00026964
	private void RpcLogic___SoundServerEffect2_2166136261()
	{
		this.SoundObserversEffect2();
	}

	// Token: 0x06000623 RID: 1571 RVA: 0x0002876C File Offset: 0x0002696C
	private void RpcReader___Server_SoundServerEffect2_2166136261(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___SoundServerEffect2_2166136261();
	}

	// Token: 0x06000624 RID: 1572 RVA: 0x000287AC File Offset: 0x000269AC
	private void RpcWriter___Observers_SoundObserversEffect2_2166136261()
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendObserversRpc(33U, writer, channel, DataOrderType.Default, false, false, true);
		writer.Store();
	}

	// Token: 0x06000625 RID: 1573 RVA: 0x00028855 File Offset: 0x00026A55
	private void RpcLogic___SoundObserversEffect2_2166136261()
	{
		this.newSource.PlayOneShot(this.smallFireClip);
	}

	// Token: 0x06000626 RID: 1574 RVA: 0x00028868 File Offset: 0x00026A68
	private void RpcReader___Observers_SoundObserversEffect2_2166136261(PooledReader PooledReader0, Channel channel)
	{
		if (!base.IsClient)
		{
			return;
		}
		if (base.IsHost)
		{
			return;
		}
		this.RpcLogic___SoundObserversEffect2_2166136261();
	}

	// Token: 0x06000627 RID: 1575 RVA: 0x00028894 File Offset: 0x00026A94
	private void RpcWriter___Server_ServerFire_2754081237(Vector3 position, Vector3 direction, uint tick)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteVector3(position);
		writer.WriteVector3(direction);
		writer.WriteUInt32(tick, AutoPackType.Packed);
		base.SendServerRpc(34U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000628 RID: 1576 RVA: 0x000289B4 File Offset: 0x00026BB4
	private void RpcLogic___ServerFire_2754081237(Vector3 position, Vector3 direction, uint tick)
	{
		if (this.needsAmmo)
		{
			base.sync___set_value_currentAmmo(base.SyncAccessor_currentAmmo - 1, true);
		}
		float num = (float)base.TimeManager.TimePassed(tick, false);
		num = Mathf.Min(0.15f, num);
		this.ShootObserversEffect();
		if (!base.IsServer)
		{
			this.SpawnProjectile(position, direction, num);
		}
		this.ObserversFire(position, direction, tick);
	}

	// Token: 0x06000629 RID: 1577 RVA: 0x00028A14 File Offset: 0x00026C14
	private void RpcReader___Server_ServerFire_2754081237(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		Vector3 vector = PooledReader0.ReadVector3();
		Vector3 vector2 = PooledReader0.ReadVector3();
		uint num = PooledReader0.ReadUInt32(AutoPackType.Packed);
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___ServerFire_2754081237(vector, vector2, num);
	}

	// Token: 0x0600062A RID: 1578 RVA: 0x00028A8C File Offset: 0x00026C8C
	private void RpcWriter___Observers_ObserversFire_2754081237(Vector3 position, Vector3 direction, uint tick)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteVector3(position);
		writer.WriteVector3(direction);
		writer.WriteUInt32(tick, AutoPackType.Packed);
		base.SendObserversRpc(35U, writer, channel, DataOrderType.Default, false, false, true);
		writer.Store();
	}

	// Token: 0x0600062B RID: 1579 RVA: 0x00028B64 File Offset: 0x00026D64
	private void RpcLogic___ObserversFire_2754081237(Vector3 position, Vector3 direction, uint tick)
	{
		float num = (float)base.TimeManager.TimePassed(tick, false);
		num = Mathf.Min(0.3f, num);
		this.SpawnProjectile(position, direction, num);
	}

	// Token: 0x0600062C RID: 1580 RVA: 0x00028B98 File Offset: 0x00026D98
	private void RpcReader___Observers_ObserversFire_2754081237(PooledReader PooledReader0, Channel channel)
	{
		Vector3 vector = PooledReader0.ReadVector3();
		Vector3 vector2 = PooledReader0.ReadVector3();
		uint num = PooledReader0.ReadUInt32(AutoPackType.Packed);
		if (!base.IsClient)
		{
			return;
		}
		this.RpcLogic___ObserversFire_2754081237(vector, vector2, num);
	}

	// Token: 0x0600062D RID: 1581 RVA: 0x00028BF0 File Offset: 0x00026DF0
	private void RpcWriter___Observers_ShootObserversEffect_2166136261()
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendObserversRpc(36U, writer, channel, DataOrderType.Default, false, false, true);
		writer.Store();
	}

	// Token: 0x0600062E RID: 1582 RVA: 0x00028C9C File Offset: 0x00026E9C
	private void RpcLogic___ShootObserversEffect_2166136261()
	{
		if (Settings.Instance.reduceVFX)
		{
			return;
		}
		this.audio.PlayOneShot(this.smallFireClip);
		GameObject gameObject = global::UnityEngine.Object.Instantiate<GameObject>(this.muzzleFlash2, this.muzzleFlashPoint.position, this.shootPoint.rotation);
		gameObject.GetComponent<ParticleSystem>().Play();
		foreach (Transform transform in gameObject.transform.GetComponentsInChildren<Transform>(true))
		{
			if (transform.GetComponent<Light>() == null && transform.tag != "vfx")
			{
				transform.gameObject.layer = 8;
			}
			if (transform.GetComponent<Light>() != null)
			{
				transform.GetComponent<Light>().intensity = this.lightIntensity;
			}
		}
		ParticleSystem[] componentsInChildren2 = gameObject.GetComponentsInChildren<ParticleSystem>();
		for (int i = 0; i < componentsInChildren2.Length; i++)
		{
			componentsInChildren2[i].Play();
		}
	}

	// Token: 0x0600062F RID: 1583 RVA: 0x00028D80 File Offset: 0x00026F80
	private void RpcReader___Observers_ShootObserversEffect_2166136261(PooledReader PooledReader0, Channel channel)
	{
		if (!base.IsClient)
		{
			return;
		}
		if (base.IsHost)
		{
			return;
		}
		this.RpcLogic___ShootObserversEffect_2166136261();
	}

	// Token: 0x06000630 RID: 1584 RVA: 0x00028DAC File Offset: 0x00026FAC
	private void RpcWriter___Server_AudioPlay_2166136261()
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendServerRpc(37U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000631 RID: 1585 RVA: 0x00028EA0 File Offset: 0x000270A0
	private void RpcLogic___AudioPlay_2166136261()
	{
		this.networkAudioSource.Play(0);
	}

	// Token: 0x06000632 RID: 1586 RVA: 0x00028EB0 File Offset: 0x000270B0
	private void RpcReader___Server_AudioPlay_2166136261(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		this.RpcLogic___AudioPlay_2166136261();
	}

	// Token: 0x06000633 RID: 1587 RVA: 0x00028EE4 File Offset: 0x000270E4
	private void RpcWriter___Server_AudioStop_2166136261()
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendServerRpc(38U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000634 RID: 1588 RVA: 0x00028FD8 File Offset: 0x000271D8
	private void RpcLogic___AudioStop_2166136261()
	{
		this.networkAudioSource.Stop();
	}

	// Token: 0x06000635 RID: 1589 RVA: 0x00028FE8 File Offset: 0x000271E8
	private void RpcReader___Server_AudioStop_2166136261(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		this.RpcLogic___AudioStop_2166136261();
	}

	// Token: 0x06000636 RID: 1590 RVA: 0x0002901C File Offset: 0x0002721C
	private void RpcWriter___Server_SpawnVFXServer_606331033(int index, Vector3 hitPoint, Vector3 hitNormal, string surface, Transform parent)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteInt32(index, AutoPackType.Packed);
		writer.WriteVector3(hitPoint);
		writer.WriteVector3(hitNormal);
		writer.WriteString(surface);
		writer.WriteTransform(parent);
		base.SendServerRpc(39U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000637 RID: 1591 RVA: 0x00029156 File Offset: 0x00027356
	private void RpcLogic___SpawnVFXServer_606331033(int index, Vector3 hitPoint, Vector3 hitNormal, string surface, Transform parent)
	{
		this.SpawnVFX(index, hitPoint, hitNormal, surface, parent);
	}

	// Token: 0x06000638 RID: 1592 RVA: 0x00029168 File Offset: 0x00027368
	private void RpcReader___Server_SpawnVFXServer_606331033(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		int num = PooledReader0.ReadInt32(AutoPackType.Packed);
		Vector3 vector = PooledReader0.ReadVector3();
		Vector3 vector2 = PooledReader0.ReadVector3();
		string text = PooledReader0.ReadString();
		Transform transform = PooledReader0.ReadTransform();
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___SpawnVFXServer_606331033(num, vector, vector2, text, transform);
	}

	// Token: 0x06000639 RID: 1593 RVA: 0x00029200 File Offset: 0x00027400
	private void RpcWriter___Observers_SpawnVFX_606331033(int index, Vector3 hitPoint, Vector3 hitNormal, string surface, Transform parent)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteInt32(index, AutoPackType.Packed);
		writer.WriteVector3(hitPoint);
		writer.WriteVector3(hitNormal);
		writer.WriteString(surface);
		writer.WriteTransform(parent);
		base.SendObserversRpc(40U, writer, channel, DataOrderType.Default, false, false, true);
		writer.Store();
	}

	// Token: 0x0600063A RID: 1594 RVA: 0x000292F0 File Offset: 0x000274F0
	private void RpcLogic___SpawnVFX_606331033(int index, Vector3 hitPoint, Vector3 hitNormal, string surface, Transform parent)
	{
		if (Settings.Instance.reduceVFX)
		{
			return;
		}
		if (this.genericImpact)
		{
			global::UnityEngine.Object.Instantiate<GameObject>(this.genericImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
		}
		uint num = <PrivateImplementationDetails>.ComputeStringHash(surface);
		if (num <= 1825421690U)
		{
			if (num <= 1378315797U)
			{
				if (num <= 464173256U)
				{
					if (num != 456440475U)
					{
						if (num == 464173256U)
						{
							if (surface == "Footsteps/Concrete/Dalles")
							{
								if (index == 0)
								{
									global::UnityEngine.Object.Instantiate<GameObject>(this.concreteHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
								}
								if (index == 1)
								{
									global::UnityEngine.Object.Instantiate<GameObject>(this.bulletHole, hitPoint, Quaternion.LookRotation(hitNormal), parent);
									goto IL_06E3;
								}
								goto IL_06E3;
							}
						}
					}
					else if (surface == "Footsteps/Sand")
					{
						if (index == 0)
						{
							global::UnityEngine.Object.Instantiate<GameObject>(this.sandHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
						}
						if (index == 1)
						{
							global::UnityEngine.Object.Instantiate<GameObject>(this.sandHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
							goto IL_06E3;
						}
						goto IL_06E3;
					}
				}
				else if (num != 913360285U)
				{
					if (num != 977466297U)
					{
						if (num == 1378315797U)
						{
							if (surface == "Footsteps/Moquette")
							{
								if (index == 0)
								{
									global::UnityEngine.Object.Instantiate<GameObject>(this.softbodyHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
								}
								if (index == 1)
								{
									global::UnityEngine.Object.Instantiate<GameObject>(this.softbodyHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
									goto IL_06E3;
								}
								goto IL_06E3;
							}
						}
					}
					else if (surface == "Footsteps/Concrete/Solide")
					{
						if (index == 0)
						{
							global::UnityEngine.Object.Instantiate<GameObject>(this.concreteHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
						}
						if (index == 1)
						{
							global::UnityEngine.Object.Instantiate<GameObject>(this.bulletHole, hitPoint, Quaternion.LookRotation(hitNormal), parent);
							goto IL_06E3;
						}
						goto IL_06E3;
					}
				}
				else if (surface == "Grenade")
				{
					goto IL_06E3;
				}
			}
			else if (num <= 1430892386U)
			{
				if (num != 1429664136U)
				{
					if (num == 1430892386U)
					{
						if (surface == "Hat")
						{
							goto IL_06E3;
						}
					}
				}
				else if (surface == "Footsteps/Water")
				{
					if (index == 0)
					{
						global::UnityEngine.Object.Instantiate<GameObject>(this.waterHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
					}
					if (index == 1)
					{
						global::UnityEngine.Object.Instantiate<GameObject>(this.waterHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
						goto IL_06E3;
					}
					goto IL_06E3;
				}
			}
			else if (num != 1610969363U)
			{
				if (num != 1624496828U)
				{
					if (num == 1825421690U)
					{
						if (surface == "Footsteps/Wood/Creux")
						{
							if (index == 0)
							{
								global::UnityEngine.Object.Instantiate<GameObject>(this.woodHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
							}
							if (index == 1)
							{
								global::UnityEngine.Object.Instantiate<GameObject>(this.woodHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
								goto IL_06E3;
							}
							goto IL_06E3;
						}
					}
				}
				else if (surface == "Footsteps/Concrete/Default")
				{
					if (index == 0)
					{
						global::UnityEngine.Object.Instantiate<GameObject>(this.concreteHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
					}
					if (index == 1)
					{
						global::UnityEngine.Object.Instantiate<GameObject>(this.bulletHole, hitPoint, Quaternion.LookRotation(hitNormal), parent);
						goto IL_06E3;
					}
					goto IL_06E3;
				}
			}
			else if (surface == "Footsteps/Concrete/PleinAir")
			{
				if (index == 0)
				{
					global::UnityEngine.Object.Instantiate<GameObject>(this.concreteHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
				}
				if (index == 1)
				{
					global::UnityEngine.Object.Instantiate<GameObject>(this.bulletHole, hitPoint, Quaternion.LookRotation(hitNormal), parent);
					goto IL_06E3;
				}
				goto IL_06E3;
			}
		}
		else if (num <= 2833365437U)
		{
			if (num <= 2180110808U)
			{
				if (num != 1954265137U)
				{
					if (num == 2180110808U)
					{
						if (surface == "Footsteps/Dirt")
						{
							if (index == 0)
							{
								global::UnityEngine.Object.Instantiate<GameObject>(this.dirtHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
							}
							if (index == 1)
							{
								global::UnityEngine.Object.Instantiate<GameObject>(this.dirtHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
								goto IL_06E3;
							}
							goto IL_06E3;
						}
					}
				}
				else if (surface == "NoSound")
				{
					goto IL_06E3;
				}
			}
			else if (num != 2251139421U)
			{
				if (num != 2400559108U)
				{
					if (num == 2833365437U)
					{
						if (surface == "Footsteps/Metal/Pipe2")
						{
							if (index == 0)
							{
								global::UnityEngine.Object.Instantiate<GameObject>(this.metalHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
							}
							if (index == 1)
							{
								global::UnityEngine.Object.Instantiate<GameObject>(this.metalHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
								goto IL_06E3;
							}
							goto IL_06E3;
						}
					}
				}
				else if (surface == "Footsteps/Matelas")
				{
					if (index == 0)
					{
						global::UnityEngine.Object.Instantiate<GameObject>(this.softbodyHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
					}
					if (index == 1)
					{
						global::UnityEngine.Object.Instantiate<GameObject>(this.softbodyHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
						goto IL_06E3;
					}
					goto IL_06E3;
				}
			}
			else if (surface == "Footsteps/Metal/Pipe")
			{
				if (index == 0)
				{
					global::UnityEngine.Object.Instantiate<GameObject>(this.tauleHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
				}
				if (index == 1)
				{
					global::UnityEngine.Object.Instantiate<GameObject>(this.tauleHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
					goto IL_06E3;
				}
				goto IL_06E3;
			}
		}
		else if (num <= 3455850406U)
		{
			if (num != 3075599786U)
			{
				if (num == 3455850406U)
				{
					if (surface == "Footsteps/Graviers")
					{
						if (index == 0)
						{
							global::UnityEngine.Object.Instantiate<GameObject>(this.concreteHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
						}
						if (index == 1)
						{
							global::UnityEngine.Object.Instantiate<GameObject>(this.bulletHole, hitPoint, Quaternion.LookRotation(hitNormal), parent);
							goto IL_06E3;
						}
						goto IL_06E3;
					}
				}
			}
			else if (surface == "Footsteps/Wood/Sec")
			{
				if (index == 0)
				{
					global::UnityEngine.Object.Instantiate<GameObject>(this.woodHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
				}
				if (index == 1)
				{
					global::UnityEngine.Object.Instantiate<GameObject>(this.woodHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
					goto IL_06E3;
				}
				goto IL_06E3;
			}
		}
		else if (num != 3492154005U)
		{
			if (num != 3807801418U)
			{
				if (num == 3848897750U)
				{
					if (surface == "Mine")
					{
						goto IL_06E3;
					}
				}
			}
			else if (surface == "Footsteps/Metal/Grille")
			{
				if (index == 0)
				{
					global::UnityEngine.Object.Instantiate<GameObject>(this.tauleHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
				}
				if (index == 1)
				{
					global::UnityEngine.Object.Instantiate<GameObject>(this.tauleHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
					goto IL_06E3;
				}
				goto IL_06E3;
			}
		}
		else if (surface == "Footsteps/Grass")
		{
			if (index == 0)
			{
				global::UnityEngine.Object.Instantiate<GameObject>(this.dirtHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
			}
			if (index == 1)
			{
				global::UnityEngine.Object.Instantiate<GameObject>(this.dirtHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
				goto IL_06E3;
			}
			goto IL_06E3;
		}
		if (index == 0)
		{
			global::UnityEngine.Object.Instantiate<GameObject>(this.concreteHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
		}
		if (index == 1)
		{
			global::UnityEngine.Object.Instantiate<GameObject>(this.bulletHole, hitPoint, Quaternion.LookRotation(hitNormal), parent);
		}
		IL_06E3:
		if (index == 2)
		{
			global::UnityEngine.Object.Instantiate<GameObject>(this.bodyImpact, hitPoint, Quaternion.identity, parent);
		}
	}

	// Token: 0x0600063B RID: 1595 RVA: 0x000299F8 File Offset: 0x00027BF8
	private void RpcReader___Observers_SpawnVFX_606331033(PooledReader PooledReader0, Channel channel)
	{
		int num = PooledReader0.ReadInt32(AutoPackType.Packed);
		Vector3 vector = PooledReader0.ReadVector3();
		Vector3 vector2 = PooledReader0.ReadVector3();
		string text = PooledReader0.ReadString();
		Transform transform = PooledReader0.ReadTransform();
		if (!base.IsClient)
		{
			return;
		}
		if (base.IsHost)
		{
			return;
		}
		this.RpcLogic___SpawnVFX_606331033(num, vector, vector2, text, transform);
	}

	// Token: 0x0600063C RID: 1596 RVA: 0x00029A7C File Offset: 0x00027C7C
	public override void Awake()
	{
		this.NetworkInitialize___Early();
		base.Awake();
		this.NetworkInitialize__Late();
	}

	// Token: 0x0600063D RID: 1597 RVA: 0x000023D6 File Offset: 0x000005D6
	public override void Awake___UserLogic()
	{
	}

	// Token: 0x040005FD RID: 1533
	[Header("Weapon Specials")]
	[SerializeField]
	private float launchForce = 12f;

	// Token: 0x040005FE RID: 1534
	[SerializeField]
	private float playerKnockback = 2f;

	// Token: 0x040005FF RID: 1535
	[SerializeField]
	private PredictedProjectile _projectile;

	// Token: 0x04000600 RID: 1536
	[SerializeField]
	private GameObject muzzleFlash2;

	// Token: 0x04000601 RID: 1537
	[SerializeField]
	private AudioClip smallFireClip;

	// Token: 0x04000602 RID: 1538
	private const float MAX_PASSED_TIME = 0.3f;

	// Token: 0x04000603 RID: 1539
	private float accumulatedPower;

	// Token: 0x04000604 RID: 1540
	[SerializeField]
	private AudioSource newSource;

	// Token: 0x04000605 RID: 1541
	[SerializeField]
	private float maxChargeTime;

	// Token: 0x04000606 RID: 1542
	[SerializeField]
	private bool hasIntermediateStates;

	// Token: 0x04000607 RID: 1543
	[SerializeField]
	private float radius = 0.1f;

	// Token: 0x04000608 RID: 1544
	private float fireTimer;

	// Token: 0x04000609 RID: 1545
	private bool isClicked2;

	// Token: 0x0400060A RID: 1546
	private bool isClicked3 = true;

	// Token: 0x0400060B RID: 1547
	private bool isClicked4;

	// Token: 0x0400060C RID: 1548
	private NetworkAudioSource networkAudioSource;

	// Token: 0x0400060D RID: 1549
	private bool hitOK;

	// Token: 0x0400060E RID: 1550
	private ParticleSystem tempBeam;

	// Token: 0x0400060F RID: 1551
	private bool NetworkInitializeEarly_BeamGun_Assembly-CSharp.dll;

	// Token: 0x04000610 RID: 1552
	private bool NetworkInitializeLate_BeamGun_Assembly-CSharp.dll;
}

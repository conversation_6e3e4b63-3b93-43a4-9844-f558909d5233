﻿using System;
using UnityEngine;

namespace UnityStandardAssets.Effects
{
	// Token: 0x02000192 RID: 402
	public class ExtinguishableParticleSystem : MonoBehaviour
	{
		// Token: 0x06001029 RID: 4137 RVA: 0x00069244 File Offset: 0x00067444
		private void Start()
		{
			this.m_Systems = base.GetComponentsInChildren<ParticleSystem>();
		}

		// Token: 0x0600102A RID: 4138 RVA: 0x00069254 File Offset: 0x00067454
		public void Extinguish()
		{
			ParticleSystem[] systems = this.m_Systems;
			for (int i = 0; i < systems.Length; i++)
			{
				systems[i].emission.enabled = false;
			}
		}

		// Token: 0x04000EF3 RID: 3827
		public float multiplier = 1f;

		// Token: 0x04000EF4 RID: 3828
		private ParticleSystem[] m_Systems;
	}
}

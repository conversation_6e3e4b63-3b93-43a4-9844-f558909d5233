﻿using System;
using UnityEngine;

// Token: 0x020000B1 RID: 177
public class MenuAnimation : MonoBehaviour
{
	// Token: 0x06000A33 RID: 2611 RVA: 0x000023D6 File Offset: 0x000005D6
	private void Start()
	{
	}

	// Token: 0x06000A34 RID: 2612 RVA: 0x0004B092 File Offset: 0x00049292
	private void Update()
	{
		this.cube.Rotate(this.axis * this.speed * Time.deltaTime);
	}

	// Token: 0x04000906 RID: 2310
	[SerializeField]
	private Transform cube;

	// Token: 0x04000907 RID: 2311
	[SerializeField]
	private float speed = -16f;

	// Token: 0x04000908 RID: 2312
	[SerializeField]
	private Vector3 axis;
}

﻿using System;
using UnityEngine;
using UnityEngine.SceneManagement;
using UnityEngine.UI;

// Token: 0x020000C7 RID: 199
public class Crosshair : MonoBehaviour
{
	// Token: 0x06000B30 RID: 2864 RVA: 0x0004F1C4 File Offset: 0x0004D3C4
	private void Awake()
	{
		if (Crosshair.Instance == null)
		{
			Crosshair.Instance = this;
		}
		this.image = base.GetComponent<Image>();
	}

	// Token: 0x06000B31 RID: 2865 RVA: 0x0004F1E5 File Offset: 0x0004D3E5
	private void Start()
	{
		this.settings = Settings.Instance;
	}

	// Token: 0x06000B32 RID: 2866 RVA: 0x0004F1F4 File Offset: 0x0004D3F4
	private void Update()
	{
		if (SceneManager.GetActiveScene().name == "MainMenu")
		{
			this.image.sprite = this.invisibleCrosshair;
			this.scopeImage.sprite = this.invisibleCrosshair;
		}
		base.transform.localScale = (this.settings.disableCrosshair ? Vector3.zero : Vector3.one);
		if (!this.player)
		{
			return;
		}
		Sprite sprite = ((!this.player.playerPickupScript.SyncAccessor_hasObjectInHand && !this.player.playerPickupScript.SyncAccessor_hasObjectInLeftHand) ? this.nogunCrosshair : ((this.canScopeAim && this.player.playerPickupScript.SyncAccessor_hasObjectInHand && this.aimCrosshair != null && this.player.isAiming && this.player.zoomFOV < this.settings.fovValue - this.aimCrosshairLimit) ? this.invisibleCrosshair : ((this.player.isSprinting || !this.player.safeGrounded) ? this.sprintCrosshair : this.standCrosshair)));
		Sprite sprite2 = ((this.player.playerPickupScript.SyncAccessor_hasObjectInHand && this.player.gameObject.activeSelf && this.canScopeAim && this.aimCrosshair != null && this.player.isAiming && (this.player.playerCamera.fieldOfView < this.settings.fovValue - this.aimCrosshairLimit || this.instantAimLens)) ? this.aimCrosshair : this.invisibleCrosshair);
		if (this.settings.enableFixedCrosshair)
		{
			sprite = ((!this.player.playerPickupScript.SyncAccessor_hasObjectInHand && !this.player.playerPickupScript.SyncAccessor_hasObjectInLeftHand) ? this.FixedCrosshair : ((this.canScopeAim && this.player.playerPickupScript.SyncAccessor_hasObjectInHand && this.aimCrosshair != null && this.player.isAiming && this.player.zoomFOV < this.settings.fovValue - this.aimCrosshairLimit) ? this.invisibleCrosshair : ((this.player.isSprinting || !this.player.safeGrounded) ? this.FixedCrosshair : this.FixedCrosshair)));
		}
		if (this.image.sprite != sprite)
		{
			this.image.sprite = sprite;
		}
		if (this.scopeImage.sprite != sprite2)
		{
			this.scopeImage.sprite = sprite2;
		}
		if (this.hatObj)
		{
			this.hatObj.SetActive(true);
		}
		if (Camera.main)
		{
			this.image.sprite = this.invisibleCrosshair;
			this.scopeImage.sprite = this.invisibleCrosshair;
		}
	}

	// Token: 0x04000964 RID: 2404
	public static Crosshair Instance;

	// Token: 0x04000965 RID: 2405
	public Image image;

	// Token: 0x04000966 RID: 2406
	public Image scopeImage;

	// Token: 0x04000967 RID: 2407
	public Sprite invisibleCrosshair;

	// Token: 0x04000968 RID: 2408
	public Sprite nogunCrosshair;

	// Token: 0x04000969 RID: 2409
	public Sprite standCrosshair;

	// Token: 0x0400096A RID: 2410
	public Sprite sprintCrosshair;

	// Token: 0x0400096B RID: 2411
	public Sprite aimCrosshair;

	// Token: 0x0400096C RID: 2412
	public Sprite FixedCrosshair;

	// Token: 0x0400096D RID: 2413
	public bool canScopeAim;

	// Token: 0x0400096E RID: 2414
	public AudioClip headshotHitClip;

	// Token: 0x0400096F RID: 2415
	[SerializeField]
	private float aimCrosshairLimit = 18f;

	// Token: 0x04000970 RID: 2416
	public FirstPersonController player;

	// Token: 0x04000971 RID: 2417
	private Settings settings;

	// Token: 0x04000972 RID: 2418
	public GameObject hatObj;

	// Token: 0x04000973 RID: 2419
	public bool instantAimLens;
}

﻿using System;
using System.Collections;
using FishNet.Object;
using UnityEngine;

// Token: 0x0200000D RID: 13
public class BulletTrailAutoDestroy : NetworkBehaviour
{
	// Token: 0x06000059 RID: 89 RVA: 0x00003FEE File Offset: 0x000021EE
	private void Start()
	{
		base.StartCoroutine(this.DespawnTrail());
	}

	// Token: 0x0600005A RID: 90 RVA: 0x00003FFD File Offset: 0x000021FD
	private IEnumerator DespawnTrail()
	{
		yield return new WaitForSeconds(0.4f);
		base.Despawn(base.gameObject, null);
		yield break;
	}

	// Token: 0x0600005C RID: 92 RVA: 0x00004014 File Offset: 0x00002214
	public virtual void NetworkInitialize___Early()
	{
		if (this.NetworkInitializeEarly_BulletTrailAutoDestroy_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeEarly_BulletTrailAutoDestroy_Assembly-CSharp.dll = true;
	}

	// Token: 0x0600005D RID: 93 RVA: 0x00004027 File Offset: 0x00002227
	public virtual void NetworkInitialize__Late()
	{
		if (this.NetworkInitializeLate_BulletTrailAutoDestroy_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeLate_BulletTrailAutoDestroy_Assembly-CSharp.dll = true;
	}

	// Token: 0x0600005E RID: 94 RVA: 0x0000403A File Offset: 0x0000223A
	public override void NetworkInitializeIfDisabled()
	{
		this.NetworkInitialize___Early();
		this.NetworkInitialize__Late();
	}

	// Token: 0x0600005F RID: 95 RVA: 0x0000403A File Offset: 0x0000223A
	public virtual void Awake()
	{
		this.NetworkInitialize___Early();
		this.NetworkInitialize__Late();
	}

	// Token: 0x06000060 RID: 96 RVA: 0x000023D6 File Offset: 0x000005D6
	public virtual void Awake___UserLogic()
	{
	}

	// Token: 0x0400006F RID: 111
	private bool NetworkInitializeEarly_BulletTrailAutoDestroy_Assembly-CSharp.dll;

	// Token: 0x04000070 RID: 112
	private bool NetworkInitializeLate_BulletTrailAutoDestroy_Assembly-CSharp.dll;
}

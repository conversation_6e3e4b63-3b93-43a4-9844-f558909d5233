﻿using System;
using System.Collections.Generic;
using UnityEngine;

namespace UnityStandardAssets.Effects
{
	// Token: 0x02000197 RID: 407
	public class WaterHoseParticles : MonoBehaviour
	{
		// Token: 0x06001036 RID: 4150 RVA: 0x000695B2 File Offset: 0x000677B2
		private void Start()
		{
			this.m_ParticleSystem = base.GetComponent<ParticleSystem>();
		}

		// Token: 0x06001037 RID: 4151 RVA: 0x000695C0 File Offset: 0x000677C0
		private void OnParticleCollision(GameObject other)
		{
			int collisionEvents = this.m_ParticleSystem.GetCollisionEvents(other, this.m_CollisionEvents);
			for (int i = 0; i < collisionEvents; i++)
			{
				if (Time.time > WaterHoseParticles.lastSoundTime + 0.2f)
				{
					WaterHoseParticles.lastSoundTime = Time.time;
				}
				Rigidbody component = this.m_CollisionEvents[i].colliderComponent.GetComponent<Rigidbody>();
				if (component != null)
				{
					Vector3 velocity = this.m_CollisionEvents[i].velocity;
					component.AddForce(velocity * this.force, ForceMode.Impulse);
				}
				other.BroadcastMessage("Extinguish", SendMessageOptions.DontRequireReceiver);
			}
		}

		// Token: 0x04000F00 RID: 3840
		public static float lastSoundTime;

		// Token: 0x04000F01 RID: 3841
		public float force = 1f;

		// Token: 0x04000F02 RID: 3842
		private List<ParticleCollisionEvent> m_CollisionEvents = new List<ParticleCollisionEvent>();

		// Token: 0x04000F03 RID: 3843
		private ParticleSystem m_ParticleSystem;
	}
}

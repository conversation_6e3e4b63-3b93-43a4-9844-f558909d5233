﻿using System;
using System.Collections;
using System.Collections.Generic;
using DG.Tweening;
using UnityEngine;
using UnityEngine.UI;

// Token: 0x020000AD RID: 173
public class ShrapnelBallistic : MonoBehaviour
{
	// Token: 0x060009DB RID: 2523 RVA: 0x00048814 File Offset: 0x00046A14
	private void Awake()
	{
		this.audio = base.GetComponent<AudioSource>();
	}

	// Token: 0x060009DC RID: 2524 RVA: 0x00048822 File Offset: 0x00046A22
	public void Initialize(GameObject rootObject, GameObject gun, float passedTime)
	{
		this._rootObject = rootObject;
		this._passedTime = passedTime;
		this._gun = gun;
		base.StartCoroutine(this.Action());
	}

	// Token: 0x060009DD RID: 2525 RVA: 0x00048846 File Offset: 0x00046A46
	private IEnumerator Action()
	{
		yield return new WaitForSeconds(0.1f);
		this.Explode();
		yield break;
	}

	// Token: 0x060009DE RID: 2526 RVA: 0x00048858 File Offset: 0x00046A58
	private void Explode()
	{
		this.audio.PlayOneShot(this.explosionClip);
		Collider[] array = new Collider[1];
		List<Collider> list = new List<Collider>();
		if (!this.fragGrenade)
		{
			array = Physics.OverlapSphere(base.transform.position, this.explosionRadius, this.bodyLayer);
		}
		else
		{
			int num = 0;
			while ((float)num < this.numberOfRays)
			{
				Vector3 normalized = global::UnityEngine.Random.onUnitSphere.normalized;
				RaycastHit raycastHit;
				if (Physics.Raycast(base.transform.position, normalized, out raycastHit, this.explosionRadius, this.bodyLayer))
				{
					list.Add(raycastHit.transform.GetComponent<Collider>());
					this.headshot = raycastHit.transform.gameObject.name == "Head_Col" || raycastHit.transform.gameObject.name == "Neck_1_Col";
				}
				this.RenderObject(normalized, (raycastHit.collider == null) ? this.explosionRadius : Vector3.Distance(base.transform.position, raycastHit.point));
				num++;
			}
			array = new Collider[list.Count];
			for (int i = 0; i < list.Count; i++)
			{
				array[i] = list[i];
			}
		}
		if (array.Length != 0)
		{
			this.ph2 = new PlayerHealth[array.Length];
			for (int j = 0; j < array.Length; j++)
			{
				if (array[j].transform.tag == "ShatterableGlass" && array[j].gameObject.GetComponent<ShatterableGlass>() != null)
				{
					array[j].gameObject.GetComponent<ShatterableGlass>().Shatter3D(array[j].transform.position, array[j].transform.position - base.transform.position);
				}
				if (array[j].GetComponentInParent<PlayerHealth>() != null)
				{
					this.ph2[j] = array[j].GetComponentInParent<PlayerHealth>();
					if (this.bloodSplatter && this.makeBlood)
					{
						global::UnityEngine.Object.Instantiate<GameObject>(this.bloodSplatter, array[j].transform.position, Quaternion.Euler(0f, (float)global::UnityEngine.Random.Range(0, 360), 0f));
						this.makeBlood = false;
					}
				}
			}
			for (int k = 0; k < this.ph2.Length; k++)
			{
				if (this.ph2[k] != null && this.isOwner)
				{
					if (this.ph2[k].transform.gameObject == this._rootObject && !this.touched)
					{
						if (this.ph2[k].SyncAccessor_health - (this.headshot ? (this.damage * 2f) : this.damage) <= 0f)
						{
							this.ph2[k].ChangeKilledState(true);
							this.ph2[k].suicide = true;
							this.IncreaseSuicidesAmount();
							this.ph2[k].Explode(false, true, this.ph2[k].gameObject.name, this.ph2[k].transform.position - base.transform.position, this.ragdollEjectForce, base.transform.position);
						}
						this.HitMarker(this.headshot);
						this.ph2[k].RemoveHealth(this.headshot ? (this.damage * 2f) : this.damage);
						this.ph2[k].SetKiller(this._rootObject.transform);
						this.touched = true;
					}
					else if (this.ph2[k].transform.gameObject != this._rootObject && !this.touched2)
					{
						if (this.ph2[k].SyncAccessor_health - (this.headshot ? (this.damage * 2f) : this.damage) <= 0f)
						{
							this.ph2[k].ChangeKilledState(true);
							this.KillShockWave();
							this.SendKillLog(this.ph2[k]);
							this.ph2[k].Explode(false, true, this.ph2[k].gameObject.name, this.ph2[k].transform.position - base.transform.position, this.ragdollEjectForce, base.transform.position);
						}
						this.HitMarker(this.headshot);
						this.ph2[k].RemoveHealth(this.headshot ? (this.damage * 2f) : this.damage);
						this.ph2[k].SetKiller(this._rootObject.transform);
						this.touched2 = true;
					}
				}
			}
		}
		GameObject[] array2 = GameObject.FindGameObjectsWithTag("Player");
		for (int l = 0; l < array2.Length; l++)
		{
			float num2 = Vector3.Distance(base.transform.position, array2[l].transform.position);
			array2[l].GetComponent<PlayerHealth>().LocalScreenshake(this.duration, Mathf.Lerp(this.maxStrength, this.minStrength, Mathf.Clamp(num2 / this.maxDistance, 0f, 1f)), this.vibrato, this.randomness, this.shakeEase);
		}
		global::UnityEngine.Object.Destroy(base.gameObject, 3f);
		base.enabled = false;
		if (!this.fragGrenade)
		{
			global::UnityEngine.Object.Instantiate<GameObject>(this.explosionVfx, base.transform.position, Quaternion.identity);
		}
		global::UnityEngine.Object.Instantiate<GameObject>(this.explosionDecal, base.transform.position, Quaternion.identity);
		this.audio.Play();
	}

	// Token: 0x060009DF RID: 2527 RVA: 0x00048E58 File Offset: 0x00047058
	private void SendKillLog(PlayerHealth enemyHealth)
	{
		if (this.sendKillLog)
		{
			return;
		}
		this.sendKillLog = true;
		PauseManager.Instance.WriteLog(string.Concat(new string[]
		{
			"<b><color=#",
			PauseManager.Instance.selfNameLogColor,
			">",
			enemyHealth.SyncAccessor_playerValues.SyncAccessor_playerClient.PlayerName,
			"</color></b> was killed with a <b><color=white>",
			this.weaponName,
			"</color></b> by <b><color=#",
			PauseManager.Instance.enemyNameLogColor,
			">",
			ClientInstance.Instance.PlayerName,
			"</color></b>"
		}));
	}

	// Token: 0x060009E0 RID: 2528 RVA: 0x00048F00 File Offset: 0x00047100
	private void IncreaseSuicidesAmount()
	{
		if (this.suicide)
		{
			this.suicide = false;
			Settings.Instance.IncreaseSuicidesAmount();
		}
	}

	// Token: 0x060009E1 RID: 2529 RVA: 0x00048F1C File Offset: 0x0004711C
	public void KillShockWave()
	{
		if (!this.increaseKillAmount)
		{
			Settings.Instance.IncreaseKillsAmount();
			this.increaseKillAmount = true;
		}
		this._rootObject.GetComponent<FirstPersonController>().lensDistortion.intensity.value = this._rootObject.GetComponent<FirstPersonController>().killShockWaveStrength;
		this._rootObject.GetComponent<FirstPersonController>().colorGrading.saturation.value = -100f;
	}

	// Token: 0x060009E2 RID: 2530 RVA: 0x00048F8C File Offset: 0x0004718C
	private void HitMarker(bool head)
	{
		this.audio.PlayOneShot(this.hitSfx);
		if (head)
		{
			this.audio.PlayOneShot(Crosshair.Instance.headshotHitClip);
		}
		if (this.marker == null)
		{
			this.marker = global::UnityEngine.Object.Instantiate<GameObject>(this.hitMarker, Crosshair.Instance.transform.position, Quaternion.identity, PauseManager.Instance.transform);
			this.marker.transform.DOPunchScale(head ? new Vector3(2.5f, 2.5f, 2.5f) : Vector3.one, 0.3f, 8, 2f);
			if (head)
			{
				this.marker.GetComponent<Image>().color = Color.red;
			}
			global::UnityEngine.Object.Destroy(this.marker, 0.3f);
			return;
		}
		global::UnityEngine.Object.Destroy(this.marker);
		this.marker = global::UnityEngine.Object.Instantiate<GameObject>(this.hitMarker, Crosshair.Instance.transform.position, Quaternion.identity, PauseManager.Instance.transform);
		this.marker.transform.DOPunchScale(head ? new Vector3(2.5f, 2.5f, 2.5f) : Vector3.one, 0.3f, 8, 2f);
		if (head)
		{
			this.marker.GetComponent<Image>().color = Color.red;
		}
		global::UnityEngine.Object.Destroy(this.marker, 0.3f);
	}

	// Token: 0x060009E3 RID: 2531 RVA: 0x00049104 File Offset: 0x00047304
	private void RenderObject(Vector3 direction, float maxDistance)
	{
		GameObject gameObject = new GameObject("RaycastLine");
		LineRenderer lineRenderer = gameObject.AddComponent<LineRenderer>();
		LineFade lineFade = gameObject.AddComponent<LineFade>();
		lineFade.decreaseInSize = true;
		lineFade.decreaseInSizeSpeed = this.lineDecreaseSpeed;
		lineFade.color = Color.white;
		lineFade.speed = this.lineFadeSpeed;
		lineRenderer.startWidth = 0.05f;
		lineRenderer.endWidth = 0.03f;
		lineRenderer.material = new Material(Shader.Find("Sprites/Default"));
		lineRenderer.startColor = Color.white;
		lineRenderer.endColor = Color.white;
		Vector3 position = base.transform.position;
		lineRenderer.SetPosition(0, position);
		lineRenderer.SetPosition(1, position + direction * maxDistance);
		lineRenderer.startColor = Color.white;
		lineRenderer.endColor = Color.white;
		global::UnityEngine.Object.Destroy(gameObject, 2.5f);
	}

	// Token: 0x060009E4 RID: 2532 RVA: 0x000491DC File Offset: 0x000473DC
	private void OnCollisionEnter(Collision col)
	{
		if (this.hits > this.maxHits)
		{
			return;
		}
		if (this.explosionTimer < 0f)
		{
			return;
		}
		this.audio.PlayOneShot(this.hitClip, 0.56f);
		this.hits++;
	}

	// Token: 0x040008AB RID: 2219
	[SerializeField]
	private string weaponName;

	// Token: 0x040008AC RID: 2220
	[SerializeField]
	private float damage = 1f;

	// Token: 0x040008AD RID: 2221
	public bool isOwner;

	// Token: 0x040008AE RID: 2222
	private Vector3 impact = Vector3.zero;

	// Token: 0x040008AF RID: 2223
	private CharacterController character;

	// Token: 0x040008B0 RID: 2224
	[SerializeField]
	private float ragdollEjectForce;

	// Token: 0x040008B1 RID: 2225
	[SerializeField]
	private LayerMask playerLayer;

	// Token: 0x040008B2 RID: 2226
	[SerializeField]
	private LayerMask bodyLayer;

	// Token: 0x040008B3 RID: 2227
	[SerializeField]
	private GameObject hitVfx;

	// Token: 0x040008B4 RID: 2228
	[SerializeField]
	private GameObject explosionVfx;

	// Token: 0x040008B5 RID: 2229
	[SerializeField]
	private AudioClip hitClip;

	// Token: 0x040008B6 RID: 2230
	[SerializeField]
	private AudioClip explosionClip;

	// Token: 0x040008B7 RID: 2231
	[SerializeField]
	private AudioClip swooshClip;

	// Token: 0x040008B8 RID: 2232
	[SerializeField]
	private float rotateSpeed;

	// Token: 0x040008B9 RID: 2233
	[SerializeField]
	private Vector3 rotateAxis;

	// Token: 0x040008BA RID: 2234
	[SerializeField]
	private GameObject explosionDecal;

	// Token: 0x040008BB RID: 2235
	[SerializeField]
	private GameObject bloodSplatter;

	// Token: 0x040008BC RID: 2236
	[SerializeField]
	private float timeBeforeExplosion = 2f;

	// Token: 0x040008BD RID: 2237
	[SerializeField]
	private float explosionRadius = 3f;

	// Token: 0x040008BE RID: 2238
	[SerializeField]
	private float lineDecreaseSpeed = 10f;

	// Token: 0x040008BF RID: 2239
	[SerializeField]
	private float lineFadeSpeed = 10f;

	// Token: 0x040008C0 RID: 2240
	private float explosionTimer;

	// Token: 0x040008C1 RID: 2241
	[SerializeField]
	private float rebondForce;

	// Token: 0x040008C2 RID: 2242
	private bool isGrounded;

	// Token: 0x040008C3 RID: 2243
	[Header("Screenshake values")]
	[SerializeField]
	private float duration;

	// Token: 0x040008C4 RID: 2244
	[SerializeField]
	private float minStrength;

	// Token: 0x040008C5 RID: 2245
	[SerializeField]
	private float maxStrength;

	// Token: 0x040008C6 RID: 2246
	[SerializeField]
	private int vibrato;

	// Token: 0x040008C7 RID: 2247
	[SerializeField]
	private float randomness;

	// Token: 0x040008C8 RID: 2248
	[SerializeField]
	private Ease shakeEase;

	// Token: 0x040008C9 RID: 2249
	[SerializeField]
	private float maxDistance;

	// Token: 0x040008CA RID: 2250
	private bool touched;

	// Token: 0x040008CB RID: 2251
	private bool touched2;

	// Token: 0x040008CC RID: 2252
	private GameObject _gun;

	// Token: 0x040008CD RID: 2253
	private PlayerHealth[] ph2;

	// Token: 0x040008CE RID: 2254
	private float _passedTime;

	// Token: 0x040008CF RID: 2255
	private GameObject _rootObject;

	// Token: 0x040008D0 RID: 2256
	private AudioSource audio;

	// Token: 0x040008D1 RID: 2257
	[HideInInspector]
	public Weapon weapon;

	// Token: 0x040008D2 RID: 2258
	private Vector3 currentPosition;

	// Token: 0x040008D3 RID: 2259
	private Vector3 lastPosition;

	// Token: 0x040008D4 RID: 2260
	private Vector3 velocity;

	// Token: 0x040008D5 RID: 2261
	[SerializeField]
	private bool fragGrenade;

	// Token: 0x040008D6 RID: 2262
	[SerializeField]
	private float numberOfRays;

	// Token: 0x040008D7 RID: 2263
	private float safeTimer;

	// Token: 0x040008D8 RID: 2264
	private bool makeBlood = true;

	// Token: 0x040008D9 RID: 2265
	private bool hit;

	// Token: 0x040008DA RID: 2266
	private bool headshot;

	// Token: 0x040008DB RID: 2267
	private bool sendKillLog;

	// Token: 0x040008DC RID: 2268
	private bool suicide = true;

	// Token: 0x040008DD RID: 2269
	private bool increaseKillAmount;

	// Token: 0x040008DE RID: 2270
	[SerializeField]
	private GameObject hitMarker;

	// Token: 0x040008DF RID: 2271
	[SerializeField]
	private AudioClip hitSfx;

	// Token: 0x040008E0 RID: 2272
	private GameObject marker;

	// Token: 0x040008E1 RID: 2273
	private int maxHits = 5;

	// Token: 0x040008E2 RID: 2274
	private int hits;
}

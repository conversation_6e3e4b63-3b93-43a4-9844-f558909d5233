﻿using System;
using System.Collections.Generic;
using System.Linq;
using TMPro;
using UnityEngine;

// Token: 0x0200005D RID: 93
public class MatchPoitnsHUD : MonoBehaviour
{
	// Token: 0x06000415 RID: 1045 RVA: 0x0001D0B3 File Offset: 0x0001B2B3
	public void Start()
	{
		this.UpdateVisuals(-1, ScoreManager.Instance.GetRoundScoreDictionary());
	}

	// Token: 0x06000416 RID: 1046 RVA: 0x0001D0C8 File Offset: 0x0001B2C8
	public void UpdateVisuals(int winnerTeamId, Dictionary<int, int> roundScores)
	{
		if (ClientInstance.playerInstances.Count <= 1)
		{
			return;
		}
		List<int> list = new List<int>();
		foreach (int num in ScoreManager.Instance.TeamIdToPlayerIds.Keys)
		{
			foreach (int num2 in ScoreManager.Instance.TeamIdToPlayerIds[num])
			{
				if (ClientInstance.playerInstances.ContainsKey(num2))
				{
					list.Add(num);
					break;
				}
			}
		}
		int num3 = list.Count - 2;
		for (int i = 0; i < num3; i++)
		{
			this.secondaryPointObjects[i].gameObject.SetActive(true);
		}
		int num4 = this.secondaryPointObjects.Length - num3;
		for (int j = this.secondaryPointObjects.Length - 1; j >= this.secondaryPointObjects.Length - num4; j--)
		{
			if (j >= 0)
			{
				this.secondaryPointObjects[j].gameObject.SetActive(false);
			}
		}
		TMP_Text[] array = this.pointsTexts;
		for (int k = 0; k < array.Length; k++)
		{
			array[k].text = "";
		}
		Material[] materials = this.primaryPointMesh.materials;
		for (int l = 0; l < ScoreManager.Instance.TeamIdToPlayerIds.Count; l++)
		{
			int num5 = ScoreManager.Instance.TeamIdToPlayerIds.Keys.ElementAt(l);
			int valueOrDefault = roundScores.GetValueOrDefault(num5, 0);
			this.UpdateVisuals(l, valueOrDefault, num5 == winnerTeamId, materials);
		}
		this.primaryPointMesh.materials = materials;
	}

	// Token: 0x06000417 RID: 1047 RVA: 0x0001D29C File Offset: 0x0001B49C
	public void UpdateVisuals(int teamNumber, int roundScore, bool isWinner, Material[] primaryPointMeshMaterials)
	{
		Material material = ((roundScore > 0) ? this.activeMaterials[teamNumber] : this.inactiveMaterial);
		if (teamNumber < 2)
		{
			int num = ((teamNumber == 0) ? 1 : 3);
			primaryPointMeshMaterials[num] = material;
		}
		else
		{
			int num2 = teamNumber - 2;
			Material[] materials = this.secondaryPointObjects[num2].materials;
			materials[1] = material;
			this.secondaryPointObjects[num2].materials = materials;
		}
		if ((roundScore == 2 && !isWinner) || roundScore > 2)
		{
			this.pointsTexts[teamNumber].text = roundScore.ToString();
		}
		if (isWinner)
		{
			primaryPointMeshMaterials[2] = material;
		}
	}

	// Token: 0x04000467 RID: 1127
	[SerializeField]
	private Material inactiveMaterial;

	// Token: 0x04000468 RID: 1128
	[SerializeField]
	private Material[] activeMaterials;

	// Token: 0x04000469 RID: 1129
	[SerializeField]
	private MeshRenderer primaryPointMesh;

	// Token: 0x0400046A RID: 1130
	[SerializeField]
	private MeshRenderer[] secondaryPointObjects;

	// Token: 0x0400046B RID: 1131
	[SerializeField]
	private TMP_Text[] pointsTexts;
}

﻿using System;
using System.Collections.Generic;
using Goodgulf.Graphics;
using TMPro;
using UnityEngine;

// Token: 0x020000E0 RID: 224
public class MatchLogsOffline : MonoBehaviour
{
	// Token: 0x170000B1 RID: 177
	// (get) Token: 0x06000C09 RID: 3081 RVA: 0x00053DF7 File Offset: 0x00051FF7
	// (set) Token: 0x06000C0A RID: 3082 RVA: 0x00053DFE File Offset: 0x00051FFE
	public static MatchLogsOffline Instance { get; private set; }

	// Token: 0x06000C0B RID: 3083 RVA: 0x00053E06 File Offset: 0x00052006
	private void Awake()
	{
		if (MatchLogsOffline.Instance != null && MatchLogsOffline.Instance != this)
		{
			global::UnityEngine.Object.Destroy(this);
			return;
		}
		MatchLogsOffline.Instance = this;
		this.parentForChatLines = GameObject.Find("LogsPosition").transform;
	}

	// Token: 0x06000C0C RID: 3084 RVA: 0x00053E44 File Offset: 0x00052044
	public void WriteLog(string line)
	{
		int childCount = this.parentForChatLines.childCount;
		List<GameObject> list = new List<GameObject>();
		for (int i = 0; i < childCount; i++)
		{
			GameObject gameObject = this.parentForChatLines.GetChild(i).gameObject;
			if (gameObject.GetComponentInChildren<TMP_Text>() != null)
			{
				list.Add(gameObject);
			}
		}
		for (int j = list.Count - 1; j >= 0; j--)
		{
			GameObject gameObject2 = list[j];
			if (gameObject2.GetComponent<MatchChatLine>().deleteMe)
			{
				list.RemoveAt(j);
				global::UnityEngine.Object.Destroy(gameObject2);
			}
			else
			{
				RectTransform component = gameObject2.GetComponent<RectTransform>();
				component.anchoredPosition = new Vector2(component.anchoredPosition.x, component.anchoredPosition.y + 35f);
			}
		}
		GameObject gameObject3 = global::UnityEngine.Object.Instantiate<GameObject>(this.chatLinePrefab, this.parentForChatLines);
		gameObject3.GetComponentInChildren<TMP_Text>().text = line;
		MatchChatLine component2 = gameObject3.GetComponent<MatchChatLine>();
		component2.duration = this.duration;
		component2.fadeDuration = this.fadeDuration;
		component2.StartDuration();
		this.previousLine = line;
	}

	// Token: 0x06000C0D RID: 3085 RVA: 0x000023D6 File Offset: 0x000005D6
	private void OnDisable()
	{
	}

	// Token: 0x04000A1F RID: 2591
	private GameObject ChatBox;

	// Token: 0x04000A20 RID: 2592
	public GameObject chatLinePrefab;

	// Token: 0x04000A21 RID: 2593
	public Transform parentForChatLines;

	// Token: 0x04000A22 RID: 2594
	public float duration;

	// Token: 0x04000A23 RID: 2595
	public float fadeDuration;

	// Token: 0x04000A24 RID: 2596
	public ClientInstance localPlayer;

	// Token: 0x04000A25 RID: 2597
	private string previousLine;
}

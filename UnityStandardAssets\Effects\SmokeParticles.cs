﻿using System;
using UnityEngine;

namespace UnityStandardAssets.Effects
{
	// Token: 0x02000196 RID: 406
	public class SmokeParticles : MonoBehaviour
	{
		// Token: 0x06001034 RID: 4148 RVA: 0x00069585 File Offset: 0x00067785
		private void Start()
		{
			base.GetComponent<AudioSource>().clip = this.extinguishSounds[global::UnityEngine.Random.Range(0, this.extinguishSounds.Length)];
			base.GetComponent<AudioSource>().Play();
		}

		// Token: 0x04000EFF RID: 3839
		public AudioClip[] extinguishSounds;
	}
}

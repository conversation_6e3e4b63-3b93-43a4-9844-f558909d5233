﻿using System;
using UnityEngine;

// Token: 0x0200001C RID: 28
[RequireComponent(typeof(Camera))]
public class CameraEffect : MonoBehaviour
{
	// Token: 0x060000DE RID: 222 RVA: 0x00006509 File Offset: 0x00004709
	private void Awake()
	{
		this.material.SetFloat(CameraEffect.DamageID, 0f);
		this.material.SetFloat(CameraEffect.SuppressionID, 0f);
	}

	// Token: 0x060000DF RID: 223 RVA: 0x00006538 File Offset: 0x00004738
	private void OnRenderImage(RenderTexture source, RenderTexture destination)
	{
		if (this.material == null)
		{
			Graphics.Blit(source, destination);
			return;
		}
		this.material.SetFloat(CameraEffect.DamageID, this.material.GetFloat(CameraEffect.DamageID) - Time.deltaTime * this.speed);
		this.material.SetFloat(CameraEffect.DamageID, Mathf.Clamp(this.material.GetFloat(CameraEffect.DamageID), 0f, this.start * CameraEffect.Intensity));
		this.material.SetFloat(CameraEffect.SuppressionID, this.material.GetFloat(CameraEffect.SuppressionID) - Time.deltaTime * this.suppSpeed);
		this.material.SetFloat(CameraEffect.SuppressionID, Mathf.Clamp(this.material.GetFloat(CameraEffect.SuppressionID), 0f, this.suppStart * CameraEffect.Intensity));
		Graphics.Blit(source, destination, this.material);
	}

	// Token: 0x060000E0 RID: 224 RVA: 0x00006630 File Offset: 0x00004830
	[ContextMenu("TakeHit")]
	public void TakeHit()
	{
		CameraEffect.Intensity = Settings.Instance.damageIntensity;
		this.material.SetFloat(CameraEffect.DamageID, this.start * CameraEffect.Intensity);
		SoundManager.Instance.PlaySoundWithPitch(this.selfBodyHitClips[Mathf.RoundToInt((float)global::UnityEngine.Random.Range(0, this.selfBodyHitClips.Length))], global::UnityEngine.Random.Range(0.95f, 1.05f));
	}

	// Token: 0x060000E1 RID: 225 RVA: 0x0000669C File Offset: 0x0000489C
	[ContextMenu("supp")]
	public void TrigSup()
	{
		this.material.SetFloat(CameraEffect.SuppressionID, this.suppStart * CameraEffect.Intensity);
	}

	// Token: 0x040000C6 RID: 198
	private static readonly int DamageID = Shader.PropertyToID("_Damage");

	// Token: 0x040000C7 RID: 199
	private static readonly int SuppressionID = Shader.PropertyToID("_Suppression");

	// Token: 0x040000C8 RID: 200
	public static float Intensity = 1f;

	// Token: 0x040000C9 RID: 201
	public Material material;

	// Token: 0x040000CA RID: 202
	private float dmg;

	// Token: 0x040000CB RID: 203
	[SerializeField]
	private float speed = 5f;

	// Token: 0x040000CC RID: 204
	[SerializeField]
	private float suppSpeed = 10f;

	// Token: 0x040000CD RID: 205
	[SerializeField]
	private float start = 1f;

	// Token: 0x040000CE RID: 206
	[SerializeField]
	private float suppStart = 3f;

	// Token: 0x040000CF RID: 207
	[Space]
	[SerializeField]
	private AudioClip[] selfBodyHitClips;
}

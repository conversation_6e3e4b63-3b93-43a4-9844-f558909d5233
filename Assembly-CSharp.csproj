﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{9687CF22-C5F0-4C80-A6B7-BC4F47C5E736}</ProjectGuid>
    <OutputType>Library</OutputType>
    <RootNamespace>Assembly-CSharp</RootNamespace>
    <AssemblyName>Assembly-CSharp</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Assembly-CSharp-firstpass">
      <HintPath>E:\SteamLibrary\steamapps\common\STRAFTAT\STRAFTAT_Data\Managed\Assembly-CSharp-firstpass.dll</HintPath>
    </Reference>
    <Reference Include="ch.sycoforge.Decal">
      <HintPath>E:\SteamLibrary\steamapps\common\STRAFTAT\STRAFTAT_Data\Managed\ch.sycoforge.Decal.dll</HintPath>
    </Reference>
    <Reference Include="com.rlabrecque.steamworks.net">
      <HintPath>E:\SteamLibrary\steamapps\common\STRAFTAT\STRAFTAT_Data\Managed\com.rlabrecque.steamworks.net.dll</HintPath>
    </Reference>
    <Reference Include="ComputerysModdingUtilities">
      <HintPath>E:\SteamLibrary\steamapps\common\STRAFTAT\STRAFTAT_Data\Managed\ComputerysModdingUtilities.dll</HintPath>
    </Reference>
    <Reference Include="DOTween">
      <HintPath>E:\SteamLibrary\steamapps\common\STRAFTAT\STRAFTAT_Data\Managed\DOTween.dll</HintPath>
    </Reference>
    <Reference Include="FishNet.Runtime">
      <HintPath>E:\SteamLibrary\steamapps\common\STRAFTAT\STRAFTAT_Data\Managed\FishNet.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="Heathen.PhysKit">
      <HintPath>E:\SteamLibrary\steamapps\common\STRAFTAT\STRAFTAT_Data\Managed\Heathen.PhysKit.dll</HintPath>
    </Reference>
    <Reference Include="Heathen.Steamworks">
      <HintPath>E:\SteamLibrary\steamapps\common\STRAFTAT\STRAFTAT_Data\Managed\Heathen.Steamworks.dll</HintPath>
    </Reference>
    <Reference Include="Heathen.Steamworks.Examples">
      <HintPath>E:\SteamLibrary\steamapps\common\STRAFTAT\STRAFTAT_Data\Managed\Heathen.Steamworks.Examples.dll</HintPath>
    </Reference>
    <Reference Include="LambdaTheDev.NetworkAudioSync">
      <HintPath>E:\SteamLibrary\steamapps\common\STRAFTAT\STRAFTAT_Data\Managed\LambdaTheDev.NetworkAudioSync.dll</HintPath>
    </Reference>
    <Reference Include="netstandard">
      <HintPath>E:\SteamLibrary\steamapps\common\STRAFTAT\STRAFTAT_Data\Managed\netstandard.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json">
      <HintPath>E:\SteamLibrary\steamapps\common\STRAFTAT\STRAFTAT_Data\Managed\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="ProfanityFilter">
      <HintPath>E:\SteamLibrary\steamapps\common\STRAFTAT\STRAFTAT_Data\Managed\ProfanityFilter.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Animation.Rigging">
      <HintPath>E:\SteamLibrary\steamapps\common\STRAFTAT\STRAFTAT_Data\Managed\Unity.Animation.Rigging.dll</HintPath>
    </Reference>
    <Reference Include="Unity.InputSystem">
      <HintPath>E:\SteamLibrary\steamapps\common\STRAFTAT\STRAFTAT_Data\Managed\Unity.InputSystem.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Postprocessing.Runtime">
      <HintPath>E:\SteamLibrary\steamapps\common\STRAFTAT\STRAFTAT_Data\Managed\Unity.Postprocessing.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="Unity.TextMeshPro">
      <HintPath>E:\SteamLibrary\steamapps\common\STRAFTAT\STRAFTAT_Data\Managed\Unity.TextMeshPro.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AIModule">
      <HintPath>E:\SteamLibrary\steamapps\common\STRAFTAT\STRAFTAT_Data\Managed\UnityEngine.AIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AnimationModule">
      <HintPath>E:\SteamLibrary\steamapps\common\STRAFTAT\STRAFTAT_Data\Managed\UnityEngine.AnimationModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.AudioModule">
      <HintPath>E:\SteamLibrary\steamapps\common\STRAFTAT\STRAFTAT_Data\Managed\UnityEngine.AudioModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.CoreModule">
      <HintPath>E:\SteamLibrary\steamapps\common\STRAFTAT\STRAFTAT_Data\Managed\UnityEngine.CoreModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.IMGUIModule">
      <HintPath>E:\SteamLibrary\steamapps\common\STRAFTAT\STRAFTAT_Data\Managed\UnityEngine.IMGUIModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.InputLegacyModule">
      <HintPath>E:\SteamLibrary\steamapps\common\STRAFTAT\STRAFTAT_Data\Managed\UnityEngine.InputLegacyModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.JSONSerializeModule">
      <HintPath>E:\SteamLibrary\steamapps\common\STRAFTAT\STRAFTAT_Data\Managed\UnityEngine.JSONSerializeModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.ParticleSystemModule">
      <HintPath>E:\SteamLibrary\steamapps\common\STRAFTAT\STRAFTAT_Data\Managed\UnityEngine.ParticleSystemModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.Physics2DModule">
      <HintPath>E:\SteamLibrary\steamapps\common\STRAFTAT\STRAFTAT_Data\Managed\UnityEngine.Physics2DModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.PhysicsModule">
      <HintPath>E:\SteamLibrary\steamapps\common\STRAFTAT\STRAFTAT_Data\Managed\UnityEngine.PhysicsModule.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UI">
      <HintPath>E:\SteamLibrary\steamapps\common\STRAFTAT\STRAFTAT_Data\Managed\UnityEngine.UI.dll</HintPath>
    </Reference>
    <Reference Include="UnityEngine.UIModule">
      <HintPath>E:\SteamLibrary\steamapps\common\STRAFTAT\STRAFTAT_Data\Managed\UnityEngine.UIModule.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <AppDesigner Include="Properties\" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="AboubiCameraControl.cs" />
    <Compile Include="AboubiPreview.cs" />
    <Compile Include="AboubiPreviewLobby.cs" />
    <Compile Include="AdjustTimeScale.cs" />
    <Compile Include="AimStrafePivot.cs" />
    <Compile Include="AlphaYoyo.cs" />
    <Compile Include="AnimationSoundEvent.cs" />
    <Compile Include="AnimResetOnRound.cs" />
    <Compile Include="AudioPositionSlider.cs" />
    <Compile Include="BabbdiPostProcess.cs" />
    <Compile Include="BaseEventInteract.cs" />
    <Compile Include="BeamGun.cs" />
    <Compile Include="BettaOutButton.cs" />
    <Compile Include="BodyParts.cs" />
    <Compile Include="Bubble.cs" />
    <Compile Include="BulletSound.cs" />
    <Compile Include="BulletTrailAutoDestroy.cs" />
    <Compile Include="BumpBullet.cs" />
    <Compile Include="BumpGun.cs" />
    <Compile Include="ButtonSizeTween.cs" />
    <Compile Include="CableProceduralCurve.cs" />
    <Compile Include="CableProceduralMultipoint.cs" />
    <Compile Include="CableProceduralSimple.cs" />
    <Compile Include="CableProceduralStatic.cs" />
    <Compile Include="CameraEffect.cs" />
    <Compile Include="CameraShakeConstrains.cs" />
    <Compile Include="CamPointPosition.cs" />
    <Compile Include="CandidateFilter.cs" />
    <Compile Include="CFX_AutoDestructShuriken.cs" />
    <Compile Include="CFX_AutoStopLoopedEffect.cs" />
    <Compile Include="CFX_Demo_RandomDir.cs" />
    <Compile Include="CFX_Demo_RotateCamera.cs" />
    <Compile Include="CFX_Demo_Translate.cs" />
    <Compile Include="CFX_LightIntensityFade.cs" />
    <Compile Include="CFX_SpawnSystem.cs" />
    <Compile Include="ChangeOtherDropdownValue.cs" />
    <Compile Include="ChangeTextValue.cs" />
    <Compile Include="ChargeGun.cs" />
    <Compile Include="ChatBroadcast.cs" />
    <Compile Include="ch\sycoforge\Decal\Demo\AdvancedBulletHoles.cs" />
    <Compile Include="ch\sycoforge\Decal\Demo\BasicBulletHoles.cs" />
    <Compile Include="ch\sycoforge\Decal\Demo\BezierUtil.cs" />
    <Compile Include="ch\sycoforge\Decal\Demo\Footprints.cs" />
    <Compile Include="ch\sycoforge\Decal\Demo\LineUtil.cs" />
    <Compile Include="ch\sycoforge\Decal\Demo\PathAgent.cs" />
    <Compile Include="ch\sycoforge\Decal\Demo\ProxyRegister.cs" />
    <Compile Include="ch\sycoforge\Decal\Demo\Sinoid.cs" />
    <Compile Include="ch\sycoforge\Decal\Wrapper\EasyDecal.cs" />
    <Compile Include="Claymore.cs" />
    <Compile Include="ClientInstance.cs" />
    <Compile Include="ClientManagerLogger.cs" />
    <Compile Include="ClientPersist.cs" />
    <Compile Include="CosmeticInstance.cs" />
    <Compile Include="CosmeticsManager.cs" />
    <Compile Include="Crosshair.cs" />
    <Compile Include="CustomAddForce.cs" />
    <Compile Include="CustomPostProcessing.cs" />
    <Compile Include="Death.cs" />
    <Compile Include="DecalPreProcessor.cs" />
    <Compile Include="DetachableObject.cs" />
    <Compile Include="DF_SkyRotation.cs" />
    <Compile Include="DisableAfterTime.cs" />
    <Compile Include="DisableTimeAttackParkour.cs" />
    <Compile Include="DisplayLoadValues.cs" />
    <Compile Include="DlcOutButton.cs" />
    <Compile Include="DlcStorePageButton.cs" />
    <Compile Include="DollHealth.cs" />
    <Compile Include="Door.cs" />
    <Compile Include="DoorGlassDemo.cs" />
    <Compile Include="DualLauncher.cs" />
    <Compile Include="EjectCasePointObject.cs" />
    <Compile Include="EjectedCaseScript.cs" />
    <Compile Include="ElbowPivotPoint.cs" />
    <Compile Include="EventInteract.cs" />
    <Compile Include="EventSystemObject.cs" />
    <Compile Include="FirstPersonController.cs" />
    <Compile Include="FirstToWinsText.cs" />
    <Compile Include="FishNet\Serializing\Generated\GeneratedComparers___Internal.cs" />
    <Compile Include="FishNet\Serializing\Generated\GeneratedReaders___Internal.cs" />
    <Compile Include="FishNet\Serializing\Generated\GeneratedWriters___Internal.cs" />
    <Compile Include="FlashLight.cs" />
    <Compile Include="FlashPointObject.cs" />
    <Compile Include="FlingTrigger.cs" />
    <Compile Include="FloatingFriendButtons.cs" />
    <Compile Include="FloatingName.cs" />
    <Compile Include="FPArms.cs" />
    <Compile Include="FpsCounter.cs" />
    <Compile Include="GameManager.cs" />
    <Compile Include="GameParameters.cs" />
    <Compile Include="Goodgulf\Graphics\MatchChatLine.cs" />
    <Compile Include="GravityZone.cs" />
    <Compile Include="Grip.cs" />
    <Compile Include="Gun.cs" />
    <Compile Include="GunsMenu.cs" />
    <Compile Include="GunView.cs" />
    <Compile Include="HandGrenade.cs" />
    <Compile Include="HandGrenadeTwo.cs" />
    <Compile Include="HatPosition.cs" />
    <Compile Include="HealthTween.cs" />
    <Compile Include="HUD.cs" />
    <Compile Include="HUDTween.cs" />
    <Compile Include="InfoBubble.cs" />
    <Compile Include="InputManager.cs" />
    <Compile Include="InputReset.cs" />
    <Compile Include="Interactable.cs" />
    <Compile Include="InteractEnvironment.cs" />
    <Compile Include="ISaveable.cs" />
    <Compile Include="ItemBehaviour.cs" />
    <Compile Include="ItemDispenser.cs" />
    <Compile Include="ItemPosition.cs" />
    <Compile Include="ItemSpawner.cs" />
    <Compile Include="KillCam.cs" />
    <Compile Include="LargeRaycastGun.cs" />
    <Compile Include="LineFade.cs" />
    <Compile Include="LineRendererEffect.cs" />
    <Compile Include="LoadListInstance.cs" />
    <Compile Include="LobbiesListManager.cs" />
    <Compile Include="LobbyController.cs" />
    <Compile Include="LobbyDataEntry.cs" />
    <Compile Include="LobbyInviteInstance.cs" />
    <Compile Include="Map.cs" />
    <Compile Include="MapInstance.cs" />
    <Compile Include="MapInstanceButton.cs" />
    <Compile Include="MapSelection.cs" />
    <Compile Include="MapsManager.cs" />
    <Compile Include="MatchChat.cs" />
    <Compile Include="MatchLogs.cs" />
    <Compile Include="MatchLogsOffline.cs" />
    <Compile Include="MatchPoitnsHUD.cs" />
    <Compile Include="MeleeChildCollision.cs" />
    <Compile Include="MeleeWeapon.cs" />
    <Compile Include="MenuAnimation.cs" />
    <Compile Include="MenuAnimator.cs" />
    <Compile Include="MenuController.cs" />
    <Compile Include="MenuHUDTween.cs" />
    <Compile Include="MenuMusic.cs" />
    <Compile Include="Minigun.cs" />
    <Compile Include="ModelButtonHandler.cs" />
    <Compile Include="ModelButtonTween.cs" />
    <Compile Include="MoveUIObject.cs" />
    <Compile Include="MovingPlatform.cs" />
    <Compile Include="MovingPlatformAnimation.cs" />
    <Compile Include="MovingPlatformParent.cs" />
    <Compile Include="NeedsDlc.cs" />
    <Compile Include="Obus.cs" />
    <Compile Include="OnePlayerActive.cs" />
    <Compile Include="OnlyForHost.cs" />
    <Compile Include="OpenSlot.cs" />
    <Compile Include="PauseManager.cs" />
    <Compile Include="PhysicsGrenade.cs" />
    <Compile Include="PhysicsProp.cs" />
    <Compile Include="Pig.cs" />
    <Compile Include="Pigeon.cs" />
    <Compile Include="PigHealth.cs" />
    <Compile Include="PigItem.cs" />
    <Compile Include="Player.cs" />
    <Compile Include="PlayerControls.cs" />
    <Compile Include="PlayerHealth.cs" />
    <Compile Include="PlayerListItem.cs" />
    <Compile Include="PlayerManager.cs" />
    <Compile Include="PlayerPickup.cs" />
    <Compile Include="PlayerSetup.cs" />
    <Compile Include="PlayerShoot.cs" />
    <Compile Include="PlayerSpawnObject.cs" />
    <Compile Include="PlayerTracker.cs" />
    <Compile Include="PlayerTrackerEnable.cs" />
    <Compile Include="PlayerValues.cs" />
    <Compile Include="Playlist.cs" />
    <Compile Include="PlaylistInstance.cs" />
    <Compile Include="PlaylistPreset.cs" />
    <Compile Include="PlayRandomSound.cs" />
    <Compile Include="PopulateMenuSlots.cs" />
    <Compile Include="Pot.cs" />
    <Compile Include="PredictedProjectile.cs" />
    <Compile Include="PresetSharing.cs" />
    <Compile Include="ProfileDisplay.cs" />
    <Compile Include="ProgressInstance.cs" />
    <Compile Include="ProgressManager.cs" />
    <Compile Include="PropDamage.cs" />
    <Compile Include="Propeller.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="ProximityActivate.cs" />
    <Compile Include="ProximityChatPlayer.cs" />
    <Compile Include="ProximityMine.cs" />
    <Compile Include="RagdollDress.cs" />
    <Compile Include="RagdollSound.cs" />
    <Compile Include="RampAsset.cs" />
    <Compile Include="Readme.cs" />
    <Compile Include="ReBindUI.cs" />
    <Compile Include="RebondBalle.cs" />
    <Compile Include="RelativeOppositveState.cs" />
    <Compile Include="RepulsiveGun.cs" />
    <Compile Include="ResetAllBindings.cs" />
    <Compile Include="ResetScreenResolution.cs" />
    <Compile Include="RNGWeaponButton.cs" />
    <Compile Include="Rotate.cs" />
    <Compile Include="RoundManager.cs" />
    <Compile Include="RuntimeDecalCombiner.cs" />
    <Compile Include="SaveableEntity.cs" />
    <Compile Include="SaveLoadSystem.cs" />
    <Compile Include="ScaleTweenLoop.cs" />
    <Compile Include="SceneLoader.cs" />
    <Compile Include="SceneMotor.cs" />
    <Compile Include="ScoreManager.cs" />
    <Compile Include="Screenshake.cs" />
    <Compile Include="SelectSceneInstance.cs" />
    <Compile Include="SetActiveFromRef.cs" />
    <Compile Include="SetAmbientVolume.cs" />
    <Compile Include="SetEffectVolume.cs" />
    <Compile Include="SetItemPosition.cs" />
    <Compile Include="SetMenuMusicVolume.cs" />
    <Compile Include="SetMusicVolume.cs" />
    <Compile Include="SetPlayerVolume.cs" />
    <Compile Include="Settings.cs" />
    <Compile Include="SettingsResolution.cs" />
    <Compile Include="SetVoiceChatVolume.cs" />
    <Compile Include="ShatterableGlass.cs" />
    <Compile Include="ShootPointObject.cs" />
    <Compile Include="Shotgun.cs" />
    <Compile Include="ShrapnelBallistic.cs" />
    <Compile Include="SimpleCharacterMotor.cs" />
    <Compile Include="SimpleMove.cs" />
    <Compile Include="Slope.cs" />
    <Compile Include="SlopeDetectionFix.cs" />
    <Compile Include="SlopeSlide.cs" />
    <Compile Include="SoundManager.cs" />
    <Compile Include="SparksExecutioner.cs" />
    <Compile Include="SpawnEffect.cs" />
    <Compile Include="Spawner.cs" />
    <Compile Include="SpawnerManager.cs" />
    <Compile Include="SpawnPoint.cs" />
    <Compile Include="SteamLobby.cs" />
    <Compile Include="SteamManager.cs" />
    <Compile Include="Suppression.cs" />
    <Compile Include="TabScreen.cs" />
    <Compile Include="Taser.cs" />
    <Compile Include="TeamIdDropdown.cs" />
    <Compile Include="Teleporter.cs" />
    <Compile Include="TextToPlayerProfile.cs" />
    <Compile Include="TimerManager.cs" />
    <Compile Include="TimerTrigger.cs" />
    <Compile Include="ToggleAudio.cs" />
    <Compile Include="ToggleEventInteract.cs" />
    <Compile Include="Tooltip.cs" />
    <Compile Include="Trigger.cs" />
    <Compile Include="TurningGlow.cs" />
    <Compile Include="TutorialButton.cs" />
    <Compile Include="TutorialText.cs" />
    <Compile Include="UnityEngine\InputSystem\CustomUiInput.cs" />
    <Compile Include="UnitySA\Characters\FirstPerson\FPCtrl.cs" />
    <Compile Include="UnitySA\Characters\FirstPerson\MLook.cs" />
    <Compile Include="UnitySA\Utility\CurveCtrlBob.cs" />
    <Compile Include="UnitySA\Utility\FOVZoom.cs" />
    <Compile Include="UnitySA\Utility\LerpCtrlBob.cs" />
    <Compile Include="UnityStandardAssets\Effects\AfterburnerPhysicsForce.cs" />
    <Compile Include="UnityStandardAssets\Effects\ExplosionFireAndDebris.cs" />
    <Compile Include="UnityStandardAssets\Effects\ExplosionPhysicsForce.cs" />
    <Compile Include="UnityStandardAssets\Effects\ExtinguishableParticleSystem.cs" />
    <Compile Include="UnityStandardAssets\Effects\FireLight.cs" />
    <Compile Include="UnityStandardAssets\Effects\Hose.cs" />
    <Compile Include="UnityStandardAssets\Effects\ParticleSystemMultiplier.cs" />
    <Compile Include="UnityStandardAssets\Effects\SmokeParticles.cs" />
    <Compile Include="UnityStandardAssets\Effects\WaterHoseParticles.cs" />
    <Compile Include="UnityStandardAssets\Water\Displace.cs" />
    <Compile Include="UnityStandardAssets\Water\GerstnerDisplace.cs" />
    <Compile Include="UnityStandardAssets\Water\MeshContainer.cs" />
    <Compile Include="UnityStandardAssets\Water\PlanarReflection.cs" />
    <Compile Include="UnityStandardAssets\Water\SpecularLighting.cs" />
    <Compile Include="UnityStandardAssets\Water\Water.cs" />
    <Compile Include="UnityStandardAssets\Water\WaterBase.cs" />
    <Compile Include="UnityStandardAssets\Water\WaterBasic.cs" />
    <Compile Include="UnityStandardAssets\Water\WaterQuality.cs" />
    <Compile Include="UnityStandardAssets\Water\WaterTile.cs" />
    <Compile Include="UnityTemplateProjects\SimpleCameraController.cs" />
    <Compile Include="UseArea.cs" />
    <Compile Include="Vault.cs" />
    <Compile Include="VictoryMenu.cs" />
    <Compile Include="VictoryMenuUI.cs" />
    <Compile Include="VisualInfo.cs" />
    <Compile Include="Weapon.cs" />
    <Compile Include="WeaponData.cs" />
    <Compile Include="WeaponDropper.cs" />
    <Compile Include="WeaponHandSpawner.cs" />
    <Compile Include="WeaponPreset.cs" />
    <Compile Include="WeaponPresetManager.cs" />
    <Compile Include="WeaponPresetUtility.cs" />
    <Compile Include="WeaponRandomizationMenu.cs" />
    <Compile Include="WeaponRow.cs" />
    <Compile Include="WFX_BulletHoleDecal.cs" />
    <Compile Include="WFX_Demo.cs" />
    <Compile Include="WFX_Demo_DeleteAfterDelay.cs" />
    <Compile Include="WFX_Demo_New.cs" />
    <Compile Include="WFX_Demo_RandomDir.cs" />
    <Compile Include="WFX_Demo_Wall.cs" />
    <Compile Include="WFX_LightFlicker.cs" />
    <Compile Include="WindowPositionTween.cs" />
    <Compile Include="XpContentInstance.cs" />
    <Compile Include="XpContentLayout.cs" />
    <Compile Include="XpPopupInstance.cs" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>
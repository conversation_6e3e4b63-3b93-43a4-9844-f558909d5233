﻿using System;
using System.Collections;
using DG.Tweening;
using DG.Tweening.Core;
using DG.Tweening.Plugins.Options;
using FishNet;
using FishNet.Component.Animating;
using FishNet.Connection;
using FishNet.Managing;
using FishNet.Object;
using FishNet.Object.Delegating;
using FishNet.Object.Synchronizing;
using FishNet.Serializing;
using FishNet.Transporting;
using HeathenEngineering.PhysKit;
using HeathenEngineering.SteamworksIntegration;
using LambdaTheDev.NetworkAudioSync;
using TMPro;
using UnityEngine;
using UnityEngine.InputSystem;
using UnityEngine.Rendering.PostProcessing;

// Token: 0x02000020 RID: 32
public class FirstPersonController : NetworkBehaviour
{
	// Token: 0x17000033 RID: 51
	// (get) Token: 0x060000ED RID: 237 RVA: 0x000068FC File Offset: 0x00004AFC
	private float GetCurrentOffset
	{
		get
		{
			if (this.isCrouching)
			{
				return this.baseStepSpeed * this.crouchStepMultiplier;
			}
			if (!this.isSprinting)
			{
				return this.baseStepSpeed;
			}
			return this.baseStepSpeed * this.sprintStepMultiplier;
		}
	}

	// Token: 0x17000034 RID: 52
	// (get) Token: 0x060000EE RID: 238 RVA: 0x00006930 File Offset: 0x00004B30
	private float GetCurrentVolume
	{
		get
		{
			if (this.isCrouching)
			{
				return 0f;
			}
			if (!this.isSprinting)
			{
				return this.walkStepVolume;
			}
			return 1f;
		}
	}

	// Token: 0x060000EF RID: 239 RVA: 0x00006954 File Offset: 0x00004B54
	private void OnControllerColliderHit(ControllerColliderHit collision)
	{
		if (this.characterController.isGrounded && (double)collision.moveDirection.y < -0.3)
		{
			this.hitPointNormal = collision.normal;
			RaycastHit raycastHit;
			bool flag = Physics.Raycast(base.transform.position, -Vector3.up, out raycastHit, 0.5f, this.landLayer);
			if (Vector3.Angle(collision.normal, Vector3.up) > 65f && (!flag || Vector3.Angle(raycastHit.normal, Vector3.up) > 65f))
			{
				this.IsSliding = true;
			}
			else
			{
				this.IsSliding = false;
			}
		}
		else
		{
			this.IsSliding = false;
		}
		if (!this.characterController.isGrounded && this.wallJumpsCount < this.maxWallJumps && !this.onLadder && (collision.transform.gameObject.layer == 0 || collision.transform.gameObject.layer == 14) && !this.downRay && Vector3.Angle(collision.normal, Vector3.up) > 88f && Vector3.Angle(collision.normal, Vector3.up) < 100f)
		{
			if (!this.CanWallJump)
			{
				this.ChangeSlideClipServer(1);
				if (!this.slideAudio.isPlaying)
				{
					this.SlideAudioPlay();
				}
				this.slideAudio.volume = 1f * this.audio.volume;
			}
			if (this.rightHeadCast)
			{
				this.wallSlideLean = 10f;
			}
			else if (this.leftHeadCast)
			{
				this.wallSlideLean = -10f;
			}
			this.wallPointNormal = collision.normal;
			this.currentwalltag = collision.transform.tag;
			this.CanWallJump = true;
		}
		else
		{
			this.CanWallJump = false;
		}
		if ((double)collision.moveDirection.y < -0.3 && collision.transform.tag == "ShatterableGlass" && this.fallDistance > 1f)
		{
			this.BreakGlassServer(collision.point, collision.normal, collision.transform.gameObject);
		}
		if (!this.characterController.isGrounded && collision.moveDirection.y > 0.2f && !this.hitDiagonalCeiling && this.moveDirection.y > 0f)
		{
			if (Vector3.Angle(collision.normal, Vector3.up) > 91f || Vector3.Angle(collision.normal, Vector3.up) < 170f)
			{
				this.hitDiagonalCeiling = true;
				this.audio.PlayOneShot(this.ceilingHitClip);
				if (Vector3.Angle(collision.normal, Vector3.up) > 130f)
				{
					this.AddVerticalForce(new Vector3(0f, -1f, 0f), this.isSprinting ? (-2f) : (-1f));
				}
				this.BForce(new Vector3(collision.normal.x, 0f, collision.normal.z), 5.5f, false, true, this.jumpSlopeDecel * 2f, true);
				this.playerCamera.transform.DOPunchRotation(-this.fallShakeForce, this.fallShakeDuration, this.fallShakeVibrato, this.fallShakeElasticity);
				return;
			}
		}
		else if (this.moveDirection.y > 0f && this.allJumped)
		{
			if (Vector3.Angle(collision.normal, Vector3.up) < 170f || Vector3.Angle(collision.normal, Vector3.up) > 182f)
			{
				return;
			}
			this.allJumped = false;
			this.AddVerticalForce(new Vector3(0f, -1f, 0f), this.isSprinting ? (-8f) : (-5f));
			this.audio.PlayOneShot(this.ceilingHitClip);
			this.playerCamera.transform.DOPunchRotation(new Vector3(-this.fallShakeForce.x, 0f, -this.fallShakeForce.z), this.fallShakeDuration, this.fallShakeVibrato, this.fallShakeElasticity);
			this.moveDirection.y = (this.isSprinting ? (-8f) : (-5f));
		}
	}

	// Token: 0x060000F0 RID: 240 RVA: 0x00006DC8 File Offset: 0x00004FC8
	private bool OnSlopeAir()
	{
		RaycastHit raycastHit;
		return this.safeGrounded && (Physics.Raycast(base.transform.position, Vector3.down, out raycastHit, this.slopeForceRayLength) && raycastHit.normal != Vector3.up && Vector3.Angle(raycastHit.normal, Vector3.up) > 10f && Vector3.Angle(raycastHit.normal, Vector3.up) < 65f);
	}

	// Token: 0x060000F1 RID: 241 RVA: 0x00006E44 File Offset: 0x00005044
	private bool OnSlopeForSlide()
	{
		RaycastHit raycastHit;
		return Physics.Raycast(base.transform.position, Vector3.down, out raycastHit, this.slopeForceRayLength) && raycastHit.normal != Vector3.up && Vector3.Angle(raycastHit.normal, Vector3.up) > 10f && Vector3.Angle(raycastHit.normal, Vector3.up) < 65f;
	}

	// Token: 0x060000F2 RID: 242 RVA: 0x00006EB8 File Offset: 0x000050B8
	private bool OnSlopeIce()
	{
		RaycastHit raycastHit;
		return this.safeGrounded && (Physics.Raycast(base.transform.position, Vector3.down, out raycastHit, this.slopeForceRayLength * 10f) && raycastHit.normal != Vector3.up && Vector3.Angle(raycastHit.normal, Vector3.up) > 10f && Vector3.Angle(raycastHit.normal, Vector3.up) < 65f);
	}

	// Token: 0x060000F3 RID: 243 RVA: 0x00006F3C File Offset: 0x0000513C
	public virtual void Awake()
	{
		this.NetworkInitialize___Early();
		this.Awake___UserLogic();
		this.NetworkInitialize__Late();
	}

	// Token: 0x060000F4 RID: 244 RVA: 0x00006F5C File Offset: 0x0000515C
	private void OnEnable()
	{
		this.move = this.playerControls.Player.Move;
		this.move.Enable();
		this.zoom = this.playerControls.Player.Zoom;
		this.zoom.Enable();
		this.zoom.started += this.SetZoom;
		this.zoom.canceled += this.SetZoom;
		this.zoom.performed += this.SetZoomToggle;
		this.lookY = this.playerControls.Player.MouseY;
		this.lookY.Enable();
		this.lookX = this.playerControls.Player.MouseX;
		this.lookX.Enable();
		this.jump = this.playerControls.Player.Jump;
		this.jump.Enable();
		this.jump.performed += this.Jump;
		this.run = this.playerControls.Player.Run;
		this.run.Enable();
		this.leanLeft = this.playerControls.Player.LeanLeft;
		this.leanLeft.Enable();
		this.leanRight = this.playerControls.Player.LeanRight;
		this.leanRight.Enable();
		this.record = this.playerControls.Player.VoiceChat;
		this.record.Enable();
		this.moveUp = this.playerControls.Player.MoveUp;
		this.moveUp.Enable();
		this.crouch = this.playerControls.Player.Crouch;
		this.crouch.Enable();
		this.crouch.performed += this.Slide;
		this.crouch.started += this.SetCrouch;
		this.crouch.canceled += this.SetCrouch;
		this.crouch.canceled += this.SlideEnd;
		this.fire1 = this.playerControls.Player.FireHold;
		this.fire1.Enable();
		this.fire2 = this.playerControls.Player.RightClick;
		this.fire2.Enable();
		this.reload = this.playerControls.Player.Reload;
		this.reload.Enable();
		PauseManager.OnRoundStarted += this.roundStartEvent;
	}

	// Token: 0x060000F5 RID: 245 RVA: 0x0000722C File Offset: 0x0000542C
	private void OnDisable()
	{
		this.move.Disable();
		this.moveUp.Disable();
		this.jump.Disable();
		this.jump.performed -= this.Jump;
		this.run.Disable();
		this.zoom.Disable();
		this.zoom.started -= this.SetZoom;
		this.zoom.canceled -= this.SetZoom;
		this.zoom.performed -= this.SetZoomToggle;
		this.lookY.Disable();
		this.lookX.Disable();
		this.crouch.Disable();
		this.crouch.performed -= this.Slide;
		this.crouch.started -= this.SetCrouch;
		this.crouch.canceled -= this.SetCrouch;
		this.crouch.canceled -= this.SlideEnd;
		this.leanLeft.Disable();
		this.leanRight.Disable();
		this.record.Disable();
		this.fire1.Disable();
		this.reload.Disable();
		this.fire2.Disable();
		PauseManager.OnRoundStarted -= this.roundStartEvent;
		this.speedometer.text = "";
	}

	// Token: 0x060000F6 RID: 246 RVA: 0x000073AC File Offset: 0x000055AC
	[ServerRpc(RunLocally = true)]
	public void CmdChangeRootMotion(bool istrue)
	{
		this.RpcWriter___Server_CmdChangeRootMotion_1140765316(istrue);
		this.RpcLogic___CmdChangeRootMotion_1140765316(istrue);
	}

	// Token: 0x060000F7 RID: 247 RVA: 0x000073C2 File Offset: 0x000055C2
	[ObserversRpc(RunLocally = true, ExcludeOwner = true)]
	private void ChangeRootMotion(bool istrue)
	{
		this.RpcWriter___Observers_ChangeRootMotion_1140765316(istrue);
		this.RpcLogic___ChangeRootMotion_1140765316(istrue);
	}

	// Token: 0x060000F8 RID: 248 RVA: 0x000073D8 File Offset: 0x000055D8
	private void Start()
	{
		this.settings = Settings.Instance;
		this.distToRunFov = this.runFOV - this.defaultFOV;
		this.distToSlideFov = this.slideFOV - this.defaultFOV;
		this.distToRunSlideFov = this.runSlideFOV - this.defaultFOV;
		this.defaultFOV = this.settings.fovValue;
	}

	// Token: 0x060000F9 RID: 249 RVA: 0x0000743C File Offset: 0x0000563C
	private void Update()
	{
		this.HandleTimers();
		this.VoiceChat();
		this.defaultFOV = this.settings.fovValue;
		this.runFOV = this.defaultFOV + this.distToRunFov;
		this.slideFOV = this.defaultFOV + this.distToSlideFov;
		this.runSlideFOV = this.defaultFOV + this.distToRunSlideFov;
		if (base.transform.position.y < -300f && !this.safeDeathFalling)
		{
			this.safeDeathFalling = true;
			Settings.Instance.IncreaseSuicidesAmount();
			base.GetComponent<PlayerHealth>().fellVoid = true;
			this.DespawnObject(base.gameObject);
		}
		if (Input.GetKeyDown(KeyCode.P) && Input.GetKey(KeyCode.LeftControl) && Application.isEditor)
		{
			Debug.Break();
		}
		if (Input.GetKeyDown(KeyCode.L) && Input.GetKey(KeyCode.LeftControl) && (Application.isEditor || SceneMotor.Instance.testMap))
		{
			this.flymode = !this.flymode;
		}
		if (this.characterController.skinWidth != ((this.isCrouching && !this.isSliding) ? 0.07f : 0.2f))
		{
			this.setupScript.ChangeSkinWidth((this.isCrouching && !this.isSliding) ? 0.07f : 0.2f);
		}
		this.characterController.skinWidth = ((this.isCrouching && !this.isSliding) ? 0.07f : 0.2f);
		this.colorGrading.saturation.value = Mathf.Lerp(this.colorGrading.saturation.value, 0f, this.saturationSpeed * Time.deltaTime);
		this.colorGrading.gamma.value = new Vector4(1f, 1f, 1f, this.settings.brightness - 1f);
		this.dirForward = Vector3.Lerp(this.dirForward, base.transform.TransformDirection(Vector3.forward), 13f * Time.deltaTime);
		this.dirRight = Vector3.Lerp(this.dirRight, base.transform.TransformDirection(Vector3.right), 13f * Time.deltaTime);
		if (this.pauseManager.nonSteamworksTransport)
		{
			this.sync___set_value_canMove(true, true);
		}
		if (MapsManager.Instance != null && (this.pauseManager.inVictoryMenu || MapsManager.Instance.inExplorationMap))
		{
			this.sync___set_value_canMove(true, true);
		}
		if (SceneMotor.Instance != null && SceneMotor.Instance.testMap)
		{
			this.sync___set_value_canMove(true, true);
		}
		if (this.SyncAccessor_canMove)
		{
			this.startOfRound = false;
		}
		if (!(this.pauseManager != null) || (!this.pauseManager.pause && (!this.pauseManager.steamPlaying || !this.pauseManager.chatting)))
		{
			if (!this.pauseManager.otherPauseBools && (this.startOfRound || this.SyncAccessor_canMove))
			{
				this.HandleMouseLook();
			}
			this.HandleCameraController();
			if (this.SyncAccessor_canMove)
			{
				if (this.playerCamera == null)
				{
					return;
				}
				this.CalculateMovementInput();
				this.HandlePhysicsCasting();
				this.CheckForVault();
				this.HandleLadders();
				if (!this.flymode)
				{
					this.HandleCameraLean();
				}
				if (this.canCrouch)
				{
					this.HandleCrouch();
				}
				if (this.canUseHeadMovement)
				{
					this.HandleHeadMovement();
				}
				if (this.useFootsteps)
				{
					this.HandleFootsteps();
				}
			}
		}
		else
		{
			this.currentInput = Vector2.zero;
		}
		this.HandleBForce();
		if (!this.flymode)
		{
			this.HandleMovementInput();
		}
		this.HandleAddingForce();
		if ((!this.pauseManager.steamPlaying || !this.pauseManager.chatting) && this.SyncAccessor_canMove)
		{
			this.HandleAnimation();
		}
		if (!this.pauseManager.steamPlaying || !this.pauseManager.chatting)
		{
			this.HandleTaunt();
		}
		if (!this.flymode)
		{
			this.ApplyFinalMovements();
		}
		if (this.SyncAccessor_canMove)
		{
			if (this.flymode)
			{
				this.characterController.Move((this.move.ReadValue<Vector2>().y * base.transform.forward + this.move.ReadValue<Vector2>().x * base.transform.right + base.transform.up * (float)(Input.GetKey(KeyCode.E) ? 1 : (Input.GetKey(KeyCode.Q) ? (-1) : 0))) * Time.deltaTime * 10f * (float)(this.isSprinting ? 2 : 1));
			}
			if (!this.characterController.isGrounded && this.moveDirection.y < 0f)
			{
				this.fallDistance -= this.moveDirection.y * Time.deltaTime;
			}
			if (this.moveDirection.y > 0f)
			{
				this.fallDistance = 0f;
			}
			if (this.characterController.isGrounded)
			{
				this.moveDirection.y = -1f;
				this.coyoteTimer = 0.15f;
				this.hitDiagonalCeiling = false;
			}
			if ((Physics.Raycast(base.transform.position, Vector3.down, 0.5f) && this.landTimer > 0f && !this.allJumped && this.moveDirection.y < 0f) || this.characterController.isGrounded)
			{
				this.safeGrounded = true;
			}
			else
			{
				this.safeGrounded = false;
			}
			if (!this.playerPickupScript.SyncAccessor_hasObjectInLeftHand)
			{
				this.isAiming = this.isZooming;
			}
			else
			{
				this.isAiming = false;
			}
			if (this.sprintToggle)
			{
				if (this.run.ReadValue<float>() > 0.1f && this.funcSprintTrigger)
				{
					this.funcSprint = !this.funcSprint;
					this.funcSprintTrigger = false;
				}
				if (this.run.ReadValue<float>() < 0.1f)
				{
					this.funcSprintTrigger = true;
				}
			}
			else
			{
				this.funcSprint = (this.reverseSprintBind ? (this.run.ReadValue<float>() < 0.1f) : (this.run.ReadValue<float>() > 0.1f));
			}
			this.isSprinting = !this.isLeaning && !this.isAiming && this.move.ReadValue<Vector2>() != Vector2.zero && !this.isCrouching && this.funcSprint;
			this.isSlideSprinting = !this.isLeaning && !this.isAiming && this.move.ReadValue<Vector2>() != Vector2.zero && this.funcSprint;
			this.isWalking = this.move.ReadValue<Vector2>() != Vector2.zero;
			this.isGrounded = this.characterController.isGrounded;
			if (this.leanToggle)
			{
				if (this.leanLeft.ReadValue<float>() > 0.1f && this.funcLeanLeftTrigger)
				{
					this.isLeaningLeft = !this.isLeaningLeft;
					this.isLeaningRight = false;
					this.funcLeanLeftTrigger = false;
				}
				else if (this.leanRight.ReadValue<float>() > 0.1f && this.funcLeanRightTrigger)
				{
					this.isLeaningLeft = false;
					this.isLeaningRight = !this.isLeaningRight;
					this.funcLeanRightTrigger = false;
				}
				if (this.leanLeft.ReadValue<float>() < 0.1f)
				{
					this.funcLeanLeftTrigger = true;
				}
				if (this.leanRight.ReadValue<float>() < 0.1f)
				{
					this.funcLeanRightTrigger = true;
				}
			}
			else
			{
				this.isLeaningLeft = this.leanLeft.ReadValue<float>() > 0.1f;
				this.isLeaningRight = this.leanRight.ReadValue<float>() > 0.1f;
				if (this.isLeaningLeft && this.isLeaningRight)
				{
					this.isLeaningLeft = false;
					this.isLeaningRight = false;
				}
			}
			this.isLeaning = this.isLeaningLeft || this.isLeaningRight;
			if (this.isGrounded)
			{
				this.wallJumpsCount = 0;
				this.jumped = false;
				this.allJumped = false;
				this.slopeSlideJumped = false;
				this.settings.timeSpentOnGround += Time.deltaTime;
			}
			else
			{
				this.settings.timeSpentInAir += Time.deltaTime;
			}
		}
		if (Settings.Instance.showSpeedometer)
		{
			this.speedometer.text = "Speed : " + ((float)Mathf.RoundToInt(this.playerSpeed * 100f) * 0.01f).ToString();
		}
	}

	// Token: 0x060000FA RID: 250 RVA: 0x00007CE4 File Offset: 0x00005EE4
	private void HandleTaunt()
	{
		this.tauntTimer -= Time.deltaTime;
		if (this.tauntTimer > 0f)
		{
			return;
		}
		if (Input.GetKeyDown(KeyCode.Alpha1))
		{
			this.AboubiPlayServer(0);
			Settings.Instance.IncreaseTauntsAmount();
		}
		if (Input.GetKeyDown(KeyCode.Alpha2))
		{
			this.AboubiPlayServer(1);
			Settings.Instance.IncreaseTauntsAmount();
		}
		if (Input.GetKeyDown(KeyCode.Alpha3))
		{
			this.AboubiPlayServer(2);
			Settings.Instance.IncreaseTauntsAmount();
		}
		if (Input.GetKeyDown(KeyCode.Alpha4))
		{
			this.AboubiPlayServer(3);
			Settings.Instance.IncreaseTauntsAmount();
		}
		if (Input.GetKeyDown(KeyCode.Alpha5))
		{
			this.AboubiPlayServer(4);
			Settings.Instance.IncreaseTauntsAmount();
		}
		if (Input.GetKeyDown(KeyCode.Alpha6))
		{
			this.AboubiPlayServer(5);
			Settings.Instance.IncreaseTauntsAmount();
		}
		if (Input.GetKeyDown(KeyCode.Alpha7))
		{
			this.AboubiPlayServer(6);
			Settings.Instance.IncreaseTauntsAmount();
		}
		if (Input.GetKeyDown(KeyCode.Alpha8))
		{
			this.AboubiPlayServer(7);
			Settings.Instance.IncreaseTauntsAmount();
		}
		if (Input.GetKeyDown(KeyCode.Alpha9))
		{
			this.AboubiPlayServer(8);
			Settings.Instance.IncreaseTauntsAmount();
		}
		if (Input.GetKeyDown(KeyCode.Alpha0))
		{
			this.AboubiPlayServer(9);
			Settings.Instance.IncreaseTauntsAmount();
		}
		if (Input.GetKeyDown(KeyCode.Alpha1))
		{
			this.tauntTimer = 0.4f;
		}
		if (Input.GetKeyDown(KeyCode.Alpha2))
		{
			this.tauntTimer = 0.3f;
		}
		if (Input.GetKeyDown(KeyCode.Alpha3))
		{
			this.tauntTimer = 0.3f;
		}
		if (Input.GetKeyDown(KeyCode.Alpha4))
		{
			this.tauntTimer = 0.5f;
		}
		if (Input.GetKeyDown(KeyCode.Alpha5))
		{
			this.tauntTimer = 0.7f;
		}
		if (Input.GetKeyDown(KeyCode.Alpha6))
		{
			this.tauntTimer = 0.4f;
		}
		if (Input.GetKeyDown(KeyCode.Alpha7))
		{
			this.tauntTimer = 0.7f;
		}
		if (Input.GetKeyDown(KeyCode.Alpha8))
		{
			this.tauntTimer = 0.9f;
		}
		if (Input.GetKeyDown(KeyCode.Alpha9))
		{
			this.tauntTimer = 1f;
		}
		if (Input.GetKeyDown(KeyCode.Alpha0))
		{
			this.tauntTimer = 0.3f;
		}
	}

	// Token: 0x060000FB RID: 251 RVA: 0x00007EDE File Offset: 0x000060DE
	[ServerRpc(RunLocally = true)]
	private void AboubiPlayServer(int clip)
	{
		this.RpcWriter___Server_AboubiPlayServer_3316948804(clip);
		this.RpcLogic___AboubiPlayServer_3316948804(clip);
	}

	// Token: 0x060000FC RID: 252 RVA: 0x00007EF4 File Offset: 0x000060F4
	[ObserversRpc(RunLocally = true, ExcludeOwner = true)]
	private void AboubiPlayObservers(int clip)
	{
		this.RpcWriter___Observers_AboubiPlayObservers_3316948804(clip);
		this.RpcLogic___AboubiPlayObservers_3316948804(clip);
	}

	// Token: 0x060000FD RID: 253 RVA: 0x00007F0C File Offset: 0x0000610C
	private void HandleAnimation()
	{
		this.animator.SetBool("Crouch", this.isCrouching);
		this.animator.SetBool("Grounded", this.slideDownRay && this.moveDirection.y <= 0f);
		this.animator.SetBool("Slide", this.isSliding || this.slopeSlideScript.isCrouchSlopeSliding || this.slideTimer > 0.1f);
		this.animator.SetFloat("crouchMove", this.move.ReadValue<Vector2>().magnitude);
		this.animator.SetFloat("MovementSpeed", this.isSprinting ? 1f : (this.isWalking ? 0.5f : 0f));
		this.animator.SetFloat("Vertical", this.isSliding ? (-(this.rotationX / 90f - 1f) - 2f) : (-(this.rotationX / 90f)));
		this.animator.SetBool("LeanRight", this.isLeaningRight && !this.isSliding && this.isGrounded && Mathf.Abs(this.leanCamera.transform.localEulerAngles.z) < 339f);
		this.animator.SetBool("LeanLeft", this.isLeaningLeft && !this.isSliding && this.isGrounded && Mathf.Abs(this.leanCamera.transform.localEulerAngles.z) > 19f);
		if (this.torso.activeSelf && !this.isSliding && !this.slopeSlideScript.isCrouchSlopeSliding && base.IsOwner)
		{
			this.torso.SetActive(false);
		}
		if (this.legLeft.activeSelf && !this.isSliding && !this.slopeSlideScript.isCrouchSlopeSliding && base.IsOwner)
		{
			this.legLeft.SetActive(false);
		}
		if (this.legRight.activeSelf && !this.isSliding && !this.slopeSlideScript.isCrouchSlopeSliding && base.IsOwner)
		{
			this.legRight.SetActive(false);
		}
	}

	// Token: 0x060000FE RID: 254 RVA: 0x00008164 File Offset: 0x00006364
	private IEnumerator ActiveTorso()
	{
		yield return new WaitForSeconds(0f);
		this.torso.SetActive(true);
		this.legLeft.SetActive(true);
		this.legRight.SetActive(true);
		yield break;
	}

	// Token: 0x060000FF RID: 255 RVA: 0x00008174 File Offset: 0x00006374
	private void CalculateMovementInput()
	{
		this.playerSpeed = Mathf.Round(this.characterController.velocity.magnitude * 100f) * 0.01f;
		if (this.move.ReadValue<Vector2>().x > 0f && this.baircontrol)
		{
			this.horizontalInput = Mathf.Lerp(this.horizontalInput, 1f, this.globalAcceleration * ((!this.characterController.isGrounded) ? this.airControl : 1f) * Time.deltaTime);
		}
		else if (this.move.ReadValue<Vector2>().x < 0f && this.baircontrol)
		{
			this.horizontalInput = Mathf.Lerp(this.horizontalInput, -1f, this.globalAcceleration * ((!this.characterController.isGrounded) ? this.airControl : 1f) * Time.deltaTime);
		}
		else
		{
			this.horizontalInput = Mathf.Lerp(this.horizontalInput, 0f, this.globalDeceleration * ((!this.characterController.isGrounded) ? this.airControl : 1f) * Time.deltaTime);
		}
		if (this.move.ReadValue<Vector2>().y > 0f && this.baircontrol)
		{
			this.verticalInput = Mathf.Lerp(this.verticalInput, 1f, this.globalAcceleration * ((!this.characterController.isGrounded) ? this.airControl : 1f) * Time.deltaTime);
		}
		else if (this.move.ReadValue<Vector2>().y < 0f && this.baircontrol)
		{
			this.verticalInput = Mathf.Lerp(this.verticalInput, -1f, this.globalAcceleration * ((!this.characterController.isGrounded) ? this.airControl : 1f) * Time.deltaTime);
		}
		else
		{
			this.verticalInput = Mathf.Lerp(this.verticalInput, 0f, this.globalDeceleration * ((!this.characterController.isGrounded) ? this.airControl : 1f) * Time.deltaTime);
		}
		if (!this.slopeSlideScript.isCrouchSlopeSliding || !this.slopeScript.uphill)
		{
			if (!this.characterController.isGrounded && this.funcSprint && this.move.ReadValue<Vector2>().magnitude != 0f && !this.isLeaning)
			{
				this.speedFactor = Mathf.Round(Mathf.Lerp(this.speedFactor, this.sprintAirSpeed, this.sprintAirAcceleration * Time.deltaTime) * 100f) * 0.01f;
			}
			else if (!this.characterController.isGrounded && this.move.ReadValue<Vector2>().magnitude != 0f && !this.isLeaning)
			{
				this.speedFactor = Mathf.Round(Mathf.Lerp(this.speedFactor, this.airSpeed, this.airAcceleration * Time.deltaTime) * 100f) * 0.01f;
			}
			else if (this.isCrouching && this.move.ReadValue<Vector2>().magnitude != 0f && this.characterController.isGrounded && !this.isLeaning)
			{
				this.speedFactor = Mathf.Round(Mathf.Lerp(this.speedFactor, this.crouchSpeed, this.crouchAcceleration * Time.deltaTime) * 100f) * 0.01f;
			}
			else if (this.isSprinting && this.move.ReadValue<Vector2>().magnitude != 0f && !this.isLeaning)
			{
				this.speedFactor = Mathf.Round(Mathf.Lerp(this.speedFactor, this.sprintSpeed, this.sprintAcceleration * Time.deltaTime) * 100f) * 0.01f;
			}
			else if (this.move.ReadValue<Vector2>().magnitude != 0f && !this.isLeaning)
			{
				this.speedFactor = Mathf.Round(Mathf.Lerp(this.speedFactor, this.walkSpeed, this.walkAcceleration * Time.deltaTime) * 100f) * 0.01f;
			}
			else
			{
				this.speedFactor = Mathf.Round(Mathf.Lerp(this.speedFactor, 1f, this.globalDeceleration * Time.deltaTime) * 100f) * 0.01f;
			}
		}
		else
		{
			this.speedFactor = Mathf.Round(Mathf.Lerp(this.speedFactor, 1f, this.globalDeceleration * Time.deltaTime) * 100f) * 0.01f;
		}
		this.setSpeedTimer -= Time.deltaTime;
		if (this.setSpeedTimer > 0f)
		{
			this.speedFactor = this.tempSetSpeed;
		}
		this.currentInput = new Vector2(Mathf.Round(this.verticalInput * 100f) * 0.01f, Mathf.Round(this.horizontalInput * 100f) * 0.01f) * this.speedFactor;
		this.currentInputRaw = ((!this.characterController.isGrounded && this.isSprinting) ? this.sprintAirSpeed : ((!this.characterController.isGrounded) ? this.airSpeed : (this.isCrouching ? this.crouchSpeed : (this.isSprinting ? this.sprintSpeed : this.walkSpeed)))) * this.move.ReadValue<Vector2>();
		this.audio.pitch = Mathf.Lerp(0.5f, 1f, this.movementFactor);
	}

	// Token: 0x06000100 RID: 256 RVA: 0x0000872E File Offset: 0x0000692E
	public void SetSpeed(float speed, float time)
	{
		this.tempSetSpeed = speed;
		this.setSpeedTimer = time;
	}

	// Token: 0x06000101 RID: 257 RVA: 0x00008740 File Offset: 0x00006940
	private void HandleMovementInput()
	{
		if (this.SyncAccessor_canMove)
		{
			this.currentInput = Vector2.ClampMagnitude(this.currentInput, (!this.characterController.isGrounded && this.isSprinting) ? this.sprintAirSpeed : ((!this.characterController.isGrounded) ? this.airSpeed : (this.isCrouching ? this.crouchSpeed : (this.isSprinting ? this.sprintSpeed : this.walkSpeed)))) * this.movementFactor;
		}
		else
		{
			this.currentInput = Vector2.zero;
		}
		float y = this.moveDirection.y;
		this.moveDirection = this.dirForward * this.currentInput.x + this.dirRight * this.currentInput.y;
		this.moveDirection.y = y;
	}

	// Token: 0x06000102 RID: 258 RVA: 0x00008828 File Offset: 0x00006A28
	private void HandleMouseLook()
	{
		this.rotationX -= this.mouseInput.y * 0.1f * (this.isScopeAiming ? this.lookSpeedAim : (this.isAiming ? this.lookSpeedAimNoScope : this.lookSpeedY)) * (float)(this.invertY ? (-1) : 1) * (float)(this.pauseManager.gamepad ? 14 : 1);
		this.rotationX = Mathf.Clamp(this.rotationX, -this.upperLookLimit, this.lowerLookLimit);
		this.playerCameraHolder.transform.localRotation = Quaternion.Euler(this.rotationX, 0f, this.rotationZ);
		base.transform.rotation *= Quaternion.Euler(0f, this.mouseInput.x * 0.1f * (float)(this.invertX ? (-1) : 1) * (this.isScopeAiming ? this.lookSpeedAim : (this.isAiming ? this.lookSpeedAimNoScope : this.lookSpeedX)) * (float)(this.pauseManager.gamepad ? 14 : 1), 0f);
	}

	// Token: 0x06000103 RID: 259 RVA: 0x00008960 File Offset: 0x00006B60
	private void HandleCameraLean()
	{
		if (this.isLeaningRight && !this.isSliding && this.isGrounded)
		{
			this.CameraLean(Quaternion.Euler(0f, 0f, -this.leanLimit));
			return;
		}
		if (this.isLeaningLeft && !this.isSliding && this.isGrounded)
		{
			this.CameraLean(Quaternion.Euler(0f, 0f, this.leanLimit));
			return;
		}
		this.CameraLean(Quaternion.Euler(0f, 0f, 0f));
	}

	// Token: 0x06000104 RID: 260 RVA: 0x000089F0 File Offset: 0x00006BF0
	private void CameraLean(Quaternion rot)
	{
		RaycastHit raycastHit;
		bool flag = Physics.Raycast(base.transform.position + Vector3.up * 2f, -base.transform.right, out raycastHit, 1.8f, this.landLayer);
		RaycastHit raycastHit2;
		bool flag2 = Physics.Raycast(base.transform.position + Vector3.up * 2f, base.transform.right, out raycastHit2, 1.8f, this.landLayer);
		if (this.isLeaningRight)
		{
			if (flag2)
			{
				this.leanCamera.transform.localRotation = Quaternion.Slerp(this.leanCamera.transform.localRotation, Quaternion.Slerp(Quaternion.Euler(0f, 0f, 0f), rot, Vector3.Distance(base.transform.position + Vector3.up * 2f, raycastHit2.point) / 1.8f), ((Vector3.Distance(this.playerCamera.transform.position, raycastHit2.point) < 0.8f) ? 200f : this.leanSpeed) * Time.deltaTime);
				return;
			}
			this.leanCamera.transform.localRotation = Quaternion.Slerp(this.leanCamera.transform.localRotation, rot, this.leanSpeed * Time.deltaTime);
			return;
		}
		else
		{
			if (!this.isLeaningLeft)
			{
				this.leanCamera.transform.localRotation = Quaternion.Slerp(this.leanCamera.transform.localRotation, Quaternion.Euler(0f, 0f, 0f), this.leanSpeed * Time.deltaTime);
				return;
			}
			if (flag)
			{
				this.leanCamera.transform.localRotation = Quaternion.Slerp(this.leanCamera.transform.localRotation, Quaternion.Slerp(Quaternion.Euler(0f, 0f, 0f), rot, Vector3.Distance(base.transform.position + Vector3.up * 2f, raycastHit.point) / 1.8f), ((Vector3.Distance(this.playerCamera.transform.position, raycastHit.point) < 0.8f) ? 200f : this.leanSpeed) * Time.deltaTime);
				return;
			}
			this.leanCamera.transform.localRotation = Quaternion.Slerp(this.leanCamera.transform.localRotation, rot, this.leanSpeed * Time.deltaTime);
			return;
		}
	}

	// Token: 0x06000105 RID: 261 RVA: 0x00008CA4 File Offset: 0x00006EA4
	public void Jump(global::UnityEngine.InputSystem.InputAction.CallbackContext ctx)
	{
		if (!this.canJump || !this.SyncAccessor_canMove || this.slopeSlideScript.onSuperIce)
		{
			return;
		}
		if (this.CanWallJump)
		{
			this.wallJumpsCount++;
			this.SlideFadeOut();
			this.WallJumpClip(this.currentwalltag);
			this.PlaySoundServer(22);
			this.BForce(this.wallPointNormal, ((this.move.ReadValue<Vector2>() != Vector2.zero) ? this.jumpSlopeForce : 7f) * 0.7f, false, true, this.jumpSlopeDecel, true);
			this.moveDirection.y = this.jumpForce * 0.8f * this.wallJumpFactor;
			this.playerCamera.transform.DOPunchRotation(-this.fallShakeForce * 1.5f, this.fallShakeDuration, this.fallShakeVibrato, this.fallShakeElasticity);
			this.CanWallJump = false;
		}
		if (!(this.pauseManager != null) || this.pauseManager.pause || (this.pauseManager.chatting && this.pauseManager.steamPlaying))
		{
			return;
		}
		if (this.characterController.isGrounded)
		{
			this.jumpSlope = true;
			this.moveDirection.y = 0f;
		}
		if (this.slopeSlideScript.isCrouchSlopeSliding)
		{
			this.slopeSlideScript.slopeSlideMove = new Vector3(this.slopeSlideScript.slopeSlideMove.x, 0f, this.slopeSlideScript.slopeSlideMove.z);
		}
		this.jumpSlope = true;
		RaycastHit raycastHit;
		if (Physics.Raycast(base.transform.position, Vector3.down, out raycastHit, 1.3f) && this.moveDirection.y < 0f && !this.characterController.isGrounded)
		{
			this.prejump = true;
		}
		if (this.characterController.isGrounded && !this.prejump)
		{
			this.allJumped = true;
			this.slopeSlideJumped = true;
			this.coyoteTimer = 0f;
			this.moveDirection.y = this.jumpForce * this.jumpFactor;
			this.slideCancelTimer = 2f;
			this.animator.Rebind();
			this.networkAnimator.SetTrigger("Jump");
			VerletSpring[] array = this.physCables;
			for (int i = 0; i < array.Length; i++)
			{
				array[i].AddForce(0, -base.transform.forward * 5f);
			}
			if (this.IsSliding)
			{
				this.BForce(this.hitPointNormal, (this.move.ReadValue<Vector2>() != Vector2.zero) ? this.jumpSlopeForce : 7f, false, true, this.jumpSlopeDecel, true);
				this.playerCamera.transform.DOPunchRotation(-this.fallShakeForce, this.fallShakeDuration, this.fallShakeVibrato, this.fallShakeElasticity);
			}
			this.PlaySoundServer(10);
			return;
		}
		if (this.coyoteTimer > 0f && !this.characterController.isGrounded && this.moveDirection.y < 0f)
		{
			this.allJumped = true;
			this.slopeSlideJumped = true;
			this.prejump = false;
			this.coyoteTimer = 0f;
			this.moveDirection.y = this.jumpForce * this.jumpFactor;
			this.slideCancelTimer = 2f;
			this.animator.Rebind();
			this.networkAnimator.SetTrigger("Jump");
			if (this.IsSliding)
			{
				this.BForce(this.hitPointNormal, (this.move.ReadValue<Vector2>() != Vector2.zero) ? this.jumpSlopeForce : 7f, false, true, this.jumpSlopeDecel, true);
				this.playerCamera.transform.DOPunchRotation(-this.fallShakeForce, this.fallShakeDuration, this.fallShakeVibrato, this.fallShakeElasticity);
			}
			this.PlaySoundServer(10);
			return;
		}
		if (this.aftervaultjumpTimer > 0f)
		{
			this.allJumped = true;
			this.jumped = true;
			this.slopeSlideJumped = true;
			this.vaultActivate = true;
			this.prejump = false;
			this.aftervaultjumpTimer = 0f;
			this.moveDirection.y = 0f;
			this.moveDirection.y = this.jumpForce * this.jumpFactor;
			this.slideCancelTimer = 2f;
			this.animator.Rebind();
			this.networkAnimator.SetTrigger("Jump");
			if (this.IsSliding)
			{
				this.BForce(this.hitPointNormal, (this.move.ReadValue<Vector2>() != Vector2.zero) ? this.jumpSlopeForce : 7f, false, true, this.jumpSlopeDecel, true);
				this.playerCamera.transform.DOPunchRotation(-this.fallShakeForce, this.fallShakeDuration, this.fallShakeVibrato, this.fallShakeElasticity);
			}
			this.PlaySoundServer(10);
		}
	}

	// Token: 0x06000106 RID: 262 RVA: 0x000091C0 File Offset: 0x000073C0
	public void SetZoom(global::UnityEngine.InputSystem.InputAction.CallbackContext ctx)
	{
		if ((!(this.pauseManager != null) || (!this.pauseManager.pause && (!this.pauseManager.steamPlaying || !this.pauseManager.chatting))) && !this.aimToggle)
		{
			this.isZooming = ctx.started;
		}
	}

	// Token: 0x06000107 RID: 263 RVA: 0x00009224 File Offset: 0x00007424
	public void SetZoomToggle(global::UnityEngine.InputSystem.InputAction.CallbackContext ctx)
	{
		if ((!(this.pauseManager != null) || (!this.pauseManager.pause && (!this.pauseManager.steamPlaying || !this.pauseManager.chatting))) && this.aimToggle)
		{
			this.isZooming = !this.isZooming;
		}
	}

	// Token: 0x06000108 RID: 264 RVA: 0x00009289 File Offset: 0x00007489
	public void SetCrouch(global::UnityEngine.InputSystem.InputAction.CallbackContext ctx)
	{
		if (this.crouchToggle)
		{
			return;
		}
		this.isCrouching = ctx.started;
		this.crouchPress = ctx.started;
	}

	// Token: 0x06000109 RID: 265 RVA: 0x000092B0 File Offset: 0x000074B0
	private void HandleCrouch()
	{
		if (Physics.Raycast(base.transform.position, Vector3.up, 2f, this.landLayer))
		{
			this.crouchExit = true;
			this.isCrouching = true;
		}
		else if (this.crouchExit && !this.crouchPress)
		{
			this.isCrouching = false;
			this.crouchExit = false;
		}
		float num = ((this.isSliding || this.slopeSlideScript.isCrouchSlopeSliding) ? this.slideHeight : (this.isCrouching ? this.crouchHeight : this.standHeight));
		if (this.characterController.height != num)
		{
			this.AdjustHeight(num);
		}
		this.HandleSlide();
	}

	// Token: 0x0600010A RID: 266 RVA: 0x00009364 File Offset: 0x00007564
	private void HandleHeadMovement()
	{
		if (this.camController)
		{
			this.leanCamera.transform.localPosition = this.characterController.center + new Vector3(0f, this.characterController.height / 2f - 2.5f, 0f);
		}
	}

	// Token: 0x0600010B RID: 267 RVA: 0x000093C0 File Offset: 0x000075C0
	private void ApplyFinalMovements()
	{
		if (this.pauseManager.startRound || this.startOfRound)
		{
			this.highFallVfxComponent.startColor = new Color(255f, 255f, 255f, 0f);
		}
		if (this.pauseManager.startRound || this.startOfRound)
		{
			return;
		}
		if (this.playerSpeed < 15f)
		{
			this.activateFallAudio = true;
		}
		if (this.playerSpeed > 15f && this.activateFallAudio)
		{
			this.activateFallAudio = false;
			this.fallAudio.Play();
		}
		if (this.playerSpeed > 15f && this.fallDistance > 4f)
		{
			this.fallAudio.volume = Mathf.Lerp(this.fallAudio.volume, 1f, this.fallAudioLerpSpeed * Time.deltaTime);
		}
		else
		{
			this.fallAudio.volume = Mathf.Lerp(this.fallAudio.volume, 0f, this.fallAudioLerpOutSpeed * Time.deltaTime);
		}
		this.highFallVfxComponent.startColor = new Color(255f, 255f, 255f, (this.rotationX > 20f && this.fallAudio.volume > 0.05f) ? Mathf.Lerp(this.highFallVfxComponent.startColor.a, 255f, 50f * Time.deltaTime) : ((this.rotationX < 20f && this.fallAudio.volume > 0.05f) ? Mathf.Lerp(this.highFallVfxComponent.startColor.a, 0f, 50f * Time.deltaTime) : Mathf.Lerp(this.highFallVfxComponent.startColor.a, 0f, 50f * Time.deltaTime)));
		this.lensDistortion.intensity.value = ((this.rotationX > 20f && this.fallAudio.volume > 0.05f) ? Mathf.Lerp(this.lensDistortion.intensity.value, this.lensDistortionWhenFalling, this.lensDistortionLerpSpeed * Time.deltaTime) : ((this.rotationX < 20f && this.fallAudio.volume > 0.05f) ? Mathf.Lerp(this.lensDistortion.intensity.value, 0f, this.lensDistortionLerpOutSpeed * Time.deltaTime) : Mathf.Lerp(this.lensDistortion.intensity.value, 0f, this.lensDistortionLerpOutSpeed * Time.deltaTime)));
		if (this.fallAudio.volume < 0.03f && this.playerSpeed < 15f)
		{
			this.fallAudio.Stop();
		}
		if (this.prejump && this.characterController.isGrounded && this.moveDirection.y < 0f)
		{
			this.allJumped = true;
			this.slopeSlideJumped = true;
			this.moveDirection.y = this.jumpForce * this.jumpFactor;
			this.animator.Rebind();
			this.networkAnimator.SetTrigger("Jump");
			if (this.IsSliding)
			{
				this.BForce(this.hitPointNormal, (this.move.ReadValue<Vector2>() != Vector2.zero) ? this.jumpSlopeForce : 7f, false, true, this.jumpSlopeDecel, true);
				this.playerCamera.transform.DOPunchRotation(-this.fallShakeForce, this.fallShakeDuration, this.fallShakeVibrato, this.fallShakeElasticity);
			}
			this.PlaySoundServer(10);
			this.slideCancelTimer = 2f;
			this.prejump = false;
			this.prejumpLand = true;
		}
		if (this.slideEnd && this.characterController.isGrounded)
		{
			this.slideEnd = false;
			this.deceleration = 2.5f;
		}
		if (this.moveDirection.y < -3f && !this.characterController.isGrounded)
		{
			this.landBool = true;
		}
		if (this.landBool && this.characterController.isGrounded && !this.IsSliding)
		{
			this.landBool = false;
			if (!this.prejumpLand)
			{
				this.PlaySoundServer(11);
				if (!this.isCrouching && this.landBobPivot.localPosition.y > -0.4f)
				{
					this.landBobPivot.DOLocalMove(this.landBobPivot.localPosition - Vector3.up * this.landBobAmount, this.landBobDuration, false).SetEase(this.landBobEase);
				}
			}
			VerletSpring[] array = this.physCables;
			for (int i = 0; i < array.Length; i++)
			{
				array[i].AddForce(0, Vector3.up * 4.5f);
			}
			Vector3 eulerAngles = this.playerCamera.transform.eulerAngles;
			if (this.fallDistance > this.fallShakeHeight && !this.vault)
			{
				if (this.fallDistance > this.highFallShakeHeight)
				{
					this.playerCamera.transform.DOLocalRotate(this.playerCamera.transform.localEulerAngles - this.highFallShakeForce, this.highFallShakeDuration, RotateMode.Fast);
					this.cameraScript.rotateBack = true;
					this.cameraScript.baseSpeed = this.highFallShakeRecoverSpeed;
					this.PlaySoundServer(21);
					base.StartCoroutine(this.HighCameraShake());
				}
				else
				{
					this.playerCamera.transform.DOPunchRotation(-this.fallShakeForce, this.fallShakeDuration, this.fallShakeVibrato, this.fallShakeElasticity);
				}
			}
			this.footstepTimer = this.GetCurrentOffset;
			this.prejumpLand = false;
			this.fallDistance = 0f;
		}
		if (this.landBobPivot.localPosition.y < -0.02f)
		{
			this.landBobPivot.localPosition = Vector3.Lerp(this.landBobPivot.localPosition, Vector3.zero, this.landRecoverSpeed * Time.deltaTime * (float)(this.isCrouching ? 7 : ((!this.isGrounded) ? 5 : 1)));
		}
		if (this.characterController.isGrounded)
		{
			this.landTimer = 0.3f;
		}
		if (this.groundSphereCast && Vector3.Angle(Vector3.up, this.groundNormal) > 65f && !this.jumpSlope && this.isGrounded)
		{
			this.bforcefinal = Vector3.zero;
			this.forceAdded = Vector3.zero;
		}
		if (this.OnSlopeIce() && this.slopeSlideScript.onIce && !this.jumpSlope)
		{
			this.moveDirection.y = this.moveDirection.y - (this.isCrouching ? (this.crouchGravity * this.slopeForce * 2f) : ((this.moveDirection.y > 0f) ? this.jumpGravity : (this.gravity * this.slopeForce))) * this.gravityMultiplier * Time.deltaTime;
		}
		else if (this.OnSlopeAir() && this.currentInput.magnitude != 0f && !this.jumpSlope)
		{
			this.moveDirection.y = this.moveDirection.y - (this.isCrouching ? (this.crouchGravity * this.slopeForce * 2f) : ((this.moveDirection.y > 0f) ? this.jumpGravity : (this.gravity * this.slopeForce))) * this.gravityMultiplier * Time.deltaTime;
		}
		else if (!this.characterController.isGrounded && this.moveDirection.y > -40f)
		{
			this.moveDirection.y = this.moveDirection.y - (this.isCrouching ? this.crouchGravity : ((this.moveDirection.y > 0f) ? this.jumpGravity : this.gravity)) * this.gravityMultiplier * Time.deltaTime;
		}
		if (!this.pauseManager.startRound)
		{
			this.characterController.Move((this.moveDirection + this.forceAdded + this.bforcefinal + this.moveAdded + this.slopeSlideScript.slopeSlideMove + this.slopeSlideScript.steepSlopeSlideMove + this.objectCollisionMoveDirection + this.customForceFinal) * Time.deltaTime);
		}
		this.jumpSlope = false;
	}

	// Token: 0x0600010C RID: 268 RVA: 0x00009C3E File Offset: 0x00007E3E
	private IEnumerator HighCameraShake()
	{
		yield return new WaitForSeconds(this.highFallShakeRecoverTime);
		this.cameraScript.rotateBack = true;
		yield break;
	}

	// Token: 0x0600010D RID: 269 RVA: 0x00009C50 File Offset: 0x00007E50
	private void HandleCameraController()
	{
		float num = (this.isAiming ? this.zoomFOV : (((this.isSliding && this.isSprinting) || this.slopeSlideScript.isCrouchSlopeSliding) ? this.runSlideFOV : (this.isSliding ? this.slideFOV : ((this.isWalking && this.funcSprint) ? this.runFOV : this.defaultFOV))));
		num = Mathf.Round(num * 100f) * 0.01f;
		if (this.playerCamera.fieldOfView != num)
		{
			this.ChangeFOV(num, this.sprintSlide ? this.runSlideEase : (this.walkSlide ? this.slideEase : (this.isSprinting ? this.runEase : this.zoomEase)), this.sprintSlide ? this.runSlideEaseSpeed : (this.walkSlide ? this.slideEaseSpeed : (this.isSprinting ? this.runEaseSpeed : this.zoomSpeed)));
		}
		float num2 = ((this.isSliding || this.slopeSlideScript.isCrouchSlopeSliding) ? (-this.slideTiltAmount) : ((this.isSprinting && this.move.ReadValue<Vector2>().x > 0f) ? (-this.tiltAmount) : ((this.isSprinting && this.move.ReadValue<Vector2>().x < 0f) ? this.tiltAmount : 0f))) + this.wallSlideLean;
		if (this.camController)
		{
			this.SideTilt(num2);
		}
	}

	// Token: 0x0600010E RID: 270 RVA: 0x00009DE0 File Offset: 0x00007FE0
	private void AdjustHeight(float height)
	{
		float num = height / 2f;
		this.characterController.height = Mathf.Lerp(this.characterController.height, height, this.crouchingSpeed * Time.deltaTime);
		this.characterController.center = Vector3.Lerp(this.characterController.center, new Vector3(0f, num, 0f), this.crouchingSpeed * Time.deltaTime);
	}

	// Token: 0x0600010F RID: 271 RVA: 0x00009E54 File Offset: 0x00008054
	private void ChangeFOV(float fov, Ease easeType, float speed)
	{
		this.playerCamera.DOFieldOfView(fov, speed).SetEase(easeType);
	}

	// Token: 0x06000110 RID: 272 RVA: 0x00009E6C File Offset: 0x0000806C
	private void SideTilt(float temptiltAmount)
	{
		float num = Mathf.Clamp(-this.mouseInput.x * 0.1f * this.rotationAmount, -this.maxRotationAmount, this.maxRotationAmount);
		this.rotationZ = Mathf.Lerp(this.rotationZ, temptiltAmount, this.tiltSpeed * Time.deltaTime) + Mathf.Lerp(base.transform.localRotation.z, num, Time.deltaTime * this.smoothRotation * 2.3f);
	}

	// Token: 0x06000111 RID: 273 RVA: 0x00009EEC File Offset: 0x000080EC
	public void Slide(global::UnityEngine.InputSystem.InputAction.CallbackContext ctx)
	{
		if (this.crouchToggle)
		{
			this.isCrouching = !this.isCrouching;
			if (!this.isCrouching)
			{
				this.EndSlide();
			}
		}
		if (this.move.ReadValue<Vector2>().y < 0f)
		{
			return;
		}
		this.deceleration = 1f;
		if (this.move.ReadValue<Vector2>() != Vector2.zero && this.slideResetTimer < 0f)
		{
			if (this.funcSprint)
			{
				this.slideTimer = 0.8f;
			}
			this.slideResetTimer = this.slideResetTime;
		}
	}

	// Token: 0x06000112 RID: 274 RVA: 0x00009F84 File Offset: 0x00008184
	public void SlideEnd(global::UnityEngine.InputSystem.InputAction.CallbackContext ctx)
	{
		if (this.crouchToggle)
		{
			return;
		}
		this.isSliding = false;
		if (this.forceFactor > 1f)
		{
			this.slideEnd = true;
		}
		this.isCrouching = false;
		this.crouchPress = false;
		this.slideTimer = 0f;
		this.sprintSlide = false;
		this.walkSlide = false;
		if (this.characterController.isGrounded)
		{
			this.slideCancelTimer = 2f;
		}
		if (base.IsOwner && !this.slopeSlideScript.isCrouchSlopeSliding)
		{
			this.legRight.SetActive(false);
			this.legLeft.SetActive(false);
			this.torso.SetActive(false);
		}
	}

	// Token: 0x06000113 RID: 275 RVA: 0x0000A030 File Offset: 0x00008230
	private void EndSlide()
	{
		this.isSliding = false;
		if (this.forceFactor > 1f)
		{
			this.slideEnd = true;
		}
		this.isCrouching = false;
		this.crouchPress = false;
		this.slideTimer = 0f;
		this.sprintSlide = false;
		this.walkSlide = false;
		if (this.characterController.isGrounded)
		{
			this.slideCancelTimer = 2f;
		}
		if (base.IsOwner && !this.slopeSlideScript.isCrouchSlopeSliding)
		{
			this.legRight.SetActive(false);
			this.legLeft.SetActive(false);
			this.torso.SetActive(false);
		}
	}

	// Token: 0x06000114 RID: 276 RVA: 0x0000A0D0 File Offset: 0x000082D0
	[ServerRpc(RunLocally = true)]
	private void ChangeSlideClipServer(int index)
	{
		this.RpcWriter___Server_ChangeSlideClipServer_3316948804(index);
		this.RpcLogic___ChangeSlideClipServer_3316948804(index);
	}

	// Token: 0x06000115 RID: 277 RVA: 0x0000A0E6 File Offset: 0x000082E6
	[ObserversRpc(RunLocally = true, ExcludeOwner = true)]
	private void ChangeSlideClipObservers(int index)
	{
		this.RpcWriter___Observers_ChangeSlideClipObservers_3316948804(index);
		this.RpcLogic___ChangeSlideClipObservers_3316948804(index);
	}

	// Token: 0x06000116 RID: 278 RVA: 0x0000A0FC File Offset: 0x000082FC
	private void HandleSlide()
	{
		if (!this.CanWallJump)
		{
			this.SlideFadeOut();
		}
		if (this.slopeSlideScript.isCrouchSlopeSliding)
		{
			this.legRight.SetActive(true);
			this.legLeft.SetActive(true);
		}
		if (this.slopeSlideScript.slopeSlideTrigger && this.slopeSlideScript.isCrouchSlopeSliding)
		{
			this.slopeSlideScript.slopeSlideTrigger = false;
			base.StartCoroutine(this.ActiveTorso());
		}
		if (!this.isCrouching && this.move.ReadValue<Vector2>() == Vector2.zero)
		{
			return;
		}
		if (this.slideTimer > 0f && this.slideDownRay && this.moveDirection.y < 0f && this.isCrouching && this.isSlideSprinting)
		{
			this.ChangeSlideClipServer(0);
			this.isSliding = true;
			this.slideTimer = 0f;
			this.slideCancelTimer = -1f;
			this.slideAudio.volume = 1f * this.audio.volume;
			if (!this.slopeSlideScript.sprintSlideTrigger)
			{
				this.SlideAudioPlay();
			}
			base.StartCoroutine(this.ActiveTorso());
			if (this.isSprinting)
			{
				this.sprintSlide = true;
				this.walkSlide = false;
			}
			else
			{
				this.sprintSlide = false;
				this.walkSlide = true;
			}
			this.AddHorizontalForce((this.isSprinting ? this.sprintSlideImpulsion : this.walkSlideImpulsion) * base.transform.forward, this.isSprinting ? this.sprintSlideDuration : this.walkSlideDuration);
		}
		if (this.forceFactor < 0.1f && this.isSliding && this.pauseManager.gamepad)
		{
			this.EndSlide();
		}
		if (this.forceFactor == 0f || !this.slideDownRay)
		{
			this.isSliding = false;
			this.sprintSlide = false;
			this.walkSlide = false;
			this.slideCancelTimer = 2f;
			if (base.IsOwner && !this.slopeSlideScript.isCrouchSlopeSliding)
			{
				this.legRight.SetActive(false);
				this.legLeft.SetActive(false);
				this.torso.SetActive(false);
			}
		}
		this.slideTime -= Time.deltaTime;
		if (this.isSliding && this.slideTime < 0f)
		{
			this.slideTime = 0.2f;
			this.SpawnVFX(base.transform.position + base.transform.forward + base.transform.right, Quaternion.identity);
			this.SpawnVFX(base.transform.position + base.transform.forward - base.transform.right, Quaternion.identity);
		}
	}

	// Token: 0x06000117 RID: 279 RVA: 0x0000A3D4 File Offset: 0x000085D4
	private void HandleFootsteps()
	{
		if (!this.characterController.isGrounded || this.isSliding)
		{
			return;
		}
		if (this.currentInputRaw == Vector2.zero || this.playerSpeed < 1f)
		{
			return;
		}
		if (this.isLeaning || this.isCrouching)
		{
			return;
		}
		this.footstepTimer -= Time.deltaTime * this.movementFactor;
		if (this.footstepTimer <= 0f)
		{
			RaycastHit raycastHit;
			if (Physics.Raycast(base.transform.position, Vector3.down, out raycastHit, 3f))
			{
				string tag = raycastHit.collider.tag;
				uint num = <PrivateImplementationDetails>.ComputeStringHash(tag);
				if (num <= 1825421690U)
				{
					if (num <= 1378315797U)
					{
						if (num <= 464173256U)
						{
							if (num != 456440475U)
							{
								if (num == 464173256U)
								{
									if (tag == "Footsteps/Concrete/Dalles")
									{
										this.PlaySoundServer(2);
										goto IL_03B0;
									}
								}
							}
							else if (tag == "Footsteps/Sand")
							{
								this.PlaySoundServer(6);
								goto IL_03B0;
							}
						}
						else if (num != 977466297U)
						{
							if (num == 1378315797U)
							{
								if (tag == "Footsteps/Moquette")
								{
									this.PlaySoundServer(15);
									goto IL_03B0;
								}
							}
						}
						else if (tag == "Footsteps/Concrete/Solide")
						{
							this.PlaySoundServer(3);
							goto IL_03B0;
						}
					}
					else if (num <= 1610969363U)
					{
						if (num != 1429664136U)
						{
							if (num == 1610969363U)
							{
								if (tag == "Footsteps/Concrete/PleinAir")
								{
									this.PlaySoundServer(20);
									goto IL_03B0;
								}
							}
						}
						else if (tag == "Footsteps/Water")
						{
							this.PlaySoundServer(9);
							goto IL_03B0;
						}
					}
					else if (num != 1624496828U)
					{
						if (num == 1825421690U)
						{
							if (tag == "Footsteps/Wood/Creux")
							{
								this.PlaySoundServer(4);
								goto IL_03B0;
							}
						}
					}
					else if (tag == "Footsteps/Concrete/Default")
					{
						this.PlaySoundServer(1);
						goto IL_03B0;
					}
				}
				else if (num <= 2833365437U)
				{
					if (num <= 2251139421U)
					{
						if (num != 2180110808U)
						{
							if (num == 2251139421U)
							{
								if (tag == "Footsteps/Metal/Pipe")
								{
									this.PlaySoundServer(13);
									goto IL_03B0;
								}
							}
						}
						else if (tag == "Footsteps/Dirt")
						{
							this.PlaySoundServer(5);
							goto IL_03B0;
						}
					}
					else if (num != 2400559108U)
					{
						if (num == 2833365437U)
						{
							if (tag == "Footsteps/Metal/Pipe2")
							{
								this.PlaySoundServer(12);
								goto IL_03B0;
							}
						}
					}
					else if (tag == "Footsteps/Matelas")
					{
						this.PlaySoundServer(14);
						goto IL_03B0;
					}
				}
				else if (num <= 3455850406U)
				{
					if (num != 3075599786U)
					{
						if (num == 3455850406U)
						{
							if (tag == "Footsteps/Graviers")
							{
								this.PlaySoundServer(8);
								goto IL_03B0;
							}
						}
					}
					else if (tag == "Footsteps/Wood/Sec")
					{
						this.PlaySoundServer(16);
						goto IL_03B0;
					}
				}
				else if (num != 3492154005U)
				{
					if (num == 3807801418U)
					{
						if (tag == "Footsteps/Metal/Grille")
						{
							this.PlaySoundServer(19);
							goto IL_03B0;
						}
					}
				}
				else if (tag == "Footsteps/Grass")
				{
					this.PlaySoundServer(7);
					goto IL_03B0;
				}
				this.PlaySoundServer(1);
			}
			IL_03B0:
			this.footstepTimer = this.GetCurrentOffset;
		}
	}

	// Token: 0x06000118 RID: 280 RVA: 0x0000A7A0 File Offset: 0x000089A0
	private void HandlePhysicsCasting()
	{
		this.shortfrontRay = Physics.Raycast(base.transform.position - Vector3.up * this.characterController.skinWidth, base.transform.forward, 0.6f);
		this.rightHeadCast = Physics.Raycast(base.transform.position, base.transform.right, 2f, this.landLayer);
		this.leftHeadCast = Physics.Raycast(base.transform.position, -base.transform.right, 2f, this.landLayer);
		if (!this.CanWallJump)
		{
			this.wallSlideLean = 0f;
		}
		RaycastHit raycastHit;
		bool flag = Physics.Raycast(base.transform.position, -Vector3.up, out raycastHit, 8f, this.landLayer);
		this.groundNormal = raycastHit.normal;
		if (flag)
		{
			this.groundTag = raycastHit.transform.gameObject.name;
		}
		RaycastHit raycastHit2;
		if (Physics.Raycast(base.transform.position, base.transform.forward, out raycastHit2, 1.5f))
		{
			if (Vector3.Angle(raycastHit2.normal, Vector3.up) > 88f)
			{
				this.vaulRayDown = true;
			}
			else
			{
				this.vaulRayDown = false;
			}
		}
		else
		{
			this.vaulRayDown = false;
		}
		this.groundSphereCast = Physics.CheckSphere(base.transform.position, 0.28f, this.landLayer);
		RaycastHit raycastHit3;
		if (Physics.Raycast(base.transform.position, base.transform.forward, out raycastHit3, 1.5f))
		{
			if (Vector3.Angle(raycastHit3.normal, Vector3.up) > 65f && this.IsSliding)
			{
				this.steepSlideForward = true;
			}
			else
			{
				this.steepSlideForward = false;
			}
		}
		else
		{
			this.steepSlideForward = false;
		}
		RaycastHit raycastHit4;
		if (Physics.Raycast(base.transform.position, -base.transform.forward, out raycastHit4, 1.5f))
		{
			if (Vector3.Angle(raycastHit4.normal, Vector3.up) > 65f && this.IsSliding)
			{
				this.steepSlideBackward = true;
			}
			else
			{
				this.steepSlideBackward = false;
			}
		}
		else
		{
			this.steepSlideBackward = false;
		}
		this.ladderColliders = Physics.OverlapSphere(base.transform.position + new Vector3(0f, this.ladderDetectionLength, 0f), this.ladderDetectionLength, this.ladderLayer);
		this.ladderRay = this.ladderColliders.Length != 0;
		RaycastHit raycastHit5;
		if (Physics.Raycast(base.transform.position + Vector3.up, base.transform.forward, out raycastHit5, 1.5f))
		{
			if (Vector3.Angle(raycastHit5.normal, Vector3.up) > 88f)
			{
				this.vaulRayUp = true;
			}
			else
			{
				this.vaulRayUp = false;
			}
		}
		else
		{
			this.vaulRayUp = false;
		}
		this.vaultRayUpShort = Physics.Raycast(base.transform.position + Vector3.up / 2f, base.transform.forward, 1.3f);
		this.downRay = Physics.Raycast(base.transform.position, -base.transform.up, 0.3f);
		this.animDownRay = Physics.Raycast(base.transform.position, -base.transform.up, 0.6f);
		this.slideDownRay = Physics.Raycast(base.transform.position, -base.transform.up, 0.8f);
		this.upSphere = Physics.Raycast(base.transform.position + new Vector3(0f, this.characterController.height, 0f), base.transform.up, 0.25f, this.landLayer);
	}

	// Token: 0x06000119 RID: 281 RVA: 0x0000ABB0 File Offset: 0x00008DB0
	private void HandleLadders()
	{
		this.HandleLadderSounds();
		if (this.ladderRay && !this.isCrouching)
		{
			if (this.move.ReadValue<Vector2>().magnitude > 0f || this.moveUp.ReadValue<float>() > 0.1f)
			{
				this.onLadder = true;
				this.moveDirection.y = this.ladderSpeed;
			}
			else
			{
				this.onLadder = false;
			}
		}
		else
		{
			this.onLadder = false;
		}
		if (this.isCrouching)
		{
			this.onLadder = false;
		}
		if (this.onLadder && Physics.Raycast(base.transform.position, base.transform.forward, 1f, this.ladderLayer) && this.move.ReadValue<Vector2>().y < 0f)
		{
			this.BForce(-base.transform.forward, 7f, false, true, 3f, true);
		}
		if (this.onLadder && Physics.Raycast(base.transform.position, -base.transform.forward, 1f, this.ladderLayer) && this.move.ReadValue<Vector2>().y < 0f)
		{
			this.BForce(base.transform.forward, 7f, false, true, 3f, true);
		}
	}

	// Token: 0x0600011A RID: 282 RVA: 0x0000AD18 File Offset: 0x00008F18
	private void HandleLadderSounds()
	{
		if (!this.onLadder)
		{
			return;
		}
		if (this.ladderColliders.Length == 0)
		{
			return;
		}
		this.ladderStepTimer -= Time.deltaTime;
		if (this.ladderStepTimer <= 0f)
		{
			string tag = this.ladderColliders[0].tag;
			if (!(tag == "Ladder/Metal"))
			{
				if (!(tag == "Ladder/Chain"))
				{
					this.PlaySoundServer(17);
				}
				else
				{
					this.PlaySoundServer(18);
				}
			}
			else
			{
				this.PlaySoundServer(17);
			}
			this.ladderStepTimer = this.ladderStep;
		}
	}

	// Token: 0x0600011B RID: 283 RVA: 0x0000ADAC File Offset: 0x00008FAC
	public void AddForce(Vector3 tempforce, float tempforceFactor)
	{
		this.deceleration = 1f;
		this.moveDirection = new Vector3(0f, 0f, 0f);
		this.forceFactor = tempforceFactor;
		this.force = tempforce;
		this.moveDirection.y = this.force.y * this.forceFactor;
		this.tempforceSpeed = this.forceSpeed;
		this.initforceFactor = tempforceFactor;
	}

	// Token: 0x0600011C RID: 284 RVA: 0x0000AE1C File Offset: 0x0000901C
	public void AddAccumulatedForce(Vector3 tempforce, float tempforceFactor)
	{
		this.deceleration = 1f;
		this.forceFactor = tempforceFactor;
		this.force = tempforce;
		this.moveDirection.y = this.force.y * this.forceFactor;
		this.tempforceSpeed = this.forceSpeed;
		this.initforceFactor = tempforceFactor;
	}

	// Token: 0x0600011D RID: 285 RVA: 0x0000AE74 File Offset: 0x00009074
	public void AddHorizontalForce(Vector3 tempforce, float tempforceFactor)
	{
		this.deceleration = 1f;
		this.moveDirection = new Vector3(0f, this.moveDirection.y, 0f);
		this.forceFactor = tempforceFactor;
		this.force = tempforce;
		this.tempforceSpeed = this.forceSpeed;
		this.initforceFactor = tempforceFactor;
	}

	// Token: 0x0600011E RID: 286 RVA: 0x0000AED0 File Offset: 0x000090D0
	public void AddVerticalForce(Vector3 tempforce, float tempforceFactor)
	{
		this.deceleration = 1f;
		this.moveDirection = new Vector3(this.moveDirection.x, -1f, this.moveDirection.z);
		this.forceFactor = tempforceFactor;
		this.force = tempforce;
		this.moveDirection.y = this.force.y * this.forceFactor;
		this.tempforceSpeed = this.forceSpeed;
		this.initforceFactor = tempforceFactor;
	}

	// Token: 0x0600011F RID: 287 RVA: 0x0000AF4C File Offset: 0x0000914C
	private void HandleAddingForce()
	{
		if (this.initforceFactor > 20f && this.tempforceSpeed < 10f)
		{
			this.tempforceSpeed = 10f;
		}
		else if (this.tempforceSpeed < 2f)
		{
			this.tempforceSpeed = 2f;
		}
		if (!this.characterController.isGrounded)
		{
			if (this.forceFactor < 0f)
			{
				this.forceFactor = 0f;
			}
			if (this.forceFactor > 0f)
			{
				this.tempforceSpeed -= Time.deltaTime * this.initforceFactor / 2f;
			}
		}
		else
		{
			this.tempforceSpeed = this.forceSpeed;
		}
		this.forceFactor -= Time.deltaTime * (this.tempforceSpeed * this.deceleration);
		if (this.forceFactor > 0f)
		{
			this.addingForce = true;
			this.forceAdded = this.force * this.forceFactor;
		}
		else
		{
			this.addingForce = false;
			this.forceAdded = new Vector3(0f, 0f, 0f);
			this.forceFactor = 0f;
			this.force = new Vector3(0f, 0f, 0f);
		}
		if (this.forceFactor <= 1f)
		{
			this.deceleration = 1f;
		}
	}

	// Token: 0x06000120 RID: 288 RVA: 0x0000B0A2 File Offset: 0x000092A2
	public void CustomAddForce(Vector3 dir, float force)
	{
		this.customForceScript.AddForce(dir, force);
	}

	// Token: 0x06000121 RID: 289 RVA: 0x0000B0B4 File Offset: 0x000092B4
	public void BForce(Vector3 dir, float factor, bool vertical, bool stopOnGround, float decel, bool aircontrol)
	{
		this.bgroundStop = false;
		this.bstop = false;
		this.btimer = 0f;
		this.bdirection = dir;
		this.bfactor = factor;
		this.bdecel = decel;
		this.bgroundStop = stopOnGround;
		this.baircontrol = aircontrol;
		if (vertical)
		{
			this.moveDirection.y = dir.y * factor;
		}
	}

	// Token: 0x06000122 RID: 290 RVA: 0x0000B118 File Offset: 0x00009318
	private void HandleBForce()
	{
		if (this.bfactor > 0f)
		{
			this.bforcefinal = new Vector3(this.bdirection.x, 0f, this.bdirection.z) * this.bfactor;
			this.bfactor -= Time.deltaTime * this.bdecel;
		}
		if (this.bfactor < 0f)
		{
			this.bforcefinal = Vector3.zero;
		}
		if (this.bfactor <= 0.3f)
		{
			this.baircontrol = true;
		}
		if (this.bstop)
		{
			this.bstop = false;
			this.bdirection = Vector3.zero;
			this.bfactor = 0f;
			this.bforcefinal = Vector3.zero;
		}
		if (this.bgroundStop && this.characterController.isGrounded && this.btimer > 0.5f)
		{
			this.baircontrol = true;
			this.bdirection = Vector3.zero;
			this.bfactor = 0f;
			this.bforcefinal = Vector3.zero;
			this.bgroundStop = false;
		}
		this.btimer += Time.deltaTime;
	}

	// Token: 0x06000123 RID: 291 RVA: 0x0000B23C File Offset: 0x0000943C
	private void HandleTimers()
	{
		this.aftervaultjumpTimer -= Time.deltaTime;
		this.slideTimer -= Time.deltaTime;
		this.slideResetTimer -= Time.deltaTime;
		this.slideCancelTimer -= Time.deltaTime;
		this.coyoteTimer -= Time.deltaTime;
		this.landTimer -= Time.deltaTime;
		this.settings.timeSpentInGame += Time.deltaTime;
	}

	// Token: 0x06000124 RID: 292 RVA: 0x0000B2CC File Offset: 0x000094CC
	private void CheckForVault()
	{
		this.aftervaultjumpTimer -= Time.deltaTime;
		if (this.isGrounded)
		{
			this.vaultActivate = true;
		}
		if (this.safeGrounded || this.isCrouching)
		{
			return;
		}
		RaycastHit raycastHit;
		bool flag = Physics.Raycast(base.transform.position, base.transform.forward, out raycastHit, 1.4f, this.landLayer) && !this.isGrounded;
		bool flag2 = Physics.Raycast(base.transform.position + Vector3.up * 1.2f, base.transform.forward, 1.5f, this.landLayer);
		bool flag3 = Physics.Raycast(base.transform.position + Vector3.up * 1.8f, base.transform.forward, 2f, this.landLayer);
		bool flag4 = Physics.Raycast(base.transform.position, -Vector3.up, 1f, this.landLayer);
		if (flag && !flag2 && !flag3 && Vector3.Angle(raycastHit.normal, Vector3.up) > this.vaultMinimumAngle && Vector3.Angle(raycastHit.normal, Vector3.up) < 130f && this.vaultActivate && this.move.ReadValue<Vector2>().y > 0f && (this.moveDirection.y <= 0f || !flag4))
		{
			this.jumped = false;
			this.slopeSlideJumped = false;
			this.vaultActivate = false;
			this.aftervaultjumpTimer = 0.5f;
			this.playerCamera.transform.DOPunchRotation(new Vector3(6f, 0f, 3f), 0.45f, 0, 3f);
			this.vault = true;
			this.moveDirection.y = 9f;
			this.BForce(base.transform.forward, 1.5f, false, true, 3f, true);
			this.VaultClip(raycastHit.transform.tag);
			base.StartCoroutine(this.DeactivateVault());
		}
	}

	// Token: 0x06000125 RID: 293 RVA: 0x0000B520 File Offset: 0x00009720
	private IEnumerator DeactivateVault()
	{
		yield return new WaitForSeconds(0.15f);
		this.cameraScript.rotateBack = true;
		if (this.downRay && !this.jumped)
		{
			this.moveDirection.y = -5f;
		}
		else if (!this.jumped)
		{
			this.moveDirection.y = 0f;
		}
		base.StartCoroutine(this.RotateBackAfterVault());
		yield break;
	}

	// Token: 0x06000126 RID: 294 RVA: 0x0000B52F File Offset: 0x0000972F
	private IEnumerator RotateBackAfterVault()
	{
		yield return new WaitForSeconds(0.3f);
		this.cameraScript.rotateBack = true;
		this.vault = false;
		yield break;
	}

	// Token: 0x06000127 RID: 295 RVA: 0x000023D6 File Offset: 0x000005D6
	private void OnGUI()
	{
	}

	// Token: 0x06000128 RID: 296 RVA: 0x0000B53E File Offset: 0x0000973E
	private void OnDrawGizmos()
	{
		Gizmos.color = Color.red;
	}

	// Token: 0x06000129 RID: 297 RVA: 0x0000B54C File Offset: 0x0000974C
	private void SlideFadeOut()
	{
		if (this.slideCancelTimer > 0f)
		{
			this.slideAudio.volume = Mathf.Lerp(this.slideAudio.volume, 0f, 5f * Time.deltaTime);
		}
		if (this.slideAudio.volume < 0.03f)
		{
			this.SlideAudioStop();
		}
	}

	// Token: 0x0600012A RID: 298 RVA: 0x0000B5A9 File Offset: 0x000097A9
	[ServerRpc]
	private void SlideAudioPlay()
	{
		this.RpcWriter___Server_SlideAudioPlay_2166136261();
	}

	// Token: 0x0600012B RID: 299 RVA: 0x0000B5B1 File Offset: 0x000097B1
	[ServerRpc]
	private void SlideAudioStop()
	{
		this.RpcWriter___Server_SlideAudioStop_2166136261();
	}

	// Token: 0x0600012C RID: 300 RVA: 0x0000B5B9 File Offset: 0x000097B9
	[ServerRpc(RunLocally = true)]
	private void PlaySoundServer(int clip)
	{
		this.RpcWriter___Server_PlaySoundServer_3316948804(clip);
		this.RpcLogic___PlaySoundServer_3316948804(clip);
	}

	// Token: 0x0600012D RID: 301 RVA: 0x0000B5D0 File Offset: 0x000097D0
	[ObserversRpc(RunLocally = true, ExcludeOwner = true)]
	private void PlaySoundObservers(int clip)
	{
		this.RpcWriter___Observers_PlaySoundObservers_3316948804(clip);
		this.RpcLogic___PlaySoundObservers_3316948804(clip);
	}

	// Token: 0x0600012E RID: 302 RVA: 0x0000B5F1 File Offset: 0x000097F1
	private void JumpClip()
	{
		this.JumpClipObservers();
	}

	// Token: 0x0600012F RID: 303 RVA: 0x0000B5FC File Offset: 0x000097FC
	private void JumpClipObservers()
	{
		RaycastHit raycastHit;
		if (Physics.Raycast(base.transform.position, Vector3.down, out raycastHit, 3f))
		{
			string tag = raycastHit.collider.tag;
			uint num = <PrivateImplementationDetails>.ComputeStringHash(tag);
			if (num <= 1825421690U)
			{
				if (num <= 1378315797U)
				{
					if (num <= 464173256U)
					{
						if (num != 456440475U)
						{
							if (num == 464173256U)
							{
								if (tag == "Footsteps/Concrete/Dalles")
								{
									this.audio.PlayOneShot(this.dallesClips[Mathf.RoundToInt((float)global::UnityEngine.Random.Range(0, this.dallesClips.Length))]);
									return;
								}
							}
						}
						else if (tag == "Footsteps/Sand")
						{
							this.audio.PlayOneShot(this.sandClips[Mathf.RoundToInt((float)global::UnityEngine.Random.Range(0, this.sandClips.Length))]);
							return;
						}
					}
					else if (num != 977466297U)
					{
						if (num == 1378315797U)
						{
							if (tag == "Footsteps/Moquette")
							{
								this.audio.PlayOneShot(this.moquetteClips[Mathf.RoundToInt((float)global::UnityEngine.Random.Range(0, this.moquetteClips.Length))]);
								return;
							}
						}
					}
					else if (tag == "Footsteps/Concrete/Solide")
					{
						this.audio.PlayOneShot(this.betonSolideClips[Mathf.RoundToInt((float)global::UnityEngine.Random.Range(0, this.betonSolideClips.Length))]);
						return;
					}
				}
				else if (num <= 1610969363U)
				{
					if (num != 1429664136U)
					{
						if (num == 1610969363U)
						{
							if (tag == "Footsteps/Concrete/PleinAir")
							{
								this.audio.PlayOneShot(this.betonPleinairClips[Mathf.RoundToInt((float)global::UnityEngine.Random.Range(0, this.betonPleinairClips.Length))]);
								return;
							}
						}
					}
					else if (tag == "Footsteps/Water")
					{
						this.audio.PlayOneShot(this.waterClips[Mathf.RoundToInt((float)global::UnityEngine.Random.Range(0, this.waterClips.Length))]);
						return;
					}
				}
				else if (num != 1624496828U)
				{
					if (num == 1825421690U)
					{
						if (tag == "Footsteps/Wood/Creux")
						{
							this.audio.PlayOneShot(this.woodClips[Mathf.RoundToInt((float)global::UnityEngine.Random.Range(0, this.woodClips.Length))]);
							return;
						}
					}
				}
				else if (tag == "Footsteps/Concrete/Default")
				{
					this.audio.PlayOneShot(this.concreteClips[Mathf.RoundToInt((float)global::UnityEngine.Random.Range(0, this.concreteClips.Length))]);
					return;
				}
			}
			else if (num <= 2833365437U)
			{
				if (num <= 2251139421U)
				{
					if (num != 2180110808U)
					{
						if (num == 2251139421U)
						{
							if (tag == "Footsteps/Metal/Pipe")
							{
								this.audio.PlayOneShot(this.pipeClips[Mathf.RoundToInt((float)global::UnityEngine.Random.Range(0, this.pipeClips.Length))]);
								return;
							}
						}
					}
					else if (tag == "Footsteps/Dirt")
					{
						this.audio.PlayOneShot(this.dirtClips[Mathf.RoundToInt((float)global::UnityEngine.Random.Range(0, this.dirtClips.Length))]);
						return;
					}
				}
				else if (num != 2400559108U)
				{
					if (num == 2833365437U)
					{
						if (tag == "Footsteps/Metal/Pipe2")
						{
							this.audio.PlayOneShot(this.metalClips[Mathf.RoundToInt((float)global::UnityEngine.Random.Range(0, this.metalClips.Length))]);
							return;
						}
					}
				}
				else if (tag == "Footsteps/Matelas")
				{
					this.audio.PlayOneShot(this.matelasClips[Mathf.RoundToInt((float)global::UnityEngine.Random.Range(0, this.matelasClips.Length))]);
					return;
				}
			}
			else if (num <= 3455850406U)
			{
				if (num != 3075599786U)
				{
					if (num == 3455850406U)
					{
						if (tag == "Footsteps/Graviers")
						{
							this.audio.PlayOneShot(this.graviersClips[Mathf.RoundToInt((float)global::UnityEngine.Random.Range(0, this.graviersClips.Length))]);
							return;
						}
					}
				}
				else if (tag == "Footsteps/Wood/Sec")
				{
					this.audio.PlayOneShot(this.woodSecClips[Mathf.RoundToInt((float)global::UnityEngine.Random.Range(0, this.woodSecClips.Length))]);
					return;
				}
			}
			else if (num != 3492154005U)
			{
				if (num == 3807801418U)
				{
					if (tag == "Footsteps/Metal/Grille")
					{
						this.audio.PlayOneShot(this.grilleClips[Mathf.RoundToInt((float)global::UnityEngine.Random.Range(0, this.grilleClips.Length))]);
						return;
					}
				}
			}
			else if (tag == "Footsteps/Grass")
			{
				this.audio.PlayOneShot(this.grassClips[Mathf.RoundToInt((float)global::UnityEngine.Random.Range(0, this.grassClips.Length))]);
				return;
			}
			this.audio.PlayOneShot(this.concreteClips[Mathf.RoundToInt((float)global::UnityEngine.Random.Range(0, this.concreteClips.Length))]);
			return;
		}
		this.audio.PlayOneShot(this.jumpClip);
	}

	// Token: 0x06000130 RID: 304 RVA: 0x0000BB49 File Offset: 0x00009D49
	[ServerRpc(RunLocally = true)]
	private void WallJumpClip(string walltag)
	{
		this.RpcWriter___Server_WallJumpClip_3615296227(walltag);
		this.RpcLogic___WallJumpClip_3615296227(walltag);
	}

	// Token: 0x06000131 RID: 305 RVA: 0x0000BB60 File Offset: 0x00009D60
	[ObserversRpc(RunLocally = true, ExcludeOwner = true)]
	private void JumpClipObservers(string walltag)
	{
		this.RpcWriter___Observers_JumpClipObservers_3615296227(walltag);
		this.RpcLogic___JumpClipObservers_3615296227(walltag);
	}

	// Token: 0x06000132 RID: 306 RVA: 0x0000BB81 File Offset: 0x00009D81
	[ServerRpc(RunLocally = true)]
	private void VaultClip(string surface)
	{
		this.RpcWriter___Server_VaultClip_3615296227(surface);
		this.RpcLogic___VaultClip_3615296227(surface);
	}

	// Token: 0x06000133 RID: 307 RVA: 0x0000BB98 File Offset: 0x00009D98
	[ObserversRpc(RunLocally = true, ExcludeOwner = true)]
	private void VaultClipObservers(string surface)
	{
		this.RpcWriter___Observers_VaultClipObservers_3615296227(surface);
		this.RpcLogic___VaultClipObservers_3615296227(surface);
	}

	// Token: 0x06000134 RID: 308 RVA: 0x0000BBB9 File Offset: 0x00009DB9
	[ServerRpc(RunLocally = true)]
	private void SpawnVFX(Vector3 position, Quaternion rotation)
	{
		this.RpcWriter___Server_SpawnVFX_3848837105(position, rotation);
		this.RpcLogic___SpawnVFX_3848837105(position, rotation);
	}

	// Token: 0x06000135 RID: 309 RVA: 0x0000BBD7 File Offset: 0x00009DD7
	[ObserversRpc(RunLocally = true, ExcludeOwner = true)]
	private void SpawnVFXObservers(Vector3 position, Quaternion rotation)
	{
		this.RpcWriter___Observers_SpawnVFXObservers_3848837105(position, rotation);
		this.RpcLogic___SpawnVFXObservers_3848837105(position, rotation);
	}

	// Token: 0x06000136 RID: 310 RVA: 0x0000BBF5 File Offset: 0x00009DF5
	private void roundStartEvent()
	{
		this.roundHasStarted = true;
	}

	// Token: 0x06000137 RID: 311 RVA: 0x0000BC00 File Offset: 0x00009E00
	private void OnTriggerEnter(Collider col)
	{
		if (!this.roundHasStarted)
		{
			return;
		}
		if (col.CompareTag("Teleport"))
		{
			Teleporter component = col.GetComponent<Teleporter>();
			Transform teleportPoint = component.teleportPoint;
			this.Teleport(teleportPoint.position, component.anglesDifference, true, teleportPoint, component.propulsionPower, component.propulsionDecel, component.dontTranslateRotation);
		}
		if (col.CompareTag("Killz"))
		{
			Settings.Instance.IncreaseSuicidesAmount();
			base.GetComponent<PlayerHealth>().fellVoid = true;
			this.DespawnObject(base.gameObject);
		}
	}

	// Token: 0x06000138 RID: 312 RVA: 0x0000BC88 File Offset: 0x00009E88
	private void OnTriggerStay(Collider col)
	{
		if (col.CompareTag("MovingObject") && !this.onMovingPlatform && this.isGrounded)
		{
			this.networkObject.SetParent(col.transform.GetComponentInParent<NetworkBehaviour>());
			this.objectCollisionMoveDirection = base.transform.forward * 0.01f;
			this.onMovingPlatform = true;
			this.CmdChangeRootMotion(false);
		}
	}

	// Token: 0x06000139 RID: 313 RVA: 0x0000BCF4 File Offset: 0x00009EF4
	private void OnTriggerExit(Collider col)
	{
		if (col.CompareTag("MovingObject") && this.onMovingPlatform)
		{
			this.networkObject.UnsetParent();
			if (col.transform.GetComponentInParent<MovingPlatformParent>().doesEject)
			{
				this.CustomAddForce(col.transform.GetComponentInParent<MovingPlatformParent>().movingVector, this.movingPlatformEjectForce);
			}
			this.objectCollisionMoveDirection = Vector3.zero;
			this.onMovingPlatform = false;
			this.CmdChangeRootMotion(true);
		}
	}

	// Token: 0x0600013A RID: 314 RVA: 0x0000BD68 File Offset: 0x00009F68
	public void SetNetworkParent(bool set, Transform t)
	{
		if (!set)
		{
			this.networkObject.UnsetParent();
			return;
		}
		this.networkObject.SetParent(t.GetComponent<NetworkBehaviour>());
	}

	// Token: 0x0600013B RID: 315 RVA: 0x0000BD8A File Offset: 0x00009F8A
	[ServerRpc]
	public void DespawnObject(GameObject obj)
	{
		this.RpcWriter___Server_DespawnObject_1934289915(obj);
	}

	// Token: 0x0600013C RID: 316 RVA: 0x0000BD98 File Offset: 0x00009F98
	[ObserversRpc]
	private void ExplodeOnDeath()
	{
		this.RpcWriter___Observers_ExplodeOnDeath_2166136261();
	}

	// Token: 0x0600013D RID: 317 RVA: 0x0000BDAC File Offset: 0x00009FAC
	public void Teleport(Vector3 position, float angle, bool boost, Transform cac, float power, float decel, bool dontTranslateRotation)
	{
		base.transform.position = position;
		if (!dontTranslateRotation)
		{
			base.transform.eulerAngles -= new Vector3(0f, angle - 180f, 0f);
		}
		if (boost)
		{
			this.BForce(cac.forward, power, true, false, decel, true);
		}
		if (!dontTranslateRotation)
		{
			this.dirForward = base.transform.TransformDirection(Vector3.forward);
			this.dirRight = base.transform.TransformDirection(Vector3.right);
		}
		this.PlaySoundServer(23);
	}

	// Token: 0x0600013E RID: 318 RVA: 0x0000BE44 File Offset: 0x0000A044
	public void KillShockWave()
	{
		Settings.Instance.IncreaseKillsAmount();
		this.lensDistortion.intensity.value = this.killShockWaveStrength;
		this.colorGrading.saturation.value = -100f;
	}

	// Token: 0x0600013F RID: 319 RVA: 0x0000BE7B File Offset: 0x0000A07B
	[ServerRpc(RunLocally = true)]
	public void BreakGlassServer(Vector3 hitPoint, Vector3 direction, GameObject obj)
	{
		this.RpcWriter___Server_BreakGlassServer_4203392553(hitPoint, direction, obj);
		this.RpcLogic___BreakGlassServer_4203392553(hitPoint, direction, obj);
	}

	// Token: 0x06000140 RID: 320 RVA: 0x0000BEA1 File Offset: 0x0000A0A1
	[ObserversRpc(RunLocally = true, ExcludeOwner = true)]
	private void BreakGlassObservers(Vector3 hitPoint, Vector3 direction, GameObject obj)
	{
		this.RpcWriter___Observers_BreakGlassObservers_4203392553(hitPoint, direction, obj);
		this.RpcLogic___BreakGlassObservers_4203392553(hitPoint, direction, obj);
	}

	// Token: 0x06000141 RID: 321 RVA: 0x0000BEC8 File Offset: 0x0000A0C8
	public void Shuffle(AudioClip[] texts)
	{
		AudioClip audioClip = texts[0];
		int num = global::UnityEngine.Random.Range(1, texts.Length);
		texts[0] = texts[num];
		texts[num] = audioClip;
	}

	// Token: 0x06000142 RID: 322 RVA: 0x0000BEED File Offset: 0x0000A0ED
	[ServerRpc]
	public void PlayVoiceChat(byte[] data)
	{
		this.RpcWriter___Server_PlayVoiceChat_3345084894(data);
	}

	// Token: 0x06000143 RID: 323 RVA: 0x0000BEF9 File Offset: 0x0000A0F9
	[ObserversRpc]
	public void PlayVoiceChatObservers(byte[] data)
	{
		this.RpcWriter___Observers_PlayVoiceChatObservers_3345084894(data);
	}

	// Token: 0x06000144 RID: 324 RVA: 0x0000BF08 File Offset: 0x0000A108
	private void VoiceChat()
	{
		if (this.pauseManager.chatting)
		{
			return;
		}
		if (this.record.ReadValue<float>() > 0.1f || !Settings.Instance.pushToTalk)
		{
			if (Settings.Instance.enableVoiceChat)
			{
				this.vstreamRecorder.IsRecording = true;
				this.pauseManager.isRecording = true;
				return;
			}
		}
		else
		{
			this.vstreamRecorder.IsRecording = false;
			this.pauseManager.isRecording = false;
		}
	}

	// Token: 0x06000148 RID: 328 RVA: 0x0000C42C File Offset: 0x0000A62C
	public virtual void NetworkInitialize___Early()
	{
		if (this.NetworkInitializeEarly_FirstPersonController_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeEarly_FirstPersonController_Assembly-CSharp.dll = true;
		this.syncVar___canMove = new SyncVar<bool>(this, 0U, WritePermission.ClientUnsynchronized, ReadPermission.Observers, -1f, Channel.Reliable, this.canMove);
		base.RegisterServerRpc(0U, new ServerRpcDelegate(this.RpcReader___Server_CmdChangeRootMotion_1140765316));
		base.RegisterObserversRpc(1U, new ClientRpcDelegate(this.RpcReader___Observers_ChangeRootMotion_1140765316));
		base.RegisterServerRpc(2U, new ServerRpcDelegate(this.RpcReader___Server_AboubiPlayServer_3316948804));
		base.RegisterObserversRpc(3U, new ClientRpcDelegate(this.RpcReader___Observers_AboubiPlayObservers_3316948804));
		base.RegisterServerRpc(4U, new ServerRpcDelegate(this.RpcReader___Server_ChangeSlideClipServer_3316948804));
		base.RegisterObserversRpc(5U, new ClientRpcDelegate(this.RpcReader___Observers_ChangeSlideClipObservers_3316948804));
		base.RegisterServerRpc(6U, new ServerRpcDelegate(this.RpcReader___Server_SlideAudioPlay_2166136261));
		base.RegisterServerRpc(7U, new ServerRpcDelegate(this.RpcReader___Server_SlideAudioStop_2166136261));
		base.RegisterServerRpc(8U, new ServerRpcDelegate(this.RpcReader___Server_PlaySoundServer_3316948804));
		base.RegisterObserversRpc(9U, new ClientRpcDelegate(this.RpcReader___Observers_PlaySoundObservers_3316948804));
		base.RegisterServerRpc(10U, new ServerRpcDelegate(this.RpcReader___Server_WallJumpClip_3615296227));
		base.RegisterObserversRpc(11U, new ClientRpcDelegate(this.RpcReader___Observers_JumpClipObservers_3615296227));
		base.RegisterServerRpc(12U, new ServerRpcDelegate(this.RpcReader___Server_VaultClip_3615296227));
		base.RegisterObserversRpc(13U, new ClientRpcDelegate(this.RpcReader___Observers_VaultClipObservers_3615296227));
		base.RegisterServerRpc(14U, new ServerRpcDelegate(this.RpcReader___Server_SpawnVFX_3848837105));
		base.RegisterObserversRpc(15U, new ClientRpcDelegate(this.RpcReader___Observers_SpawnVFXObservers_3848837105));
		base.RegisterServerRpc(16U, new ServerRpcDelegate(this.RpcReader___Server_DespawnObject_1934289915));
		base.RegisterObserversRpc(17U, new ClientRpcDelegate(this.RpcReader___Observers_ExplodeOnDeath_2166136261));
		base.RegisterServerRpc(18U, new ServerRpcDelegate(this.RpcReader___Server_BreakGlassServer_4203392553));
		base.RegisterObserversRpc(19U, new ClientRpcDelegate(this.RpcReader___Observers_BreakGlassObservers_4203392553));
		base.RegisterServerRpc(20U, new ServerRpcDelegate(this.RpcReader___Server_PlayVoiceChat_3345084894));
		base.RegisterObserversRpc(21U, new ClientRpcDelegate(this.RpcReader___Observers_PlayVoiceChatObservers_3345084894));
		base.RegisterSyncVarRead(new SyncVarReadDelegate(this.ReadSyncVar___FirstPersonController));
	}

	// Token: 0x06000149 RID: 329 RVA: 0x0000C681 File Offset: 0x0000A881
	public virtual void NetworkInitialize__Late()
	{
		if (this.NetworkInitializeLate_FirstPersonController_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeLate_FirstPersonController_Assembly-CSharp.dll = true;
		this.syncVar___canMove.SetRegistered();
	}

	// Token: 0x0600014A RID: 330 RVA: 0x0000C69F File Offset: 0x0000A89F
	public override void NetworkInitializeIfDisabled()
	{
		this.NetworkInitialize___Early();
		this.NetworkInitialize__Late();
	}

	// Token: 0x0600014B RID: 331 RVA: 0x0000C6B0 File Offset: 0x0000A8B0
	private void RpcWriter___Server_CmdChangeRootMotion_1140765316(bool istrue)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteBoolean(istrue);
		base.SendServerRpc(0U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x0600014C RID: 332 RVA: 0x0000C7B1 File Offset: 0x0000A9B1
	public void RpcLogic___CmdChangeRootMotion_1140765316(bool istrue)
	{
		this.ChangeRootMotion(istrue);
	}

	// Token: 0x0600014D RID: 333 RVA: 0x0000C7BC File Offset: 0x0000A9BC
	private void RpcReader___Server_CmdChangeRootMotion_1140765316(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		bool flag = PooledReader0.ReadBoolean();
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___CmdChangeRootMotion_1140765316(flag);
	}

	// Token: 0x0600014E RID: 334 RVA: 0x0000C80C File Offset: 0x0000AA0C
	private void RpcWriter___Observers_ChangeRootMotion_1140765316(bool istrue)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteBoolean(istrue);
		base.SendObserversRpc(1U, writer, channel, DataOrderType.Default, false, false, true);
		writer.Store();
	}

	// Token: 0x0600014F RID: 335 RVA: 0x0000C8C2 File Offset: 0x0000AAC2
	private void RpcLogic___ChangeRootMotion_1140765316(bool istrue)
	{
		this.animator.applyRootMotion = istrue;
	}

	// Token: 0x06000150 RID: 336 RVA: 0x0000C8D0 File Offset: 0x0000AAD0
	private void RpcReader___Observers_ChangeRootMotion_1140765316(PooledReader PooledReader0, Channel channel)
	{
		bool flag = PooledReader0.ReadBoolean();
		if (!base.IsClient)
		{
			return;
		}
		if (base.IsHost)
		{
			return;
		}
		this.RpcLogic___ChangeRootMotion_1140765316(flag);
	}

	// Token: 0x06000151 RID: 337 RVA: 0x0000C90C File Offset: 0x0000AB0C
	private void RpcWriter___Server_AboubiPlayServer_3316948804(int clip)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteInt32(clip, AutoPackType.Packed);
		base.SendServerRpc(2U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000152 RID: 338 RVA: 0x0000CA12 File Offset: 0x0000AC12
	private void RpcLogic___AboubiPlayServer_3316948804(int clip)
	{
		this.AboubiPlayObservers(clip);
	}

	// Token: 0x06000153 RID: 339 RVA: 0x0000CA1C File Offset: 0x0000AC1C
	private void RpcReader___Server_AboubiPlayServer_3316948804(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		int num = PooledReader0.ReadInt32(AutoPackType.Packed);
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___AboubiPlayServer_3316948804(num);
	}

	// Token: 0x06000154 RID: 340 RVA: 0x0000CA70 File Offset: 0x0000AC70
	private void RpcWriter___Observers_AboubiPlayObservers_3316948804(int clip)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteInt32(clip, AutoPackType.Packed);
		base.SendObserversRpc(3U, writer, channel, DataOrderType.Default, false, false, true);
		writer.Store();
	}

	// Token: 0x06000155 RID: 341 RVA: 0x0000CB2B File Offset: 0x0000AD2B
	private void RpcLogic___AboubiPlayObservers_3316948804(int clip)
	{
		this.audio.PlayOneShot(this.tauntClip[clip]);
	}

	// Token: 0x06000156 RID: 342 RVA: 0x0000CB40 File Offset: 0x0000AD40
	private void RpcReader___Observers_AboubiPlayObservers_3316948804(PooledReader PooledReader0, Channel channel)
	{
		int num = PooledReader0.ReadInt32(AutoPackType.Packed);
		if (!base.IsClient)
		{
			return;
		}
		if (base.IsHost)
		{
			return;
		}
		this.RpcLogic___AboubiPlayObservers_3316948804(num);
	}

	// Token: 0x06000157 RID: 343 RVA: 0x0000CB80 File Offset: 0x0000AD80
	private void RpcWriter___Server_ChangeSlideClipServer_3316948804(int index)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteInt32(index, AutoPackType.Packed);
		base.SendServerRpc(4U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000158 RID: 344 RVA: 0x0000CC86 File Offset: 0x0000AE86
	private void RpcLogic___ChangeSlideClipServer_3316948804(int index)
	{
		this.ChangeSlideClipObservers(index);
	}

	// Token: 0x06000159 RID: 345 RVA: 0x0000CC90 File Offset: 0x0000AE90
	private void RpcReader___Server_ChangeSlideClipServer_3316948804(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		int num = PooledReader0.ReadInt32(AutoPackType.Packed);
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___ChangeSlideClipServer_3316948804(num);
	}

	// Token: 0x0600015A RID: 346 RVA: 0x0000CCE4 File Offset: 0x0000AEE4
	private void RpcWriter___Observers_ChangeSlideClipObservers_3316948804(int index)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteInt32(index, AutoPackType.Packed);
		base.SendObserversRpc(5U, writer, channel, DataOrderType.Default, false, false, true);
		writer.Store();
	}

	// Token: 0x0600015B RID: 347 RVA: 0x0000CD9F File Offset: 0x0000AF9F
	private void RpcLogic___ChangeSlideClipObservers_3316948804(int index)
	{
		if (index == 0)
		{
			this.slideAudio.clip = this.groundSlideClip;
		}
		if (index == 1)
		{
			this.slideAudio.clip = this.wallSlideClip;
		}
	}

	// Token: 0x0600015C RID: 348 RVA: 0x0000CDCC File Offset: 0x0000AFCC
	private void RpcReader___Observers_ChangeSlideClipObservers_3316948804(PooledReader PooledReader0, Channel channel)
	{
		int num = PooledReader0.ReadInt32(AutoPackType.Packed);
		if (!base.IsClient)
		{
			return;
		}
		if (base.IsHost)
		{
			return;
		}
		this.RpcLogic___ChangeSlideClipObservers_3316948804(num);
	}

	// Token: 0x0600015D RID: 349 RVA: 0x0000CE0C File Offset: 0x0000B00C
	private void RpcWriter___Server_SlideAudioPlay_2166136261()
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendServerRpc(6U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x0600015E RID: 350 RVA: 0x0000CF00 File Offset: 0x0000B100
	private void RpcLogic___SlideAudioPlay_2166136261()
	{
		this.networkSlideAudio.Play(0);
	}

	// Token: 0x0600015F RID: 351 RVA: 0x0000CF10 File Offset: 0x0000B110
	private void RpcReader___Server_SlideAudioPlay_2166136261(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		this.RpcLogic___SlideAudioPlay_2166136261();
	}

	// Token: 0x06000160 RID: 352 RVA: 0x0000CF44 File Offset: 0x0000B144
	private void RpcWriter___Server_SlideAudioStop_2166136261()
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendServerRpc(7U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000161 RID: 353 RVA: 0x0000D038 File Offset: 0x0000B238
	private void RpcLogic___SlideAudioStop_2166136261()
	{
		this.networkSlideAudio.Stop();
	}

	// Token: 0x06000162 RID: 354 RVA: 0x0000D048 File Offset: 0x0000B248
	private void RpcReader___Server_SlideAudioStop_2166136261(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		this.RpcLogic___SlideAudioStop_2166136261();
	}

	// Token: 0x06000163 RID: 355 RVA: 0x0000D07C File Offset: 0x0000B27C
	private void RpcWriter___Server_PlaySoundServer_3316948804(int clip)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteInt32(clip, AutoPackType.Packed);
		base.SendServerRpc(8U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000164 RID: 356 RVA: 0x0000D182 File Offset: 0x0000B382
	private void RpcLogic___PlaySoundServer_3316948804(int clip)
	{
		this.PlaySoundObservers(clip);
	}

	// Token: 0x06000165 RID: 357 RVA: 0x0000D18C File Offset: 0x0000B38C
	private void RpcReader___Server_PlaySoundServer_3316948804(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		int num = PooledReader0.ReadInt32(AutoPackType.Packed);
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___PlaySoundServer_3316948804(num);
	}

	// Token: 0x06000166 RID: 358 RVA: 0x0000D1E0 File Offset: 0x0000B3E0
	private void RpcWriter___Observers_PlaySoundObservers_3316948804(int clip)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteInt32(clip, AutoPackType.Packed);
		base.SendObserversRpc(9U, writer, channel, DataOrderType.Default, false, false, true);
		writer.Store();
	}

	// Token: 0x06000167 RID: 359 RVA: 0x0000D29C File Offset: 0x0000B49C
	private void RpcLogic___PlaySoundObservers_3316948804(int clip)
	{
		if (clip == 0)
		{
			this.audio.PlayOneShot(this.jumpClip);
		}
		if (clip == 1)
		{
			this.audio.PlayOneShot(this.concreteClips[0]);
			this.Shuffle(this.concreteClips);
		}
		if (clip == 2)
		{
			this.audio.PlayOneShot(this.dallesClips[0]);
			this.Shuffle(this.dallesClips);
		}
		if (clip == 3)
		{
			this.audio.PlayOneShot(this.betonSolideClips[0]);
			this.Shuffle(this.betonSolideClips);
		}
		if (clip == 4)
		{
			this.audio.PlayOneShot(this.woodClips[0]);
			this.Shuffle(this.woodClips);
		}
		if (clip == 5)
		{
			this.audio.PlayOneShot(this.dirtClips[0]);
			this.Shuffle(this.dirtClips);
		}
		if (clip == 6)
		{
			this.audio.PlayOneShot(this.sandClips[0]);
			this.Shuffle(this.sandClips);
		}
		if (clip == 7)
		{
			this.audio.PlayOneShot(this.grassClips[0]);
			this.Shuffle(this.grassClips);
		}
		if (clip == 8)
		{
			this.audio.PlayOneShot(this.graviersClips[0]);
			this.Shuffle(this.graviersClips);
		}
		if (clip == 9)
		{
			this.audio.PlayOneShot(this.waterClips[0]);
			this.Shuffle(this.waterClips);
		}
		if (clip == 12)
		{
			this.audio.PlayOneShot(this.metalClips[0]);
			this.Shuffle(this.metalClips);
		}
		if (clip == 13)
		{
			this.audio.PlayOneShot(this.pipeClips[0]);
			this.Shuffle(this.pipeClips);
		}
		if (clip == 14)
		{
			this.audio.PlayOneShot(this.matelasClips[0]);
			this.Shuffle(this.matelasClips);
		}
		if (clip == 15)
		{
			this.audio.PlayOneShot(this.moquetteClips[0]);
			this.Shuffle(this.moquetteClips);
		}
		if (clip == 16)
		{
			this.audio.PlayOneShot(this.woodSecClips[0]);
			this.Shuffle(this.woodSecClips);
		}
		if (clip == 19)
		{
			this.audio.PlayOneShot(this.grilleClips[0]);
			this.Shuffle(this.grilleClips);
		}
		if (clip == 20)
		{
			this.audio.PlayOneShot(this.betonPleinairClips[0]);
			this.Shuffle(this.betonPleinairClips);
		}
		if (clip == 17)
		{
			this.audio.PlayOneShot(this.ladderClips[Mathf.RoundToInt((float)global::UnityEngine.Random.Range(0, this.ladderClips.Length))]);
		}
		if (clip == 18)
		{
			this.audio.PlayOneShot(this.chainClips[Mathf.RoundToInt((float)global::UnityEngine.Random.Range(0, this.chainClips.Length))]);
		}
		if (clip == 21)
		{
			this.audio.PlayOneShot(this.highFallAudioClip);
		}
		if (clip == 10)
		{
			this.JumpClip();
		}
		if (clip == 11)
		{
			this.JumpClip();
		}
		if (clip == 22)
		{
			this.audio.PlayOneShot(this.wallOutClip);
		}
		if (clip == 23)
		{
			this.audio.PlayOneShot(this.teleportClip);
		}
	}

	// Token: 0x06000168 RID: 360 RVA: 0x0000D5A4 File Offset: 0x0000B7A4
	private void RpcReader___Observers_PlaySoundObservers_3316948804(PooledReader PooledReader0, Channel channel)
	{
		int num = PooledReader0.ReadInt32(AutoPackType.Packed);
		if (!base.IsClient)
		{
			return;
		}
		if (base.IsHost)
		{
			return;
		}
		this.RpcLogic___PlaySoundObservers_3316948804(num);
	}

	// Token: 0x06000169 RID: 361 RVA: 0x0000D5E4 File Offset: 0x0000B7E4
	private void RpcWriter___Server_WallJumpClip_3615296227(string walltag)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteString(walltag);
		base.SendServerRpc(10U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x0600016A RID: 362 RVA: 0x0000D6E5 File Offset: 0x0000B8E5
	private void RpcLogic___WallJumpClip_3615296227(string walltag)
	{
		this.JumpClipObservers(walltag);
	}

	// Token: 0x0600016B RID: 363 RVA: 0x0000D6F0 File Offset: 0x0000B8F0
	private void RpcReader___Server_WallJumpClip_3615296227(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		string text = PooledReader0.ReadString();
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___WallJumpClip_3615296227(text);
	}

	// Token: 0x0600016C RID: 364 RVA: 0x0000D740 File Offset: 0x0000B940
	private void RpcWriter___Observers_JumpClipObservers_3615296227(string walltag)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteString(walltag);
		base.SendObserversRpc(11U, writer, channel, DataOrderType.Default, false, false, true);
		writer.Store();
	}

	// Token: 0x0600016D RID: 365 RVA: 0x0000D7F8 File Offset: 0x0000B9F8
	private void RpcLogic___JumpClipObservers_3615296227(string walltag)
	{
		uint num = <PrivateImplementationDetails>.ComputeStringHash(walltag);
		if (num <= 1825421690U)
		{
			if (num <= 1378315797U)
			{
				if (num <= 464173256U)
				{
					if (num != 456440475U)
					{
						if (num == 464173256U)
						{
							if (walltag == "Footsteps/Concrete/Dalles")
							{
								this.audio.PlayOneShot(this.dallesClips[Mathf.RoundToInt((float)global::UnityEngine.Random.Range(0, this.dallesClips.Length))]);
								return;
							}
						}
					}
					else if (walltag == "Footsteps/Sand")
					{
						this.audio.PlayOneShot(this.sandClips[Mathf.RoundToInt((float)global::UnityEngine.Random.Range(0, this.sandClips.Length))]);
						return;
					}
				}
				else if (num != 977466297U)
				{
					if (num == 1378315797U)
					{
						if (walltag == "Footsteps/Moquette")
						{
							this.audio.PlayOneShot(this.moquetteClips[Mathf.RoundToInt((float)global::UnityEngine.Random.Range(0, this.moquetteClips.Length))]);
							return;
						}
					}
				}
				else if (walltag == "Footsteps/Concrete/Solide")
				{
					this.audio.PlayOneShot(this.betonSolideClips[Mathf.RoundToInt((float)global::UnityEngine.Random.Range(0, this.betonSolideClips.Length))]);
					return;
				}
			}
			else if (num <= 1610969363U)
			{
				if (num != 1429664136U)
				{
					if (num == 1610969363U)
					{
						if (walltag == "Footsteps/Concrete/PleinAir")
						{
							this.audio.PlayOneShot(this.betonPleinairClips[Mathf.RoundToInt((float)global::UnityEngine.Random.Range(0, this.betonPleinairClips.Length))]);
							return;
						}
					}
				}
				else if (walltag == "Footsteps/Water")
				{
					this.audio.PlayOneShot(this.waterClips[Mathf.RoundToInt((float)global::UnityEngine.Random.Range(0, this.waterClips.Length))]);
					return;
				}
			}
			else if (num != 1624496828U)
			{
				if (num == 1825421690U)
				{
					if (walltag == "Footsteps/Wood/Creux")
					{
						this.audio.PlayOneShot(this.woodClips[Mathf.RoundToInt((float)global::UnityEngine.Random.Range(0, this.woodClips.Length))]);
						return;
					}
				}
			}
			else if (walltag == "Footsteps/Concrete/Default")
			{
				this.audio.PlayOneShot(this.concreteClips[Mathf.RoundToInt((float)global::UnityEngine.Random.Range(0, this.concreteClips.Length))]);
				return;
			}
		}
		else if (num <= 2833365437U)
		{
			if (num <= 2251139421U)
			{
				if (num != 2180110808U)
				{
					if (num == 2251139421U)
					{
						if (walltag == "Footsteps/Metal/Pipe")
						{
							this.audio.PlayOneShot(this.pipeClips[Mathf.RoundToInt((float)global::UnityEngine.Random.Range(0, this.pipeClips.Length))]);
							return;
						}
					}
				}
				else if (walltag == "Footsteps/Dirt")
				{
					this.audio.PlayOneShot(this.dirtClips[Mathf.RoundToInt((float)global::UnityEngine.Random.Range(0, this.dirtClips.Length))]);
					return;
				}
			}
			else if (num != 2400559108U)
			{
				if (num == 2833365437U)
				{
					if (walltag == "Footsteps/Metal/Pipe2")
					{
						this.audio.PlayOneShot(this.metalClips[Mathf.RoundToInt((float)global::UnityEngine.Random.Range(0, this.metalClips.Length))]);
						return;
					}
				}
			}
			else if (walltag == "Footsteps/Matelas")
			{
				this.audio.PlayOneShot(this.matelasClips[Mathf.RoundToInt((float)global::UnityEngine.Random.Range(0, this.matelasClips.Length))]);
				return;
			}
		}
		else if (num <= 3455850406U)
		{
			if (num != 3075599786U)
			{
				if (num == 3455850406U)
				{
					if (walltag == "Footsteps/Graviers")
					{
						this.audio.PlayOneShot(this.graviersClips[Mathf.RoundToInt((float)global::UnityEngine.Random.Range(0, this.graviersClips.Length))]);
						return;
					}
				}
			}
			else if (walltag == "Footsteps/Wood/Sec")
			{
				this.audio.PlayOneShot(this.woodSecClips[Mathf.RoundToInt((float)global::UnityEngine.Random.Range(0, this.woodSecClips.Length))]);
				return;
			}
		}
		else if (num != 3492154005U)
		{
			if (num == 3807801418U)
			{
				if (walltag == "Footsteps/Metal/Grille")
				{
					this.audio.PlayOneShot(this.grilleClips[Mathf.RoundToInt((float)global::UnityEngine.Random.Range(0, this.grilleClips.Length))]);
					return;
				}
			}
		}
		else if (walltag == "Footsteps/Grass")
		{
			this.audio.PlayOneShot(this.grassClips[Mathf.RoundToInt((float)global::UnityEngine.Random.Range(0, this.grassClips.Length))]);
			return;
		}
		this.audio.PlayOneShot(this.concreteClips[Mathf.RoundToInt((float)global::UnityEngine.Random.Range(0, this.concreteClips.Length))]);
	}

	// Token: 0x0600016E RID: 366 RVA: 0x0000DD08 File Offset: 0x0000BF08
	private void RpcReader___Observers_JumpClipObservers_3615296227(PooledReader PooledReader0, Channel channel)
	{
		string text = PooledReader0.ReadString();
		if (!base.IsClient)
		{
			return;
		}
		if (base.IsHost)
		{
			return;
		}
		this.RpcLogic___JumpClipObservers_3615296227(text);
	}

	// Token: 0x0600016F RID: 367 RVA: 0x0000DD44 File Offset: 0x0000BF44
	private void RpcWriter___Server_VaultClip_3615296227(string surface)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteString(surface);
		base.SendServerRpc(12U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000170 RID: 368 RVA: 0x0000DE45 File Offset: 0x0000C045
	private void RpcLogic___VaultClip_3615296227(string surface)
	{
		this.VaultClipObservers(surface);
	}

	// Token: 0x06000171 RID: 369 RVA: 0x0000DE50 File Offset: 0x0000C050
	private void RpcReader___Server_VaultClip_3615296227(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		string text = PooledReader0.ReadString();
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___VaultClip_3615296227(text);
	}

	// Token: 0x06000172 RID: 370 RVA: 0x0000DEA0 File Offset: 0x0000C0A0
	private void RpcWriter___Observers_VaultClipObservers_3615296227(string surface)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteString(surface);
		base.SendObserversRpc(13U, writer, channel, DataOrderType.Default, false, false, true);
		writer.Store();
	}

	// Token: 0x06000173 RID: 371 RVA: 0x0000DF58 File Offset: 0x0000C158
	private void RpcLogic___VaultClipObservers_3615296227(string surface)
	{
		uint num = <PrivateImplementationDetails>.ComputeStringHash(surface);
		if (num <= 1825421690U)
		{
			if (num <= 1378315797U)
			{
				if (num <= 464173256U)
				{
					if (num != 456440475U)
					{
						if (num == 464173256U)
						{
							if (surface == "Footsteps/Concrete/Dalles")
							{
								this.audio.PlayOneShot(this.dallesClips[global::UnityEngine.Random.Range(0, this.dallesClips.Length - 1)]);
								return;
							}
						}
					}
					else if (surface == "Footsteps/Sand")
					{
						this.audio.PlayOneShot(this.sandClips[global::UnityEngine.Random.Range(0, this.sandClips.Length - 1)]);
						return;
					}
				}
				else if (num != 977466297U)
				{
					if (num == 1378315797U)
					{
						if (surface == "Footsteps/Moquette")
						{
							this.audio.PlayOneShot(this.moquetteClips[global::UnityEngine.Random.Range(0, this.moquetteClips.Length - 1)]);
							return;
						}
					}
				}
				else if (surface == "Footsteps/Concrete/Solide")
				{
					this.audio.PlayOneShot(this.betonSolideClips[global::UnityEngine.Random.Range(0, this.betonSolideClips.Length - 1)]);
					return;
				}
			}
			else if (num <= 1610969363U)
			{
				if (num != 1429664136U)
				{
					if (num == 1610969363U)
					{
						if (surface == "Footsteps/Concrete/PleinAir")
						{
							this.audio.PlayOneShot(this.betonPleinairClips[global::UnityEngine.Random.Range(0, this.betonPleinairClips.Length - 1)]);
							return;
						}
					}
				}
				else if (surface == "Footsteps/Water")
				{
					this.audio.PlayOneShot(this.waterClips[global::UnityEngine.Random.Range(0, this.waterClips.Length - 1)]);
					return;
				}
			}
			else if (num != 1624496828U)
			{
				if (num == 1825421690U)
				{
					if (surface == "Footsteps/Wood/Creux")
					{
						this.audio.PlayOneShot(this.woodClips[global::UnityEngine.Random.Range(0, this.woodClips.Length - 1)]);
						return;
					}
				}
			}
			else if (surface == "Footsteps/Concrete/Default")
			{
				this.audio.PlayOneShot(this.concreteClips[global::UnityEngine.Random.Range(0, this.concreteClips.Length - 1)]);
				return;
			}
		}
		else if (num <= 2833365437U)
		{
			if (num <= 2251139421U)
			{
				if (num != 2180110808U)
				{
					if (num == 2251139421U)
					{
						if (surface == "Footsteps/Metal/Pipe")
						{
							this.audio.PlayOneShot(this.pipeClips[global::UnityEngine.Random.Range(0, this.pipeClips.Length - 1)]);
							return;
						}
					}
				}
				else if (surface == "Footsteps/Dirt")
				{
					this.audio.PlayOneShot(this.dirtClips[global::UnityEngine.Random.Range(0, this.dirtClips.Length - 1)]);
					return;
				}
			}
			else if (num != 2400559108U)
			{
				if (num == 2833365437U)
				{
					if (surface == "Footsteps/Metal/Pipe2")
					{
						this.audio.PlayOneShot(this.metalClips[global::UnityEngine.Random.Range(0, this.metalClips.Length - 1)]);
						return;
					}
				}
			}
			else if (surface == "Footsteps/Matelas")
			{
				this.audio.PlayOneShot(this.matelasClips[global::UnityEngine.Random.Range(0, this.matelasClips.Length - 1)]);
				return;
			}
		}
		else if (num <= 3455850406U)
		{
			if (num != 3075599786U)
			{
				if (num == 3455850406U)
				{
					if (surface == "Footsteps/Graviers")
					{
						this.audio.PlayOneShot(this.graviersClips[global::UnityEngine.Random.Range(0, this.graviersClips.Length - 1)]);
						return;
					}
				}
			}
			else if (surface == "Footsteps/Wood/Sec")
			{
				this.audio.PlayOneShot(this.woodSecClips[global::UnityEngine.Random.Range(0, this.woodSecClips.Length - 1)]);
				return;
			}
		}
		else if (num != 3492154005U)
		{
			if (num == 3807801418U)
			{
				if (surface == "Footsteps/Metal/Grille")
				{
					this.audio.PlayOneShot(this.grilleClips[global::UnityEngine.Random.Range(0, this.grilleClips.Length - 1)]);
					return;
				}
			}
		}
		else if (surface == "Footsteps/Grass")
		{
			this.audio.PlayOneShot(this.grassClips[global::UnityEngine.Random.Range(0, this.grassClips.Length - 1)]);
			return;
		}
		this.audio.PlayOneShot(this.concreteClips[global::UnityEngine.Random.Range(0, this.concreteClips.Length - 1)]);
	}

	// Token: 0x06000174 RID: 372 RVA: 0x0000E424 File Offset: 0x0000C624
	private void RpcReader___Observers_VaultClipObservers_3615296227(PooledReader PooledReader0, Channel channel)
	{
		string text = PooledReader0.ReadString();
		if (!base.IsClient)
		{
			return;
		}
		if (base.IsHost)
		{
			return;
		}
		this.RpcLogic___VaultClipObservers_3615296227(text);
	}

	// Token: 0x06000175 RID: 373 RVA: 0x0000E460 File Offset: 0x0000C660
	private void RpcWriter___Server_SpawnVFX_3848837105(Vector3 position, Quaternion rotation)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteVector3(position);
		writer.WriteQuaternion(rotation, AutoPackType.Packed);
		base.SendServerRpc(14U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000176 RID: 374 RVA: 0x0000E573 File Offset: 0x0000C773
	private void RpcLogic___SpawnVFX_3848837105(Vector3 position, Quaternion rotation)
	{
		this.SpawnVFXObservers(position, rotation);
	}

	// Token: 0x06000177 RID: 375 RVA: 0x0000E580 File Offset: 0x0000C780
	private void RpcReader___Server_SpawnVFX_3848837105(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		Vector3 vector = PooledReader0.ReadVector3();
		Quaternion quaternion = PooledReader0.ReadQuaternion(AutoPackType.Packed);
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___SpawnVFX_3848837105(vector, quaternion);
	}

	// Token: 0x06000178 RID: 376 RVA: 0x0000E5E8 File Offset: 0x0000C7E8
	private void RpcWriter___Observers_SpawnVFXObservers_3848837105(Vector3 position, Quaternion rotation)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteVector3(position);
		writer.WriteQuaternion(rotation, AutoPackType.Packed);
		base.SendObserversRpc(15U, writer, channel, DataOrderType.Default, false, false, true);
		writer.Store();
	}

	// Token: 0x06000179 RID: 377 RVA: 0x0000E6B0 File Offset: 0x0000C8B0
	private void RpcLogic___SpawnVFXObservers_3848837105(Vector3 position, Quaternion rotation)
	{
		global::UnityEngine.Object.Instantiate<GameObject>(this.slideDust, position, rotation);
	}

	// Token: 0x0600017A RID: 378 RVA: 0x0000E6C0 File Offset: 0x0000C8C0
	private void RpcReader___Observers_SpawnVFXObservers_3848837105(PooledReader PooledReader0, Channel channel)
	{
		Vector3 vector = PooledReader0.ReadVector3();
		Quaternion quaternion = PooledReader0.ReadQuaternion(AutoPackType.Packed);
		if (!base.IsClient)
		{
			return;
		}
		if (base.IsHost)
		{
			return;
		}
		this.RpcLogic___SpawnVFXObservers_3848837105(vector, quaternion);
	}

	// Token: 0x0600017B RID: 379 RVA: 0x0000E714 File Offset: 0x0000C914
	private void RpcWriter___Server_DespawnObject_1934289915(GameObject obj)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteGameObject(obj);
		base.SendServerRpc(16U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x0600017C RID: 380 RVA: 0x0000E815 File Offset: 0x0000CA15
	public void RpcLogic___DespawnObject_1934289915(GameObject obj)
	{
		base.transform.DOKill(false);
		base.GetComponent<PlayerHealth>().sync___set_value_health(-8f, true);
		base.GetComponent<PlayerHealth>().sync___set_value_isKilled(true, true);
		this.ExplodeOnDeath();
	}

	// Token: 0x0600017D RID: 381 RVA: 0x0000E848 File Offset: 0x0000CA48
	private void RpcReader___Server_DespawnObject_1934289915(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		GameObject gameObject = PooledReader0.ReadGameObject();
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		this.RpcLogic___DespawnObject_1934289915(gameObject);
	}

	// Token: 0x0600017E RID: 382 RVA: 0x0000E88C File Offset: 0x0000CA8C
	private void RpcWriter___Observers_ExplodeOnDeath_2166136261()
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendObserversRpc(17U, writer, channel, DataOrderType.Default, false, false, false);
		writer.Store();
	}

	// Token: 0x0600017F RID: 383 RVA: 0x0000E938 File Offset: 0x0000CB38
	private void RpcLogic___ExplodeOnDeath_2166136261()
	{
		base.GetComponent<PlayerHealth>().Explode(false, false, "", -base.transform.forward, 30f, base.transform.position + Vector3.up * 2f + Vector3.right);
	}

	// Token: 0x06000180 RID: 384 RVA: 0x0000E998 File Offset: 0x0000CB98
	private void RpcReader___Observers_ExplodeOnDeath_2166136261(PooledReader PooledReader0, Channel channel)
	{
		if (!base.IsClient)
		{
			return;
		}
		this.RpcLogic___ExplodeOnDeath_2166136261();
	}

	// Token: 0x06000181 RID: 385 RVA: 0x0000E9B8 File Offset: 0x0000CBB8
	private void RpcWriter___Server_BreakGlassServer_4203392553(Vector3 hitPoint, Vector3 direction, GameObject obj)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteVector3(hitPoint);
		writer.WriteVector3(direction);
		writer.WriteGameObject(obj);
		base.SendServerRpc(18U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000182 RID: 386 RVA: 0x0000EAD3 File Offset: 0x0000CCD3
	public void RpcLogic___BreakGlassServer_4203392553(Vector3 hitPoint, Vector3 direction, GameObject obj)
	{
		this.BreakGlassObservers(hitPoint, direction, obj);
	}

	// Token: 0x06000183 RID: 387 RVA: 0x0000EAE0 File Offset: 0x0000CCE0
	private void RpcReader___Server_BreakGlassServer_4203392553(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		Vector3 vector = PooledReader0.ReadVector3();
		Vector3 vector2 = PooledReader0.ReadVector3();
		GameObject gameObject = PooledReader0.ReadGameObject();
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___BreakGlassServer_4203392553(vector, vector2, gameObject);
	}

	// Token: 0x06000184 RID: 388 RVA: 0x0000EB54 File Offset: 0x0000CD54
	private void RpcWriter___Observers_BreakGlassObservers_4203392553(Vector3 hitPoint, Vector3 direction, GameObject obj)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteVector3(hitPoint);
		writer.WriteVector3(direction);
		writer.WriteGameObject(obj);
		base.SendObserversRpc(19U, writer, channel, DataOrderType.Default, false, false, true);
		writer.Store();
	}

	// Token: 0x06000185 RID: 389 RVA: 0x0000EC24 File Offset: 0x0000CE24
	private void RpcLogic___BreakGlassObservers_4203392553(Vector3 hitPoint, Vector3 direction, GameObject obj)
	{
		if (obj.GetComponent<ShatterableGlass>() != null)
		{
			obj.GetComponent<ShatterableGlass>().Shatter3D(hitPoint, direction);
		}
	}

	// Token: 0x06000186 RID: 390 RVA: 0x0000EC44 File Offset: 0x0000CE44
	private void RpcReader___Observers_BreakGlassObservers_4203392553(PooledReader PooledReader0, Channel channel)
	{
		Vector3 vector = PooledReader0.ReadVector3();
		Vector3 vector2 = PooledReader0.ReadVector3();
		GameObject gameObject = PooledReader0.ReadGameObject();
		if (!base.IsClient)
		{
			return;
		}
		if (base.IsHost)
		{
			return;
		}
		this.RpcLogic___BreakGlassObservers_4203392553(vector, vector2, gameObject);
	}

	// Token: 0x06000187 RID: 391 RVA: 0x0000ECA4 File Offset: 0x0000CEA4
	private void RpcWriter___Server_PlayVoiceChat_3345084894(byte[] data)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteBytesAndSize(data);
		base.SendServerRpc(20U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000188 RID: 392 RVA: 0x0000EDA5 File Offset: 0x0000CFA5
	public void RpcLogic___PlayVoiceChat_3345084894(byte[] data)
	{
		this.PlayVoiceChatObservers(data);
	}

	// Token: 0x06000189 RID: 393 RVA: 0x0000EDB0 File Offset: 0x0000CFB0
	private void RpcReader___Server_PlayVoiceChat_3345084894(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		byte[] array = PooledReader0.ReadBytesAndSizeAllocated();
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		this.RpcLogic___PlayVoiceChat_3345084894(array);
	}

	// Token: 0x0600018A RID: 394 RVA: 0x0000EDF4 File Offset: 0x0000CFF4
	private void RpcWriter___Observers_PlayVoiceChatObservers_3345084894(byte[] data)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteBytesAndSize(data);
		base.SendObserversRpc(21U, writer, channel, DataOrderType.Default, false, false, false);
		writer.Store();
	}

	// Token: 0x0600018B RID: 395 RVA: 0x0000EEAA File Offset: 0x0000D0AA
	public void RpcLogic___PlayVoiceChatObservers_3345084894(byte[] data)
	{
		if (!base.IsOwner)
		{
			this.vstream.PlayVoiceData(data);
		}
	}

	// Token: 0x0600018C RID: 396 RVA: 0x0000EEC0 File Offset: 0x0000D0C0
	private void RpcReader___Observers_PlayVoiceChatObservers_3345084894(PooledReader PooledReader0, Channel channel)
	{
		byte[] array = PooledReader0.ReadBytesAndSizeAllocated();
		if (!base.IsClient)
		{
			return;
		}
		this.RpcLogic___PlayVoiceChatObservers_3345084894(array);
	}

	// Token: 0x17000035 RID: 53
	// (get) Token: 0x0600018D RID: 397 RVA: 0x0000EEF1 File Offset: 0x0000D0F1
	// (set) Token: 0x0600018E RID: 398 RVA: 0x0000EEF9 File Offset: 0x0000D0F9
	public bool SyncAccessor_canMove
	{
		get
		{
			return this.canMove;
		}
		set
		{
			if (value || !base.IsServer)
			{
				this.canMove = value;
			}
			this.syncVar___canMove.SetValue(value, value);
		}
	}

	// Token: 0x0600018F RID: 399 RVA: 0x0000EF30 File Offset: 0x0000D130
	public virtual bool ReadSyncVar___FirstPersonController(PooledReader PooledReader0, uint UInt321, bool Boolean2)
	{
		if (UInt321 != 0U)
		{
			return false;
		}
		if (PooledReader0 == null)
		{
			this.sync___set_value_canMove(this.syncVar___canMove.GetValue(true), true);
			return true;
		}
		bool flag = PooledReader0.ReadBoolean();
		this.sync___set_value_canMove(flag, Boolean2);
		return true;
	}

	// Token: 0x06000190 RID: 400 RVA: 0x0000EF84 File Offset: 0x0000D184
	public virtual void Awake___UserLogic()
	{
		FirstPersonController.instance = this;
		this.playerVolume = base.GetComponent<SetPlayerVolume>();
		this.physCables = base.GetComponentsInChildren<VerletSpring>();
		this.playerControls = InputManager.inputActions;
		this.networkObject = base.GetComponent<NetworkObject>();
		this.startOfRound = true;
		this.playerPickupScript = base.GetComponent<PlayerPickup>();
		this.slopeSlideScript = base.GetComponent<SlopeSlide>();
		this.slopeScript = base.GetComponent<Slope>();
		this.cameraScript = this.playerCamera.GetComponent<CameraShakeConstrains>();
		this.customForceScript = base.GetComponent<CustomAddForce>();
		this.setupScript = base.GetComponent<PlayerSetup>();
		this.characterController = base.GetComponent<CharacterController>();
		this.playerCameraHolder.transform.localPosition = new Vector3(0f, this.playerCameraHolder.transform.localPosition.y + this.offset.y, 0f);
		this.defaultYPos = this.playerCameraHolder.transform.localPosition.y + this.offset.y;
		this.defaultFOV = this.playerCamera.fieldOfView;
		if (GameObject.FindGameObjectWithTag("PauseManager").GetComponent<PauseManager>() != null)
		{
			this.pauseManager = GameObject.FindGameObjectWithTag("PauseManager").GetComponent<PauseManager>();
		}
		Cursor.lockState = CursorLockMode.Locked;
		Cursor.visible = false;
		this.highFallVfxComponent = this.highFallVfx.GetComponent<ParticleSystem>();
		this.volume = this.playerCamera.transform.GetComponent<PostProcessVolume>();
		LensDistortion lensDistortion;
		if (this.volume.profile.TryGetSettings<LensDistortion>(out lensDistortion))
		{
			this.lensDistortion = lensDistortion;
		}
		ColorGrading colorGrading;
		if (this.volume.profile.TryGetSettings<ColorGrading>(out colorGrading))
		{
			this.colorGrading = colorGrading;
		}
		this.playerControls.Player.MouseX.performed += delegate(global::UnityEngine.InputSystem.InputAction.CallbackContext ctx)
		{
			this.mouseInput.x = ctx.ReadValue<float>();
		};
		this.playerControls.Player.MouseY.performed += delegate(global::UnityEngine.InputSystem.InputAction.CallbackContext ctx)
		{
			this.mouseInput.y = ctx.ReadValue<float>();
		};
		if (GameObject.Find("Speedometer") != null)
		{
			this.speedometer = GameObject.Find("Speedometer").GetComponent<TextMeshProUGUI>();
		}
	}

	// Token: 0x040000DB RID: 219
	[SyncVar(WritePermissions = WritePermission.ClientUnsynchronized)]
	public bool canMove = true;

	// Token: 0x040000DC RID: 220
	public bool isSprinting;

	// Token: 0x040000DD RID: 221
	public bool isWalking;

	// Token: 0x040000DE RID: 222
	public bool isCrouching;

	// Token: 0x040000DF RID: 223
	public bool isGrounded;

	// Token: 0x040000E0 RID: 224
	public bool safeGrounded;

	// Token: 0x040000E1 RID: 225
	public bool isSliding;

	// Token: 0x040000E2 RID: 226
	public bool isLeaning;

	// Token: 0x040000E3 RID: 227
	public PlayerControls playerControls;

	// Token: 0x040000E4 RID: 228
	[HideInInspector]
	public global::UnityEngine.InputSystem.InputAction move;

	// Token: 0x040000E5 RID: 229
	[HideInInspector]
	public global::UnityEngine.InputSystem.InputAction jump;

	// Token: 0x040000E6 RID: 230
	[HideInInspector]
	public global::UnityEngine.InputSystem.InputAction run;

	// Token: 0x040000E7 RID: 231
	[HideInInspector]
	public global::UnityEngine.InputSystem.InputAction lookY;

	// Token: 0x040000E8 RID: 232
	[HideInInspector]
	public global::UnityEngine.InputSystem.InputAction lookX;

	// Token: 0x040000E9 RID: 233
	[HideInInspector]
	public global::UnityEngine.InputSystem.InputAction zoom;

	// Token: 0x040000EA RID: 234
	[HideInInspector]
	public global::UnityEngine.InputSystem.InputAction crouch;

	// Token: 0x040000EB RID: 235
	[HideInInspector]
	public global::UnityEngine.InputSystem.InputAction moveUp;

	// Token: 0x040000EC RID: 236
	[HideInInspector]
	public global::UnityEngine.InputSystem.InputAction leanLeft;

	// Token: 0x040000ED RID: 237
	[HideInInspector]
	public global::UnityEngine.InputSystem.InputAction leanRight;

	// Token: 0x040000EE RID: 238
	[HideInInspector]
	public global::UnityEngine.InputSystem.InputAction record;

	// Token: 0x040000EF RID: 239
	[HideInInspector]
	public global::UnityEngine.InputSystem.InputAction fire1;

	// Token: 0x040000F0 RID: 240
	[HideInInspector]
	public global::UnityEngine.InputSystem.InputAction fire2;

	// Token: 0x040000F1 RID: 241
	[HideInInspector]
	public global::UnityEngine.InputSystem.InputAction reload;

	// Token: 0x040000F2 RID: 242
	[Header("Functional Options")]
	[SerializeField]
	private bool canSprint = true;

	// Token: 0x040000F3 RID: 243
	[SerializeField]
	private bool canJump = true;

	// Token: 0x040000F4 RID: 244
	[SerializeField]
	private bool canCrouch = true;

	// Token: 0x040000F5 RID: 245
	[SerializeField]
	private bool canUseHeadMovement = true;

	// Token: 0x040000F6 RID: 246
	[SerializeField]
	private bool WillSlideOnSlopes = true;

	// Token: 0x040000F7 RID: 247
	[SerializeField]
	private bool useFootsteps = true;

	// Token: 0x040000F8 RID: 248
	[Header("Movement Parameters")]
	[SerializeField]
	private float walkSpeed = 7f;

	// Token: 0x040000F9 RID: 249
	[SerializeField]
	private float sprintSpeed = 12f;

	// Token: 0x040000FA RID: 250
	[SerializeField]
	private float crouchSpeed = 5f;

	// Token: 0x040000FB RID: 251
	[SerializeField]
	private float slopeSpeed = 8f;

	// Token: 0x040000FC RID: 252
	[SerializeField]
	private float airControl = 1f;

	// Token: 0x040000FD RID: 253
	[SerializeField]
	private float airSpeed = 10f;

	// Token: 0x040000FE RID: 254
	[SerializeField]
	private float sprintAirSpeed = 14f;

	// Token: 0x040000FF RID: 255
	[SerializeField]
	private float globalAcceleration = 15f;

	// Token: 0x04000100 RID: 256
	[SerializeField]
	private float globalDeceleration = 15f;

	// Token: 0x04000101 RID: 257
	[SerializeField]
	private float walkAcceleration = 15f;

	// Token: 0x04000102 RID: 258
	[SerializeField]
	private float sprintAcceleration = 15f;

	// Token: 0x04000103 RID: 259
	[SerializeField]
	private float crouchAcceleration = 15f;

	// Token: 0x04000104 RID: 260
	[SerializeField]
	private float airAcceleration = 15f;

	// Token: 0x04000105 RID: 261
	[SerializeField]
	private float sprintAirAcceleration = 15f;

	// Token: 0x04000106 RID: 262
	public float movementFactor = 1f;

	// Token: 0x04000107 RID: 263
	public float jumpFactor = 1f;

	// Token: 0x04000108 RID: 264
	public float wallJumpFactor = 1f;

	// Token: 0x04000109 RID: 265
	public int maxWallJumps = 1;

	// Token: 0x0400010A RID: 266
	public Vector3 moveDirection;

	// Token: 0x0400010B RID: 267
	private Vector3 objectCollisionMoveDirection;

	// Token: 0x0400010C RID: 268
	public Vector3 moveAdded;

	// Token: 0x0400010D RID: 269
	public Vector3 groundNormal;

	// Token: 0x0400010E RID: 270
	public float playerSpeed;

	// Token: 0x0400010F RID: 271
	public float speedFactor = 1f;

	// Token: 0x04000110 RID: 272
	public Vector2 currentInput;

	// Token: 0x04000111 RID: 273
	public Vector2 currentInputRaw;

	// Token: 0x04000112 RID: 274
	private float verticalInput;

	// Token: 0x04000113 RID: 275
	private float horizontalInput;

	// Token: 0x04000114 RID: 276
	private float verticalInputRaw;

	// Token: 0x04000115 RID: 277
	private float horizontalInputRaw;

	// Token: 0x04000116 RID: 278
	[Header("Look Parameters")]
	[SerializeField]
	[Range(0.1f, 5f)]
	public float lookSpeedX = 2f;

	// Token: 0x04000117 RID: 279
	[SerializeField]
	[Range(0.1f, 5f)]
	public float lookSpeedY = 2f;

	// Token: 0x04000118 RID: 280
	[SerializeField]
	[Range(0.1f, 5f)]
	public float lookSpeedAim = 0.8f;

	// Token: 0x04000119 RID: 281
	[SerializeField]
	[Range(0.1f, 5f)]
	public float lookSpeedAimNoScope = 2f;

	// Token: 0x0400011A RID: 282
	public bool isScopeAiming;

	// Token: 0x0400011B RID: 283
	[SerializeField]
	[Range(0f, 90f)]
	private float upperLookLimit = 90f;

	// Token: 0x0400011C RID: 284
	[SerializeField]
	[Range(0f, 90f)]
	private float lowerLookLimit = 90f;

	// Token: 0x0400011D RID: 285
	[SerializeField]
	private Vector3 offset = new Vector3(0f, -0.25f, 0f);

	// Token: 0x0400011E RID: 286
	public float rotationX;

	// Token: 0x0400011F RID: 287
	private float rotationZ;

	// Token: 0x04000120 RID: 288
	private Vector2 mouseInput;

	// Token: 0x04000121 RID: 289
	[Space]
	public float killShockWaveStrength = 10f;

	// Token: 0x04000122 RID: 290
	[SerializeField]
	private float saturationSpeed = 20f;

	// Token: 0x04000123 RID: 291
	[Header("Lean Parameters")]
	[SerializeField]
	private Transform leanCamera;

	// Token: 0x04000124 RID: 292
	[SerializeField]
	private float leanSpeed = 8f;

	// Token: 0x04000125 RID: 293
	[SerializeField]
	private float leanLimit = 30f;

	// Token: 0x04000126 RID: 294
	[SerializeField]
	private float constrainSphereRadius = 0.5f;

	// Token: 0x04000127 RID: 295
	private float currentRot;

	// Token: 0x04000128 RID: 296
	[Header("Jumping Parameters")]
	public float jumpForce = 8f;

	// Token: 0x04000129 RID: 297
	[SerializeField]
	private float jumpSlopeForce = 8f;

	// Token: 0x0400012A RID: 298
	[SerializeField]
	private float jumpSlopeDecel = 3f;

	// Token: 0x0400012B RID: 299
	[SerializeField]
	private float gravity = 30f;

	// Token: 0x0400012C RID: 300
	[SerializeField]
	private float crouchGravity = 40f;

	// Token: 0x0400012D RID: 301
	[SerializeField]
	private float jumpGravity = 20f;

	// Token: 0x0400012E RID: 302
	[HideInInspector]
	public float gravityMultiplier = 1f;

	// Token: 0x0400012F RID: 303
	[SerializeField]
	private float ladderDetectionLength = 0.5f;

	// Token: 0x04000130 RID: 304
	[SerializeField]
	private float ladderSpeed = 2f;

	// Token: 0x04000131 RID: 305
	[SerializeField]
	private float vaultMinimumAngle = 80f;

	// Token: 0x04000132 RID: 306
	[SerializeField]
	private LayerMask ladderLayer;

	// Token: 0x04000133 RID: 307
	public LayerMask landLayer;

	// Token: 0x04000134 RID: 308
	[SerializeField]
	private AudioClip jumpClip;

	// Token: 0x04000135 RID: 309
	[SerializeField]
	private AudioClip wallOutClip;

	// Token: 0x04000136 RID: 310
	[SerializeField]
	private AudioClip teleportClip;

	// Token: 0x04000137 RID: 311
	[SerializeField]
	private AudioClip landClip;

	// Token: 0x04000138 RID: 312
	[SerializeField]
	private AudioClip ceilingHitClip;

	// Token: 0x04000139 RID: 313
	[SerializeField]
	private AudioClip groundSlideClip;

	// Token: 0x0400013A RID: 314
	[SerializeField]
	private AudioClip wallSlideClip;

	// Token: 0x0400013B RID: 315
	[SerializeField]
	private GameObject jumpDust;

	// Token: 0x0400013C RID: 316
	[SerializeField]
	private GameObject landDust;

	// Token: 0x0400013D RID: 317
	private bool prejump;

	// Token: 0x0400013E RID: 318
	[Header("Crouch Parameters")]
	[SerializeField]
	private float crouchHeight = 1f;

	// Token: 0x0400013F RID: 319
	[SerializeField]
	private float slideHeight = 0.8f;

	// Token: 0x04000140 RID: 320
	[SerializeField]
	private float standHeight = 2f;

	// Token: 0x04000141 RID: 321
	[SerializeField]
	private float crouchingSpeed = 0.3f;

	// Token: 0x04000142 RID: 322
	[SerializeField]
	private Ease crouchingEase = Ease.Linear;

	// Token: 0x04000143 RID: 323
	[Header("Slide Parameters")]
	[SerializeField]
	private float walkSlideImpulsion = 2f;

	// Token: 0x04000144 RID: 324
	[SerializeField]
	private float walkSlideDuration = 20f;

	// Token: 0x04000145 RID: 325
	[SerializeField]
	private float sprintSlideImpulsion = 2f;

	// Token: 0x04000146 RID: 326
	[SerializeField]
	private float sprintSlideDuration = 30f;

	// Token: 0x04000147 RID: 327
	[SerializeField]
	private float slideResetTime = 1.5f;

	// Token: 0x04000148 RID: 328
	[SerializeField]
	private GameObject slideDust;

	// Token: 0x04000149 RID: 329
	[SerializeField]
	private GameObject legRight;

	// Token: 0x0400014A RID: 330
	[SerializeField]
	private GameObject legLeft;

	// Token: 0x0400014B RID: 331
	[SerializeField]
	private GameObject torso;

	// Token: 0x0400014C RID: 332
	private bool walkSlide;

	// Token: 0x0400014D RID: 333
	private bool sprintSlide;

	// Token: 0x0400014E RID: 334
	[Header("Head Movement")]
	[SerializeField]
	private Transform landBobPivot;

	// Token: 0x0400014F RID: 335
	[SerializeField]
	private float landRecoverSpeed;

	// Token: 0x04000150 RID: 336
	[SerializeField]
	private float landBobAmount;

	// Token: 0x04000151 RID: 337
	[SerializeField]
	private float landBobDuration;

	// Token: 0x04000152 RID: 338
	[SerializeField]
	private Ease landBobEase;

	// Token: 0x04000153 RID: 339
	[Header("Headbob Parameters")]
	[SerializeField]
	private float walkBobSpeed = 14f;

	// Token: 0x04000154 RID: 340
	[SerializeField]
	private float walkBobAmount = 0.05f;

	// Token: 0x04000155 RID: 341
	[SerializeField]
	private float sprintBobSpeed = 18f;

	// Token: 0x04000156 RID: 342
	[SerializeField]
	private float sprintBobAmount = 0.11f;

	// Token: 0x04000157 RID: 343
	[SerializeField]
	private float crouchBobSpeed = 8f;

	// Token: 0x04000158 RID: 344
	[SerializeField]
	private float crouchBobAmount = 0.025f;

	// Token: 0x04000159 RID: 345
	[SerializeField]
	private float fallBobSpeed = 8f;

	// Token: 0x0400015A RID: 346
	[SerializeField]
	private float fallBobUpSpeed = 2f;

	// Token: 0x0400015B RID: 347
	[SerializeField]
	private float fallBobAmount = 0.025f;

	// Token: 0x0400015C RID: 348
	[SerializeField]
	private float fallShakeHeight = 2f;

	// Token: 0x0400015D RID: 349
	[SerializeField]
	private Vector3 fallShakeForce = new Vector3(-1f, 0.4f, -1f);

	// Token: 0x0400015E RID: 350
	[SerializeField]
	private float fallShakeDuration = 0.2f;

	// Token: 0x0400015F RID: 351
	[SerializeField]
	private int fallShakeVibrato = 2;

	// Token: 0x04000160 RID: 352
	[SerializeField]
	private float fallShakeElasticity = 1f;

	// Token: 0x04000161 RID: 353
	[SerializeField]
	private Vector3 highFallShakeForce = new Vector3(0f, 0f, -21f);

	// Token: 0x04000162 RID: 354
	[SerializeField]
	private float highFallShakeDuration = 0.15f;

	// Token: 0x04000163 RID: 355
	[SerializeField]
	private float highFallShakeRecoverTime = 1f;

	// Token: 0x04000164 RID: 356
	[SerializeField]
	private float highFallShakeRecoverSpeed = 3f;

	// Token: 0x04000165 RID: 357
	[SerializeField]
	private float highFallShakeHeight = 7f;

	// Token: 0x04000166 RID: 358
	[SerializeField]
	private AudioClip highFallAudioClip;

	// Token: 0x04000167 RID: 359
	[SerializeField]
	private GameObject highFallVfx;

	// Token: 0x04000168 RID: 360
	[SerializeField]
	private float fallAudioLerpSpeed;

	// Token: 0x04000169 RID: 361
	[SerializeField]
	private float fallAudioLerpOutSpeed;

	// Token: 0x0400016A RID: 362
	[SerializeField]
	private float lensDistortionWhenFalling;

	// Token: 0x0400016B RID: 363
	[SerializeField]
	private float lensDistortionLerpSpeed;

	// Token: 0x0400016C RID: 364
	[SerializeField]
	private float lensDistortionLerpOutSpeed;

	// Token: 0x0400016D RID: 365
	private ParticleSystem highFallVfxComponent;

	// Token: 0x0400016E RID: 366
	private float defaultYPos;

	// Token: 0x0400016F RID: 367
	[Header("Camera Controller")]
	[SerializeField]
	private float zoomSpeed = 0.3f;

	// Token: 0x04000170 RID: 368
	[SerializeField]
	public float zoomFOV = 30f;

	// Token: 0x04000171 RID: 369
	[SerializeField]
	private Ease zoomEase = Ease.Linear;

	// Token: 0x04000172 RID: 370
	[SerializeField]
	private float runEaseSpeed = 0.3f;

	// Token: 0x04000173 RID: 371
	[SerializeField]
	private float runFOV = 90f;

	// Token: 0x04000174 RID: 372
	[SerializeField]
	private Ease slideEase = Ease.Linear;

	// Token: 0x04000175 RID: 373
	[SerializeField]
	private float slideEaseSpeed = 0.3f;

	// Token: 0x04000176 RID: 374
	[SerializeField]
	private float slideFOV = 80f;

	// Token: 0x04000177 RID: 375
	[SerializeField]
	private Ease runSlideEase = Ease.Linear;

	// Token: 0x04000178 RID: 376
	[SerializeField]
	private float runSlideEaseSpeed = 0.3f;

	// Token: 0x04000179 RID: 377
	[SerializeField]
	private float runSlideFOV = 94f;

	// Token: 0x0400017A RID: 378
	[SerializeField]
	private Ease runEase = Ease.Linear;

	// Token: 0x0400017B RID: 379
	[SerializeField]
	private float tiltAmount = 7f;

	// Token: 0x0400017C RID: 380
	[SerializeField]
	private float tiltSpeed = 7f;

	// Token: 0x0400017D RID: 381
	[SerializeField]
	private float slideTiltAmount = 15f;

	// Token: 0x0400017E RID: 382
	public bool camController = true;

	// Token: 0x0400017F RID: 383
	public bool isZooming;

	// Token: 0x04000180 RID: 384
	public bool isAiming;

	// Token: 0x04000181 RID: 385
	[HideInInspector]
	public float defaultFOV;

	// Token: 0x04000182 RID: 386
	public bool smoothCam;

	// Token: 0x04000183 RID: 387
	[Header("Camera mouse incidence")]
	[SerializeField]
	private float rotationAmount = 4f;

	// Token: 0x04000184 RID: 388
	[SerializeField]
	private float maxRotationAmount = 5f;

	// Token: 0x04000185 RID: 389
	[SerializeField]
	private float smoothRotation = 12f;

	// Token: 0x04000186 RID: 390
	[Header("Footsteps Parameters")]
	[SerializeField]
	private float baseStepSpeed = 0.5f;

	// Token: 0x04000187 RID: 391
	[SerializeField]
	private float crouchStepMultiplier = 1.5f;

	// Token: 0x04000188 RID: 392
	[SerializeField]
	private float sprintStepMultiplier = 0.6f;

	// Token: 0x04000189 RID: 393
	[SerializeField]
	private float ladderStep = 0.75f;

	// Token: 0x0400018A RID: 394
	[SerializeField]
	private float walkStepVolume = 0.75f;

	// Token: 0x0400018B RID: 395
	[SerializeField]
	private AudioClip[] concreteClips;

	// Token: 0x0400018C RID: 396
	[SerializeField]
	private AudioClip[] dallesClips;

	// Token: 0x0400018D RID: 397
	[SerializeField]
	private AudioClip[] betonSolideClips;

	// Token: 0x0400018E RID: 398
	[SerializeField]
	private AudioClip[] betonPleinairClips;

	// Token: 0x0400018F RID: 399
	[SerializeField]
	private AudioClip[] woodClips;

	// Token: 0x04000190 RID: 400
	[SerializeField]
	private AudioClip[] woodSecClips;

	// Token: 0x04000191 RID: 401
	[SerializeField]
	private AudioClip[] dirtClips;

	// Token: 0x04000192 RID: 402
	[SerializeField]
	private AudioClip[] sandClips;

	// Token: 0x04000193 RID: 403
	[SerializeField]
	private AudioClip[] grassClips;

	// Token: 0x04000194 RID: 404
	[SerializeField]
	private AudioClip[] graviersClips;

	// Token: 0x04000195 RID: 405
	[SerializeField]
	private AudioClip[] waterClips;

	// Token: 0x04000196 RID: 406
	[SerializeField]
	private AudioClip[] metalClips;

	// Token: 0x04000197 RID: 407
	[SerializeField]
	private AudioClip[] grilleClips;

	// Token: 0x04000198 RID: 408
	[SerializeField]
	private AudioClip[] pipeClips;

	// Token: 0x04000199 RID: 409
	[SerializeField]
	private AudioClip[] matelasClips;

	// Token: 0x0400019A RID: 410
	[SerializeField]
	private AudioClip[] moquetteClips;

	// Token: 0x0400019B RID: 411
	private float footstepTimer;

	// Token: 0x0400019C RID: 412
	private float ladderStepTimer;

	// Token: 0x0400019D RID: 413
	[Space]
	[SerializeField]
	private AudioClip[] chainClips;

	// Token: 0x0400019E RID: 414
	[SerializeField]
	private AudioClip[] ladderClips;

	// Token: 0x0400019F RID: 415
	[Space]
	[SerializeField]
	private AudioClip[] tauntClip;

	// Token: 0x040001A0 RID: 416
	private Vector3 hitPointNormal;

	// Token: 0x040001A1 RID: 417
	private Vector3 wallPointNormal;

	// Token: 0x040001A2 RID: 418
	public bool IsSliding;

	// Token: 0x040001A3 RID: 419
	public bool CanWallJump;

	// Token: 0x040001A4 RID: 420
	private string currentwalltag;

	// Token: 0x040001A5 RID: 421
	private int wallJumpsCount;

	// Token: 0x040001A6 RID: 422
	private bool hitDiagonalCeiling;

	// Token: 0x040001A7 RID: 423
	[HideInInspector]
	public float wallSlideLean;

	// Token: 0x040001A8 RID: 424
	[SerializeField]
	public Camera playerCamera;

	// Token: 0x040001A9 RID: 425
	[SerializeField]
	public GameObject playerCameraHolder;

	// Token: 0x040001AA RID: 426
	[SerializeField]
	private Animator animator;

	// Token: 0x040001AB RID: 427
	[SerializeField]
	private NetworkAnimator networkAnimator;

	// Token: 0x040001AC RID: 428
	public CharacterController characterController;

	// Token: 0x040001AD RID: 429
	private NetworkObject networkObject;

	// Token: 0x040001AE RID: 430
	public SlopeSlide slopeSlideScript;

	// Token: 0x040001AF RID: 431
	public CustomAddForce customForceScript;

	// Token: 0x040001B0 RID: 432
	[HideInInspector]
	public PauseManager pauseManager;

	// Token: 0x040001B1 RID: 433
	public PlayerPickup playerPickupScript;

	// Token: 0x040001B2 RID: 434
	private PlayerSetup setupScript;

	// Token: 0x040001B3 RID: 435
	public CameraShakeConstrains cameraScript;

	// Token: 0x040001B4 RID: 436
	private Slope slopeScript;

	// Token: 0x040001B5 RID: 437
	[HideInInspector]
	public PostProcessVolume volume;

	// Token: 0x040001B6 RID: 438
	[HideInInspector]
	public LensDistortion lensDistortion;

	// Token: 0x040001B7 RID: 439
	[HideInInspector]
	public ColorGrading colorGrading;

	// Token: 0x040001B8 RID: 440
	[SerializeField]
	private AudioSource slideAudio;

	// Token: 0x040001B9 RID: 441
	[SerializeField]
	private NetworkAudioSource networkSlideAudio;

	// Token: 0x040001BA RID: 442
	[SerializeField]
	private AudioSource audio;

	// Token: 0x040001BB RID: 443
	[SerializeField]
	private AudioSource fallAudio;

	// Token: 0x040001BC RID: 444
	[Header("Functionnal Variables")]
	public bool landBool;

	// Token: 0x040001BD RID: 445
	private float landTimer;

	// Token: 0x040001BE RID: 446
	private bool slideEnd;

	// Token: 0x040001BF RID: 447
	private bool jumpSlope;

	// Token: 0x040001C0 RID: 448
	private bool onLadder;

	// Token: 0x040001C1 RID: 449
	private float aftervaultjumpTimer;

	// Token: 0x040001C2 RID: 450
	public float headbobTimer;

	// Token: 0x040001C3 RID: 451
	private float slideTimer;

	// Token: 0x040001C4 RID: 452
	private float setSpeedTimer;

	// Token: 0x040001C5 RID: 453
	private float slideResetTimer;

	// Token: 0x040001C6 RID: 454
	private float slideCancelTimer;

	// Token: 0x040001C7 RID: 455
	private float slideTime;

	// Token: 0x040001C8 RID: 456
	private float coyoteTimer;

	// Token: 0x040001C9 RID: 457
	private float fallShakeOldPos;

	// Token: 0x040001CA RID: 458
	private float fallDistance;

	// Token: 0x040001CB RID: 459
	private bool crouchPress;

	// Token: 0x040001CC RID: 460
	[HideInInspector]
	public bool jumped;

	// Token: 0x040001CD RID: 461
	private bool allJumped;

	// Token: 0x040001CE RID: 462
	public bool onMovingPlatform;

	// Token: 0x040001CF RID: 463
	public bool groundSphereCast;

	// Token: 0x040001D0 RID: 464
	private float tauntTimer;

	// Token: 0x040001D1 RID: 465
	[HideInInspector]
	public bool slopeSlideJumped;

	// Token: 0x040001D2 RID: 466
	[Header("Raycasts")]
	public bool shortfrontRay;

	// Token: 0x040001D3 RID: 467
	public bool vaulRayDown;

	// Token: 0x040001D4 RID: 468
	public bool vaulRayUp;

	// Token: 0x040001D5 RID: 469
	public bool vaultRayUpShort;

	// Token: 0x040001D6 RID: 470
	private bool steepSlideForward;

	// Token: 0x040001D7 RID: 471
	private bool steepSlideBackward;

	// Token: 0x040001D8 RID: 472
	public bool downRay;

	// Token: 0x040001D9 RID: 473
	private bool animDownRay;

	// Token: 0x040001DA RID: 474
	private bool slideDownRay;

	// Token: 0x040001DB RID: 475
	private bool upSphere;

	// Token: 0x040001DC RID: 476
	private bool ladderRay;

	// Token: 0x040001DD RID: 477
	[Header("Slope Parameters")]
	[SerializeField]
	private float slopeForce;

	// Token: 0x040001DE RID: 478
	[SerializeField]
	private float slopeForceRayLength;

	// Token: 0x040001DF RID: 479
	private bool flymode;

	// Token: 0x040001E0 RID: 480
	public static FirstPersonController instance;

	// Token: 0x040001E1 RID: 481
	private VerletSpring[] physCables;

	// Token: 0x040001E2 RID: 482
	private SetPlayerVolume playerVolume;

	// Token: 0x040001E3 RID: 483
	private TextMeshProUGUI speedometer;

	// Token: 0x040001E4 RID: 484
	public bool sprintToggle;

	// Token: 0x040001E5 RID: 485
	public bool aimToggle;

	// Token: 0x040001E6 RID: 486
	public bool leanToggle;

	// Token: 0x040001E7 RID: 487
	public bool crouchToggle;

	// Token: 0x040001E8 RID: 488
	public bool reverseSprintBind;

	// Token: 0x040001E9 RID: 489
	public bool invertX;

	// Token: 0x040001EA RID: 490
	public bool invertY;

	// Token: 0x040001EB RID: 491
	private bool funcSprint;

	// Token: 0x040001EC RID: 492
	private bool funcSprintTrigger;

	// Token: 0x040001ED RID: 493
	private bool isLeaningLeft;

	// Token: 0x040001EE RID: 494
	private bool isLeaningRight;

	// Token: 0x040001EF RID: 495
	private bool funcLeanRightTrigger;

	// Token: 0x040001F0 RID: 496
	private bool funcLeanLeftTrigger;

	// Token: 0x040001F1 RID: 497
	private bool funcAim;

	// Token: 0x040001F2 RID: 498
	private bool funcInvertX;

	// Token: 0x040001F3 RID: 499
	private bool funcInvertY;

	// Token: 0x040001F4 RID: 500
	private float distToRunFov;

	// Token: 0x040001F5 RID: 501
	private float distToSlideFov;

	// Token: 0x040001F6 RID: 502
	private float distToRunSlideFov;

	// Token: 0x040001F7 RID: 503
	private bool safeDeathFalling;

	// Token: 0x040001F8 RID: 504
	private float tempSetSpeed;

	// Token: 0x040001F9 RID: 505
	private Vector3 dirForward;

	// Token: 0x040001FA RID: 506
	private Vector3 dirRight;

	// Token: 0x040001FB RID: 507
	[SerializeField]
	private Transform legsPivot;

	// Token: 0x040001FC RID: 508
	private float legsRotation;

	// Token: 0x040001FD RID: 509
	private bool camWall;

	// Token: 0x040001FE RID: 510
	private bool camWallExit;

	// Token: 0x040001FF RID: 511
	private Quaternion wallExitRot;

	// Token: 0x04000200 RID: 512
	private bool crouchExit;

	// Token: 0x04000201 RID: 513
	private bool prejumpLand;

	// Token: 0x04000202 RID: 514
	private bool activateFallAudio;

	// Token: 0x04000203 RID: 515
	public bool startOfRound;

	// Token: 0x04000204 RID: 516
	private bool isSlideSprinting;

	// Token: 0x04000205 RID: 517
	private Collider[] ladderColliders;

	// Token: 0x04000206 RID: 518
	private string groundTag;

	// Token: 0x04000207 RID: 519
	private bool rightHeadCast;

	// Token: 0x04000208 RID: 520
	private bool leftHeadCast;

	// Token: 0x04000209 RID: 521
	public bool addingForce;

	// Token: 0x0400020A RID: 522
	public Vector3 forceAdded;

	// Token: 0x0400020B RID: 523
	public Vector3 force;

	// Token: 0x0400020C RID: 524
	public float forceFactor;

	// Token: 0x0400020D RID: 525
	public float initforceFactor;

	// Token: 0x0400020E RID: 526
	public float forceSpeed = 30f;

	// Token: 0x0400020F RID: 527
	public float tempforceSpeed;

	// Token: 0x04000210 RID: 528
	[Range(0f, 100f)]
	public float deceleration = 1f;

	// Token: 0x04000211 RID: 529
	public Vector3 customForceFinal;

	// Token: 0x04000212 RID: 530
	private Vector3 bforcefinal;

	// Token: 0x04000213 RID: 531
	private Vector3 bdirection;

	// Token: 0x04000214 RID: 532
	private float bfactor;

	// Token: 0x04000215 RID: 533
	private float bdecel;

	// Token: 0x04000216 RID: 534
	private bool bstop;

	// Token: 0x04000217 RID: 535
	private bool bgroundStop;

	// Token: 0x04000218 RID: 536
	private bool baircontrol = true;

	// Token: 0x04000219 RID: 537
	private float btimer;

	// Token: 0x0400021A RID: 538
	private Settings settings;

	// Token: 0x0400021B RID: 539
	public bool vault;

	// Token: 0x0400021C RID: 540
	private bool vaultActivate;

	// Token: 0x0400021D RID: 541
	private bool roundHasStarted;

	// Token: 0x0400021E RID: 542
	private bool entered;

	// Token: 0x0400021F RID: 543
	[SerializeField]
	private float movingPlatformEjectForce = 2f;

	// Token: 0x04000220 RID: 544
	[SerializeField]
	private VoiceStream vstream;

	// Token: 0x04000221 RID: 545
	[SerializeField]
	private VoiceRecorder vstreamRecorder;

	// Token: 0x04000222 RID: 546
	public MatchPoitnsHUD matchPoitnsHUD;

	// Token: 0x04000223 RID: 547
	public SyncVar<bool> syncVar___canMove;

	// Token: 0x04000224 RID: 548
	private bool NetworkInitializeEarly_FirstPersonController_Assembly-CSharp.dll;

	// Token: 0x04000225 RID: 549
	private bool NetworkInitializeLate_FirstPersonController_Assembly-CSharp.dll;
}

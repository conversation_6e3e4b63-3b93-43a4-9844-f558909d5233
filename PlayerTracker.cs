﻿using System;
using System.Collections.Generic;
using FishNet.Object;
using UnityEngine;

// Token: 0x020000EA RID: 234
public class PlayerTracker : MonoBehaviour
{
	// Token: 0x06000C71 RID: 3185 RVA: 0x00055B80 File Offset: 0x00053D80
	private void Update()
	{
		if (Input.GetKeyDown(KeyCode.J))
		{
			for (int i = 0; i < this.players.Count; i++)
			{
			}
		}
	}

	// Token: 0x04000AB1 RID: 2737
	public List<NetworkObject> players = new List<NetworkObject>();
}

﻿using System;
using UnityEngine;

namespace UnityStandardAssets.Water
{
	// Token: 0x020001A2 RID: 418
	[ExecuteInEditMode]
	public class WaterTile : MonoBehaviour
	{
		// Token: 0x06001063 RID: 4195 RVA: 0x0006AD83 File Offset: 0x00068F83
		public void Start()
		{
			this.AcquireComponents();
		}

		// Token: 0x06001064 RID: 4196 RVA: 0x0006AD8C File Offset: 0x00068F8C
		private void AcquireComponents()
		{
			if (!this.reflection)
			{
				if (base.transform.parent)
				{
					this.reflection = base.transform.parent.GetComponent<PlanarReflection>();
				}
				else
				{
					this.reflection = base.transform.GetComponent<PlanarReflection>();
				}
			}
			if (!this.waterBase)
			{
				if (base.transform.parent)
				{
					this.waterBase = base.transform.parent.GetComponent<WaterBase>();
					return;
				}
				this.waterBase = base.transform.GetComponent<WaterBase>();
			}
		}

		// Token: 0x06001065 RID: 4197 RVA: 0x0006AE28 File Offset: 0x00069028
		public void OnWillRenderObject()
		{
			if (this.reflection)
			{
				this.reflection.WaterTileBeingRendered(base.transform, Camera.current);
			}
			if (this.waterBase)
			{
				this.waterBase.WaterTileBeingRendered(base.transform, Camera.current);
			}
		}

		// Token: 0x04000F2B RID: 3883
		public PlanarReflection reflection;

		// Token: 0x04000F2C RID: 3884
		public WaterBase waterBase;
	}
}

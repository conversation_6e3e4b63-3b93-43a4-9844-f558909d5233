﻿using System;
using DG.Tweening;
using DG.Tweening.Core;
using DG.Tweening.Plugins.Options;
using TMPro;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.EventSystems;
using UnityEngine.UI;

// Token: 0x02000002 RID: 2
public class ButtonSizeTween : MonoBehaviour, IPointerEnterHandler, IEventSystemHandler, IPointerExitHandler, IPointerDownHandler, IPointerUpHandler, ISelectHandler, IDeselectHandler
{
	// Token: 0x06000001 RID: 1 RVA: 0x00002050 File Offset: 0x00000250
	private void Awake()
	{
		if (base.GetComponent<Button>())
		{
			this.button = base.GetComponent<Button>();
		}
		this.initScale = (this.takeInitScale ? (this.scaleTextOnly ? this.textChild.transform : (this.parentScale ? base.transform.parent : base.transform)).localScale : Vector3.one);
		if (this.scaleTextOnly)
		{
			this.textChild = base.GetComponentInChildren<TextMeshProUGUI>();
		}
	}

	// Token: 0x06000002 RID: 2 RVA: 0x000020D4 File Offset: 0x000002D4
	private void Start()
	{
		if (this.button)
		{
			this.button.onClick.AddListener(new UnityAction(this.TaskOnClick));
		}
	}

	// Token: 0x06000003 RID: 3 RVA: 0x00002100 File Offset: 0x00000300
	private void OnEnable()
	{
		(this.scaleTextOnly ? this.textChild.transform : (this.parentScale ? base.transform.parent : base.transform)).localScale = this.initScale;
		if (this.dlcLockedButton)
		{
			this.button.interactable = SteamLobby.ownDlc0;
		}
	}

	// Token: 0x06000004 RID: 4 RVA: 0x00002160 File Offset: 0x00000360
	public void OnPointerEnter(PointerEventData eventData)
	{
		if (!Application.isFocused)
		{
			return;
		}
		PauseManager.Instance.PlayMenuClip(PauseManager.Instance.genericMenuClip);
		(this.scaleTextOnly ? this.textChild.transform : (this.parentScale ? base.transform.parent : base.transform)).transform.DOKill(false);
		if (PauseManager.Instance.gamepad || !this.biggerWithGamepadOnly)
		{
			(this.scaleTextOnly ? this.textChild.transform : (this.parentScale ? base.transform.parent : base.transform)).DOScale(this.targetScale, this.tweenDuration).SetEase(this.easeType);
		}
		if (this.dlcLockedButton && !this.button.interactable)
		{
			FloatingName.Instance.nameToShow = "Get the DLC !";
		}
		if (this.addFriendButton)
		{
			FloatingName.Instance.nameToShow = "Add friend on steam";
		}
		if (this.customText != "")
		{
			FloatingName.Instance.nameToShow = this.customText;
		}
		if (this.randomizedLockedButton)
		{
			if (!base.GetComponent<Toggle>().interactable)
			{
				if (Settings.Instance.gamesPlayed >= 10f)
				{
					FloatingName.Instance.nameToShow = "Unlocked !";
				}
				else
				{
					FloatingName.Instance.nameToShow = "Play " + ((int)(10f - Settings.Instance.gamesPlayed)).ToString() + " games to unlock";
				}
			}
			if (Settings.Instance.gamesPlayed >= 10f)
			{
				base.GetComponent<Toggle>().interactable = true;
			}
		}
	}

	// Token: 0x06000005 RID: 5 RVA: 0x00002310 File Offset: 0x00000510
	public void OnPointerDown(PointerEventData eventData)
	{
		PauseManager.Instance.PlayMenuClip(PauseManager.Instance.pressMenuClip);
		if (this.startGameButton)
		{
			if (base.GetComponent<Button>().interactable)
			{
				return;
			}
			if (ClientInstance.Instance.PlayerId != 0)
			{
				PauseManager.Instance.WriteOfflineLog("You are not the host, you can't start the game");
				if (!ClientInstance.Instance.Ready)
				{
					PauseManager.Instance.WriteOfflineLog("You must be ready for the host to start the game, click the Ready Up button");
					return;
				}
				PauseManager.Instance.WriteLog("Waiting on other players to be ready and the host to start the game...");
				return;
			}
			else
			{
				if (SteamLobby.Instance.players.Count < 2)
				{
					PauseManager.Instance.WriteOfflineLog("There must be 2 players to start a game");
					return;
				}
				PauseManager.Instance.WriteLog("Not all players are ready");
				GameObject.Find("ReadyUpButton").GetComponent<ScaleTweenLoop>().TempTween();
			}
		}
	}

	// Token: 0x06000006 RID: 6 RVA: 0x000023D6 File Offset: 0x000005D6
	public void OnPointerUp(PointerEventData eventData)
	{
	}

	// Token: 0x06000007 RID: 7 RVA: 0x000023D8 File Offset: 0x000005D8
	public void OnSelect(BaseEventData eventData)
	{
		if (this.biggerWhenSelected)
		{
			this.isSelected = true;
			if (PauseManager.Instance.gamepad || !this.biggerWithGamepadOnly)
			{
				(this.scaleTextOnly ? this.textChild.transform : (this.parentScale ? base.transform.parent : base.transform)).DOScale(this.selectedScale, this.tweenDuration2).SetEase(this.easeType2);
			}
		}
		if (PauseManager.Instance.gamepad)
		{
			PauseManager.Instance.PlayMenuClip(PauseManager.Instance.genericMenuClip);
			(this.scaleTextOnly ? this.textChild.transform : (this.parentScale ? base.transform.parent : base.transform)).transform.DOKill(false);
			if (PauseManager.Instance.gamepad || !this.biggerWithGamepadOnly)
			{
				(this.scaleTextOnly ? this.textChild.transform : (this.parentScale ? base.transform.parent : base.transform)).DOScale(this.targetScale, this.tweenDuration).SetEase(this.easeType);
			}
		}
	}

	// Token: 0x06000008 RID: 8 RVA: 0x00002524 File Offset: 0x00000724
	public void OnDeselect(BaseEventData eventData)
	{
		if (this.biggerWhenSelected)
		{
			this.isSelected = false;
			if (PauseManager.Instance.gamepad || !this.biggerWithGamepadOnly)
			{
				(this.scaleTextOnly ? this.textChild.transform : (this.parentScale ? base.transform.parent : base.transform)).DOScale(this.initScale, this.tweenDuration2).SetEase(this.easeType2);
			}
		}
		if (PauseManager.Instance.gamepad)
		{
			if (this.isSelected)
			{
				return;
			}
			(this.scaleTextOnly ? this.textChild.transform : (this.parentScale ? base.transform.parent : base.transform)).transform.DOKill(false);
			if (PauseManager.Instance.gamepad || !this.biggerWithGamepadOnly)
			{
				(this.scaleTextOnly ? this.textChild.transform : (this.parentScale ? base.transform.parent : base.transform)).transform.DOScale(this.initScale, this.tweenDuration).SetEase(this.easeType);
			}
		}
	}

	// Token: 0x06000009 RID: 9 RVA: 0x00002667 File Offset: 0x00000867
	private void TaskOnClick()
	{
		if (this.closeWindow)
		{
			PauseManager.Instance.PlayMenuClip(PauseManager.Instance.closeMenuClip);
			return;
		}
		PauseManager.Instance.PlayMenuClip(PauseManager.Instance.releaseMenuClip);
	}

	// Token: 0x0600000A RID: 10 RVA: 0x0000269C File Offset: 0x0000089C
	public void OnPointerExit(PointerEventData eventData)
	{
		if (!Application.isFocused)
		{
			return;
		}
		if (this.isSelected)
		{
			return;
		}
		(this.scaleTextOnly ? this.textChild.transform : (this.parentScale ? base.transform.parent : base.transform)).transform.DOKill(false);
		if (PauseManager.Instance.gamepad || !this.biggerWithGamepadOnly)
		{
			(this.scaleTextOnly ? this.textChild.transform : (this.parentScale ? base.transform.parent : base.transform)).transform.DOScale(this.initScale, this.tweenDuration).SetEase(this.easeType);
		}
		FloatingName.Instance.nameToShow = "";
	}

	// Token: 0x04000001 RID: 1
	[SerializeField]
	private Vector3 targetScale = new Vector3(1.1f, 1.1f, 1.1f);

	// Token: 0x04000002 RID: 2
	[SerializeField]
	private float tweenDuration;

	// Token: 0x04000003 RID: 3
	[SerializeField]
	private Ease easeType;

	// Token: 0x04000004 RID: 4
	[SerializeField]
	private bool parentScale;

	// Token: 0x04000005 RID: 5
	[SerializeField]
	private bool takeInitScale;

	// Token: 0x04000006 RID: 6
	private Vector3 initScale;

	// Token: 0x04000007 RID: 7
	[Space]
	[SerializeField]
	private bool closeWindow;

	// Token: 0x04000008 RID: 8
	[Space]
	[SerializeField]
	private bool biggerWhenSelected;

	// Token: 0x04000009 RID: 9
	[SerializeField]
	private bool biggerWithGamepadOnly;

	// Token: 0x0400000A RID: 10
	[SerializeField]
	private Vector3 selectedScale;

	// Token: 0x0400000B RID: 11
	[SerializeField]
	private float tweenDuration2;

	// Token: 0x0400000C RID: 12
	[SerializeField]
	private Ease easeType2;

	// Token: 0x0400000D RID: 13
	[Space]
	[SerializeField]
	private bool scaleTextOnly;

	// Token: 0x0400000E RID: 14
	private TextMeshProUGUI textChild;

	// Token: 0x0400000F RID: 15
	private Button button;

	// Token: 0x04000010 RID: 16
	[Space]
	[SerializeField]
	private bool startGameButton;

	// Token: 0x04000011 RID: 17
	[SerializeField]
	private bool dlcLockedButton;

	// Token: 0x04000012 RID: 18
	[SerializeField]
	private bool addFriendButton;

	// Token: 0x04000013 RID: 19
	[SerializeField]
	private bool randomizedLockedButton;

	// Token: 0x04000014 RID: 20
	public string customText;

	// Token: 0x04000015 RID: 21
	private bool isSelected;
}

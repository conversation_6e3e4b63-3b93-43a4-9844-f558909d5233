﻿using System;
using UnityEngine;

// Token: 0x02000156 RID: 342
public class SparksExecutioner : MonoBeh<PERSON><PERSON>
{
	// Token: 0x06000EF6 RID: 3830 RVA: 0x00062BA3 File Offset: 0x00060DA3
	private void Start()
	{
		base.Invoke("Kill", this.Lifetime);
	}

	// Token: 0x06000EF7 RID: 3831 RVA: 0x00002E03 File Offset: 0x00001003
	private void Kill()
	{
		global::UnityEngine.Object.Destroy(base.gameObject);
	}

	// Token: 0x04000D95 RID: 3477
	public float Lifetime = 1f;
}

﻿using System;
using UnityEngine;

// Token: 0x0200013A RID: 314
public class TutorialButton : MonoBehaviour
{
	// Token: 0x06000E89 RID: 3721 RVA: 0x0005FD97 File Offset: 0x0005DF97
	private void Start()
	{
		if (PlayerPrefs.HasKey("tutorialClicked") && PlayerPrefs.GetInt("tutorialClicked") == 1)
		{
			base.gameObject.SetActive(false);
		}
	}

	// Token: 0x06000E8A RID: 3722 RVA: 0x0005FDBE File Offset: 0x0005DFBE
	public void DisableObject()
	{
		PlayerPrefs.SetInt("tutorialClicked", 1);
		base.gameObject.SetActive(false);
	}
}

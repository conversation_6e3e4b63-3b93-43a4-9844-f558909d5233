﻿using System;
using TMPro;
using UnityEngine;

// Token: 0x0200013B RID: 315
public class TutorialText : MonoBehaviour
{
	// Token: 0x06000E8C RID: 3724 RVA: 0x0005FDD8 File Offset: 0x0005DFD8
	private void Start()
	{
		this.initScale = base.transform.localScale;
		base.transform.localScale = new Vector3(-this.initScale.x, this.initScale.y, this.initScale.z);
		this.text = base.GetComponent<TMP_Text>();
	}

	// Token: 0x06000E8D RID: 3725 RVA: 0x0005FE34 File Offset: 0x0005E034
	private void Update()
	{
		if (this.target == null && GameObject.FindGameObjectWithTag("Player") != null)
		{
			this.target = GameObject.FindGameObjectWithTag("Player").transform;
		}
		if (this.target == null)
		{
			return;
		}
		Vector3 vector = new Vector3(this.target.position.x, base.transform.position.y, this.target.position.z);
		base.transform.LookAt(vector);
		Color color = new Color(this.text.color.r, this.text.color.g, this.text.color.b, Mathf.Lerp(0f, 1f, 1f - (Vector3.Distance(this.target.position, base.transform.position) - this.minDistance) / this.maxDistance));
		this.text.color = color;
	}

	// Token: 0x04000D17 RID: 3351
	[SerializeField]
	private float maxDistance = 8f;

	// Token: 0x04000D18 RID: 3352
	[SerializeField]
	private float minDistance = 5f;

	// Token: 0x04000D19 RID: 3353
	private Transform target;

	// Token: 0x04000D1A RID: 3354
	private TMP_Text text;

	// Token: 0x04000D1B RID: 3355
	private Vector3 initScale;
}

﻿using System;
using System.Collections.Generic;
using UnityEngine;

// Token: 0x0200004E RID: 78
public class EjectedCaseScript : MonoBehaviour
{
	// Token: 0x060003B5 RID: 949 RVA: 0x0001B86C File Offset: 0x00019A6C
	private void Awake()
	{
		this.part = base.GetComponent<ParticleSystem>();
		this.collisionEvents = new List<ParticleCollisionEvent>();
		this.source = base.GetComponent<AudioSource>();
		this.source.pitch = global::UnityEngine.Random.Range(0.9f, 1.1f);
	}

	// Token: 0x060003B6 RID: 950 RVA: 0x0001B8AC File Offset: 0x00019AAC
	private void OnParticleCollision(GameObject other)
	{
		float num = (float)global::UnityEngine.Random.Range(-1, 2);
		if (!this.triggered && this.shouldPlaySound)
		{
			this.source.PlayOneShot((this.ejectCaseIndex == 1) ? this.shotgunCase : this.normalCase[(num > 0f) ? 0 : 1]);
		}
		this.triggered = true;
	}

	// Token: 0x0400040C RID: 1036
	[SerializeField]
	private AudioClip[] normalCase;

	// Token: 0x0400040D RID: 1037
	[SerializeField]
	private AudioClip shotgunCase;

	// Token: 0x0400040E RID: 1038
	public int ejectCaseIndex;

	// Token: 0x0400040F RID: 1039
	private AudioSource source;

	// Token: 0x04000410 RID: 1040
	public bool shouldPlaySound;

	// Token: 0x04000411 RID: 1041
	private bool triggered;

	// Token: 0x04000412 RID: 1042
	public ParticleSystem part;

	// Token: 0x04000413 RID: 1043
	public List<ParticleCollisionEvent> collisionEvents;
}

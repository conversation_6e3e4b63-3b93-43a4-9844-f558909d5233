﻿using System;
using System.Collections;
using DG.Tweening;
using FishNet;
using FishNet.Connection;
using FishNet.Managing;
using FishNet.Object;
using FishNet.Object.Delegating;
using FishNet.Serializing;
using FishNet.Serializing.Generated;
using FishNet.Transporting;
using UnityEngine;
using UnityEngine.UI;

// Token: 0x02000097 RID: 151
public class LargeRaycastGun : Weapon
{
	// Token: 0x060007B4 RID: 1972 RVA: 0x00034D4F File Offset: 0x00032F4F
	private void Start()
	{
		if (InstanceFinder.NetworkManager.IsServer && this.reloadWeapon)
		{
			this.InitChargedBullets();
		}
	}

	// Token: 0x060007B5 RID: 1973 RVA: 0x00034D6B File Offset: 0x00032F6B
	[ServerRpc(RequireOwnership = false)]
	private void InitChargedBullets()
	{
		this.RpcWriter___Server_InitChargedBullets_2166136261();
	}

	// Token: 0x060007B6 RID: 1974 RVA: 0x00034D73 File Offset: 0x00032F73
	[ObserversRpc(BufferLast = true)]
	private void InitChargedBulletsObservers()
	{
		this.RpcWriter___Observers_InitChargedBulletsObservers_2166136261();
	}

	// Token: 0x060007B7 RID: 1975 RVA: 0x00034D7C File Offset: 0x00032F7C
	private void Update()
	{
		base.WeaponUpdate();
		if (this.fireTimer > 0f)
		{
			this.fireTimer -= Time.deltaTime;
		}
		if (base.gameObject.layer == 7)
		{
			return;
		}
		if (this.reload.ReadValue<float>() > 0.1f && this.reloadWeapon && this.chargedBullets != (float)this.ammoCharge && !this.isReloading && this.fireTimer <= 0f && base.SyncAccessor_currentAmmo > 0 && base.IsOwner && !PauseManager.Instance.pause)
		{
			base.StartCoroutine(this.Reload());
			Debug.Log("reload2");
		}
		if (!this.onePressShoot)
		{
			if ((this.invertFire ? this.fire2 : this.fire1).ReadValue<float>() > 0.1f && this.inRightHand && ((!this.reloadWeapon) ? (base.SyncAccessor_currentAmmo > 0) : (base.SyncAccessor_currentAmmo > 0 || this.chargedBullets > 0f)))
			{
				this.Fire();
			}
			if ((this.invertFire ? this.fire1 : this.fire2).ReadValue<float>() > 0.1f && this.inLeftHand && ((!this.reloadWeapon) ? (base.SyncAccessor_currentAmmo > 0) : (base.SyncAccessor_currentAmmo > 0 || this.chargedBullets > 0f)))
			{
				this.Fire();
			}
			if ((this.invertFire ? this.fire2 : this.fire1).ReadValue<float>() > 0.1f && this.inRightHand && this.akAnim && base.SyncAccessor_currentAmmo > 0)
			{
				this.camAnimScript.rotateBack = false;
			}
			else if ((this.invertFire ? this.fire1 : this.fire2).ReadValue<float>() > 0.1f && this.inLeftHand && this.akAnim && base.SyncAccessor_currentAmmo > 0)
			{
				this.camAnimScript.rotateBack = false;
			}
			else
			{
				this.camAnimScript.rotateBack = true;
			}
			if (this.reloadWeapon ? (base.SyncAccessor_currentAmmo <= 0 && this.chargedBullets <= 0f) : (!this.reloadWeapon && base.SyncAccessor_currentAmmo <= 0))
			{
				if ((this.invertFire ? this.fire2 : this.fire1).ReadValue<float>() < 0.1f && this.inRightHand)
				{
					this.isClicked = true;
				}
				if ((this.invertFire ? this.fire1 : this.fire2).ReadValue<float>() < 0.1f && this.inLeftHand)
				{
					this.isClicked = true;
				}
			}
		}
		else
		{
			if ((this.invertFire ? this.fire2 : this.fire1).ReadValue<float>() < 0.1f && this.inRightHand)
			{
				this.isClicked = true;
			}
			if ((this.invertFire ? this.fire1 : this.fire2).ReadValue<float>() < 0.1f && this.inLeftHand)
			{
				this.isClicked = true;
			}
			this.camAnimScript.rotateBack = true;
		}
		if (this.isClicked)
		{
			if ((this.invertFire ? this.fire2 : this.fire1).ReadValue<float>() > 0.1f && this.inRightHand)
			{
				this.Fire();
				this.isClicked = false;
			}
			if ((this.invertFire ? this.fire1 : this.fire2).ReadValue<float>() > 0.1f && this.inLeftHand)
			{
				this.Fire();
				this.isClicked = false;
			}
		}
		if (this.shot && this.reloadTime < 0.2f && this.onePressShoot)
		{
			this.Fire();
		}
	}

	// Token: 0x060007B8 RID: 1976 RVA: 0x00035144 File Offset: 0x00033344
	private void Fire()
	{
		if (PauseManager.Instance.pause || !this.playerController.SyncAccessor_canMove)
		{
			return;
		}
		if (!this.playerController.IsOwner)
		{
			return;
		}
		if (this.changePitchOnShoot)
		{
			this.audio.pitch = global::UnityEngine.Random.Range(0.97f, 1.03f);
		}
		if (this.fireTimer > 0f)
		{
			this.shot = true;
			return;
		}
		this.shot = false;
		this.fireTimer = this.timeBetweenFire;
		if (base.SyncAccessor_currentAmmo <= 0 && this.chargedBullets <= 0f)
		{
			this.audio.PlayOneShot(this.nobulletClip);
			this.noAmmoClicks++;
			if (this.noAmmoClicks > 1 && this.inRightHand)
			{
				this.rootObject.GetComponent<PlayerPickup>().RightHandDrop();
			}
			if (this.noAmmoClicks > 1 && this.inLeftHand)
			{
				this.rootObject.GetComponent<PlayerPickup>().LeftHandDrop();
			}
		}
		if (!this.reloadWeapon && base.SyncAccessor_currentAmmo <= 0)
		{
			return;
		}
		if (this.reloadWeapon && this.isReloading)
		{
			return;
		}
		if (this.reloadWeapon && this.chargedBullets <= 0f && base.SyncAccessor_currentAmmo > 0)
		{
			base.StartCoroutine(this.Reload());
		}
		if (this.reloadWeapon && this.chargedBullets <= 0f && base.SyncAccessor_currentAmmo <= 0)
		{
			this.audio.PlayOneShot(this.nobulletClip);
		}
		if (this.reloadWeapon && this.chargedBullets <= 0f)
		{
			return;
		}
		if (this.revolverShake)
		{
			base.CameraRevolverAnimation();
		}
		else
		{
			base.CameraAnimation();
		}
		base.WeaponAnimation();
		if (this.boxcast)
		{
			this.ShootServerBox(this.damage, this.cam.transform.position, this.cam.transform.forward, this.playerController.GetComponent<PlayerHealth>());
			return;
		}
		Vector3 vector = this.cam.transform.forward;
		Vector3 vector2 = this.cam.transform.position;
		bool flag = false;
		for (int i = 0; i < 8; i++)
		{
			float num = ((!this.playerController.isGrounded && this.ScopeAimWeapon && this.playerController.isAiming) ? 0f : ((this.ScopeAimWeapon && !this.playerController.isAiming) ? this.notAimingAccuracy : ((this.playerController.isSprinting || !this.playerController.isGrounded || !this.playerController.safeGrounded) ? Mathf.Lerp(this.maxSpread, this.minSpread, this.sprintAccuracy) : (this.playerController.isCrouching ? Mathf.Lerp(this.maxSpread, this.minSpread, this.standingAccuracy) : (this.playerController.isWalking ? Mathf.Lerp(this.maxSpread, this.minSpread, this.walkAccuracy) : Mathf.Lerp(this.maxSpread, this.minSpread, this.standingAccuracy))))));
			this.spread = this.cam.transform.right * global::UnityEngine.Random.Range(num, -num) + this.cam.transform.up * global::UnityEngine.Random.Range(num, -num);
			Vector3 vector3 = ((i == 0) ? (this.cam.transform.position + this.cam.transform.right * this.bulletRadius) : ((i == 1) ? (this.cam.transform.position + -this.cam.transform.right * this.bulletRadius) : ((i == 2) ? (this.cam.transform.position + this.cam.transform.up * this.bulletRadius) : ((i == 3) ? (this.cam.transform.position + -this.cam.transform.up * this.bulletRadius) : ((i == 4) ? (this.cam.transform.position + (this.cam.transform.right + this.cam.transform.up).normalized * this.bulletRadius) : ((i == 5) ? (this.cam.transform.position + (this.cam.transform.right + -this.cam.transform.up).normalized * this.bulletRadius) : ((i == 6) ? (this.cam.transform.position + (-this.cam.transform.right + this.cam.transform.up).normalized * this.bulletRadius) : ((i == 7) ? (this.cam.transform.position + (-this.cam.transform.right + -this.cam.transform.up).normalized * this.bulletRadius) : this.cam.transform.position))))))));
			Vector3 vector4 = this.cam.transform.forward + this.spread;
			Debug.DrawRay(vector3, vector4 * 100f, Color.red, 1f);
			RaycastHit raycastHit;
			PlayerHealth playerHealth;
			if (Physics.Raycast(vector3, vector4, out raycastHit, float.PositiveInfinity, this.playerLayer) && raycastHit.transform.root.TryGetComponent<PlayerHealth>(out playerHealth))
			{
				vector = vector4;
				vector2 = vector3;
				flag = true;
				break;
			}
		}
		if (!flag)
		{
			vector = this.cam.transform.forward + this.spread;
			vector2 = this.cam.transform.position;
		}
		this.ShootServer(this.damage, vector2, vector, this.playerController.GetComponent<PlayerHealth>());
		if (this.playerKnockback != 0f)
		{
			this.playerController.AddForce(-this.cam.transform.forward, this.playerKnockback);
		}
	}

	// Token: 0x060007B9 RID: 1977 RVA: 0x00035808 File Offset: 0x00033A08
	private void ShootServerBox(float damageToGive, Vector3 position, Vector3 direction, PlayerHealth attacker)
	{
		this.RemoveAmmo();
		this.ShootServerEffect();
		this.enemyHealth = null;
		RaycastHit raycastHit;
		if (Physics.Raycast(position, direction, out raycastHit, float.PositiveInfinity, this.playerLayer))
		{
			this.endPoint = raycastHit.point;
		}
		else
		{
			this.endPoint = direction * 10000f;
		}
		if (this.determineLengthWithRay)
		{
			this.boxdimensions.z = Vector3.Distance(base.transform.position, this.endPoint);
			RaycastHit[] array = Physics.RaycastAll(position, direction, float.PositiveInfinity, this.defaultLayer);
			Array.Sort<RaycastHit>(array, (RaycastHit x, RaycastHit y) => x.distance.CompareTo(y.distance));
			if (array.Length != 0)
			{
				for (int i = 0; i < array.Length; i++)
				{
					base.TriggerEnvironment(array[i].transform.gameObject, array[i].point, direction, array[i].normal);
					this.SpawnBulletTrailServer(array[i].point);
					if (array[i].transform.tag == "ShatterableGlass")
					{
						base.BreakGlassServer(array[i].point, direction, array[i].transform.gameObject);
					}
					else if (array[i].transform.gameObject.layer != LayerMask.NameToLayer("Ragdoll"))
					{
						this.SpawnVFXServer(0, array[i].point, array[i].normal, array[i].transform.tag, array[i].transform);
						this.SpawnVFXServer(1, array[i].point, array[i].normal, array[i].transform.tag, array[i].transform);
					}
					if (array[i].transform.gameObject.layer != 10 && array[i].transform.gameObject.layer != 11 && array[i].transform.gameObject.layer != 14 && array[i].transform.gameObject.layer != 18 && array[i].transform.gameObject.layer != 19 && array[i].transform.gameObject.layer != 24)
					{
						break;
					}
				}
			}
		}
		Collider[] array2 = Physics.OverlapBox(position + direction * (this.boxdimensions.z / 2f), this.boxdimensions, Quaternion.LookRotation(direction), this.playerLayer);
		Transform transform = null;
		foreach (Collider collider in array2)
		{
			if (collider.GetComponentInParent<PlayerHealth>() != null)
			{
				this.enemyHealth = collider.GetComponentInParent<PlayerHealth>();
				transform = collider.transform;
				break;
			}
		}
		if (this.enemyHealth != null)
		{
			if (this.enemyHealth.gameObject == base.transform.root.gameObject)
			{
				return;
			}
			if (base.FriendlyFireCheck(this.enemyHealth))
			{
				return;
			}
			bool flag = transform.transform.gameObject.name == "Head_Col";
			if (flag)
			{
				damageToGive *= this.headMultiplier;
				Settings.Instance.IncreaseHeadshotsAmount();
			}
			else
			{
				Settings.Instance.IncreaseBodyshotsAmount();
			}
			if (flag && this.headImpact)
			{
				global::UnityEngine.Object.Instantiate<GameObject>(this.headImpact, transform.position, Quaternion.identity, (this.enemyHealth.SyncAccessor_health - damageToGive > 0f) ? transform : null);
			}
			else
			{
				global::UnityEngine.Object.Instantiate<GameObject>(this.bodyImpact, transform.position, Quaternion.LookRotation(-transform.forward));
			}
			if (this.playGenericBodyImpactOnBody ? this.genericBodyImpact : (this.genericBodyImpact && flag))
			{
				global::UnityEngine.Object.Instantiate<GameObject>(this.genericBodyImpact, transform.position, Quaternion.identity, (this.enemyHealth.SyncAccessor_health - damageToGive > 0f) ? transform : null);
			}
			this.ServerFX(transform.position + direction, Quaternion.identity);
			if (this.enemyHealth.SyncAccessor_health - damageToGive <= 0f)
			{
				base.KillShockWave();
			}
			if (this.marker == null)
			{
				this.marker = global::UnityEngine.Object.Instantiate<GameObject>(this.hitMarker, Crosshair.Instance.transform.position, Quaternion.identity, PauseManager.Instance.transform);
				this.marker.transform.DOPunchScale((transform.transform.gameObject.name == "Head_Col") ? new Vector3(2.5f, 2.5f, 2.5f) : Vector3.one, 0.3f, 8, 2f);
				if (transform.gameObject.name == "Head_Col")
				{
					this.marker.GetComponent<Image>().color = Color.red;
				}
				global::UnityEngine.Object.Destroy(this.marker, 0.3f);
			}
			else
			{
				global::UnityEngine.Object.Destroy(this.marker);
				this.marker = global::UnityEngine.Object.Instantiate<GameObject>(this.hitMarker, Crosshair.Instance.transform.position, Quaternion.identity, PauseManager.Instance.transform);
				this.marker.transform.DOPunchScale((transform.gameObject.name == "Head_Col") ? new Vector3(2.5f, 2.5f, 2.5f) : Vector3.one, 0.3f, 8, 2f);
				if (transform.gameObject.name == "Head_Col")
				{
					this.marker.GetComponent<Image>().color = Color.red;
				}
				global::UnityEngine.Object.Destroy(this.marker, 0.3f);
			}
			if (flag)
			{
				this.LocalSound(0);
				this.LocalSound(1);
			}
			else
			{
				this.LocalSound(1);
			}
			if (this.enemyHealth.SyncAccessor_health - damageToGive <= 0f)
			{
				this.LocalSound(2);
				this.enemyHealth.Explode(false, true, transform.gameObject.name, direction, this.ragdollEjectForce, transform.position);
				this.enemyHealth.graphics.SetActive(false);
				this.enemyHealth.controller.playerPickupScript.fpArms.gameObject.SetActive(false);
				this.enemyHealth.GetComponent<CharacterController>().enabled = false;
				this.KillServer(this.enemyHealth);
				this.enemyHealth.DisablePlayerObjectWhenKilled();
				PauseManager.Instance.WriteLog(string.Concat(new string[]
				{
					"<b><color=#",
					PauseManager.Instance.selfNameLogColor,
					">",
					this.enemyHealth.SyncAccessor_playerValues.SyncAccessor_playerClient.PlayerName,
					"</color></b> was ",
					flag ? "headshot" : "killed",
					" with ",
					base.StartsWithVowel(this.behaviour.weaponName) ? "an " : "a",
					" <b><color=white>",
					this.behaviour.weaponName,
					"</color></b> by <b><color=#",
					PauseManager.Instance.enemyNameLogColor,
					">",
					ClientInstance.Instance.PlayerName,
					"</color></b>"
				}));
			}
			else
			{
				this.GiveDamage(damageToGive, this.enemyHealth, transform.gameObject.name);
			}
			this.hitOK = true;
		}
		RaycastHit raycastHit2;
		if (Physics.Raycast(position, direction, out raycastHit2, float.PositiveInfinity, this.supLayer) && raycastHit2.transform.gameObject.layer == 17)
		{
			this.SupressionServer(raycastHit2.transform);
		}
		RaycastHit raycastHit3;
		DollHealth dollHealth;
		if (Physics.Raycast(position, direction, out raycastHit3, float.PositiveInfinity) && raycastHit3.transform.TryGetComponent<DollHealth>(out dollHealth) && this.hitOK)
		{
			dollHealth.health -= damageToGive;
		}
		this.hitOK = false;
	}

	// Token: 0x060007BA RID: 1978 RVA: 0x00036094 File Offset: 0x00034294
	private void ShootServer(float damageToGive, Vector3 position, Vector3 direction, PlayerHealth attacker)
	{
		this.RemoveAmmo();
		this.ShootServerEffect();
		RaycastHit[] array = Physics.RaycastAll(position, direction, float.PositiveInfinity, this.defaultLayer);
		Array.Sort<RaycastHit>(array, (RaycastHit x, RaycastHit y) => x.distance.CompareTo(y.distance));
		if (array.Length != 0)
		{
			for (int i = 0; i < array.Length; i++)
			{
				base.TriggerEnvironment(array[i].transform.gameObject, array[i].point, direction, array[i].normal);
				this.SpawnBulletTrailServer(array[i].point);
				if (array[i].transform.tag == "ShatterableGlass")
				{
					base.BreakGlassServer(array[i].point, direction, array[i].transform.gameObject);
				}
				else if (array[i].transform.gameObject.layer != LayerMask.NameToLayer("Ragdoll"))
				{
					this.SpawnVFXServer(0, array[i].point, array[i].normal, array[i].transform.tag, array[i].transform);
					this.SpawnVFXServer(1, array[i].point, array[i].normal, array[i].transform.tag, array[i].transform);
				}
				if (array[i].transform.gameObject.layer != 10 && array[i].transform.gameObject.layer != 11 && array[i].transform.gameObject.layer != 14 && array[i].transform.gameObject.layer != 18 && array[i].transform.gameObject.layer != 19 && array[i].transform.gameObject.layer != 24)
				{
					break;
				}
			}
		}
		RaycastHit raycastHit;
		if (Physics.Raycast(position, direction, out raycastHit, float.PositiveInfinity, this.playerLayer))
		{
			this.endPoint = raycastHit.point;
		}
		else
		{
			this.endPoint = direction * 1000000f;
		}
		RaycastHit raycastHit2;
		if (Physics.Raycast(position, direction, out raycastHit2, float.PositiveInfinity, this.playerLayer) && raycastHit2.transform.gameObject.layer == 11 && (raycastHit2.transform.root.CompareTag("Player") ? raycastHit2.transform.root.TryGetComponent<PlayerHealth>(out this.enemyHealth) : (raycastHit2.transform.GetComponentInParent<PlayerHealth>() != null)))
		{
			if (raycastHit2.transform.root.tag != "Player")
			{
				this.enemyHealth = raycastHit2.transform.GetComponentInParent<PlayerHealth>();
			}
			if (this.enemyHealth.gameObject == base.transform.root.gameObject)
			{
				return;
			}
			if (base.FriendlyFireCheck(this.enemyHealth))
			{
				return;
			}
			bool flag = raycastHit2.transform.gameObject.name == "Head_Col" || raycastHit2.transform.gameObject.name == "Neck_1_Col";
			if (flag)
			{
				damageToGive *= this.headMultiplier;
				Settings.Instance.IncreaseHeadshotsAmount();
			}
			else
			{
				Settings.Instance.IncreaseBodyshotsAmount();
			}
			if (base.gameObject.name == "M2000(Clone)" && this.ScopeAimWeapon && !this.playerController.isAiming)
			{
				Settings.Instance.noscope += 1f;
			}
			if (flag && this.headImpact)
			{
				global::UnityEngine.Object.Instantiate<GameObject>(this.headImpact, raycastHit2.point, Quaternion.identity, (this.enemyHealth.SyncAccessor_health - damageToGive > 0f) ? raycastHit2.transform : null);
			}
			else
			{
				global::UnityEngine.Object.Instantiate<GameObject>(this.bodyImpact, raycastHit2.point, Quaternion.LookRotation(raycastHit2.normal));
			}
			if (this.playGenericBodyImpactOnBody ? this.genericBodyImpact : (this.genericBodyImpact && flag))
			{
				global::UnityEngine.Object.Instantiate<GameObject>(this.genericBodyImpact, raycastHit2.point, Quaternion.identity, (this.enemyHealth.SyncAccessor_health - damageToGive > 0f) ? raycastHit2.transform : null);
			}
			this.ServerFX(raycastHit2.point + direction, Quaternion.identity);
			if (this.enemyHealth.SyncAccessor_health - damageToGive <= 0f)
			{
				base.KillShockWave();
			}
			if (this.marker == null)
			{
				this.marker = global::UnityEngine.Object.Instantiate<GameObject>(this.hitMarker, Crosshair.Instance.transform.position, Quaternion.identity, PauseManager.Instance.transform);
				this.marker.transform.DOPunchScale((raycastHit2.transform.gameObject.name == "Head_Col") ? new Vector3(2.5f, 2.5f, 2.5f) : Vector3.one, 0.3f, 8, 2f);
				if (raycastHit2.transform.gameObject.name == "Head_Col")
				{
					this.marker.GetComponent<Image>().color = Color.red;
				}
				global::UnityEngine.Object.Destroy(this.marker, 0.3f);
			}
			else
			{
				global::UnityEngine.Object.Destroy(this.marker);
				this.marker = global::UnityEngine.Object.Instantiate<GameObject>(this.hitMarker, Crosshair.Instance.transform.position, Quaternion.identity, PauseManager.Instance.transform);
				this.marker.transform.DOPunchScale((raycastHit2.transform.gameObject.name == "Head_Col") ? new Vector3(2.5f, 2.5f, 2.5f) : Vector3.one, 0.3f, 8, 2f);
				if (raycastHit2.transform.gameObject.name == "Head_Col")
				{
					this.marker.GetComponent<Image>().color = Color.red;
				}
				global::UnityEngine.Object.Destroy(this.marker, 0.3f);
			}
			if (flag)
			{
				this.LocalSound(0);
				this.LocalSound(1);
			}
			else
			{
				this.LocalSound(1);
			}
			if (this.enemyHealth.SyncAccessor_health - damageToGive <= 0f)
			{
				this.LocalSound(2);
				this.enemyHealth.Explode(false, true, raycastHit2.transform.gameObject.name, direction, this.ragdollEjectForce, raycastHit2.point);
				this.enemyHealth.graphics.SetActive(false);
				this.enemyHealth.controller.playerPickupScript.fpArms.gameObject.SetActive(false);
				this.enemyHealth.GetComponent<CharacterController>().enabled = false;
				this.KillServer(this.enemyHealth);
				this.enemyHealth.DisablePlayerObjectWhenKilled();
				PauseManager.Instance.WriteLog(string.Concat(new string[]
				{
					"<b><color=#",
					PauseManager.Instance.selfNameLogColor,
					">",
					this.enemyHealth.SyncAccessor_playerValues.SyncAccessor_playerClient.PlayerName,
					"</color></b> was ",
					flag ? "headshot" : "killed",
					" with ",
					base.StartsWithVowel(this.behaviour.weaponName) ? "an " : "a",
					" <b><color=white>",
					this.behaviour.weaponName,
					"</color></b> by <b><color=#",
					PauseManager.Instance.enemyNameLogColor,
					">",
					ClientInstance.Instance.PlayerName,
					"</color></b>"
				}));
			}
			else
			{
				this.GiveDamage(damageToGive, this.enemyHealth, raycastHit2.transform.gameObject.name);
			}
			this.hitOK = true;
		}
		RaycastHit raycastHit3;
		if (Physics.Raycast(position, direction, out raycastHit3, float.PositiveInfinity, this.supLayer) && raycastHit3.transform.gameObject.layer == 17)
		{
			this.SupressionServer(raycastHit3.transform);
		}
		RaycastHit raycastHit4;
		DollHealth dollHealth;
		if (Physics.Raycast(position, direction, out raycastHit4, float.PositiveInfinity) && raycastHit4.transform.TryGetComponent<DollHealth>(out dollHealth) && this.hitOK)
		{
			dollHealth.health -= damageToGive;
		}
		this.hitOK = false;
	}

	// Token: 0x060007BB RID: 1979 RVA: 0x0003697E File Offset: 0x00034B7E
	[ServerRpc(RunLocally = true)]
	private void RemoveAmmo()
	{
		this.RpcWriter___Server_RemoveAmmo_2166136261();
		this.RpcLogic___RemoveAmmo_2166136261();
	}

	// Token: 0x060007BC RID: 1980 RVA: 0x0003698C File Offset: 0x00034B8C
	[ServerRpc]
	private void GiveDamage(float damageToGive, PlayerHealth enemyHealth, string name)
	{
		this.RpcWriter___Server_GiveDamage_324487999(damageToGive, enemyHealth, name);
	}

	// Token: 0x060007BD RID: 1981 RVA: 0x000369A0 File Offset: 0x00034BA0
	[ServerRpc]
	private void KillServer(PlayerHealth enemyHealth)
	{
		this.RpcWriter___Server_KillServer_1722911636(enemyHealth);
	}

	// Token: 0x060007BE RID: 1982 RVA: 0x000369B7 File Offset: 0x00034BB7
	[TargetRpc]
	private void KillObserver(NetworkConnection conn, ClientInstance client, PlayerHealth enemyHealth)
	{
		this.RpcWriter___Target_KillObserver_123853379(conn, client, enemyHealth);
	}

	// Token: 0x060007BF RID: 1983 RVA: 0x000369CB File Offset: 0x00034BCB
	[ObserversRpc]
	private void HitFeeback(PlayerHealth enemyHealth)
	{
		this.RpcWriter___Observers_HitFeeback_1722911636(enemyHealth);
	}

	// Token: 0x060007C0 RID: 1984 RVA: 0x000369D7 File Offset: 0x00034BD7
	[ServerRpc(RunLocally = true)]
	private void ShootServerEffect()
	{
		this.RpcWriter___Server_ShootServerEffect_2166136261();
		this.RpcLogic___ShootServerEffect_2166136261();
	}

	// Token: 0x060007C1 RID: 1985 RVA: 0x000369E8 File Offset: 0x00034BE8
	[ObserversRpc(RunLocally = true, ExcludeOwner = true)]
	private void ShootObserversEffect()
	{
		this.RpcWriter___Observers_ShootObserversEffect_2166136261();
		this.RpcLogic___ShootObserversEffect_2166136261();
	}

	// Token: 0x060007C2 RID: 1986 RVA: 0x00036A01 File Offset: 0x00034C01
	[ServerRpc(RunLocally = true)]
	private void SpawnBulletTrailServer(Vector3 hitPoint)
	{
		this.RpcWriter___Server_SpawnBulletTrailServer_4276783012(hitPoint);
		this.RpcLogic___SpawnBulletTrailServer_4276783012(hitPoint);
	}

	// Token: 0x060007C3 RID: 1987 RVA: 0x00036A18 File Offset: 0x00034C18
	[ObserversRpc(RunLocally = true, ExcludeOwner = true)]
	private void SpawnBulletTrail(Vector3 hitPoint)
	{
		this.RpcWriter___Observers_SpawnBulletTrail_4276783012(hitPoint);
		this.RpcLogic___SpawnBulletTrail_4276783012(hitPoint);
	}

	// Token: 0x060007C4 RID: 1988 RVA: 0x00036A39 File Offset: 0x00034C39
	[ServerRpc(RunLocally = true)]
	private void SpawnVFXServer(int index, Vector3 hitPoint, Vector3 hitNormal, string surface, Transform parent)
	{
		this.RpcWriter___Server_SpawnVFXServer_606331033(index, hitPoint, hitNormal, surface, parent);
		this.RpcLogic___SpawnVFXServer_606331033(index, hitPoint, hitNormal, surface, parent);
	}

	// Token: 0x060007C5 RID: 1989 RVA: 0x00036A70 File Offset: 0x00034C70
	[ObserversRpc(RunLocally = true, ExcludeOwner = true)]
	private void SpawnVFX(int index, Vector3 hitPoint, Vector3 hitNormal, string surface, Transform parent)
	{
		this.RpcWriter___Observers_SpawnVFX_606331033(index, hitPoint, hitNormal, surface, parent);
		this.RpcLogic___SpawnVFX_606331033(index, hitPoint, hitNormal, surface, parent);
	}

	// Token: 0x060007C6 RID: 1990 RVA: 0x0002B771 File Offset: 0x00029971
	private void LocalSound(int index)
	{
		if (index == 0)
		{
			this.audio.PlayOneShot(this.headHitClip);
		}
		if (index == 1)
		{
			this.audio.PlayOneShot(this.bodyHitClip);
		}
		if (index == 2)
		{
			this.audio.PlayOneShot(this.deathClip);
		}
	}

	// Token: 0x060007C7 RID: 1991 RVA: 0x00036AB1 File Offset: 0x00034CB1
	private IEnumerator Reload()
	{
		this.isReloading = true;
		this.audio.PlayOneShot(this.reloadClip);
		if (this.animator != null)
		{
			this.animator.SetTrigger("Reload");
		}
		base.OnReload();
		yield return new WaitForSeconds(this.reloadTime);
		if (base.SyncAccessor_currentAmmo - this.ammoCharge >= 0)
		{
			base.sync___set_value_currentAmmo(base.SyncAccessor_currentAmmo - this.ammoCharge, true);
			this.chargedBullets = (float)this.ammoCharge;
		}
		else
		{
			this.chargedBullets = (float)base.SyncAccessor_currentAmmo;
			base.sync___set_value_currentAmmo(0, true);
		}
		this.isReloading = false;
		yield break;
	}

	// Token: 0x060007C8 RID: 1992 RVA: 0x00036AC0 File Offset: 0x00034CC0
	[ServerRpc]
	private void SupressionServer(Transform supp)
	{
		this.RpcWriter___Server_SupressionServer_3068987916(supp);
	}

	// Token: 0x060007C9 RID: 1993 RVA: 0x00036ACC File Offset: 0x00034CCC
	[ObserversRpc(ExcludeOwner = true)]
	private void SuppressionTarget(Transform supp)
	{
		this.RpcWriter___Observers_SuppressionTarget_3068987916(supp);
	}

	// Token: 0x060007CA RID: 1994 RVA: 0x00036AD8 File Offset: 0x00034CD8
	[ServerRpc(RunLocally = true)]
	public void ServerFX(Vector3 position, Quaternion rotation)
	{
		this.RpcWriter___Server_ServerFX_3848837105(position, rotation);
		this.RpcLogic___ServerFX_3848837105(position, rotation);
	}

	// Token: 0x060007CB RID: 1995 RVA: 0x00036AF6 File Offset: 0x00034CF6
	[ObserversRpc(RunLocally = true, ExcludeOwner = true)]
	private void ObserversFX(Vector3 position, Quaternion rotation)
	{
		this.RpcWriter___Observers_ObserversFX_3848837105(position, rotation);
		this.RpcLogic___ObserversFX_3848837105(position, rotation);
	}

	// Token: 0x060007CD RID: 1997 RVA: 0x00036B30 File Offset: 0x00034D30
	public override void NetworkInitialize___Early()
	{
		if (this.NetworkInitializeEarly_LargeRaycastGun_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeEarly_LargeRaycastGun_Assembly-CSharp.dll = true;
		base.NetworkInitialize___Early();
		base.RegisterServerRpc(15U, new ServerRpcDelegate(this.RpcReader___Server_InitChargedBullets_2166136261));
		base.RegisterObserversRpc(16U, new ClientRpcDelegate(this.RpcReader___Observers_InitChargedBulletsObservers_2166136261));
		base.RegisterServerRpc(17U, new ServerRpcDelegate(this.RpcReader___Server_RemoveAmmo_2166136261));
		base.RegisterServerRpc(18U, new ServerRpcDelegate(this.RpcReader___Server_GiveDamage_324487999));
		base.RegisterServerRpc(19U, new ServerRpcDelegate(this.RpcReader___Server_KillServer_1722911636));
		base.RegisterTargetRpc(20U, new ClientRpcDelegate(this.RpcReader___Target_KillObserver_123853379));
		base.RegisterObserversRpc(21U, new ClientRpcDelegate(this.RpcReader___Observers_HitFeeback_1722911636));
		base.RegisterServerRpc(22U, new ServerRpcDelegate(this.RpcReader___Server_ShootServerEffect_2166136261));
		base.RegisterObserversRpc(23U, new ClientRpcDelegate(this.RpcReader___Observers_ShootObserversEffect_2166136261));
		base.RegisterServerRpc(24U, new ServerRpcDelegate(this.RpcReader___Server_SpawnBulletTrailServer_4276783012));
		base.RegisterObserversRpc(25U, new ClientRpcDelegate(this.RpcReader___Observers_SpawnBulletTrail_4276783012));
		base.RegisterServerRpc(26U, new ServerRpcDelegate(this.RpcReader___Server_SpawnVFXServer_606331033));
		base.RegisterObserversRpc(27U, new ClientRpcDelegate(this.RpcReader___Observers_SpawnVFX_606331033));
		base.RegisterServerRpc(28U, new ServerRpcDelegate(this.RpcReader___Server_SupressionServer_3068987916));
		base.RegisterObserversRpc(29U, new ClientRpcDelegate(this.RpcReader___Observers_SuppressionTarget_3068987916));
		base.RegisterServerRpc(30U, new ServerRpcDelegate(this.RpcReader___Server_ServerFX_3848837105));
		base.RegisterObserversRpc(31U, new ClientRpcDelegate(this.RpcReader___Observers_ObserversFX_3848837105));
	}

	// Token: 0x060007CE RID: 1998 RVA: 0x00036CDB File Offset: 0x00034EDB
	public override void NetworkInitialize__Late()
	{
		if (this.NetworkInitializeLate_LargeRaycastGun_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeLate_LargeRaycastGun_Assembly-CSharp.dll = true;
		base.NetworkInitialize__Late();
	}

	// Token: 0x060007CF RID: 1999 RVA: 0x00036CF4 File Offset: 0x00034EF4
	public override void NetworkInitializeIfDisabled()
	{
		this.NetworkInitialize___Early();
		this.NetworkInitialize__Late();
	}

	// Token: 0x060007D0 RID: 2000 RVA: 0x00036D04 File Offset: 0x00034F04
	private void RpcWriter___Server_InitChargedBullets_2166136261()
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendServerRpc(15U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x060007D1 RID: 2001 RVA: 0x00036D9E File Offset: 0x00034F9E
	private void RpcLogic___InitChargedBullets_2166136261()
	{
		this.InitChargedBulletsObservers();
		base.sync___set_value_currentAmmo(base.SyncAccessor_currentAmmo - this.ammoCharge, true);
	}

	// Token: 0x060007D2 RID: 2002 RVA: 0x00036DBC File Offset: 0x00034FBC
	private void RpcReader___Server_InitChargedBullets_2166136261(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		if (!base.IsServer)
		{
			return;
		}
		this.RpcLogic___InitChargedBullets_2166136261();
	}

	// Token: 0x060007D3 RID: 2003 RVA: 0x00036DDC File Offset: 0x00034FDC
	private void RpcWriter___Observers_InitChargedBulletsObservers_2166136261()
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendObserversRpc(16U, writer, channel, DataOrderType.Default, true, false, false);
		writer.Store();
	}

	// Token: 0x060007D4 RID: 2004 RVA: 0x00036E85 File Offset: 0x00035085
	private void RpcLogic___InitChargedBulletsObservers_2166136261()
	{
		this.chargedBullets = (float)this.ammoCharge;
		Debug.Log(this.chargedBullets);
	}

	// Token: 0x060007D5 RID: 2005 RVA: 0x00036EA4 File Offset: 0x000350A4
	private void RpcReader___Observers_InitChargedBulletsObservers_2166136261(PooledReader PooledReader0, Channel channel)
	{
		if (!base.IsClient)
		{
			return;
		}
		this.RpcLogic___InitChargedBulletsObservers_2166136261();
	}

	// Token: 0x060007D6 RID: 2006 RVA: 0x00036EC4 File Offset: 0x000350C4
	private void RpcWriter___Server_RemoveAmmo_2166136261()
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendServerRpc(17U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x060007D7 RID: 2007 RVA: 0x0002BFDC File Offset: 0x0002A1DC
	private void RpcLogic___RemoveAmmo_2166136261()
	{
		if (this.reloadWeapon)
		{
			this.chargedBullets -= 1f;
			return;
		}
		base.sync___set_value_currentAmmo(base.SyncAccessor_currentAmmo - 1, true);
	}

	// Token: 0x060007D8 RID: 2008 RVA: 0x00036FB8 File Offset: 0x000351B8
	private void RpcReader___Server_RemoveAmmo_2166136261(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___RemoveAmmo_2166136261();
	}

	// Token: 0x060007D9 RID: 2009 RVA: 0x00036FF8 File Offset: 0x000351F8
	private void RpcWriter___Server_GiveDamage_324487999(float damageToGive, PlayerHealth enemyHealth, string name)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteSingle(damageToGive, AutoPackType.Unpacked);
		writer.Write___PlayerHealthFishNet.Serializing.Generated(enemyHealth);
		writer.WriteString(name);
		base.SendServerRpc(18U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x060007DA RID: 2010 RVA: 0x00037118 File Offset: 0x00035318
	private void RpcLogic___GiveDamage_324487999(float damageToGive, PlayerHealth enemyHealth, string name)
	{
		enemyHealth.sync___set_value_killer(this.rootObject.transform, true);
		enemyHealth.sync___set_value_health(enemyHealth.SyncAccessor_health - damageToGive, true);
		enemyHealth.KillCam();
		this.HitFeeback(enemyHealth);
		enemyHealth.Dismemberment(name);
	}

	// Token: 0x060007DB RID: 2011 RVA: 0x00037150 File Offset: 0x00035350
	private void RpcReader___Server_GiveDamage_324487999(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		float num = PooledReader0.ReadSingle(AutoPackType.Unpacked);
		PlayerHealth playerHealth = global::FishNet.Serializing.Generated.GeneratedReaders___Internal.Read___PlayerHealthFishNet.Serializing.Generateds(PooledReader0);
		string text = PooledReader0.ReadString();
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		this.RpcLogic___GiveDamage_324487999(num, playerHealth, text);
	}

	// Token: 0x060007DC RID: 2012 RVA: 0x000371BC File Offset: 0x000353BC
	private void RpcWriter___Server_KillServer_1722911636(PlayerHealth enemyHealth)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.Write___PlayerHealthFishNet.Serializing.Generated(enemyHealth);
		base.SendServerRpc(19U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x060007DD RID: 2013 RVA: 0x000372C0 File Offset: 0x000354C0
	private void RpcLogic___KillServer_1722911636(PlayerHealth enemyHealth)
	{
		enemyHealth.sync___set_value_isShot(true, true);
		enemyHealth.sync___set_value_health(-8f, true);
		GameManager.Instance.PlayerDied(enemyHealth.SyncAccessor_playerValues.SyncAccessor_playerClient.PlayerId);
		if (this.rootObject != null)
		{
			enemyHealth.sync___set_value_killer(this.rootObject.transform, true);
		}
		this.KillObserver(enemyHealth.SyncAccessor_playerValues.SyncAccessor_playerClient.transform.GetComponent<NetworkObject>().Owner, enemyHealth.SyncAccessor_playerValues.SyncAccessor_playerClient, enemyHealth);
	}

	// Token: 0x060007DE RID: 2014 RVA: 0x00037348 File Offset: 0x00035548
	private void RpcReader___Server_KillServer_1722911636(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		PlayerHealth playerHealth = global::FishNet.Serializing.Generated.GeneratedReaders___Internal.Read___PlayerHealthFishNet.Serializing.Generateds(PooledReader0);
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		this.RpcLogic___KillServer_1722911636(playerHealth);
	}

	// Token: 0x060007DF RID: 2015 RVA: 0x0003738C File Offset: 0x0003558C
	private void RpcWriter___Target_KillObserver_123853379(NetworkConnection conn, ClientInstance client, PlayerHealth enemyHealth)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.Write___ClientInstanceFishNet.Serializing.Generated(client);
		writer.Write___PlayerHealthFishNet.Serializing.Generated(enemyHealth);
		base.SendTargetRpc(20U, writer, channel, DataOrderType.Default, conn, false, true);
		writer.Store();
	}

	// Token: 0x060007E0 RID: 2016 RVA: 0x000275DA File Offset: 0x000257DA
	private void RpcLogic___KillObserver_123853379(NetworkConnection conn, ClientInstance client, PlayerHealth enemyHealth)
	{
		enemyHealth.shouldDropWeapon = true;
		enemyHealth.isDeadFromTargetRpc = true;
		if (this.rootObject != null)
		{
			GameObject.Find("Main Camera").GetComponent<KillCam>().enemy = this.rootObject.transform;
		}
	}

	// Token: 0x060007E1 RID: 2017 RVA: 0x00037450 File Offset: 0x00035650
	private void RpcReader___Target_KillObserver_123853379(PooledReader PooledReader0, Channel channel)
	{
		ClientInstance clientInstance = global::FishNet.Serializing.Generated.GeneratedReaders___Internal.Read___ClientInstanceFishNet.Serializing.Generateds(PooledReader0);
		PlayerHealth playerHealth = global::FishNet.Serializing.Generated.GeneratedReaders___Internal.Read___PlayerHealthFishNet.Serializing.Generateds(PooledReader0);
		if (!base.IsClient)
		{
			return;
		}
		this.RpcLogic___KillObserver_123853379(base.LocalConnection, clientInstance, playerHealth);
	}

	// Token: 0x060007E2 RID: 2018 RVA: 0x00037498 File Offset: 0x00035698
	private void RpcWriter___Observers_HitFeeback_1722911636(PlayerHealth enemyHealth)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.Write___PlayerHealthFishNet.Serializing.Generated(enemyHealth);
		base.SendObserversRpc(21U, writer, channel, DataOrderType.Default, false, false, false);
		writer.Store();
	}

	// Token: 0x060007E3 RID: 2019 RVA: 0x00027716 File Offset: 0x00025916
	private void RpcLogic___HitFeeback_1722911636(PlayerHealth enemyHealth)
	{
		enemyHealth.HitFeeback();
	}

	// Token: 0x060007E4 RID: 2020 RVA: 0x00037550 File Offset: 0x00035750
	private void RpcReader___Observers_HitFeeback_1722911636(PooledReader PooledReader0, Channel channel)
	{
		PlayerHealth playerHealth = global::FishNet.Serializing.Generated.GeneratedReaders___Internal.Read___PlayerHealthFishNet.Serializing.Generateds(PooledReader0);
		if (!base.IsClient)
		{
			return;
		}
		this.RpcLogic___HitFeeback_1722911636(playerHealth);
	}

	// Token: 0x060007E5 RID: 2021 RVA: 0x00037584 File Offset: 0x00035784
	private void RpcWriter___Server_ShootServerEffect_2166136261()
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendServerRpc(22U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x060007E6 RID: 2022 RVA: 0x00037678 File Offset: 0x00035878
	private void RpcLogic___ShootServerEffect_2166136261()
	{
		this.ShootObserversEffect();
	}

	// Token: 0x060007E7 RID: 2023 RVA: 0x00037680 File Offset: 0x00035880
	private void RpcReader___Server_ShootServerEffect_2166136261(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___ShootServerEffect_2166136261();
	}

	// Token: 0x060007E8 RID: 2024 RVA: 0x000376C0 File Offset: 0x000358C0
	private void RpcWriter___Observers_ShootObserversEffect_2166136261()
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendObserversRpc(23U, writer, channel, DataOrderType.Default, false, false, true);
		writer.Store();
	}

	// Token: 0x060007E9 RID: 2025 RVA: 0x0003776C File Offset: 0x0003596C
	private void RpcLogic___ShootObserversEffect_2166136261()
	{
		if (Settings.Instance.reduceVFX)
		{
			return;
		}
		this.audio.PlayOneShot(this.fireClip);
		if (this.behaviour.smokeTrail != null && !this.behaviour.smokeTrail.isPlaying)
		{
			this.behaviour.smokeTrail.Play();
		}
		GameObject gameObject = global::UnityEngine.Object.Instantiate<GameObject>(this.muzzleFlash, this.muzzleFlashPoint.position, this.shootPoint.rotation, this.behaviour.vfxAttachedOnGun ? base.transform : null);
		gameObject.GetComponent<ParticleSystem>().Play();
		foreach (Transform transform in gameObject.transform.GetComponentsInChildren<Transform>(true))
		{
			if (transform.GetComponent<Light>() == null && transform.tag != "vfx" && base.IsOwner)
			{
				transform.gameObject.layer = 8;
			}
			if (transform.GetComponent<Light>() != null)
			{
				transform.GetComponent<Light>().intensity = this.lightIntensity;
			}
		}
		ParticleSystem[] componentsInChildren2 = gameObject.GetComponentsInChildren<ParticleSystem>();
		for (int i = 0; i < componentsInChildren2.Length; i++)
		{
			componentsInChildren2[i].Play();
		}
	}

	// Token: 0x060007EA RID: 2026 RVA: 0x000378A4 File Offset: 0x00035AA4
	private void RpcReader___Observers_ShootObserversEffect_2166136261(PooledReader PooledReader0, Channel channel)
	{
		if (!base.IsClient)
		{
			return;
		}
		if (base.IsHost)
		{
			return;
		}
		this.RpcLogic___ShootObserversEffect_2166136261();
	}

	// Token: 0x060007EB RID: 2027 RVA: 0x000378D0 File Offset: 0x00035AD0
	private void RpcWriter___Server_SpawnBulletTrailServer_4276783012(Vector3 hitPoint)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteVector3(hitPoint);
		base.SendServerRpc(24U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x060007EC RID: 2028 RVA: 0x000379D1 File Offset: 0x00035BD1
	private void RpcLogic___SpawnBulletTrailServer_4276783012(Vector3 hitPoint)
	{
		this.SpawnBulletTrail(hitPoint);
	}

	// Token: 0x060007ED RID: 2029 RVA: 0x000379DC File Offset: 0x00035BDC
	private void RpcReader___Server_SpawnBulletTrailServer_4276783012(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		Vector3 vector = PooledReader0.ReadVector3();
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___SpawnBulletTrailServer_4276783012(vector);
	}

	// Token: 0x060007EE RID: 2030 RVA: 0x00037A2C File Offset: 0x00035C2C
	private void RpcWriter___Observers_SpawnBulletTrail_4276783012(Vector3 hitPoint)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteVector3(hitPoint);
		base.SendObserversRpc(25U, writer, channel, DataOrderType.Default, false, false, true);
		writer.Store();
	}

	// Token: 0x060007EF RID: 2031 RVA: 0x00037AE4 File Offset: 0x00035CE4
	private void RpcLogic___SpawnBulletTrail_4276783012(Vector3 hitPoint)
	{
		GameObject gameObject = global::UnityEngine.Object.Instantiate<GameObject>(this.bulletTrailLocal.gameObject, this.shootPoint.position, Quaternion.identity);
		LineRenderer component = gameObject.GetComponent<LineRenderer>();
		component.SetPosition(0, this.shootPoint.position);
		component.SetPosition(1, hitPoint);
		global::UnityEngine.Object.Destroy(gameObject, 0.4f);
	}

	// Token: 0x060007F0 RID: 2032 RVA: 0x00037B3C File Offset: 0x00035D3C
	private void RpcReader___Observers_SpawnBulletTrail_4276783012(PooledReader PooledReader0, Channel channel)
	{
		Vector3 vector = PooledReader0.ReadVector3();
		if (!base.IsClient)
		{
			return;
		}
		if (base.IsHost)
		{
			return;
		}
		this.RpcLogic___SpawnBulletTrail_4276783012(vector);
	}

	// Token: 0x060007F1 RID: 2033 RVA: 0x00037B78 File Offset: 0x00035D78
	private void RpcWriter___Server_SpawnVFXServer_606331033(int index, Vector3 hitPoint, Vector3 hitNormal, string surface, Transform parent)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteInt32(index, AutoPackType.Packed);
		writer.WriteVector3(hitPoint);
		writer.WriteVector3(hitNormal);
		writer.WriteString(surface);
		writer.WriteTransform(parent);
		base.SendServerRpc(26U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x060007F2 RID: 2034 RVA: 0x00037CB2 File Offset: 0x00035EB2
	private void RpcLogic___SpawnVFXServer_606331033(int index, Vector3 hitPoint, Vector3 hitNormal, string surface, Transform parent)
	{
		this.SpawnVFX(index, hitPoint, hitNormal, surface, parent);
	}

	// Token: 0x060007F3 RID: 2035 RVA: 0x00037CC4 File Offset: 0x00035EC4
	private void RpcReader___Server_SpawnVFXServer_606331033(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		int num = PooledReader0.ReadInt32(AutoPackType.Packed);
		Vector3 vector = PooledReader0.ReadVector3();
		Vector3 vector2 = PooledReader0.ReadVector3();
		string text = PooledReader0.ReadString();
		Transform transform = PooledReader0.ReadTransform();
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___SpawnVFXServer_606331033(num, vector, vector2, text, transform);
	}

	// Token: 0x060007F4 RID: 2036 RVA: 0x00037D5C File Offset: 0x00035F5C
	private void RpcWriter___Observers_SpawnVFX_606331033(int index, Vector3 hitPoint, Vector3 hitNormal, string surface, Transform parent)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteInt32(index, AutoPackType.Packed);
		writer.WriteVector3(hitPoint);
		writer.WriteVector3(hitNormal);
		writer.WriteString(surface);
		writer.WriteTransform(parent);
		base.SendObserversRpc(27U, writer, channel, DataOrderType.Default, false, false, true);
		writer.Store();
	}

	// Token: 0x060007F5 RID: 2037 RVA: 0x00037E4C File Offset: 0x0003604C
	private void RpcLogic___SpawnVFX_606331033(int index, Vector3 hitPoint, Vector3 hitNormal, string surface, Transform parent)
	{
		if (Settings.Instance.reduceVFX)
		{
			return;
		}
		if (this.genericImpact)
		{
			global::UnityEngine.Object.Instantiate<GameObject>(this.genericImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
		}
		uint num = <PrivateImplementationDetails>.ComputeStringHash(surface);
		if (num <= 1825421690U)
		{
			if (num <= 1378315797U)
			{
				if (num <= 464173256U)
				{
					if (num != 456440475U)
					{
						if (num == 464173256U)
						{
							if (surface == "Footsteps/Concrete/Dalles")
							{
								if (index == 0)
								{
									global::UnityEngine.Object.Instantiate<GameObject>(this.concreteHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
								}
								if (index == 1)
								{
									global::UnityEngine.Object.Instantiate<GameObject>(this.bulletHole, hitPoint, Quaternion.LookRotation(hitNormal), parent);
									goto IL_06E3;
								}
								goto IL_06E3;
							}
						}
					}
					else if (surface == "Footsteps/Sand")
					{
						if (index == 0)
						{
							global::UnityEngine.Object.Instantiate<GameObject>(this.sandHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
						}
						if (index == 1)
						{
							global::UnityEngine.Object.Instantiate<GameObject>(this.sandHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
							goto IL_06E3;
						}
						goto IL_06E3;
					}
				}
				else if (num != 913360285U)
				{
					if (num != 977466297U)
					{
						if (num == 1378315797U)
						{
							if (surface == "Footsteps/Moquette")
							{
								if (index == 0)
								{
									global::UnityEngine.Object.Instantiate<GameObject>(this.softbodyHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
								}
								if (index == 1)
								{
									global::UnityEngine.Object.Instantiate<GameObject>(this.softbodyHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
									goto IL_06E3;
								}
								goto IL_06E3;
							}
						}
					}
					else if (surface == "Footsteps/Concrete/Solide")
					{
						if (index == 0)
						{
							global::UnityEngine.Object.Instantiate<GameObject>(this.concreteHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
						}
						if (index == 1)
						{
							global::UnityEngine.Object.Instantiate<GameObject>(this.bulletHole, hitPoint, Quaternion.LookRotation(hitNormal), parent);
							goto IL_06E3;
						}
						goto IL_06E3;
					}
				}
				else if (surface == "Grenade")
				{
					goto IL_06E3;
				}
			}
			else if (num <= 1430892386U)
			{
				if (num != 1429664136U)
				{
					if (num == 1430892386U)
					{
						if (surface == "Hat")
						{
							goto IL_06E3;
						}
					}
				}
				else if (surface == "Footsteps/Water")
				{
					if (index == 0)
					{
						global::UnityEngine.Object.Instantiate<GameObject>(this.waterHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
					}
					if (index == 1)
					{
						global::UnityEngine.Object.Instantiate<GameObject>(this.waterHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
						goto IL_06E3;
					}
					goto IL_06E3;
				}
			}
			else if (num != 1610969363U)
			{
				if (num != 1624496828U)
				{
					if (num == 1825421690U)
					{
						if (surface == "Footsteps/Wood/Creux")
						{
							if (index == 0)
							{
								global::UnityEngine.Object.Instantiate<GameObject>(this.woodHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
							}
							if (index == 1)
							{
								global::UnityEngine.Object.Instantiate<GameObject>(this.woodHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
								goto IL_06E3;
							}
							goto IL_06E3;
						}
					}
				}
				else if (surface == "Footsteps/Concrete/Default")
				{
					if (index == 0)
					{
						global::UnityEngine.Object.Instantiate<GameObject>(this.concreteHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
					}
					if (index == 1)
					{
						global::UnityEngine.Object.Instantiate<GameObject>(this.bulletHole, hitPoint, Quaternion.LookRotation(hitNormal), parent);
						goto IL_06E3;
					}
					goto IL_06E3;
				}
			}
			else if (surface == "Footsteps/Concrete/PleinAir")
			{
				if (index == 0)
				{
					global::UnityEngine.Object.Instantiate<GameObject>(this.concreteHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
				}
				if (index == 1)
				{
					global::UnityEngine.Object.Instantiate<GameObject>(this.bulletHole, hitPoint, Quaternion.LookRotation(hitNormal), parent);
					goto IL_06E3;
				}
				goto IL_06E3;
			}
		}
		else if (num <= 2833365437U)
		{
			if (num <= 2180110808U)
			{
				if (num != 1954265137U)
				{
					if (num == 2180110808U)
					{
						if (surface == "Footsteps/Dirt")
						{
							if (index == 0)
							{
								global::UnityEngine.Object.Instantiate<GameObject>(this.dirtHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
							}
							if (index == 1)
							{
								global::UnityEngine.Object.Instantiate<GameObject>(this.dirtHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
								goto IL_06E3;
							}
							goto IL_06E3;
						}
					}
				}
				else if (surface == "NoSound")
				{
					goto IL_06E3;
				}
			}
			else if (num != 2251139421U)
			{
				if (num != 2400559108U)
				{
					if (num == 2833365437U)
					{
						if (surface == "Footsteps/Metal/Pipe2")
						{
							if (index == 0)
							{
								global::UnityEngine.Object.Instantiate<GameObject>(this.metalHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
							}
							if (index == 1)
							{
								global::UnityEngine.Object.Instantiate<GameObject>(this.metalHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
								goto IL_06E3;
							}
							goto IL_06E3;
						}
					}
				}
				else if (surface == "Footsteps/Matelas")
				{
					if (index == 0)
					{
						global::UnityEngine.Object.Instantiate<GameObject>(this.softbodyHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
					}
					if (index == 1)
					{
						global::UnityEngine.Object.Instantiate<GameObject>(this.softbodyHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
						goto IL_06E3;
					}
					goto IL_06E3;
				}
			}
			else if (surface == "Footsteps/Metal/Pipe")
			{
				if (index == 0)
				{
					global::UnityEngine.Object.Instantiate<GameObject>(this.tauleHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
				}
				if (index == 1)
				{
					global::UnityEngine.Object.Instantiate<GameObject>(this.tauleHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
					goto IL_06E3;
				}
				goto IL_06E3;
			}
		}
		else if (num <= 3455850406U)
		{
			if (num != 3075599786U)
			{
				if (num == 3455850406U)
				{
					if (surface == "Footsteps/Graviers")
					{
						if (index == 0)
						{
							global::UnityEngine.Object.Instantiate<GameObject>(this.concreteHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
						}
						if (index == 1)
						{
							global::UnityEngine.Object.Instantiate<GameObject>(this.bulletHole, hitPoint, Quaternion.LookRotation(hitNormal), parent);
							goto IL_06E3;
						}
						goto IL_06E3;
					}
				}
			}
			else if (surface == "Footsteps/Wood/Sec")
			{
				if (index == 0)
				{
					global::UnityEngine.Object.Instantiate<GameObject>(this.woodHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
				}
				if (index == 1)
				{
					global::UnityEngine.Object.Instantiate<GameObject>(this.woodHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
					goto IL_06E3;
				}
				goto IL_06E3;
			}
		}
		else if (num != 3492154005U)
		{
			if (num != 3807801418U)
			{
				if (num == 3848897750U)
				{
					if (surface == "Mine")
					{
						goto IL_06E3;
					}
				}
			}
			else if (surface == "Footsteps/Metal/Grille")
			{
				if (index == 0)
				{
					global::UnityEngine.Object.Instantiate<GameObject>(this.tauleHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
				}
				if (index == 1)
				{
					global::UnityEngine.Object.Instantiate<GameObject>(this.tauleHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
					goto IL_06E3;
				}
				goto IL_06E3;
			}
		}
		else if (surface == "Footsteps/Grass")
		{
			if (index == 0)
			{
				global::UnityEngine.Object.Instantiate<GameObject>(this.dirtHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
			}
			if (index == 1)
			{
				global::UnityEngine.Object.Instantiate<GameObject>(this.dirtHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
				goto IL_06E3;
			}
			goto IL_06E3;
		}
		if (index == 0)
		{
			global::UnityEngine.Object.Instantiate<GameObject>(this.concreteHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
		}
		if (index == 1)
		{
			global::UnityEngine.Object.Instantiate<GameObject>(this.bulletHole, hitPoint, Quaternion.LookRotation(hitNormal), parent);
		}
		IL_06E3:
		if (index == 2)
		{
			global::UnityEngine.Object.Instantiate<GameObject>(this.bodyImpact, hitPoint, Quaternion.identity, parent);
		}
	}

	// Token: 0x060007F6 RID: 2038 RVA: 0x00038554 File Offset: 0x00036754
	private void RpcReader___Observers_SpawnVFX_606331033(PooledReader PooledReader0, Channel channel)
	{
		int num = PooledReader0.ReadInt32(AutoPackType.Packed);
		Vector3 vector = PooledReader0.ReadVector3();
		Vector3 vector2 = PooledReader0.ReadVector3();
		string text = PooledReader0.ReadString();
		Transform transform = PooledReader0.ReadTransform();
		if (!base.IsClient)
		{
			return;
		}
		if (base.IsHost)
		{
			return;
		}
		this.RpcLogic___SpawnVFX_606331033(num, vector, vector2, text, transform);
	}

	// Token: 0x060007F7 RID: 2039 RVA: 0x000385D8 File Offset: 0x000367D8
	private void RpcWriter___Server_SupressionServer_3068987916(Transform supp)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteTransform(supp);
		base.SendServerRpc(28U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x060007F8 RID: 2040 RVA: 0x000386D9 File Offset: 0x000368D9
	private void RpcLogic___SupressionServer_3068987916(Transform supp)
	{
		this.SuppressionTarget(supp);
	}

	// Token: 0x060007F9 RID: 2041 RVA: 0x000386E4 File Offset: 0x000368E4
	private void RpcReader___Server_SupressionServer_3068987916(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		Transform transform = PooledReader0.ReadTransform();
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		this.RpcLogic___SupressionServer_3068987916(transform);
	}

	// Token: 0x060007FA RID: 2042 RVA: 0x00038728 File Offset: 0x00036928
	private void RpcWriter___Observers_SuppressionTarget_3068987916(Transform supp)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteTransform(supp);
		base.SendObserversRpc(29U, writer, channel, DataOrderType.Default, false, false, true);
		writer.Store();
	}

	// Token: 0x060007FB RID: 2043 RVA: 0x00027B2A File Offset: 0x00025D2A
	private void RpcLogic___SuppressionTarget_3068987916(Transform supp)
	{
		supp.GetComponent<Suppression>().SuppressionTrigger();
	}

	// Token: 0x060007FC RID: 2044 RVA: 0x000387E0 File Offset: 0x000369E0
	private void RpcReader___Observers_SuppressionTarget_3068987916(PooledReader PooledReader0, Channel channel)
	{
		Transform transform = PooledReader0.ReadTransform();
		if (!base.IsClient)
		{
			return;
		}
		this.RpcLogic___SuppressionTarget_3068987916(transform);
	}

	// Token: 0x060007FD RID: 2045 RVA: 0x00038814 File Offset: 0x00036A14
	private void RpcWriter___Server_ServerFX_3848837105(Vector3 position, Quaternion rotation)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteVector3(position);
		writer.WriteQuaternion(rotation, AutoPackType.Packed);
		base.SendServerRpc(30U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x060007FE RID: 2046 RVA: 0x00038927 File Offset: 0x00036B27
	public void RpcLogic___ServerFX_3848837105(Vector3 position, Quaternion rotation)
	{
		this.ObserversFX(position, rotation);
	}

	// Token: 0x060007FF RID: 2047 RVA: 0x00038934 File Offset: 0x00036B34
	private void RpcReader___Server_ServerFX_3848837105(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		Vector3 vector = PooledReader0.ReadVector3();
		Quaternion quaternion = PooledReader0.ReadQuaternion(AutoPackType.Packed);
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___ServerFX_3848837105(vector, quaternion);
	}

	// Token: 0x06000800 RID: 2048 RVA: 0x0003899C File Offset: 0x00036B9C
	private void RpcWriter___Observers_ObserversFX_3848837105(Vector3 position, Quaternion rotation)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteVector3(position);
		writer.WriteQuaternion(rotation, AutoPackType.Packed);
		base.SendObserversRpc(31U, writer, channel, DataOrderType.Default, false, false, true);
		writer.Store();
	}

	// Token: 0x06000801 RID: 2049 RVA: 0x000272E4 File Offset: 0x000254E4
	private void RpcLogic___ObserversFX_3848837105(Vector3 position, Quaternion rotation)
	{
		global::UnityEngine.Object.Instantiate<GameObject>(this.bloodSplatter, position, rotation);
	}

	// Token: 0x06000802 RID: 2050 RVA: 0x00038A64 File Offset: 0x00036C64
	private void RpcReader___Observers_ObserversFX_3848837105(PooledReader PooledReader0, Channel channel)
	{
		Vector3 vector = PooledReader0.ReadVector3();
		Quaternion quaternion = PooledReader0.ReadQuaternion(AutoPackType.Packed);
		if (!base.IsClient)
		{
			return;
		}
		if (base.IsHost)
		{
			return;
		}
		this.RpcLogic___ObserversFX_3848837105(vector, quaternion);
	}

	// Token: 0x06000803 RID: 2051 RVA: 0x00038AB5 File Offset: 0x00036CB5
	public override void Awake()
	{
		this.NetworkInitialize___Early();
		base.Awake();
		this.NetworkInitialize__Late();
	}

	// Token: 0x06000804 RID: 2052 RVA: 0x000023D6 File Offset: 0x000005D6
	public override void Awake___UserLogic()
	{
	}

	// Token: 0x04000720 RID: 1824
	[Header("Weapon Specials")]
	[SerializeField]
	private float reloadTime;

	// Token: 0x04000721 RID: 1825
	[SerializeField]
	private float bulletRadius = 0.2f;

	// Token: 0x04000722 RID: 1826
	[SerializeField]
	private bool boxcast;

	// Token: 0x04000723 RID: 1827
	[SerializeField]
	private bool determineLengthWithRay;

	// Token: 0x04000724 RID: 1828
	[SerializeField]
	private float boxcenteroffset;

	// Token: 0x04000725 RID: 1829
	[SerializeField]
	private Vector3 boxdimensions;

	// Token: 0x04000726 RID: 1830
	[SerializeField]
	private float playerKnockback;

	// Token: 0x04000727 RID: 1831
	private Vector3 endPoint;

	// Token: 0x04000728 RID: 1832
	[SerializeField]
	private AudioClip reloadClip;

	// Token: 0x04000729 RID: 1833
	private float fireTimer;

	// Token: 0x0400072A RID: 1834
	private bool touched;

	// Token: 0x0400072B RID: 1835
	private Vector3 spread;

	// Token: 0x0400072C RID: 1836
	private bool hitOK = true;

	// Token: 0x0400072D RID: 1837
	private PlayerHealth enemyHealth;

	// Token: 0x0400072E RID: 1838
	private bool NetworkInitializeEarly_LargeRaycastGun_Assembly-CSharp.dll;

	// Token: 0x0400072F RID: 1839
	private bool NetworkInitializeLate_LargeRaycastGun_Assembly-CSharp.dll;
}

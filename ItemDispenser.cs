﻿using System;
using System.Collections;
using FishNet;
using FishNet.Connection;
using FishNet.Managing;
using FishNet.Object;
using FishNet.Object.Delegating;
using FishNet.Object.Synchronizing;
using FishNet.Serializing;
using FishNet.Transporting;
using UnityEngine;

// Token: 0x02000058 RID: 88
public class ItemDispenser : InteractEnvironment
{
	// Token: 0x060003E2 RID: 994 RVA: 0x0001C11E File Offset: 0x0001A31E
	private void Start()
	{
		this.audio = base.GetComponent<AudioSource>();
	}

	// Token: 0x060003E3 RID: 995 RVA: 0x0001C12C File Offset: 0x0001A32C
	public override void OnFocus()
	{
		PauseManager.Instance.interactPopup.gameObject.SetActive(true);
		PauseManager.Instance.interactPopup.text = this.popupText.ToLower() + " [" + PauseManager.Instance.InteractPromptLetter.ToLower() + "]";
	}

	// Token: 0x060003E4 RID: 996 RVA: 0x0001C186 File Offset: 0x0001A386
	public override void OnInteract(Transform player)
	{
		if (this.SyncAccessor_timer > 0f || this.localTimer > 0f)
		{
			return;
		}
		this.CmdTimer();
		base.StartCoroutine(this.SpawnItem());
	}

	// Token: 0x060003E5 RID: 997 RVA: 0x0001C1B6 File Offset: 0x0001A3B6
	private IEnumerator SpawnItem()
	{
		yield return new WaitForSeconds(0.2f);
		if (SpawnerManager.Instance.SyncAccessor_randomiseWeapons)
		{
			this.item = SpawnerManager.Instance.GetRandomSpawnableWeapon();
		}
		else
		{
			this.item = this.itemsToSpawn[global::UnityEngine.Random.Range(0, this.itemsToSpawn.Length)];
		}
		this.SpawnWeapon(this.item);
		yield break;
	}

	// Token: 0x060003E6 RID: 998 RVA: 0x000023D6 File Offset: 0x000005D6
	public override void OnLoseFocus()
	{
	}

	// Token: 0x060003E7 RID: 999 RVA: 0x0001C1C8 File Offset: 0x0001A3C8
	private void Update()
	{
		this.sync___set_value_timer(this.SyncAccessor_timer - Time.deltaTime, true);
		this.localTimer -= Time.deltaTime;
		if (((this.SyncAccessor_timer > 0f && this.SyncAccessor_timer < this.countdown - 0.3f) || (this.localTimer > 0f && this.localTimer < this.countdown - 0.3f)) && this.availableScreen.activeSelf)
		{
			this.availableScreen.SetActive(false);
			this.loadingScreen.SetActive(true);
			this.light.color = this.loadingLightColor;
			Material[] array = new Material[] { this.loadingLightMat };
			this.screen.materials = array;
			return;
		}
		if ((this.SyncAccessor_timer <= 0f || this.localTimer <= 0f) && this.loadingScreen.activeSelf)
		{
			this.availableScreen.SetActive(true);
			this.loadingScreen.SetActive(false);
			this.light.color = this.availableLightColor;
			Material[] array2 = new Material[] { this.availableLightMat };
			this.screen.materials = array2;
		}
	}

	// Token: 0x060003E8 RID: 1000 RVA: 0x0001C2FB File Offset: 0x0001A4FB
	[ServerRpc(RunLocally = true, RequireOwnership = false)]
	private void CmdTimer()
	{
		this.RpcWriter___Server_CmdTimer_2166136261();
		this.RpcLogic___CmdTimer_2166136261();
	}

	// Token: 0x060003E9 RID: 1001 RVA: 0x0001C309 File Offset: 0x0001A509
	[ObserversRpc]
	private void PlaySound()
	{
		this.RpcWriter___Observers_PlaySound_2166136261();
	}

	// Token: 0x060003EA RID: 1002 RVA: 0x0001C314 File Offset: 0x0001A514
	[ServerRpc(RequireOwnership = false)]
	private void SpawnWeapon(GameObject item)
	{
		this.RpcWriter___Server_SpawnWeapon_1934289915(item);
	}

	// Token: 0x060003EC RID: 1004 RVA: 0x0001C340 File Offset: 0x0001A540
	public override void NetworkInitialize___Early()
	{
		if (this.NetworkInitializeEarly_ItemDispenser_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeEarly_ItemDispenser_Assembly-CSharp.dll = true;
		base.NetworkInitialize___Early();
		this.syncVar___timer = new SyncVar<float>(this, 1U, WritePermission.ClientUnsynchronized, ReadPermission.Observers, -1f, Channel.Reliable, this.timer);
		this.syncVar___spawnedItem = new SyncVar<GameObject>(this, 0U, WritePermission.ServerOnly, ReadPermission.Observers, -1f, Channel.Reliable, this.spawnedItem);
		base.RegisterServerRpc(0U, new ServerRpcDelegate(this.RpcReader___Server_CmdTimer_2166136261));
		base.RegisterObserversRpc(1U, new ClientRpcDelegate(this.RpcReader___Observers_PlaySound_2166136261));
		base.RegisterServerRpc(2U, new ServerRpcDelegate(this.RpcReader___Server_SpawnWeapon_1934289915));
		base.RegisterSyncVarRead(new SyncVarReadDelegate(this.ReadSyncVar___ItemDispenser));
	}

	// Token: 0x060003ED RID: 1005 RVA: 0x0001C411 File Offset: 0x0001A611
	public override void NetworkInitialize__Late()
	{
		if (this.NetworkInitializeLate_ItemDispenser_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeLate_ItemDispenser_Assembly-CSharp.dll = true;
		base.NetworkInitialize__Late();
		this.syncVar___timer.SetRegistered();
		this.syncVar___spawnedItem.SetRegistered();
	}

	// Token: 0x060003EE RID: 1006 RVA: 0x0001C440 File Offset: 0x0001A640
	public override void NetworkInitializeIfDisabled()
	{
		this.NetworkInitialize___Early();
		this.NetworkInitialize__Late();
	}

	// Token: 0x060003EF RID: 1007 RVA: 0x0001C450 File Offset: 0x0001A650
	private void RpcWriter___Server_CmdTimer_2166136261()
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendServerRpc(0U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x060003F0 RID: 1008 RVA: 0x0001C4EA File Offset: 0x0001A6EA
	private void RpcLogic___CmdTimer_2166136261()
	{
		this.sync___set_value_timer(this.countdown, true);
		this.localTimer = this.countdown;
		this.PlaySound();
	}

	// Token: 0x060003F1 RID: 1009 RVA: 0x0001C50C File Offset: 0x0001A70C
	private void RpcReader___Server_CmdTimer_2166136261(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		if (!base.IsServer)
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___CmdTimer_2166136261();
	}

	// Token: 0x060003F2 RID: 1010 RVA: 0x0001C53C File Offset: 0x0001A73C
	private void RpcWriter___Observers_PlaySound_2166136261()
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendObserversRpc(1U, writer, channel, DataOrderType.Default, false, false, false);
		writer.Store();
	}

	// Token: 0x060003F3 RID: 1011 RVA: 0x0001C5E5 File Offset: 0x0001A7E5
	private void RpcLogic___PlaySound_2166136261()
	{
		this.anim.SetTrigger("Trigger");
		this.audio.PlayOneShot(this.triggerClip);
	}

	// Token: 0x060003F4 RID: 1012 RVA: 0x0001C608 File Offset: 0x0001A808
	private void RpcReader___Observers_PlaySound_2166136261(PooledReader PooledReader0, Channel channel)
	{
		if (!base.IsClient)
		{
			return;
		}
		this.RpcLogic___PlaySound_2166136261();
	}

	// Token: 0x060003F5 RID: 1013 RVA: 0x0001C628 File Offset: 0x0001A828
	private void RpcWriter___Server_SpawnWeapon_1934289915(GameObject item)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteGameObject(item);
		base.SendServerRpc(2U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x060003F6 RID: 1014 RVA: 0x0001C6D0 File Offset: 0x0001A8D0
	private void RpcLogic___SpawnWeapon_1934289915(GameObject item)
	{
		GameObject gameObject = global::UnityEngine.Object.Instantiate<GameObject>(item, this.origin.position, Quaternion.identity);
		base.ServerManager.Spawn(gameObject, null);
		this.sync___set_value_spawnedItem(gameObject, true);
		gameObject.GetComponent<ItemBehaviour>().DispenserDrop(this.origin.forward);
		this.sync___set_value_timer(this.countdown, true);
		this.localTimer = this.countdown;
	}

	// Token: 0x060003F7 RID: 1015 RVA: 0x0001C738 File Offset: 0x0001A938
	private void RpcReader___Server_SpawnWeapon_1934289915(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		GameObject gameObject = PooledReader0.ReadGameObject();
		if (!base.IsServer)
		{
			return;
		}
		this.RpcLogic___SpawnWeapon_1934289915(gameObject);
	}

	// Token: 0x17000066 RID: 102
	// (get) Token: 0x060003F8 RID: 1016 RVA: 0x0001C769 File Offset: 0x0001A969
	// (set) Token: 0x060003F9 RID: 1017 RVA: 0x0001C771 File Offset: 0x0001A971
	public GameObject SyncAccessor_spawnedItem
	{
		get
		{
			return this.spawnedItem;
		}
		set
		{
			if (value || !base.IsServer)
			{
				this.spawnedItem = value;
			}
			this.syncVar___spawnedItem.SetValue(value, value);
		}
	}

	// Token: 0x060003FA RID: 1018 RVA: 0x0001C7A8 File Offset: 0x0001A9A8
	public virtual bool ReadSyncVar___ItemDispenser(PooledReader PooledReader0, uint UInt321, bool Boolean2)
	{
		if (UInt321 == 1U)
		{
			if (PooledReader0 == null)
			{
				this.sync___set_value_timer(this.syncVar___timer.GetValue(true), true);
				return true;
			}
			float num = PooledReader0.ReadSingle(AutoPackType.Unpacked);
			this.sync___set_value_timer(num, Boolean2);
			return true;
		}
		else
		{
			if (UInt321 != 0U)
			{
				return false;
			}
			if (PooledReader0 == null)
			{
				this.sync___set_value_spawnedItem(this.syncVar___spawnedItem.GetValue(true), true);
				return true;
			}
			GameObject gameObject = PooledReader0.ReadGameObject();
			this.sync___set_value_spawnedItem(gameObject, Boolean2);
			return true;
		}
	}

	// Token: 0x17000067 RID: 103
	// (get) Token: 0x060003FB RID: 1019 RVA: 0x0001C843 File Offset: 0x0001AA43
	// (set) Token: 0x060003FC RID: 1020 RVA: 0x0001C84B File Offset: 0x0001AA4B
	public float SyncAccessor_timer
	{
		get
		{
			return this.timer;
		}
		set
		{
			if (value || !base.IsServer)
			{
				this.timer = value;
			}
			this.syncVar___timer.SetValue(value, value);
		}
	}

	// Token: 0x060003FD RID: 1021 RVA: 0x0001C880 File Offset: 0x0001AA80
	public override void Awake()
	{
		this.NetworkInitialize___Early();
		base.Awake();
		this.NetworkInitialize__Late();
	}

	// Token: 0x060003FE RID: 1022 RVA: 0x000023D6 File Offset: 0x000005D6
	public override void Awake___UserLogic()
	{
	}

	// Token: 0x04000439 RID: 1081
	public GameObject[] itemsToSpawn;

	// Token: 0x0400043A RID: 1082
	public Vector3 restPos;

	// Token: 0x0400043B RID: 1083
	[SyncVar]
	public GameObject spawnedItem;

	// Token: 0x0400043C RID: 1084
	[SerializeField]
	private Transform origin;

	// Token: 0x0400043D RID: 1085
	[SerializeField]
	private Animator anim;

	// Token: 0x0400043E RID: 1086
	[SerializeField]
	private AudioClip triggerClip;

	// Token: 0x0400043F RID: 1087
	[SyncVar(WritePermissions = WritePermission.ClientUnsynchronized)]
	public float timer;

	// Token: 0x04000440 RID: 1088
	private float localTimer;

	// Token: 0x04000441 RID: 1089
	[SerializeField]
	private float countdown = 3f;

	// Token: 0x04000442 RID: 1090
	[Space]
	[SerializeField]
	private Light light;

	// Token: 0x04000443 RID: 1091
	[SerializeField]
	private MeshRenderer screen;

	// Token: 0x04000444 RID: 1092
	[SerializeField]
	private GameObject availableScreen;

	// Token: 0x04000445 RID: 1093
	[SerializeField]
	private GameObject loadingScreen;

	// Token: 0x04000446 RID: 1094
	[SerializeField]
	private Color availableLightColor;

	// Token: 0x04000447 RID: 1095
	[SerializeField]
	private Color loadingLightColor;

	// Token: 0x04000448 RID: 1096
	[SerializeField]
	private Material availableLightMat;

	// Token: 0x04000449 RID: 1097
	[SerializeField]
	private Material loadingLightMat;

	// Token: 0x0400044A RID: 1098
	private AudioSource audio;

	// Token: 0x0400044B RID: 1099
	private GameObject item;

	// Token: 0x0400044C RID: 1100
	public SyncVar<GameObject> syncVar___spawnedItem;

	// Token: 0x0400044D RID: 1101
	public SyncVar<float> syncVar___timer;

	// Token: 0x0400044E RID: 1102
	private bool NetworkInitializeEarly_ItemDispenser_Assembly-CSharp.dll;

	// Token: 0x0400044F RID: 1103
	private bool NetworkInitializeLate_ItemDispenser_Assembly-CSharp.dll;
}

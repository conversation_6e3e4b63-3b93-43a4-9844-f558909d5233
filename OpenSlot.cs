﻿using System;
using FishNet;
using TMPro;
using UnityEngine;

// Token: 0x02000062 RID: 98
public class OpenSlot : MonoBehaviour
{
	// Token: 0x06000428 RID: 1064 RVA: 0x0001D3D4 File Offset: 0x0001B5D4
	private void Start()
	{
		this.steamLobby = SteamLobby.Instance;
	}

	// Token: 0x06000429 RID: 1065 RVA: 0x0001D3E4 File Offset: 0x0001B5E4
	private void Update()
	{
		if (InstanceFinder.NetworkManager == null)
		{
			return;
		}
		if (InstanceFinder.NetworkManager.IsServer && this.steamLobby.maxPlayers < 4)
		{
			base.transform.localScale = Vector3.one;
			return;
		}
		base.transform.localScale = Vector3.zero;
	}

	// Token: 0x0600042A RID: 1066 RVA: 0x0001D43A File Offset: 0x0001B63A
	public void AddSlot(TMP_Dropdown dropdown)
	{
		dropdown.value++;
	}

	// Token: 0x04000474 RID: 1140
	private SteamLobby steamLobby;
}

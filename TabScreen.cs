﻿using System;
using TMPro;
using UnityEngine;
using UnityEngine.SceneManagement;

// Token: 0x02000136 RID: 310
public class TabScreen : MonoBehaviour
{
	// Token: 0x06000E7D RID: 3709 RVA: 0x0005FB7C File Offset: 0x0005DD7C
	private void Awake()
	{
		if (TabScreen.Instance == null)
		{
			TabScreen.Instance = this;
		}
	}

	// Token: 0x06000E7E RID: 3710 RVA: 0x0005FB91 File Offset: 0x0005DD91
	private void Start()
	{
		this.pauseManager = PauseManager.Instance;
		this.manager = SteamLobby.Instance;
	}

	// Token: 0x06000E7F RID: 3711 RVA: 0x0005FBAC File Offset: 0x0005DDAC
	private void Update()
	{
		if (this.pauseManager.inMainMenu)
		{
			return;
		}
		this.roundText.text = string.Concat(new string[]
		{
			"round ",
			SceneMotor.Instance.sceneIndex.ToString(),
			"   First to ",
			SceneMotor.Instance.roundAmount.ToString(),
			" wins"
		});
		this.mapText.text = "current map :  " + SceneManager.GetActiveScene().name;
		this.scoreOne.text = ScoreManager.Instance.GetPoints(ScoreManager.Instance.GetTeamId(0)).ToString();
		this.scoreTwo.text = ScoreManager.Instance.GetPoints(ScoreManager.Instance.GetTeamId(1)).ToString();
		this.scoreThree.text = ScoreManager.Instance.GetPoints(ScoreManager.Instance.GetTeamId(2)).ToString();
		this.scoreFour.text = ScoreManager.Instance.GetPoints(ScoreManager.Instance.GetTeamId(3)).ToString();
	}

	// Token: 0x04000D08 RID: 3336
	[SerializeField]
	private TextMeshProUGUI roundText;

	// Token: 0x04000D09 RID: 3337
	[SerializeField]
	private TextMeshProUGUI scoreOne;

	// Token: 0x04000D0A RID: 3338
	[SerializeField]
	private TextMeshProUGUI scoreTwo;

	// Token: 0x04000D0B RID: 3339
	[SerializeField]
	private TextMeshProUGUI scoreThree;

	// Token: 0x04000D0C RID: 3340
	[SerializeField]
	private TextMeshProUGUI scoreFour;

	// Token: 0x04000D0D RID: 3341
	[SerializeField]
	private TextMeshProUGUI mapText;

	// Token: 0x04000D0E RID: 3342
	private PauseManager pauseManager;

	// Token: 0x04000D0F RID: 3343
	private SteamLobby manager;

	// Token: 0x04000D10 RID: 3344
	public static TabScreen Instance;
}

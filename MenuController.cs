﻿using System;
using UnityEngine;
using UnityEngine.Rendering.PostProcessing;

// Token: 0x0200012B RID: 299
public class MenuController : MonoBehaviour
{
	// Token: 0x06000E4C RID: 3660 RVA: 0x0005F298 File Offset: 0x0005D498
	private void Awake()
	{
		if (MenuController.Instance == null)
		{
			MenuController.Instance = this;
		}
		DepthOfField depthOfField;
		if (this.blurCamera.profile.TryGetSettings<DepthOfField>(out depthOfField))
		{
			this.depth = depthOfField;
		}
	}

	// Token: 0x06000E4D RID: 3661 RVA: 0x0005F2D3 File Offset: 0x0005D4D3
	private void Start()
	{
		this.depth.enabled.value = false;
	}

	// Token: 0x06000E4E RID: 3662 RVA: 0x000023D6 File Offset: 0x000005D6
	private void Update()
	{
	}

	// Token: 0x06000E4F RID: 3663 RVA: 0x0005F2E6 File Offset: 0x0005D4E6
	public void OpenGame()
	{
		if (this.startMenu.activeSelf)
		{
			SoundManager.Instance.PlaySound(this.openingClip);
		}
		this.ActivateMenu(this.playMenu);
	}

	// Token: 0x06000E50 RID: 3664 RVA: 0x0005F314 File Offset: 0x0005D514
	public void ActivateMenu(GameObject menu)
	{
		SoundManager.Instance.SetHighpass = false;
		SoundManager.Instance.SetLowpass = false;
		this.depth.enabled.value = true;
		this.mapsMenu.SetActive(false);
		this.hatsMenu.SetActive(false);
		this.playMenu.SetActive(false);
		this.statsMenu.SetActive(false);
		this.progressMenu.SetActive(false);
		this.startMenu.SetActive(false);
		this.buttonBar.SetActive(true);
		this.hud.SetActive(true);
		menu.SetActive(true);
		if (menu == this.playMenu)
		{
			this.backButton.SetActive(false);
			this.startScreenButton.SetActive(true);
		}
		else
		{
			this.backButton.SetActive(true);
			this.startScreenButton.SetActive(false);
		}
		if (menu == this.mapsMenu)
		{
			MapsManager.Instance.OpenFirstPlaylist();
		}
		if (menu == this.mapsMenu)
		{
			SoundManager.Instance.SetHighpass = true;
		}
		if (menu == this.hatsMenu)
		{
			SoundManager.Instance.SetLowpass = true;
		}
	}

	// Token: 0x06000E51 RID: 3665 RVA: 0x0005F43C File Offset: 0x0005D63C
	public void OpenStartMenu()
	{
		this.mapsMenu.SetActive(false);
		this.hatsMenu.SetActive(false);
		this.playMenu.SetActive(false);
		this.statsMenu.SetActive(false);
		this.progressMenu.SetActive(false);
		this.startMenu.SetActive(true);
		this.buttonBar.SetActive(false);
		this.hud.SetActive(false);
		this.depth.enabled.value = false;
		PauseManager.Instance.ChangeSelectedItem(this.PlayButton);
	}

	// Token: 0x04000CDC RID: 3292
	[SerializeField]
	private PostProcessVolume blurCamera;

	// Token: 0x04000CDD RID: 3293
	[SerializeField]
	private DepthOfField depth;

	// Token: 0x04000CDE RID: 3294
	[SerializeField]
	private GameObject mapsMenu;

	// Token: 0x04000CDF RID: 3295
	public GameObject hatsMenu;

	// Token: 0x04000CE0 RID: 3296
	public GameObject playMenu;

	// Token: 0x04000CE1 RID: 3297
	[SerializeField]
	private GameObject statsMenu;

	// Token: 0x04000CE2 RID: 3298
	[SerializeField]
	private GameObject progressMenu;

	// Token: 0x04000CE3 RID: 3299
	public GameObject startMenu;

	// Token: 0x04000CE4 RID: 3300
	[SerializeField]
	private GameObject buttonBar;

	// Token: 0x04000CE5 RID: 3301
	[SerializeField]
	private GameObject hud;

	// Token: 0x04000CE6 RID: 3302
	[SerializeField]
	private AudioClip openingClip;

	// Token: 0x04000CE7 RID: 3303
	[Space]
	[SerializeField]
	private GameObject backButton;

	// Token: 0x04000CE8 RID: 3304
	[SerializeField]
	private GameObject startScreenButton;

	// Token: 0x04000CE9 RID: 3305
	public static MenuController Instance;

	// Token: 0x04000CEA RID: 3306
	[Space]
	[SerializeField]
	private GameObject PlayButton;
}

﻿using System;
using FishNet;
using UnityEngine;

// Token: 0x02000087 RID: 135
public class BumpBullet : MonoBehaviour
{
	// Token: 0x0600064F RID: 1615 RVA: 0x00029ECF File Offset: 0x000280CF
	private void Awake()
	{
		this.audio = base.GetComponent<AudioSource>();
		this._gravity = this.gravityStart;
	}

	// Token: 0x06000650 RID: 1616 RVA: 0x00029EE9 File Offset: 0x000280E9
	public void Initialize(Vector3 direction, float force, float passedTime, GameObject rootObject, GameObject gun)
	{
		this._direction = direction;
		this._passedTime = passedTime;
		this._rootObject = rootObject;
		this._force = force;
		this._gun = gun;
	}

	// Token: 0x06000651 RID: 1617 RVA: 0x00029F10 File Offset: 0x00028110
	private void Update()
	{
		this.Move();
		this.HandleCollision();
	}

	// Token: 0x06000652 RID: 1618 RVA: 0x00029F20 File Offset: 0x00028120
	private void Move()
	{
		float deltaTime = Time.deltaTime;
		float num = 0f;
		if (this._passedTime > 0f)
		{
			float num2 = this._passedTime * 0.08f;
			this._passedTime -= num2;
			if (this._passedTime <= deltaTime / 2f)
			{
				num2 += this._passedTime;
				this._passedTime = 0f;
			}
			num = num2;
		}
		if (this.useGravity)
		{
			this._gravity += this.gravity * Time.deltaTime;
		}
		if (this.usePhysics)
		{
			this._force -= this.friction * Time.deltaTime;
		}
		base.transform.position += this._direction * ((this.usePhysics ? this._force : this.MOVE_RATE) * (deltaTime + num));
		if (this.useGravity)
		{
			base.transform.position -= Vector3.up * (this._gravity * (deltaTime + num));
		}
	}

	// Token: 0x06000653 RID: 1619 RVA: 0x0002A038 File Offset: 0x00028238
	private void HandleCollision()
	{
		Collider[] array = Physics.OverlapSphere(base.transform.position, this.radius, this.playerLayer);
		if (array.Length != 0)
		{
			bool flag = false;
			for (int i = 0; i < array.Length; i++)
			{
				flag = array[i].gameObject == this._gun;
			}
			bool flag2 = false;
			for (int j = 0; j < array.Length; j++)
			{
				flag2 = array[j].gameObject == this._rootObject;
			}
			if (InstanceFinder.IsClient && !flag && !flag2)
			{
				SoundManager.Instance.PlaySound(this.hitClip);
				global::UnityEngine.Object.Instantiate<GameObject>(this.hitVfx, base.transform.position, Quaternion.identity);
			}
			if (InstanceFinder.IsServer && !flag && !flag2)
			{
				PlayerHealth componentInParent = array[0].GetComponentInParent<PlayerHealth>();
				if (componentInParent != null && componentInParent.gameObject != this._rootObject)
				{
					componentInParent.AddForce(componentInParent.transform.position - base.transform.position + Vector3.up, this.bumpForce);
				}
				Collider[] array2 = Physics.OverlapSphere(base.transform.position, this.explosionRadius, this.playerLayer);
				if (array2.Length != 0)
				{
					this.ph2 = new PlayerHealth[array2.Length];
					for (int k = 0; k < array2.Length; k++)
					{
						if (array2[k].GetComponentInParent<PlayerHealth>())
						{
							this.ph2[k] = array2[k].GetComponentInParent<PlayerHealth>();
						}
					}
					for (int l = 0; l < this.ph2.Length; l++)
					{
						if (this.ph2[l] != null)
						{
							this.ph2[l].AddForce(this.ph2[l].transform.position - base.transform.position + Vector3.up, this.bumpForce);
						}
					}
				}
			}
			if (!flag && !flag2)
			{
				global::UnityEngine.Object.Destroy(base.gameObject);
			}
		}
	}

	// Token: 0x04000634 RID: 1588
	private Vector3 _direction;

	// Token: 0x04000635 RID: 1589
	private float _passedTime;

	// Token: 0x04000636 RID: 1590
	[SerializeField]
	private float MOVE_RATE = 5f;

	// Token: 0x04000637 RID: 1591
	[SerializeField]
	private float radius = 0.2f;

	// Token: 0x04000638 RID: 1592
	[SerializeField]
	private float explosionRadius = 1f;

	// Token: 0x04000639 RID: 1593
	[SerializeField]
	private float bumpForce = 1f;

	// Token: 0x0400063A RID: 1594
	[SerializeField]
	private float bumpDecel = 6f;

	// Token: 0x0400063B RID: 1595
	[SerializeField]
	private LayerMask playerLayer;

	// Token: 0x0400063C RID: 1596
	[SerializeField]
	private AudioClip hitClip;

	// Token: 0x0400063D RID: 1597
	[SerializeField]
	private GameObject hitVfx;

	// Token: 0x0400063E RID: 1598
	private GameObject _rootObject;

	// Token: 0x0400063F RID: 1599
	private AudioSource audio;

	// Token: 0x04000640 RID: 1600
	[SerializeField]
	private bool useGravity;

	// Token: 0x04000641 RID: 1601
	[SerializeField]
	private float gravityStart;

	// Token: 0x04000642 RID: 1602
	[SerializeField]
	private float gravity;

	// Token: 0x04000643 RID: 1603
	private float _gravity;

	// Token: 0x04000644 RID: 1604
	[SerializeField]
	private bool usePhysics;

	// Token: 0x04000645 RID: 1605
	[SerializeField]
	private float friction;

	// Token: 0x04000646 RID: 1606
	private float _force;

	// Token: 0x04000647 RID: 1607
	private GameObject _gun;

	// Token: 0x04000648 RID: 1608
	private PlayerHealth[] ph2;
}

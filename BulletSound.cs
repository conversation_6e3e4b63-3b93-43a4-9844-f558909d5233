﻿using System;
using UnityEngine;

// Token: 0x02000086 RID: 134
public class BulletSound : MonoBehaviour
{
	// Token: 0x0600064D RID: 1613 RVA: 0x00029EA7 File Offset: 0x000280A7
	private void Start()
	{
		base.GetComponent<AudioSource>().PlayOneShot(this.hitClips[Mathf.RoundToInt((float)global::UnityEngine.Random.Range(0, this.hitClips.Length))]);
	}

	// Token: 0x04000633 RID: 1587
	[SerializeField]
	private AudioClip[] hitClips;
}

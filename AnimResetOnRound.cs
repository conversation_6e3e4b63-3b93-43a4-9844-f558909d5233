﻿using System;
using FishNet.Component.Transforming;
using UnityEngine;

// Token: 0x02000041 RID: 65
public class AnimResetOnRound : MonoBehaviour
{
	// Token: 0x0600032A RID: 810 RVA: 0x00019B82 File Offset: 0x00017D82
	private void OnEnable()
	{
		PauseManager.OnRoundStarted += this.StartNewRound;
		PauseManager.OnBeforeSpawn += this.BeforeSpawn;
	}

	// Token: 0x0600032B RID: 811 RVA: 0x00019BA6 File Offset: 0x00017DA6
	private void OnDisable()
	{
		PauseManager.OnRoundStarted -= this.StartNewRound;
		PauseManager.OnBeforeSpawn -= this.BeforeSpawn;
	}

	// Token: 0x0600032C RID: 812 RVA: 0x00019BCA File Offset: 0x00017DCA
	private void Awake()
	{
		this.animator = base.GetComponent<Animator>();
		this.animator.enabled = false;
	}

	// Token: 0x0600032D RID: 813 RVA: 0x00019BE4 File Offset: 0x00017DE4
	private void Start()
	{
		if (base.GetComponent<NetworkTransform>() != null)
		{
			base.GetComponent<NetworkTransform>().enabled = false;
		}
		if (SceneMotor.Instance.testMap || PauseManager.Instance.nonSteamworksTransport)
		{
			this.animator.enabled = true;
		}
	}

	// Token: 0x0600032E RID: 814 RVA: 0x00019C24 File Offset: 0x00017E24
	public void StartNewRound()
	{
		this.animator.enabled = true;
		if (!this.ResetOnRound)
		{
			return;
		}
		this.animator.Rebind();
		this.animator.Update(0f);
	}

	// Token: 0x0600032F RID: 815 RVA: 0x00019C56 File Offset: 0x00017E56
	public void BeforeSpawn()
	{
		if (!this.ResetOnRound)
		{
			return;
		}
		this.animator.Rebind();
		this.animator.Update(0f);
		this.animator.enabled = false;
	}

	// Token: 0x040003B1 RID: 945
	[SerializeField]
	private bool ResetOnRound = true;

	// Token: 0x040003B2 RID: 946
	private Animator animator;
}

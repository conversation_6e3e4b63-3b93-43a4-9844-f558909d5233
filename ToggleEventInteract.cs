﻿using System;
using FishNet;
using FishNet.Managing;
using FishNet.Object;
using FishNet.Object.Delegating;
using FishNet.Serializing;
using FishNet.Transporting;
using UnityEngine;
using UnityEngine.Events;

// Token: 0x02000074 RID: 116
public class ToggleEventInteract : BaseEventInteract
{
	// Token: 0x060004F1 RID: 1265 RVA: 0x00020F4F File Offset: 0x0001F14F
	[ObserversRpc]
	protected override void OnInteractObserverRpc()
	{
		this.RpcWriter___Observers_OnInteractObserverRpc_2166136261();
	}

	// Token: 0x060004F3 RID: 1267 RVA: 0x00020F57 File Offset: 0x0001F157
	public override void NetworkInitialize___Early()
	{
		if (this.NetworkInitializeEarly_ToggleEventInteract_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeEarly_ToggleEventInteract_Assembly-CSharp.dll = true;
		base.NetworkInitialize___Early();
		base.RegisterObserversRpc(5U, new ClientRpcDelegate(this.RpcReader___Observers_OnInteractObserverRpc_2166136261));
	}

	// Token: 0x060004F4 RID: 1268 RVA: 0x00020F87 File Offset: 0x0001F187
	public override void NetworkInitialize__Late()
	{
		if (this.NetworkInitializeLate_ToggleEventInteract_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeLate_ToggleEventInteract_Assembly-CSharp.dll = true;
		base.NetworkInitialize__Late();
	}

	// Token: 0x060004F5 RID: 1269 RVA: 0x00020FA0 File Offset: 0x0001F1A0
	public override void NetworkInitializeIfDisabled()
	{
		this.NetworkInitialize___Early();
		this.NetworkInitialize__Late();
	}

	// Token: 0x060004F6 RID: 1270 RVA: 0x00020FB0 File Offset: 0x0001F1B0
	private void RpcWriter___Observers_OnInteractObserverRpc_2166136261()
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendObserversRpc(5U, writer, channel, DataOrderType.Default, false, false, false);
		writer.Store();
	}

	// Token: 0x060004F7 RID: 1271 RVA: 0x00021059 File Offset: 0x0001F259
	protected virtual void RpcLogic___OnInteractObserverRpc_2166136261()
	{
		this.IsToggled = !this.IsToggled;
		if (this.IsToggled)
		{
			UnityEvent unityEvent = this.onEnableEvent;
			if (unityEvent == null)
			{
				return;
			}
			unityEvent.Invoke();
			return;
		}
		else
		{
			UnityEvent unityEvent2 = this.onDisableEvent;
			if (unityEvent2 == null)
			{
				return;
			}
			unityEvent2.Invoke();
			return;
		}
	}

	// Token: 0x060004F8 RID: 1272 RVA: 0x00021094 File Offset: 0x0001F294
	private void RpcReader___Observers_OnInteractObserverRpc_2166136261(PooledReader PooledReader0, Channel channel)
	{
		if (!base.IsClient)
		{
			return;
		}
		this.RpcLogic___OnInteractObserverRpc_2166136261();
	}

	// Token: 0x060004F9 RID: 1273 RVA: 0x000210B4 File Offset: 0x0001F2B4
	public override void Awake()
	{
		this.NetworkInitialize___Early();
		base.Awake();
		this.NetworkInitialize__Late();
	}

	// Token: 0x060004FA RID: 1274 RVA: 0x000023D6 File Offset: 0x000005D6
	public override void Awake___UserLogic()
	{
	}

	// Token: 0x040004ED RID: 1261
	private bool IsToggled;

	// Token: 0x040004EE RID: 1262
	[Header("This event triggers when the player interacts with this object.")]
	[SerializeField]
	private UnityEvent onEnableEvent;

	// Token: 0x040004EF RID: 1263
	[SerializeField]
	private UnityEvent onDisableEvent;

	// Token: 0x040004F0 RID: 1264
	private bool NetworkInitializeEarly_ToggleEventInteract_Assembly-CSharp.dll;

	// Token: 0x040004F1 RID: 1265
	private bool NetworkInitializeLate_ToggleEventInteract_Assembly-CSharp.dll;
}

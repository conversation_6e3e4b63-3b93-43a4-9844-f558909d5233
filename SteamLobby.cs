﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Text;
using System.Text.RegularExpressions;
using ComputerysModdingUtilities;
using FishNet;
using FishNet.Managing;
using FishNet.Object;
using FishySteamworks;
using HeathenEngineering.SteamworksIntegration;
using HeathenEngineering.SteamworksIntegration.UI;
using Steamworks;
using TMPro;
using UnityEngine;
using UnityEngine.SceneManagement;
using UnityEngine.Serialization;
using UnityEngine.UI;

// Token: 0x02000114 RID: 276
public class SteamLobby : MonoBehaviour
{
	// Token: 0x170000CD RID: 205
	// (get) Token: 0x06000DAD RID: 3501 RVA: 0x0005C785 File Offset: 0x0005A985
	private static PauseManager PauseManager
	{
		get
		{
			return PauseManager.Instance;
		}
	}

	// Token: 0x170000CE RID: 206
	// (get) Token: 0x06000DAE RID: 3502 RVA: 0x0005C78C File Offset: 0x0005A98C
	private static NetworkManager Manager
	{
		get
		{
			return InstanceFinder.NetworkManager;
		}
	}

	// Token: 0x06000DAF RID: 3503 RVA: 0x0005C793 File Offset: 0x0005A993
	private void Awake()
	{
		if (SteamLobby.Instance == null)
		{
			SteamLobby.Instance = this;
		}
	}

	// Token: 0x06000DB0 RID: 3504 RVA: 0x0005C7A8 File Offset: 0x0005A9A8
	private void Start()
	{
		if (this.lobbyManager == null)
		{
			this.lobbyManager = GameObject.Find("LobbyController").GetComponent<LobbyManager>();
		}
		AssemblyScanner.CreateMatchMakingKey();
		if (!string.IsNullOrEmpty(AssemblyScanner.MatchMakingKey))
		{
			StringBuilder stringBuilder = new StringBuilder("Incompatible mods detected: ");
			foreach (string text in AssemblyScanner.IncompatibleAssemblyNames)
			{
				stringBuilder.Append(text + ", ");
			}
			PauseManager.Instance.ShowInfoPopup(string.Format("Non-vanilla friendly mods detected, you will be using a separate matchmaking pool for this session.\n{0}", stringBuilder));
		}
		this.localSteamUser = (ulong)SteamUser.GetSteamID();
		this.LobbyCreated = Callback<LobbyCreated_t>.Create(new Callback<LobbyCreated_t>.DispatchDelegate(this.OnLobbyCreated));
		this.JoinRequest = Callback<GameLobbyJoinRequested_t>.Create(new Callback<GameLobbyJoinRequested_t>.DispatchDelegate(this.OnJoinRequest));
		this.LobbyEntered = Callback<LobbyEnter_t>.Create(new Callback<LobbyEnter_t>.DispatchDelegate(this.OnLobbyEntered));
		this.LobbyInvited = Callback<LobbyInvite_t>.Create(new Callback<LobbyInvite_t>.DispatchDelegate(this.OnInviteReceived));
		this.LobbyKicked = Callback<LobbyKicked_t>.Create(new Callback<LobbyKicked_t>.DispatchDelegate(this.OnLobbyKicked));
		this.LobbyList = Callback<LobbyMatchList_t>.Create(new Callback<LobbyMatchList_t>.DispatchDelegate(this.OnGetLobbyList));
		this.LobbyDataUpdated = Callback<LobbyDataUpdate_t>.Create(new Callback<LobbyDataUpdate_t>.DispatchDelegate(this.OnGetLobbyData));
		this.LobbyChatUpdate = Callback<LobbyChatUpdate_t>.Create(new Callback<LobbyChatUpdate_t>.DispatchDelegate(this.OnLobbyChatUpdate));
		this.lobbyManager.searchArguments.stringFilters[0] = new StringFilter
		{
			key = "version",
			value = this.versionText.text + AssemblyScanner.MatchMakingKey
		};
		this.lobbyManager.createArguments.metadata[0] = new MetadataTempalate
		{
			key = "version",
			value = this.versionText.text + AssemblyScanner.MatchMakingKey
		};
		this.lobbyManager.createArguments.slots = this.maxPlayers;
		this.lastActivityTime = Time.time;
	}

	// Token: 0x06000DB1 RID: 3505 RVA: 0x0005C9B1 File Offset: 0x0005ABB1
	private void AFKTimerThing()
	{
		if (Time.time - this.lastActivityTime <= 150f)
		{
			return;
		}
		SteamLobby.PauseManager.ShowInfoPopup("You have been removed from the lobby's waiting room due to inactivity. (AFK for 2.5 minutes)");
		this.LeaveLobby();
	}

	// Token: 0x06000DB2 RID: 3506 RVA: 0x0005C9DC File Offset: 0x0005ABDC
	private void Update()
	{
		if (this._fishySteamworks == null && SteamLobby.Manager != null)
		{
			this._fishySteamworks = SteamLobby.Manager.GetComponent<global::FishySteamworks.FishySteamworks>();
		}
		this.CooldownTimer -= Time.deltaTime;
		if (this.MatchmakingBanner.activeSelf && this.players.Count >= 2)
		{
			this.MatchmakingBanner.SetActive(false);
		}
		if (this.LobbyIdTextInfo.activeSelf != this.inSteamLobby)
		{
			this.LobbyIdTextInfo.SetActive(this.inSteamLobby);
		}
		if (this.LobbyTypeDropdownBeforeLobbyGameObject.activeSelf == this.inSteamLobby)
		{
			this.LobbyTypeDropdownBeforeLobbyGameObject.SetActive(!this.inSteamLobby);
		}
		if (this.ServerNameObject.activeSelf == this.inSteamLobby)
		{
			this.ServerNameObject.SetActive(!this.inSteamLobby);
		}
		if (this.MaxPlayersObject.activeSelf == this.inSteamLobby)
		{
			this.MaxPlayersObject.SetActive(!this.inSteamLobby);
		}
		if (this.InviteButton.activeSelf != (SteamLobby.Manager.IsServer && this.players.Count < this.maxPlayers))
		{
			this.InviteButton.SetActive(SteamLobby.Manager.IsServer && this.players.Count < this.maxPlayers);
		}
		if (this.inSteamLobby && !SteamLobby.Manager.IsOffline && SteamLobby.Manager.IsServer)
		{
			if (!SteamLobby.PauseManager.inMainMenu && !this.allowMidMatchJoining)
			{
				if (this.lobbyType != ELobbyType.k_ELobbyTypePrivate)
				{
					this.SetLobbyType(ELobbyType.k_ELobbyTypePrivate);
				}
			}
			else if (this.lobbyType != this.GetLobbyType(this.LobbyTypeDropdown) && !this.privateLobby)
			{
				this.SetLobbyType(this.LobbyTypeDropdown);
			}
		}
		if (this.players.Count > this.maxPlayers && LobbyController.Instance.LocalPlayerController != null)
		{
			int count = this.players.Count;
			for (int i = this.maxPlayers; i < count; i++)
			{
				if (LobbyController.Instance.LocalPlayerController == this.players[i].GetComponent<ClientInstance>())
				{
					this.KickSelf();
				}
			}
		}
		if (SteamLobby.PauseManager.inMainMenu && this.inSteamLobby && this.lobbyType == ELobbyType.k_ELobbyTypePublic)
		{
			this.AFKTimerThing();
			return;
		}
		this.lastActivityTime = Time.time;
	}

	// Token: 0x06000DB3 RID: 3507 RVA: 0x0005CC48 File Offset: 0x0005AE48
	public void SetMaxPlayers(TMP_Dropdown _dropdown)
	{
		this.maxPlayers = _dropdown.value + 2;
		this.lobbyManager.createArguments.slots = this.maxPlayers;
		InstanceFinder.TransportManager.Transport.SetMaximumClients(this.maxPlayers - 1);
		if (this.inSteamLobby)
		{
			SteamMatchmaking.SetLobbyMemberLimit(new CSteamID(this.CurrentLobbyID), this.maxPlayers);
			this.UpdatePlayerCountDisplay();
			if (LobbyController.Instance.LocalPlayerController != null)
			{
				LobbyController.Instance.LocalPlayerController.UpdateServerMaxPlayers();
			}
		}
	}

	// Token: 0x06000DB4 RID: 3508 RVA: 0x0005CCD6 File Offset: 0x0005AED6
	public void SetAllowMidMatchJoining(bool value)
	{
		this.allowMidMatchJoining = value;
	}

	// Token: 0x06000DB5 RID: 3509 RVA: 0x0005CCDF File Offset: 0x0005AEDF
	public void CreateLobbyDirect()
	{
		this.lobbyManager.Create();
	}

	// Token: 0x06000DB6 RID: 3510 RVA: 0x0005CCEC File Offset: 0x0005AEEC
	public void Create()
	{
		if (this.CooldownTimer > 0f)
		{
			return;
		}
		if (this.inSteamLobby)
		{
			this.LeaveLobby();
			base.StartCoroutine(this.CreateLobbyWithDelay());
		}
		else
		{
			this.CreateLobbyDirect();
		}
		this.CooldownTimer = 0.35f;
	}

	// Token: 0x06000DB7 RID: 3511 RVA: 0x0005CD2A File Offset: 0x0005AF2A
	private IEnumerator CreateLobbyWithDelay()
	{
		yield return new WaitUntil(() => SteamLobby.Manager.IsOffline);
		yield return new WaitForSeconds(0.4f);
		this.CreateLobbyDirect();
		yield break;
	}

	// Token: 0x06000DB8 RID: 3512 RVA: 0x0005CD3C File Offset: 0x0005AF3C
	public void EnterExplorationMap(string mapName)
	{
		if (!SteamLobby.Manager.IsOffline && !SteamLobby.Manager.IsServer)
		{
			SteamLobby.PauseManager.WriteOfflineLog("Only the host can start exploration mode");
			return;
		}
		this.isInExplorationMap = true;
		if (!this.inSteamLobby)
		{
			this.privateLobby = true;
		}
		this.mapsManager.inExplorationMap = true;
		base.StartCoroutine(this.EnterMap(mapName));
	}

	// Token: 0x06000DB9 RID: 3513 RVA: 0x0005CDA1 File Offset: 0x0005AFA1
	private IEnumerator EnterMap(string name)
	{
		yield return new WaitForSeconds(0.2f);
		if (!this.inSteamLobby)
		{
			this.CreateLobbyDirect();
		}
		yield return new WaitUntil(() => SceneMotor.Instance != null);
		yield return new WaitForSeconds(0.3f);
		this.SetLobbyType(ELobbyType.k_ELobbyTypePrivate);
		SceneMotor.Instance.EnterScene(name);
		yield break;
	}

	// Token: 0x06000DBA RID: 3514 RVA: 0x0005CDB7 File Offset: 0x0005AFB7
	public void SetLobbyType(TMP_Dropdown dropdown)
	{
		this.SetLobbyType(dropdown.value);
	}

	// Token: 0x06000DBB RID: 3515 RVA: 0x0005CDC5 File Offset: 0x0005AFC5
	public void SetLobbyType(ELobbyType newLobbyType)
	{
		this.lobbyType = newLobbyType;
		this.UpdateLobbyType();
	}

	// Token: 0x06000DBC RID: 3516 RVA: 0x0005CDD4 File Offset: 0x0005AFD4
	public void SetLobbyType(int value)
	{
		switch (value)
		{
		case 0:
			this.lobbyType = ELobbyType.k_ELobbyTypePublic;
			break;
		case 1:
			this.lobbyType = ELobbyType.k_ELobbyTypeFriendsOnly;
			break;
		case 2:
			this.lobbyType = ELobbyType.k_ELobbyTypePrivate;
			break;
		}
		this.UpdateLobbyType();
	}

	// Token: 0x06000DBD RID: 3517 RVA: 0x0005CE0C File Offset: 0x0005B00C
	public ELobbyType GetLobbyType(TMP_Dropdown dropdown)
	{
		switch (dropdown.value)
		{
		case 0:
			return ELobbyType.k_ELobbyTypePublic;
		case 1:
			return ELobbyType.k_ELobbyTypeFriendsOnly;
		case 2:
			return ELobbyType.k_ELobbyTypePrivate;
		default:
			return ELobbyType.k_ELobbyTypePublic;
		}
	}

	// Token: 0x06000DBE RID: 3518 RVA: 0x0005CE3B File Offset: 0x0005B03B
	public void UpdateLobbyType()
	{
		if (!this.inSteamLobby)
		{
			return;
		}
		SteamMatchmaking.SetLobbyType(new CSteamID(this.CurrentLobbyID), this.lobbyType);
	}

	// Token: 0x06000DBF RID: 3519 RVA: 0x0005CE5D File Offset: 0x0005B05D
	public void SetGamemode(TMP_Dropdown dropdown)
	{
		this.SetGamemode(dropdown.value);
	}

	// Token: 0x06000DC0 RID: 3520 RVA: 0x0005CE6C File Offset: 0x0005B06C
	public void SetGamemode(int value)
	{
		this.playingTeams = value != 0;
		if (InstanceFinder.NetworkManager.IsServer)
		{
			if (GameManager.Instance != null)
			{
				GameManager.Instance.sync___set_value_playingTeams(this.playingTeams, true);
				GameManager.Instance.ResetTeamIds();
			}
			if (this.inSteamLobby)
			{
				SteamMatchmaking.SetLobbyData(new CSteamID(this.CurrentLobbyID), "gamemode", this.playingTeams ? "Teams" : "FFA");
			}
		}
	}

	// Token: 0x06000DC1 RID: 3521 RVA: 0x0005CEEC File Offset: 0x0005B0EC
	public void SetStartAutomatically(Toggle toggle)
	{
		this.startAutomatically = toggle.isOn;
		if (this.startAutomatically)
		{
			this.AutomaticStart();
			return;
		}
		base.StopCoroutine(this.WaitForAllToConnectCoroutine);
	}

	// Token: 0x06000DC2 RID: 3522 RVA: 0x0005CF15 File Offset: 0x0005B115
	public void AutomaticStart()
	{
		this.WaitForAllToConnectCoroutine = this.WaitForAllToConnect();
		base.StartCoroutine(this.WaitForAllToConnectCoroutine);
	}

	// Token: 0x06000DC3 RID: 3523 RVA: 0x0005CF30 File Offset: 0x0005B130
	private IEnumerator WaitForAllToConnect()
	{
		yield return new WaitUntil(() => this.AllReady && this.players.Count >= 2);
		if (SteamLobby.Manager.IsServer)
		{
			SteamLobby.PauseManager.StartGameSteam();
		}
		yield break;
	}

	// Token: 0x06000DC4 RID: 3524 RVA: 0x0005CF40 File Offset: 0x0005B140
	public void SetHUDActive(bool active)
	{
		foreach (MenuHUDTween menuHUDTween in this.lobby3D)
		{
			if (active)
			{
				menuHUDTween.SetEnabled();
			}
			else
			{
				menuHUDTween.SetDisabled();
			}
		}
	}

	// Token: 0x06000DC5 RID: 3525 RVA: 0x0005CF78 File Offset: 0x0005B178
	private void OnLobbyCreated(LobbyCreated_t callback)
	{
		if (callback.m_eResult != EResult.k_EResultOK)
		{
			SteamLobby.PauseManager.WriteOfflineLog("Failed to create lobby: " + Enum.GetName(typeof(EResult), callback.m_eResult));
			return;
		}
		Debug.Log("Lobby Created");
		this.lastActivityTime = Time.time;
		this.CurrentLobbyID = callback.m_ulSteamIDLobby;
		this.inSteamLobby = true;
		if (!this.privateLobby)
		{
			this.LobbyTypeDropdown.value = this.LobbyTypeDropdownBeforeLobby.value;
			this.SetLobbyType(this.LobbyTypeDropdown);
			this.SetGamemode(this.GamemodeDropdown);
		}
		this.SetMaxPlayers(this.MaxPlayersDropdown);
		this.DestroyInviteCards();
		this.LobbyWindow.SetActive(true);
		this.HostButton.SetActive(false);
		this.StopButton.SetActive(true);
		this.SetHUDActive(true);
		SteamMatchmaking.SetLobbyData(new CSteamID(callback.m_ulSteamIDLobby), "HostAddress", SteamUser.GetSteamID().ToString());
		if (!this.privateLobby)
		{
			SteamMatchmaking.SetLobbyData(new CSteamID(callback.m_ulSteamIDLobby), "name", (this.lobbyName == "") ? (SteamFriends.GetPersonaName().ToString() + "'s lobby") : this.lobbyName);
			SteamMatchmaking.SetLobbyData(new CSteamID(callback.m_ulSteamIDLobby), "ownDlc0", SteamLobby.ownDlc0 ? "True" : "False");
			SteamMatchmaking.SetLobbyData(new CSteamID(callback.m_ulSteamIDLobby), "gamemode", this.playingTeams ? "Teams" : "FFA");
		}
		else
		{
			SteamMatchmaking.SetLobbyData(new CSteamID(callback.m_ulSteamIDLobby), "name", "private");
		}
		this.LobbyNameText.text = SteamMatchmaking.GetLobbyData(new CSteamID(callback.m_ulSteamIDLobby), "name");
		this.lobbyIdText.text = callback.m_ulSteamIDLobby.ToString();
		this._fishySteamworks.SetClientAddress(SteamUser.GetSteamID().ToString());
		if (!this._fishySteamworks.StartConnection(true))
		{
			Debug.LogError("Failed to start FishySteamworks connection");
			this.LeaveLobby();
			return;
		}
		GameObject gameObject = global::UnityEngine.Object.Instantiate<GameObject>(this._sceneMotorPrefab, base.transform.position, Quaternion.identity);
		InstanceFinder.ServerManager.Spawn(gameObject, null);
		if (this.startAutomatically)
		{
			this.AutomaticStart();
		}
	}

	// Token: 0x06000DC6 RID: 3526 RVA: 0x0005D1EA File Offset: 0x0005B3EA
	private void OnJoinRequest(GameLobbyJoinRequested_t callback)
	{
		Debug.Log("JoinRequest");
		if (this.inSteamLobby)
		{
			this.LeaveLobby();
		}
		SteamMatchmaking.RequestLobbyData(callback.m_steamIDLobby);
		SteamMatchmaking.JoinLobby(callback.m_steamIDLobby);
	}

	// Token: 0x06000DC7 RID: 3527 RVA: 0x0005D21C File Offset: 0x0005B41C
	public void UpdatePlayerCountDisplay()
	{
		SteamMatchmaking.SetLobbyData(new CSteamID(this.CurrentLobbyID), "playerCount", this.players.Count.ToString());
		SteamMatchmaking.SetLobbyData(new CSteamID(this.CurrentLobbyID), "maxPlayers", this.maxPlayers.ToString());
	}

	// Token: 0x06000DC8 RID: 3528 RVA: 0x0005D274 File Offset: 0x0005B474
	private void OnLobbyEntered(LobbyEnter_t callback)
	{
		if (callback.m_EChatRoomEnterResponse != 1U)
		{
			SteamLobby.PauseManager.WriteOfflineLog("Failed to join lobby: " + Enum.GetName(typeof(EChatRoomEnterResponse), callback.m_EChatRoomEnterResponse));
			this.LeaveLobby();
			return;
		}
		Debug.Log("Lobby Entered");
		this.lastActivityTime = Time.time;
		if (this.lobbyManager.Lobby != (CSteamID)callback.m_ulSteamIDLobby)
		{
			this.lobbyManager.Lobby = (CSteamID)callback.m_ulSteamIDLobby;
			Debug.Log("steam overlay invite join");
		}
		if (SteamMatchmaking.GetLobbyData(new CSteamID(callback.m_ulSteamIDLobby), "version") != this.versionText.text + AssemblyScanner.MatchMakingKey)
		{
			SteamLobby.PauseManager.WriteOfflineLog("Version mismatch, make sure you and the host are on the same version and have the same mods installed.");
			this.LeaveLobby();
			return;
		}
		this.DestroyInviteCards();
		if (!MenuController.Instance.playMenu.activeSelf && !this.isInExplorationMap)
		{
			MenuController.Instance.OpenGame();
		}
		this.CurrentLobbyID = callback.m_ulSteamIDLobby;
		SteamLobby.PauseManager.ShowInviteViewport(false, this.invitePopupViewport);
		this.LobbyWindow.SetActive(true);
		this.LobbiesBrowser.SetActive(false);
		this.SetHUDActive(true);
		if (!SteamLobby.Manager.IsServer)
		{
			this.HostButton.SetActive(true);
			this.StopButton.SetActive(false);
		}
		this.LobbyNameText.text = SteamMatchmaking.GetLobbyData(new CSteamID(this.CurrentLobbyID), "name");
		this.lobbyIdText.text = callback.m_ulSteamIDLobby.ToString();
		this.HostAddress = SteamMatchmaking.GetLobbyData(new CSteamID(this.CurrentLobbyID), "HostAddress");
		this._fishySteamworks.SetClientAddress(this.HostAddress);
		if (!this._fishySteamworks.StartConnection(false))
		{
			Debug.LogError("Failed to start FishySteamworks connection");
			this.LeaveLobby();
			return;
		}
		this.inSteamLobby = true;
	}

	// Token: 0x06000DC9 RID: 3529 RVA: 0x0005D470 File Offset: 0x0005B670
	private void OnInviteReceived(LobbyInvite_t result)
	{
		string friendPersonaName = SteamFriends.GetFriendPersonaName((CSteamID)result.m_ulSteamIDUser);
		GameObject gameObject = global::UnityEngine.Object.Instantiate<GameObject>(this.lobbyInviteInstance, this.invitePopupViewport.position, Quaternion.identity, this.invitePopupViewport.transform);
		if (gameObject == null)
		{
			Debug.LogError("Failed to invite to lobby???");
		}
		LobbyInviteInstance component = gameObject.GetComponent<LobbyInviteInstance>();
		component.lobbyName = friendPersonaName;
		component.lobbyID = result.m_ulSteamIDLobby;
		SteamLobby.PauseManager.ShowInviteViewport(true, this.invitePopupViewport);
	}

	// Token: 0x06000DCA RID: 3530 RVA: 0x0005D4EF File Offset: 0x0005B6EF
	private void OnGetLobbyData(LobbyDataUpdate_t result)
	{
		LobbiesListManager.Instance.DisplayLobbyWithData((CSteamID)result.m_ulSteamIDLobby);
	}

	// Token: 0x06000DCB RID: 3531 RVA: 0x0005D506 File Offset: 0x0005B706
	private void OnLobbyKicked(LobbyKicked_t callback)
	{
		this.LeaveLobby();
	}

	// Token: 0x06000DCC RID: 3532 RVA: 0x0005D510 File Offset: 0x0005B710
	private void OnLobbyChatUpdate(LobbyChatUpdate_t callback)
	{
		if ((callback.m_rgfChatMemberStateChange & 2U) != 0U)
		{
			Debug.Log("Player Left");
			CSteamID csteamID = (CSteamID)callback.m_ulSteamIDUserChanged;
			string friendPersonaName = SteamFriends.GetFriendPersonaName(csteamID);
			if (this.HostAddress == csteamID.ToString())
			{
				SteamLobby.PauseManager.WriteOfflineLog("The Host (" + friendPersonaName + ") left the lobby");
				this.LeaveLobby();
			}
			else
			{
				SteamLobby.PauseManager.WriteOfflineLog(friendPersonaName + " left the lobby");
			}
			if (ClientInstance.Instance.IsServer && GameManager.Instance.GetConnectedTeams().Length <= 1)
			{
				this.LeaveLobby();
			}
		}
	}

	// Token: 0x06000DCD RID: 3533 RVA: 0x0005D5B9 File Offset: 0x0005B7B9
	public void JoinLobby(CSteamID lobbyID)
	{
		if (this.inSteamLobby)
		{
			this.LeaveLobby();
		}
		base.StartCoroutine(this.JoinLobbyWithDelay(lobbyID));
	}

	// Token: 0x06000DCE RID: 3534 RVA: 0x0005D5D7 File Offset: 0x0005B7D7
	private IEnumerator JoinLobbyWithDelay(CSteamID lobbyID)
	{
		this.LobbiesBrowser.SetActive(false);
		yield return new WaitUntil(() => SteamLobby.Manager.IsOffline);
		yield return new WaitForSeconds(0.3f);
		this.lobbyManager.Join(lobbyID);
		yield break;
	}

	// Token: 0x06000DCF RID: 3535 RVA: 0x0005D5ED File Offset: 0x0005B7ED
	public void JoinLobbyAuth(CSteamID lobbyID)
	{
		this.JoinLobby(lobbyID);
	}

	// Token: 0x06000DD0 RID: 3536 RVA: 0x0005D5F8 File Offset: 0x0005B7F8
	public void LeaveSteamLobby(bool reloadMenu = true)
	{
		if (this.startAutomatically && this.WaitForAllToConnectCoroutine != null)
		{
			base.StopCoroutine(this.WaitForAllToConnectCoroutine);
		}
		if (this.CurrentLobbyID != 0UL)
		{
			SteamMatchmaking.LeaveLobby(new CSteamID(this.CurrentLobbyID));
		}
		this._fishySteamworks.Shutdown();
		if (reloadMenu)
		{
			base.StartCoroutine(this.ReloadNetworkManagerCoroutine());
		}
		this.CurrentLobbyID = 0UL;
	}

	// Token: 0x06000DD1 RID: 3537 RVA: 0x0005D65C File Offset: 0x0005B85C
	private IEnumerator ReloadNetworkManagerCoroutine()
	{
		yield return new WaitUntil(() => SteamLobby.Manager.IsOffline);
		yield return new WaitUntil(() => !this.inSteamLobby);
		yield return new WaitForSeconds(0.2f);
		this.ReloadNetworkManager();
		yield break;
	}

	// Token: 0x06000DD2 RID: 3538 RVA: 0x0005D66C File Offset: 0x0005B86C
	public void RunQuickMatch()
	{
		if (this.CooldownTimer > 0f)
		{
			return;
		}
		this.CooldownTimer = 0.35f;
		if (this.inSteamLobby)
		{
			this.LeaveLobby();
			this.findingQuickMatchLobby = false;
			this.MatchmakingBanner.SetActive(false);
			return;
		}
		if (this.findingQuickMatchLobby)
		{
			this.CancelQuickMatch();
			return;
		}
		this.MatchmakingBanner.SetActive(true);
		this.findingQuickMatchLobby = true;
		this.GetLobbiesList();
	}

	// Token: 0x06000DD3 RID: 3539 RVA: 0x0005D6DC File Offset: 0x0005B8DC
	public void CancelQuickMatch()
	{
		this.findingQuickMatchLobby = false;
		if (this.inSteamLobby)
		{
			this.LeaveLobby();
		}
		this.MatchmakingBanner.SetActive(false);
	}

	// Token: 0x06000DD4 RID: 3540 RVA: 0x0005D700 File Offset: 0x0005B900
	public void JoinLobbyWithText(TMP_InputField text)
	{
		ulong num = Convert.ToUInt64(text.text);
		this.lobbyManager.Join((CSteamID)num);
		this.LobbiesBrowser.SetActive(false);
	}

	// Token: 0x06000DD5 RID: 3541 RVA: 0x0005D73C File Offset: 0x0005B93C
	public void SetLobbyName(TMP_InputField text)
	{
		this.lobbyName = ProfanityFilterProvider.Filter.CensorString(text.text);
		this.lobbyName = Regex.Replace(this.lobbyName, "<size=\\d+>", "");
		this.lobbyName = Regex.Replace(this.lobbyName, "</size>", "");
	}

	// Token: 0x06000DD6 RID: 3542 RVA: 0x0005D798 File Offset: 0x0005B998
	public void LeaveLobby()
	{
		SteamLobby.PauseManager.serverStarted = false;
		this.MatchmakingBanner.SetActive(false);
		SteamLobby.PauseManager.WriteOfflineLog("You left the lobby");
		SaveLoadSystem.Instance.Save();
		this._fishySteamworks.StopConnection(SteamLobby.Manager.IsServer);
		if (!SteamLobby.PauseManager.inMainMenu)
		{
			SceneManager.LoadScene("MainMenu");
		}
		this.LobbyWindow.SetActive(false);
		this.lobbyIdText.text = "";
		this.HostButton.SetActive(true);
		this.StopButton.SetActive(false);
		this.MapSelectionWindow.transform.localScale = Vector3.zero;
		this.MapSelectionWindow3D.SetDisabled();
		this.ReadyTextScript.SetState(true);
		this.SetHUDActive(false);
		this.inSteamLobby = false;
		this.findingQuickMatchLobby = false;
		this.privateLobby = false;
		this.isInExplorationMap = false;
		this.LeaveSteamLobby(false);
	}

	// Token: 0x06000DD7 RID: 3543 RVA: 0x0005D88C File Offset: 0x0005BA8C
	private void ReloadNetworkManager()
	{
		GameObject gameObject = GameObject.Find("NetworkManager");
		if (gameObject)
		{
			global::UnityEngine.Object.Destroy(gameObject);
		}
		SceneManager.LoadScene("MainMenu");
	}

	// Token: 0x06000DD8 RID: 3544 RVA: 0x0005D8BC File Offset: 0x0005BABC
	public void LeaveMatch()
	{
		if (this.players.Count > 2 && !InstanceFinder.NetworkManager.IsServer && !PauseManager.Instance.inVictoryMenu)
		{
			this.LeaveLobby();
			return;
		}
		if (SceneMotor.Instance != null)
		{
			if (SteamLobby.Manager.IsServer)
			{
				SceneMotor.Instance.SetSceneIdToZero();
			}
			if (SceneMotor.Instance.testMap)
			{
				SceneMotor.Instance.testMap = false;
				if (this.privateLobby)
				{
					this.LeaveLobby();
					return;
				}
			}
		}
		if (SteamLobby.PauseManager.inMainMenu)
		{
			return;
		}
		SaveLoadSystem.Instance.Save();
		if (!SteamLobby.Manager.IsOffline)
		{
			SteamLobby.PauseManager.WriteLog(LobbyController.Instance.LocalPlayerController.PlayerName + " returned to main menu");
		}
		if (SceneMotor.Instance != null)
		{
			SceneMotor.Instance.LeaveMatchForAll();
		}
		else
		{
			SceneManager.LoadScene("MainMenu");
		}
		this.privateLobby = false;
		this.isInExplorationMap = false;
	}

	// Token: 0x06000DD9 RID: 3545 RVA: 0x0005D9B6 File Offset: 0x0005BBB6
	public void KickSelf()
	{
		LobbyController.Instance.LocalPlayerController.KickSelf();
		SteamLobby.PauseManager.WriteOfflineLog("Lobby is full");
	}

	// Token: 0x06000DDA RID: 3546 RVA: 0x0005D9D8 File Offset: 0x0005BBD8
	public void DestroyInviteCards()
	{
		foreach (object obj in this.invitePopupViewport)
		{
			global::UnityEngine.Object.Destroy(((Transform)obj).gameObject);
		}
	}

	// Token: 0x06000DDB RID: 3547 RVA: 0x0005DA34 File Offset: 0x0005BC34
	public void GetLobbiesList()
	{
		if (this.lobbyIDs.Count > 0)
		{
			this.lobbyIDs.Clear();
		}
		if (LobbiesListManager.Instance.listOfLobbies.Count > 0)
		{
			LobbiesListManager.Instance.DestroyLobbies();
		}
		this.lobbyManager.Search(100);
	}

	// Token: 0x06000DDC RID: 3548 RVA: 0x0005DA84 File Offset: 0x0005BC84
	private void OnGetLobbyList(LobbyMatchList_t result)
	{
		if (this.lobbyIDs.Count > 0)
		{
			this.lobbyIDs.Clear();
		}
		if (LobbiesListManager.Instance.listOfLobbies.Count > 0)
		{
			LobbiesListManager.Instance.DestroyLobbies();
		}
		int num = 0;
		while ((long)num < (long)((ulong)result.m_nLobbiesMatching))
		{
			CSteamID lobbyByIndex = SteamMatchmaking.GetLobbyByIndex(num);
			this.lobbyIDs.Add(lobbyByIndex);
			SteamMatchmaking.RequestLobbyData(lobbyByIndex);
			num++;
		}
		if (this.lastLobbyRequest != 0UL)
		{
			bool flag = this.lobbyIDs.Count == 0;
			for (int i = 0; i < this.lobbyIDs.Count; i++)
			{
				if (this.lobbyIDs[i] == (CSteamID)this.lastLobbyRequest)
				{
					flag = false;
					this.JoinLobby((CSteamID)this.lastLobbyRequest);
					break;
				}
				flag = true;
			}
			if (flag)
			{
				this.lastLobbyRequest = 0UL;
				SteamLobby.PauseManager.WriteOfflineLog("Can't join lobby");
				SteamLobby.PauseManager.WriteOfflineLog("Lobby is full");
			}
		}
		this.lastLobbyRequest = 0UL;
		if (this.findingQuickMatchLobby && this.lobbyIDs.Count > 0 && !this.inSteamLobby)
		{
			using (List<CSteamID>.Enumerator enumerator = this.lobbyIDs.GetEnumerator())
			{
				while (enumerator.MoveNext())
				{
					CSteamID csteamID = enumerator.Current;
					if (SteamMatchmaking.GetLobbyMemberLimit(csteamID) <= 4)
					{
						this.JoinLobby(csteamID);
						break;
					}
				}
				return;
			}
		}
		if (this.findingQuickMatchLobby && !this.inSteamLobby)
		{
			this.CooldownTimer = 0f;
			this.Create();
		}
	}

	// Token: 0x06000DDD RID: 3549 RVA: 0x0005DC20 File Offset: 0x0005BE20
	public void OpenDLCStorePage()
	{
		SteamFriends.ActivateGameOverlayToStore((AppId_t)3153370U, EOverlayToStoreFlag.k_EOverlayToStoreFlag_None);
	}

	// Token: 0x06000DDE RID: 3550 RVA: 0x0005DC34 File Offset: 0x0005BE34
	public void OpenFriendChat(string inputField)
	{
		uint num;
		if (!uint.TryParse(inputField, out num))
		{
			return;
		}
		if (!UserData.Get(num).IsValid)
		{
			return;
		}
		SteamFriends.ActivateGameOverlayToUser("chat", (CSteamID)((ulong)num));
	}

	// Token: 0x06000DDF RID: 3551 RVA: 0x0005DC70 File Offset: 0x0005BE70
	public void JoinFriendLobby(string inputField)
	{
		uint num;
		if (!uint.TryParse(inputField, out num))
		{
			return;
		}
		UserData userData = UserData.Get(num);
		if (!userData.IsValid)
		{
			return;
		}
		FriendGameInfo friendGameInfo;
		userData.GetGamePlayed(out friendGameInfo);
		this.JoinLobby(friendGameInfo.Lobby);
	}

	// Token: 0x06000DE0 RID: 3552 RVA: 0x0005DCB8 File Offset: 0x0005BEB8
	public void InviteFriendToLobby(string inputField)
	{
		uint num;
		if (!uint.TryParse(inputField, out num))
		{
			return;
		}
		UserData userData = UserData.Get(num);
		if (!userData.IsValid)
		{
			return;
		}
		SteamLobby.PauseManager.WriteOfflineLog("Invited " + userData.Name + " to lobby");
		this.friendInviteScript.Invited.Invoke(userData);
	}

	// Token: 0x06000DE1 RID: 3553 RVA: 0x0005DD12 File Offset: 0x0005BF12
	public void AddSteamFriend(UserData user)
	{
		if (!user.IsValid)
		{
			return;
		}
		SteamFriends.ActivateGameOverlayToUser("friendadd", user);
	}

	// Token: 0x06000DE2 RID: 3554 RVA: 0x0005DD30 File Offset: 0x0005BF30
	public bool CanJoinFriend(string inputField)
	{
		uint num;
		if (!uint.TryParse(inputField, out num))
		{
			return false;
		}
		UserData userData = UserData.Get(num);
		if (!userData.IsValid)
		{
			return false;
		}
		if (num == this.localSteamUser.FriendId)
		{
			return false;
		}
		FriendGameInfo friendGameInfo;
		bool gamePlayed = userData.GetGamePlayed(out friendGameInfo);
		bool flag = (ulong)friendGameInfo.Lobby.SteamId > 0UL;
		return gamePlayed && flag;
	}

	// Token: 0x06000DE3 RID: 3555 RVA: 0x0005DD90 File Offset: 0x0005BF90
	public bool CanInviteFriend(string inputField)
	{
		uint num;
		if (!uint.TryParse(inputField, out num))
		{
			return false;
		}
		UserData userData = UserData.Get(num);
		if (!userData.IsValid)
		{
			return false;
		}
		if (num == this.localSteamUser.FriendId)
		{
			return false;
		}
		if (this.players.Count <= 1)
		{
			return this.inSteamLobby && SteamLobby.Manager.IsServer;
		}
		return this.players[1].GetComponent<ClientInstance>().PlayerSteamID != userData.SteamId && this.inSteamLobby && SteamLobby.Manager.IsServer;
	}

	// Token: 0x04000C19 RID: 3097
	protected Callback<LobbyCreated_t> LobbyCreated;

	// Token: 0x04000C1A RID: 3098
	protected Callback<GameLobbyJoinRequested_t> JoinRequest;

	// Token: 0x04000C1B RID: 3099
	protected Callback<LobbyEnter_t> LobbyEntered;

	// Token: 0x04000C1C RID: 3100
	protected Callback<LobbyMatchList_t> LobbyList;

	// Token: 0x04000C1D RID: 3101
	protected Callback<LobbyDataUpdate_t> LobbyDataUpdated;

	// Token: 0x04000C1E RID: 3102
	protected Callback<LobbyInvite_t> LobbyInvited;

	// Token: 0x04000C1F RID: 3103
	protected Callback<LobbyKicked_t> LobbyKicked;

	// Token: 0x04000C20 RID: 3104
	protected Callback<LobbyChatUpdate_t> LobbyChatUpdate;

	// Token: 0x04000C21 RID: 3105
	[Header("Lobby Settings")]
	public int maxPlayers = 4;

	// Token: 0x04000C22 RID: 3106
	public bool allowMidMatchJoining;

	// Token: 0x04000C23 RID: 3107
	[Header("Current State (don't touch)")]
	public UserData localSteamUser;

	// Token: 0x04000C24 RID: 3108
	public ulong CurrentLobbyID;

	// Token: 0x04000C25 RID: 3109
	public bool inSteamLobby;

	// Token: 0x04000C26 RID: 3110
	public bool playingTeams;

	// Token: 0x04000C27 RID: 3111
	public bool findingQuickMatchLobby;

	// Token: 0x04000C28 RID: 3112
	public List<CSteamID> lobbyIDs = new List<CSteamID>();

	// Token: 0x04000C29 RID: 3113
	public List<NetworkObject> players = new List<NetworkObject>();

	// Token: 0x04000C2A RID: 3114
	private float CooldownTimer;

	// Token: 0x04000C2B RID: 3115
	private float lastActivityTime;

	// Token: 0x04000C2C RID: 3116
	public static bool ownDlc0;

	// Token: 0x04000C2D RID: 3117
	public static bool ownDlc1;

	// Token: 0x04000C2E RID: 3118
	public LobbyManager lobbyManager;

	// Token: 0x04000C2F RID: 3119
	private ulong lastLobbyRequest;

	// Token: 0x04000C30 RID: 3120
	[Header("GameObject References")]
	[SerializeField]
	private global::FishySteamworks.FishySteamworks _fishySteamworks;

	// Token: 0x04000C31 RID: 3121
	[SerializeField]
	private GameObject _sceneMotorPrefab;

	// Token: 0x04000C32 RID: 3122
	public GameObject ShutdownWindow;

	// Token: 0x04000C33 RID: 3123
	public GameObject LobbyTypeDropdownBeforeLobbyGameObject;

	// Token: 0x04000C34 RID: 3124
	public GameObject ServerNameObject;

	// Token: 0x04000C35 RID: 3125
	public GameObject MaxPlayersObject;

	// Token: 0x04000C36 RID: 3126
	public GameObject MapSelectionWindow;

	// Token: 0x04000C37 RID: 3127
	public TMP_Dropdown LobbyTypeDropdown;

	// Token: 0x04000C38 RID: 3128
	public TMP_Dropdown LobbyTypeDropdownBeforeLobby;

	// Token: 0x04000C39 RID: 3129
	public TMP_Dropdown MaxPlayersDropdown;

	// Token: 0x04000C3A RID: 3130
	public TMP_Dropdown GamemodeDropdown;

	// Token: 0x04000C3B RID: 3131
	public Toggle enemyOutlineToggle;

	// Token: 0x04000C3C RID: 3132
	public Toggle friendlyFireToggle;

	// Token: 0x04000C3D RID: 3133
	public AlphaYoyo ReadyTextScript;

	// Token: 0x04000C3E RID: 3134
	public MenuHUDTween MapSelectionWindow3D;

	// Token: 0x04000C3F RID: 3135
	public GameObject LobbyWindow;

	// Token: 0x04000C40 RID: 3136
	public GameObject LobbiesBrowser;

	// Token: 0x04000C41 RID: 3137
	public GameObject MatchmakingBanner;

	// Token: 0x04000C42 RID: 3138
	public GameObject MatchmakingController;

	// Token: 0x04000C43 RID: 3139
	public GameObject HostButton;

	// Token: 0x04000C44 RID: 3140
	public GameObject StopButton;

	// Token: 0x04000C45 RID: 3141
	public GameObject InviteButton;

	// Token: 0x04000C46 RID: 3142
	public GameObject lobbyInviteInstance;

	// Token: 0x04000C47 RID: 3143
	public GameObject LobbyIdTextInfo;

	// Token: 0x04000C48 RID: 3144
	public Transform invitePopupViewport;

	// Token: 0x04000C49 RID: 3145
	public TextMeshProUGUI LobbyNameText;

	// Token: 0x04000C4A RID: 3146
	public TextMeshProUGUI inLobbyText;

	// Token: 0x04000C4B RID: 3147
	public TextMeshProUGUI lobbyIdText;

	// Token: 0x04000C4C RID: 3148
	public TextMeshProUGUI versionText;

	// Token: 0x04000C4D RID: 3149
	[SerializeField]
	private MenuHUDTween[] lobby3D;

	// Token: 0x04000C4E RID: 3150
	public static SteamLobby Instance;

	// Token: 0x04000C4F RID: 3151
	[Space]
	[SerializeField]
	private MapsManager mapsManager;

	// Token: 0x04000C50 RID: 3152
	private bool privateLobby;

	// Token: 0x04000C51 RID: 3153
	[FormerlySerializedAs("isTestingMap")]
	[HideInInspector]
	public bool isInExplorationMap;

	// Token: 0x04000C52 RID: 3154
	public bool startAutomatically;

	// Token: 0x04000C53 RID: 3155
	[Space]
	public string lobbyName;

	// Token: 0x04000C54 RID: 3156
	public ELobbyType lobbyType;

	// Token: 0x04000C55 RID: 3157
	private IEnumerator WaitForAllToConnectCoroutine;

	// Token: 0x04000C56 RID: 3158
	[HideInInspector]
	public bool AllReady;

	// Token: 0x04000C57 RID: 3159
	private string HostAddress;

	// Token: 0x04000C58 RID: 3160
	private bool inviteInfo;

	// Token: 0x04000C59 RID: 3161
	[SerializeField]
	private FriendInviteDropDown friendInviteScript;
}

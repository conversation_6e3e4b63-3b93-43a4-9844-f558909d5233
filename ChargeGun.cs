﻿using System;
using DG.Tweening;
using FishNet;
using FishNet.Connection;
using FishNet.Managing;
using FishNet.Object;
using FishNet.Object.Delegating;
using FishNet.Serializing;
using FishNet.Serializing.Generated;
using FishNet.Transporting;
using LambdaTheDev.NetworkAudioSync;
using UnityEngine;
using UnityEngine.UI;

// Token: 0x02000089 RID: 137
public class ChargeGun : Weapon
{
	// Token: 0x0600066B RID: 1643 RVA: 0x0002AA8E File Offset: 0x00028C8E
	private void Start()
	{
		this.networkAudioSource = base.GetComponent<NetworkAudioSource>();
	}

	// Token: 0x0600066C RID: 1644 RVA: 0x0002AA9C File Offset: 0x00028C9C
	private void Update()
	{
		base.WeaponUpdate();
		if (this.fireTimer > 0f)
		{
			this.fireTimer -= Time.deltaTime;
		}
		if (base.gameObject.layer == 7)
		{
			this.accumulatedPower = 0f;
			this.AudioStop();
			return;
		}
		if (!base.IsOwner)
		{
			return;
		}
		if ((this.invertFire ? this.fire2 : this.fire1).ReadValue<float>() > 0.1f && this.inRightHand && !this.isClicked && this.fireTimer <= 0f)
		{
			this.ShootServerEffect();
			this.AudioPlay();
		}
		else if ((this.invertFire ? this.fire1 : this.fire2).ReadValue<float>() > 0.1f && this.inLeftHand && !this.isClicked && this.fireTimer <= 0f)
		{
			this.ShootServerEffect();
			this.AudioPlay();
		}
		if ((this.invertFire ? this.fire2 : this.fire1).ReadValue<float>() > 0.1f && this.inRightHand && this.fireTimer <= 0f)
		{
			this.accumulatedPower += Time.deltaTime;
			this.cam.DOShakeRotation(this.duration, this.strength * (this.accumulatedPower / this.maxChargeTime), this.vibrato, this.randomness, this.fadeOut, this.randomnessMode).SetEase(this.shakeEase);
			this.isClicked = true;
		}
		else if ((this.invertFire ? this.fire1 : this.fire2).ReadValue<float>() > 0.1f && this.inLeftHand && this.fireTimer <= 0f)
		{
			this.accumulatedPower += Time.deltaTime;
			this.cam.DOShakeRotation(this.duration, this.strength * (this.accumulatedPower / this.maxChargeTime), this.vibrato, this.randomness, this.fadeOut, this.randomnessMode).SetEase(this.shakeEase);
			this.isClicked = true;
		}
		this.camAnimScript.rotateBack = true;
		if (this.isClicked && this.inRightHand && this.accumulatedPower >= this.maxChargeTime)
		{
			this.isClicked = false;
			this.Fire();
			return;
		}
		if ((this.invertFire ? this.fire2 : this.fire1).ReadValue<float>() < 0.1f && this.isClicked && this.inRightHand)
		{
			this.isClicked = false;
			this.Fire();
			return;
		}
		if ((this.invertFire ? this.fire1 : this.fire2).ReadValue<float>() < 0.1f && this.isClicked && this.inLeftHand)
		{
			this.isClicked = false;
			this.Fire();
		}
	}

	// Token: 0x0600066D RID: 1645 RVA: 0x0002AD88 File Offset: 0x00028F88
	private void Fire()
	{
		if (PauseManager.Instance.pause)
		{
			return;
		}
		if (!this.playerController.IsOwner)
		{
			return;
		}
		this.AudioStop();
		if (this.fireTimer > 0f)
		{
			this.shot = true;
			return;
		}
		if (this.accumulatedPower < this.maxChargeTime)
		{
			this.BeamEffectServer(1);
			this.accumulatedPower = 0f;
			return;
		}
		this.accumulatedPower = 0f;
		this.shot = false;
		this.fireTimer = this.timeBetweenFire;
		this.ShootServer(this.damage, this.cam.transform.position, this.cam.transform.forward);
		if (this.revolverShake)
		{
			base.CameraRevolverAnimation();
		}
		else
		{
			base.CameraAnimation();
		}
		base.WeaponAnimation();
		if (this.playerKnockback != 0f)
		{
			this.playerController.AddForce(-this.cam.transform.forward, this.playerKnockback);
		}
	}

	// Token: 0x0600066E RID: 1646 RVA: 0x0002AE88 File Offset: 0x00029088
	private void ShootServer(float damageToGive, Vector3 position, Vector3 direction)
	{
		this.BeamEffectServer(0);
		this.SoundServerEffect();
		this.RemoveAmmo();
		RaycastHit[] array = Physics.RaycastAll(position, direction, float.PositiveInfinity, this.defaultLayer);
		Array.Sort<RaycastHit>(array, (RaycastHit x, RaycastHit y) => x.distance.CompareTo(y.distance));
		if (array.Length != 0)
		{
			for (int i = 0; i < array.Length; i++)
			{
				base.TriggerEnvironment(array[i].transform.gameObject, array[i].point, direction, array[i].normal);
				this.SpawnBulletTrailServer(array[i].point);
				if (array[i].transform.tag == "ShatterableGlass")
				{
					base.BreakGlassServer(array[i].point, direction, array[i].transform.gameObject);
				}
				else if (array[i].transform.gameObject.layer != LayerMask.NameToLayer("Ragdoll"))
				{
					this.SpawnVFXServer(0, array[i].point, array[i].normal, array[i].transform.tag, array[i].transform);
					this.SpawnVFXServer(1, array[i].point, array[i].normal, array[i].transform.tag, array[i].transform);
				}
				if (array[i].transform.gameObject.layer != 10 && array[i].transform.gameObject.layer != 11 && array[i].transform.gameObject.layer != 14 && array[i].transform.gameObject.layer != 18 && array[i].transform.gameObject.layer != 19 && array[i].transform.gameObject.layer != 24)
				{
					break;
				}
			}
		}
		RaycastHit raycastHit;
		PlayerHealth playerHealth;
		if (Physics.CapsuleCast(position, position, this.radius, this.cam.transform.forward, out raycastHit, float.PositiveInfinity, this.playerLayer) && raycastHit.transform.root.TryGetComponent<PlayerHealth>(out playerHealth))
		{
			if (playerHealth.gameObject == base.transform.root.gameObject)
			{
				return;
			}
			if (base.FriendlyFireCheck(playerHealth))
			{
				return;
			}
			bool flag = raycastHit.transform.gameObject.name == "Head_Col";
			if (raycastHit.transform.gameObject.name == "Head_Col")
			{
				damageToGive *= 2f;
				Settings.Instance.IncreaseHeadshotsAmount();
			}
			else
			{
				Settings.Instance.IncreaseBodyshotsAmount();
			}
			global::UnityEngine.Object.Instantiate<GameObject>(this.bodyImpact, raycastHit.point, Quaternion.identity);
			this.ServerFX(raycastHit.point + direction, Quaternion.identity);
			if (playerHealth.SyncAccessor_health - damageToGive <= 0f)
			{
				base.KillShockWave();
			}
			if (this.marker == null)
			{
				this.marker = global::UnityEngine.Object.Instantiate<GameObject>(this.hitMarker, Crosshair.Instance.transform.position, Quaternion.identity, PauseManager.Instance.transform);
				this.marker.transform.DOPunchScale((raycastHit.transform.gameObject.name == "Head_Col") ? new Vector3(2.5f, 2.5f, 2.5f) : Vector3.one, 0.3f, 8, 2f);
				if (raycastHit.transform.gameObject.name == "Head_Col")
				{
					this.marker.GetComponent<Image>().color = Color.red;
				}
				global::UnityEngine.Object.Destroy(this.marker, 0.3f);
			}
			else
			{
				global::UnityEngine.Object.Destroy(this.marker);
				this.marker = global::UnityEngine.Object.Instantiate<GameObject>(this.hitMarker, Crosshair.Instance.transform.position, Quaternion.identity, PauseManager.Instance.transform);
				this.marker.transform.DOPunchScale((raycastHit.transform.gameObject.name == "Head_Col") ? new Vector3(2.5f, 2.5f, 2.5f) : Vector3.one, 0.3f, 8, 2f);
				if (raycastHit.transform.gameObject.name == "Head_Col")
				{
					this.marker.GetComponent<Image>().color = Color.red;
				}
				global::UnityEngine.Object.Destroy(this.marker, 0.3f);
			}
			if (raycastHit.transform.gameObject.name == "Head_Col")
			{
				this.LocalSound(0);
				this.LocalSound(1);
			}
			else
			{
				this.LocalSound(1);
			}
			if (playerHealth.SyncAccessor_health - damageToGive <= 0f)
			{
				this.LocalSound(2);
				playerHealth.GetComponent<CharacterController>().enabled = false;
				playerHealth.Explode(false, false, raycastHit.transform.gameObject.name, direction, this.ragdollEjectForce, raycastHit.point);
				playerHealth.graphics.SetActive(false);
				playerHealth.controller.playerPickupScript.fpArms.gameObject.SetActive(false);
				this.KillServer(playerHealth);
				playerHealth.DisablePlayerObjectWhenKilled();
				PauseManager.Instance.WriteLog(string.Concat(new string[]
				{
					"<b><color=#",
					PauseManager.Instance.selfNameLogColor,
					">",
					playerHealth.SyncAccessor_playerValues.SyncAccessor_playerClient.PlayerName,
					"</color></b> was ",
					flag ? "headshot" : "killed",
					" with ",
					base.StartsWithVowel(this.behaviour.weaponName) ? "an" : "a",
					" <b><color=white>",
					this.behaviour.weaponName,
					"</color></b> by <b><color=#",
					PauseManager.Instance.enemyNameLogColor,
					">",
					ClientInstance.Instance.PlayerName,
					"</color></b>"
				}));
			}
			else
			{
				this.GiveDamage(damageToGive, playerHealth, raycastHit.transform.gameObject.name);
			}
			this.hitOK = true;
		}
		RaycastHit raycastHit2;
		if (Physics.Raycast(position, direction, out raycastHit2, float.PositiveInfinity, this.supLayer) && !this.hitOK && raycastHit2.transform.gameObject.layer == 17)
		{
			this.SupressionServer(raycastHit2.transform);
		}
		RaycastHit raycastHit3;
		DollHealth dollHealth;
		if (Physics.Raycast(position, direction, out raycastHit3, float.PositiveInfinity) && raycastHit3.transform.TryGetComponent<DollHealth>(out dollHealth))
		{
			dollHealth.health -= damageToGive;
		}
		this.hitOK = false;
	}

	// Token: 0x0600066F RID: 1647 RVA: 0x0002B5D1 File Offset: 0x000297D1
	[ServerRpc]
	private void AudioPlay()
	{
		this.RpcWriter___Server_AudioPlay_2166136261();
	}

	// Token: 0x06000670 RID: 1648 RVA: 0x0002B5D9 File Offset: 0x000297D9
	[ServerRpc]
	private void AudioStop()
	{
		this.RpcWriter___Server_AudioStop_2166136261();
	}

	// Token: 0x06000671 RID: 1649 RVA: 0x0002B5E1 File Offset: 0x000297E1
	[ServerRpc(RunLocally = true)]
	private void SoundServerEffect()
	{
		this.RpcWriter___Server_SoundServerEffect_2166136261();
		this.RpcLogic___SoundServerEffect_2166136261();
	}

	// Token: 0x06000672 RID: 1650 RVA: 0x0002B5EF File Offset: 0x000297EF
	[ObserversRpc(RunLocally = true, ExcludeOwner = true)]
	private void SoundObserversEffect()
	{
		this.RpcWriter___Observers_SoundObserversEffect_2166136261();
		this.RpcLogic___SoundObserversEffect_2166136261();
	}

	// Token: 0x06000673 RID: 1651 RVA: 0x0002B5FD File Offset: 0x000297FD
	[ServerRpc(RunLocally = true)]
	private void RemoveAmmo()
	{
		this.RpcWriter___Server_RemoveAmmo_2166136261();
		this.RpcLogic___RemoveAmmo_2166136261();
	}

	// Token: 0x06000674 RID: 1652 RVA: 0x0002B60B File Offset: 0x0002980B
	[ServerRpc(RunLocally = true)]
	private void GiveDamage(float damageToGive, PlayerHealth enemyHealth, string name)
	{
		this.RpcWriter___Server_GiveDamage_324487999(damageToGive, enemyHealth, name);
		this.RpcLogic___GiveDamage_324487999(damageToGive, enemyHealth, name);
	}

	// Token: 0x06000675 RID: 1653 RVA: 0x0002B634 File Offset: 0x00029834
	[ServerRpc]
	private void KillServer(PlayerHealth enemyHealth)
	{
		this.RpcWriter___Server_KillServer_1722911636(enemyHealth);
	}

	// Token: 0x06000676 RID: 1654 RVA: 0x0002B64B File Offset: 0x0002984B
	[TargetRpc]
	private void KillObserver(NetworkConnection conn, ClientInstance client, PlayerHealth enemyHealth)
	{
		this.RpcWriter___Target_KillObserver_123853379(conn, client, enemyHealth);
	}

	// Token: 0x06000677 RID: 1655 RVA: 0x0002B65F File Offset: 0x0002985F
	[ObserversRpc]
	private void HitFeeback(PlayerHealth enemyHealth)
	{
		this.RpcWriter___Observers_HitFeeback_1722911636(enemyHealth);
	}

	// Token: 0x06000678 RID: 1656 RVA: 0x0002B66B File Offset: 0x0002986B
	[ServerRpc(RunLocally = true)]
	private void ShootServerEffect()
	{
		this.RpcWriter___Server_ShootServerEffect_2166136261();
		this.RpcLogic___ShootServerEffect_2166136261();
	}

	// Token: 0x06000679 RID: 1657 RVA: 0x0002B67C File Offset: 0x0002987C
	[ObserversRpc(RunLocally = true, ExcludeOwner = true)]
	private void ShootObserversEffect()
	{
		this.RpcWriter___Observers_ShootObserversEffect_2166136261();
		this.RpcLogic___ShootObserversEffect_2166136261();
	}

	// Token: 0x0600067A RID: 1658 RVA: 0x0002B695 File Offset: 0x00029895
	[ServerRpc(RunLocally = true)]
	private void SpawnBulletTrailServer(Vector3 hitPoint)
	{
		this.RpcWriter___Server_SpawnBulletTrailServer_4276783012(hitPoint);
		this.RpcLogic___SpawnBulletTrailServer_4276783012(hitPoint);
	}

	// Token: 0x0600067B RID: 1659 RVA: 0x0002B6AC File Offset: 0x000298AC
	[ObserversRpc(RunLocally = true, ExcludeOwner = true)]
	private void SpawnBulletTrail(Vector3 hitPoint)
	{
		this.RpcWriter___Observers_SpawnBulletTrail_4276783012(hitPoint);
		this.RpcLogic___SpawnBulletTrail_4276783012(hitPoint);
	}

	// Token: 0x0600067C RID: 1660 RVA: 0x0002B6CD File Offset: 0x000298CD
	[ServerRpc(RunLocally = true)]
	private void SpawnVFXServer(int index, Vector3 hitPoint, Vector3 hitNormal, string surface, Transform parent)
	{
		this.RpcWriter___Server_SpawnVFXServer_606331033(index, hitPoint, hitNormal, surface, parent);
		this.RpcLogic___SpawnVFXServer_606331033(index, hitPoint, hitNormal, surface, parent);
	}

	// Token: 0x0600067D RID: 1661 RVA: 0x0002B704 File Offset: 0x00029904
	[ObserversRpc(RunLocally = true, ExcludeOwner = true)]
	private void SpawnVFX(int index, Vector3 hitPoint, Vector3 hitNormal, string surface, Transform parent)
	{
		this.RpcWriter___Observers_SpawnVFX_606331033(index, hitPoint, hitNormal, surface, parent);
		this.RpcLogic___SpawnVFX_606331033(index, hitPoint, hitNormal, surface, parent);
	}

	// Token: 0x0600067E RID: 1662 RVA: 0x0002B745 File Offset: 0x00029945
	[ServerRpc(RunLocally = true)]
	private void BeamEffectServer(int i)
	{
		this.RpcWriter___Server_BeamEffectServer_3316948804(i);
		this.RpcLogic___BeamEffectServer_3316948804(i);
	}

	// Token: 0x0600067F RID: 1663 RVA: 0x0002B75B File Offset: 0x0002995B
	[ObserversRpc(RunLocally = true, ExcludeOwner = true)]
	private void BeamEffectObservers(int i)
	{
		this.RpcWriter___Observers_BeamEffectObservers_3316948804(i);
		this.RpcLogic___BeamEffectObservers_3316948804(i);
	}

	// Token: 0x06000680 RID: 1664 RVA: 0x0002B771 File Offset: 0x00029971
	private void LocalSound(int index)
	{
		if (index == 0)
		{
			this.audio.PlayOneShot(this.headHitClip);
		}
		if (index == 1)
		{
			this.audio.PlayOneShot(this.bodyHitClip);
		}
		if (index == 2)
		{
			this.audio.PlayOneShot(this.deathClip);
		}
	}

	// Token: 0x06000681 RID: 1665 RVA: 0x0002B7B1 File Offset: 0x000299B1
	[ServerRpc]
	private void SupressionServer(Transform supp)
	{
		this.RpcWriter___Server_SupressionServer_3068987916(supp);
	}

	// Token: 0x06000682 RID: 1666 RVA: 0x0002B7BD File Offset: 0x000299BD
	[ObserversRpc]
	private void SuppressionTarget(Transform supp)
	{
		this.RpcWriter___Observers_SuppressionTarget_3068987916(supp);
	}

	// Token: 0x06000683 RID: 1667 RVA: 0x0002B7C9 File Offset: 0x000299C9
	[ServerRpc(RunLocally = true)]
	public void ServerFX(Vector3 position, Quaternion rotation)
	{
		this.RpcWriter___Server_ServerFX_3848837105(position, rotation);
		this.RpcLogic___ServerFX_3848837105(position, rotation);
	}

	// Token: 0x06000684 RID: 1668 RVA: 0x0002B7E7 File Offset: 0x000299E7
	[ObserversRpc(RunLocally = true, ExcludeOwner = true)]
	private void ObserversFX(Vector3 position, Quaternion rotation)
	{
		this.RpcWriter___Observers_ObserversFX_3848837105(position, rotation);
		this.RpcLogic___ObserversFX_3848837105(position, rotation);
	}

	// Token: 0x06000686 RID: 1670 RVA: 0x0002B824 File Offset: 0x00029A24
	public override void NetworkInitialize___Early()
	{
		if (this.NetworkInitializeEarly_ChargeGun_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeEarly_ChargeGun_Assembly-CSharp.dll = true;
		base.NetworkInitialize___Early();
		base.RegisterServerRpc(15U, new ServerRpcDelegate(this.RpcReader___Server_AudioPlay_2166136261));
		base.RegisterServerRpc(16U, new ServerRpcDelegate(this.RpcReader___Server_AudioStop_2166136261));
		base.RegisterServerRpc(17U, new ServerRpcDelegate(this.RpcReader___Server_SoundServerEffect_2166136261));
		base.RegisterObserversRpc(18U, new ClientRpcDelegate(this.RpcReader___Observers_SoundObserversEffect_2166136261));
		base.RegisterServerRpc(19U, new ServerRpcDelegate(this.RpcReader___Server_RemoveAmmo_2166136261));
		base.RegisterServerRpc(20U, new ServerRpcDelegate(this.RpcReader___Server_GiveDamage_324487999));
		base.RegisterServerRpc(21U, new ServerRpcDelegate(this.RpcReader___Server_KillServer_1722911636));
		base.RegisterTargetRpc(22U, new ClientRpcDelegate(this.RpcReader___Target_KillObserver_123853379));
		base.RegisterObserversRpc(23U, new ClientRpcDelegate(this.RpcReader___Observers_HitFeeback_1722911636));
		base.RegisterServerRpc(24U, new ServerRpcDelegate(this.RpcReader___Server_ShootServerEffect_2166136261));
		base.RegisterObserversRpc(25U, new ClientRpcDelegate(this.RpcReader___Observers_ShootObserversEffect_2166136261));
		base.RegisterServerRpc(26U, new ServerRpcDelegate(this.RpcReader___Server_SpawnBulletTrailServer_4276783012));
		base.RegisterObserversRpc(27U, new ClientRpcDelegate(this.RpcReader___Observers_SpawnBulletTrail_4276783012));
		base.RegisterServerRpc(28U, new ServerRpcDelegate(this.RpcReader___Server_SpawnVFXServer_606331033));
		base.RegisterObserversRpc(29U, new ClientRpcDelegate(this.RpcReader___Observers_SpawnVFX_606331033));
		base.RegisterServerRpc(30U, new ServerRpcDelegate(this.RpcReader___Server_BeamEffectServer_3316948804));
		base.RegisterObserversRpc(31U, new ClientRpcDelegate(this.RpcReader___Observers_BeamEffectObservers_3316948804));
		base.RegisterServerRpc(32U, new ServerRpcDelegate(this.RpcReader___Server_SupressionServer_3068987916));
		base.RegisterObserversRpc(33U, new ClientRpcDelegate(this.RpcReader___Observers_SuppressionTarget_3068987916));
		base.RegisterServerRpc(34U, new ServerRpcDelegate(this.RpcReader___Server_ServerFX_3848837105));
		base.RegisterObserversRpc(35U, new ClientRpcDelegate(this.RpcReader___Observers_ObserversFX_3848837105));
	}

	// Token: 0x06000687 RID: 1671 RVA: 0x0002BA2B File Offset: 0x00029C2B
	public override void NetworkInitialize__Late()
	{
		if (this.NetworkInitializeLate_ChargeGun_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeLate_ChargeGun_Assembly-CSharp.dll = true;
		base.NetworkInitialize__Late();
	}

	// Token: 0x06000688 RID: 1672 RVA: 0x0002BA44 File Offset: 0x00029C44
	public override void NetworkInitializeIfDisabled()
	{
		this.NetworkInitialize___Early();
		this.NetworkInitialize__Late();
	}

	// Token: 0x06000689 RID: 1673 RVA: 0x0002BA54 File Offset: 0x00029C54
	private void RpcWriter___Server_AudioPlay_2166136261()
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendServerRpc(15U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x0600068A RID: 1674 RVA: 0x0002BB48 File Offset: 0x00029D48
	private void RpcLogic___AudioPlay_2166136261()
	{
		this.networkAudioSource.Play(0);
	}

	// Token: 0x0600068B RID: 1675 RVA: 0x0002BB58 File Offset: 0x00029D58
	private void RpcReader___Server_AudioPlay_2166136261(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		this.RpcLogic___AudioPlay_2166136261();
	}

	// Token: 0x0600068C RID: 1676 RVA: 0x0002BB8C File Offset: 0x00029D8C
	private void RpcWriter___Server_AudioStop_2166136261()
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendServerRpc(16U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x0600068D RID: 1677 RVA: 0x0002BC80 File Offset: 0x00029E80
	private void RpcLogic___AudioStop_2166136261()
	{
		this.networkAudioSource.Stop();
	}

	// Token: 0x0600068E RID: 1678 RVA: 0x0002BC90 File Offset: 0x00029E90
	private void RpcReader___Server_AudioStop_2166136261(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		this.RpcLogic___AudioStop_2166136261();
	}

	// Token: 0x0600068F RID: 1679 RVA: 0x0002BCC4 File Offset: 0x00029EC4
	private void RpcWriter___Server_SoundServerEffect_2166136261()
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendServerRpc(17U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000690 RID: 1680 RVA: 0x0002BDB8 File Offset: 0x00029FB8
	private void RpcLogic___SoundServerEffect_2166136261()
	{
		this.SoundObserversEffect();
	}

	// Token: 0x06000691 RID: 1681 RVA: 0x0002BDC0 File Offset: 0x00029FC0
	private void RpcReader___Server_SoundServerEffect_2166136261(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___SoundServerEffect_2166136261();
	}

	// Token: 0x06000692 RID: 1682 RVA: 0x0002BE00 File Offset: 0x0002A000
	private void RpcWriter___Observers_SoundObserversEffect_2166136261()
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendObserversRpc(18U, writer, channel, DataOrderType.Default, false, false, true);
		writer.Store();
	}

	// Token: 0x06000693 RID: 1683 RVA: 0x0002BEA9 File Offset: 0x0002A0A9
	private void RpcLogic___SoundObserversEffect_2166136261()
	{
		this.newSource.PlayOneShot(this.fireClip);
	}

	// Token: 0x06000694 RID: 1684 RVA: 0x0002BEBC File Offset: 0x0002A0BC
	private void RpcReader___Observers_SoundObserversEffect_2166136261(PooledReader PooledReader0, Channel channel)
	{
		if (!base.IsClient)
		{
			return;
		}
		if (base.IsHost)
		{
			return;
		}
		this.RpcLogic___SoundObserversEffect_2166136261();
	}

	// Token: 0x06000695 RID: 1685 RVA: 0x0002BEE8 File Offset: 0x0002A0E8
	private void RpcWriter___Server_RemoveAmmo_2166136261()
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendServerRpc(19U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000696 RID: 1686 RVA: 0x0002BFDC File Offset: 0x0002A1DC
	private void RpcLogic___RemoveAmmo_2166136261()
	{
		if (this.reloadWeapon)
		{
			this.chargedBullets -= 1f;
			return;
		}
		base.sync___set_value_currentAmmo(base.SyncAccessor_currentAmmo - 1, true);
	}

	// Token: 0x06000697 RID: 1687 RVA: 0x0002C008 File Offset: 0x0002A208
	private void RpcReader___Server_RemoveAmmo_2166136261(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___RemoveAmmo_2166136261();
	}

	// Token: 0x06000698 RID: 1688 RVA: 0x0002C048 File Offset: 0x0002A248
	private void RpcWriter___Server_GiveDamage_324487999(float damageToGive, PlayerHealth enemyHealth, string name)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteSingle(damageToGive, AutoPackType.Unpacked);
		writer.Write___PlayerHealthFishNet.Serializing.Generated(enemyHealth);
		writer.WriteString(name);
		base.SendServerRpc(20U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000699 RID: 1689 RVA: 0x0002C168 File Offset: 0x0002A368
	private void RpcLogic___GiveDamage_324487999(float damageToGive, PlayerHealth enemyHealth, string name)
	{
		enemyHealth.sync___set_value_killer(this.rootObject.transform, true);
		enemyHealth.sync___set_value_health(enemyHealth.SyncAccessor_health - damageToGive, true);
		enemyHealth.KillCam();
		this.HitFeeback(enemyHealth);
		enemyHealth.Dismemberment(name);
	}

	// Token: 0x0600069A RID: 1690 RVA: 0x0002C1A0 File Offset: 0x0002A3A0
	private void RpcReader___Server_GiveDamage_324487999(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		float num = PooledReader0.ReadSingle(AutoPackType.Unpacked);
		PlayerHealth playerHealth = global::FishNet.Serializing.Generated.GeneratedReaders___Internal.Read___PlayerHealthFishNet.Serializing.Generateds(PooledReader0);
		string text = PooledReader0.ReadString();
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___GiveDamage_324487999(num, playerHealth, text);
	}

	// Token: 0x0600069B RID: 1691 RVA: 0x0002C218 File Offset: 0x0002A418
	private void RpcWriter___Server_KillServer_1722911636(PlayerHealth enemyHealth)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.Write___PlayerHealthFishNet.Serializing.Generated(enemyHealth);
		base.SendServerRpc(21U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x0600069C RID: 1692 RVA: 0x0002C31C File Offset: 0x0002A51C
	private void RpcLogic___KillServer_1722911636(PlayerHealth enemyHealth)
	{
		enemyHealth.sync___set_value_isShot(true, true);
		enemyHealth.sync___set_value_health(-8f, true);
		GameManager.Instance.PlayerDied(enemyHealth.SyncAccessor_playerValues.SyncAccessor_playerClient.PlayerId);
		if (this.rootObject != null)
		{
			enemyHealth.sync___set_value_killer(this.rootObject.transform, true);
		}
		this.KillObserver(enemyHealth.SyncAccessor_playerValues.SyncAccessor_playerClient.transform.GetComponent<NetworkObject>().Owner, enemyHealth.SyncAccessor_playerValues.SyncAccessor_playerClient, enemyHealth);
	}

	// Token: 0x0600069D RID: 1693 RVA: 0x0002C3A4 File Offset: 0x0002A5A4
	private void RpcReader___Server_KillServer_1722911636(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		PlayerHealth playerHealth = global::FishNet.Serializing.Generated.GeneratedReaders___Internal.Read___PlayerHealthFishNet.Serializing.Generateds(PooledReader0);
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		this.RpcLogic___KillServer_1722911636(playerHealth);
	}

	// Token: 0x0600069E RID: 1694 RVA: 0x0002C3E8 File Offset: 0x0002A5E8
	private void RpcWriter___Target_KillObserver_123853379(NetworkConnection conn, ClientInstance client, PlayerHealth enemyHealth)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.Write___ClientInstanceFishNet.Serializing.Generated(client);
		writer.Write___PlayerHealthFishNet.Serializing.Generated(enemyHealth);
		base.SendTargetRpc(22U, writer, channel, DataOrderType.Default, conn, false, true);
		writer.Store();
	}

	// Token: 0x0600069F RID: 1695 RVA: 0x000275DA File Offset: 0x000257DA
	private void RpcLogic___KillObserver_123853379(NetworkConnection conn, ClientInstance client, PlayerHealth enemyHealth)
	{
		enemyHealth.shouldDropWeapon = true;
		enemyHealth.isDeadFromTargetRpc = true;
		if (this.rootObject != null)
		{
			GameObject.Find("Main Camera").GetComponent<KillCam>().enemy = this.rootObject.transform;
		}
	}

	// Token: 0x060006A0 RID: 1696 RVA: 0x0002C4AC File Offset: 0x0002A6AC
	private void RpcReader___Target_KillObserver_123853379(PooledReader PooledReader0, Channel channel)
	{
		ClientInstance clientInstance = global::FishNet.Serializing.Generated.GeneratedReaders___Internal.Read___ClientInstanceFishNet.Serializing.Generateds(PooledReader0);
		PlayerHealth playerHealth = global::FishNet.Serializing.Generated.GeneratedReaders___Internal.Read___PlayerHealthFishNet.Serializing.Generateds(PooledReader0);
		if (!base.IsClient)
		{
			return;
		}
		this.RpcLogic___KillObserver_123853379(base.LocalConnection, clientInstance, playerHealth);
	}

	// Token: 0x060006A1 RID: 1697 RVA: 0x0002C4F4 File Offset: 0x0002A6F4
	private void RpcWriter___Observers_HitFeeback_1722911636(PlayerHealth enemyHealth)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.Write___PlayerHealthFishNet.Serializing.Generated(enemyHealth);
		base.SendObserversRpc(23U, writer, channel, DataOrderType.Default, false, false, false);
		writer.Store();
	}

	// Token: 0x060006A2 RID: 1698 RVA: 0x00027716 File Offset: 0x00025916
	private void RpcLogic___HitFeeback_1722911636(PlayerHealth enemyHealth)
	{
		enemyHealth.HitFeeback();
	}

	// Token: 0x060006A3 RID: 1699 RVA: 0x0002C5AC File Offset: 0x0002A7AC
	private void RpcReader___Observers_HitFeeback_1722911636(PooledReader PooledReader0, Channel channel)
	{
		PlayerHealth playerHealth = global::FishNet.Serializing.Generated.GeneratedReaders___Internal.Read___PlayerHealthFishNet.Serializing.Generateds(PooledReader0);
		if (!base.IsClient)
		{
			return;
		}
		this.RpcLogic___HitFeeback_1722911636(playerHealth);
	}

	// Token: 0x060006A4 RID: 1700 RVA: 0x0002C5E0 File Offset: 0x0002A7E0
	private void RpcWriter___Server_ShootServerEffect_2166136261()
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendServerRpc(24U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x060006A5 RID: 1701 RVA: 0x0002C6D4 File Offset: 0x0002A8D4
	private void RpcLogic___ShootServerEffect_2166136261()
	{
		this.ShootObserversEffect();
	}

	// Token: 0x060006A6 RID: 1702 RVA: 0x0002C6DC File Offset: 0x0002A8DC
	private void RpcReader___Server_ShootServerEffect_2166136261(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___ShootServerEffect_2166136261();
	}

	// Token: 0x060006A7 RID: 1703 RVA: 0x0002C71C File Offset: 0x0002A91C
	private void RpcWriter___Observers_ShootObserversEffect_2166136261()
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendObserversRpc(25U, writer, channel, DataOrderType.Default, false, false, true);
		writer.Store();
	}

	// Token: 0x060006A8 RID: 1704 RVA: 0x0002C7C8 File Offset: 0x0002A9C8
	private void RpcLogic___ShootObserversEffect_2166136261()
	{
		if (Settings.Instance.reduceVFX)
		{
			return;
		}
		GameObject gameObject = global::UnityEngine.Object.Instantiate<GameObject>(this.muzzleFlash, this.muzzleFlashPoint.position, this.shootPoint.rotation, base.transform);
		this.tempBeam = gameObject.GetComponent<ParticleSystem>();
		this.tempBeam.Play();
	}

	// Token: 0x060006A9 RID: 1705 RVA: 0x0002C824 File Offset: 0x0002AA24
	private void RpcReader___Observers_ShootObserversEffect_2166136261(PooledReader PooledReader0, Channel channel)
	{
		if (!base.IsClient)
		{
			return;
		}
		if (base.IsHost)
		{
			return;
		}
		this.RpcLogic___ShootObserversEffect_2166136261();
	}

	// Token: 0x060006AA RID: 1706 RVA: 0x0002C850 File Offset: 0x0002AA50
	private void RpcWriter___Server_SpawnBulletTrailServer_4276783012(Vector3 hitPoint)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteVector3(hitPoint);
		base.SendServerRpc(26U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x060006AB RID: 1707 RVA: 0x0002C951 File Offset: 0x0002AB51
	private void RpcLogic___SpawnBulletTrailServer_4276783012(Vector3 hitPoint)
	{
		this.SpawnBulletTrail(hitPoint);
	}

	// Token: 0x060006AC RID: 1708 RVA: 0x0002C95C File Offset: 0x0002AB5C
	private void RpcReader___Server_SpawnBulletTrailServer_4276783012(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		Vector3 vector = PooledReader0.ReadVector3();
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___SpawnBulletTrailServer_4276783012(vector);
	}

	// Token: 0x060006AD RID: 1709 RVA: 0x0002C9AC File Offset: 0x0002ABAC
	private void RpcWriter___Observers_SpawnBulletTrail_4276783012(Vector3 hitPoint)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteVector3(hitPoint);
		base.SendObserversRpc(27U, writer, channel, DataOrderType.Default, false, false, true);
		writer.Store();
	}

	// Token: 0x060006AE RID: 1710 RVA: 0x0002CA64 File Offset: 0x0002AC64
	private void RpcLogic___SpawnBulletTrail_4276783012(Vector3 hitPoint)
	{
		GameObject gameObject = global::UnityEngine.Object.Instantiate<GameObject>(this.bulletTrailLocal.gameObject, this.shootPoint.position, Quaternion.identity);
		LineRenderer component = gameObject.GetComponent<LineRenderer>();
		component.SetPosition(0, this.shootPoint.position);
		component.SetPosition(1, hitPoint);
		global::UnityEngine.Object.Destroy(gameObject, 0.4f);
	}

	// Token: 0x060006AF RID: 1711 RVA: 0x0002CABC File Offset: 0x0002ACBC
	private void RpcReader___Observers_SpawnBulletTrail_4276783012(PooledReader PooledReader0, Channel channel)
	{
		Vector3 vector = PooledReader0.ReadVector3();
		if (!base.IsClient)
		{
			return;
		}
		if (base.IsHost)
		{
			return;
		}
		this.RpcLogic___SpawnBulletTrail_4276783012(vector);
	}

	// Token: 0x060006B0 RID: 1712 RVA: 0x0002CAF8 File Offset: 0x0002ACF8
	private void RpcWriter___Server_SpawnVFXServer_606331033(int index, Vector3 hitPoint, Vector3 hitNormal, string surface, Transform parent)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteInt32(index, AutoPackType.Packed);
		writer.WriteVector3(hitPoint);
		writer.WriteVector3(hitNormal);
		writer.WriteString(surface);
		writer.WriteTransform(parent);
		base.SendServerRpc(28U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x060006B1 RID: 1713 RVA: 0x0002CC32 File Offset: 0x0002AE32
	private void RpcLogic___SpawnVFXServer_606331033(int index, Vector3 hitPoint, Vector3 hitNormal, string surface, Transform parent)
	{
		this.SpawnVFX(index, hitPoint, hitNormal, surface, parent);
	}

	// Token: 0x060006B2 RID: 1714 RVA: 0x0002CC44 File Offset: 0x0002AE44
	private void RpcReader___Server_SpawnVFXServer_606331033(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		int num = PooledReader0.ReadInt32(AutoPackType.Packed);
		Vector3 vector = PooledReader0.ReadVector3();
		Vector3 vector2 = PooledReader0.ReadVector3();
		string text = PooledReader0.ReadString();
		Transform transform = PooledReader0.ReadTransform();
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___SpawnVFXServer_606331033(num, vector, vector2, text, transform);
	}

	// Token: 0x060006B3 RID: 1715 RVA: 0x0002CCDC File Offset: 0x0002AEDC
	private void RpcWriter___Observers_SpawnVFX_606331033(int index, Vector3 hitPoint, Vector3 hitNormal, string surface, Transform parent)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteInt32(index, AutoPackType.Packed);
		writer.WriteVector3(hitPoint);
		writer.WriteVector3(hitNormal);
		writer.WriteString(surface);
		writer.WriteTransform(parent);
		base.SendObserversRpc(29U, writer, channel, DataOrderType.Default, false, false, true);
		writer.Store();
	}

	// Token: 0x060006B4 RID: 1716 RVA: 0x0002CDCC File Offset: 0x0002AFCC
	private void RpcLogic___SpawnVFX_606331033(int index, Vector3 hitPoint, Vector3 hitNormal, string surface, Transform parent)
	{
		if (Settings.Instance.reduceVFX)
		{
			return;
		}
		uint num = <PrivateImplementationDetails>.ComputeStringHash(surface);
		if (num <= 1825421690U)
		{
			if (num <= 1378315797U)
			{
				if (num <= 464173256U)
				{
					if (num != 456440475U)
					{
						if (num == 464173256U)
						{
							if (surface == "Footsteps/Concrete/Dalles")
							{
								if (index == 0)
								{
									global::UnityEngine.Object.Instantiate<GameObject>(this.genericImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
								}
								if (index == 1)
								{
									global::UnityEngine.Object.Instantiate<GameObject>(this.bulletHole, hitPoint, Quaternion.LookRotation(hitNormal), parent);
									goto IL_06C1;
								}
								goto IL_06C1;
							}
						}
					}
					else if (surface == "Footsteps/Sand")
					{
						if (index == 0)
						{
							global::UnityEngine.Object.Instantiate<GameObject>(this.genericImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
						}
						if (index == 1)
						{
							global::UnityEngine.Object.Instantiate<GameObject>(this.sandHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
							goto IL_06C1;
						}
						goto IL_06C1;
					}
				}
				else if (num != 913360285U)
				{
					if (num != 977466297U)
					{
						if (num == 1378315797U)
						{
							if (surface == "Footsteps/Moquette")
							{
								if (index == 0)
								{
									global::UnityEngine.Object.Instantiate<GameObject>(this.genericImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
								}
								if (index == 1)
								{
									global::UnityEngine.Object.Instantiate<GameObject>(this.softbodyHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
									goto IL_06C1;
								}
								goto IL_06C1;
							}
						}
					}
					else if (surface == "Footsteps/Concrete/Solide")
					{
						if (index == 0)
						{
							global::UnityEngine.Object.Instantiate<GameObject>(this.genericImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
						}
						if (index == 1)
						{
							global::UnityEngine.Object.Instantiate<GameObject>(this.bulletHole, hitPoint, Quaternion.LookRotation(hitNormal), parent);
							goto IL_06C1;
						}
						goto IL_06C1;
					}
				}
				else if (surface == "Grenade")
				{
					goto IL_06C1;
				}
			}
			else if (num <= 1430892386U)
			{
				if (num != 1429664136U)
				{
					if (num == 1430892386U)
					{
						if (surface == "Hat")
						{
							goto IL_06C1;
						}
					}
				}
				else if (surface == "Footsteps/Water")
				{
					if (index == 0)
					{
						global::UnityEngine.Object.Instantiate<GameObject>(this.genericImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
					}
					if (index == 1)
					{
						global::UnityEngine.Object.Instantiate<GameObject>(this.waterHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
						goto IL_06C1;
					}
					goto IL_06C1;
				}
			}
			else if (num != 1610969363U)
			{
				if (num != 1624496828U)
				{
					if (num == 1825421690U)
					{
						if (surface == "Footsteps/Wood/Creux")
						{
							if (index == 0)
							{
								global::UnityEngine.Object.Instantiate<GameObject>(this.genericImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
							}
							if (index == 1)
							{
								global::UnityEngine.Object.Instantiate<GameObject>(this.woodHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
								goto IL_06C1;
							}
							goto IL_06C1;
						}
					}
				}
				else if (surface == "Footsteps/Concrete/Default")
				{
					if (index == 0)
					{
						global::UnityEngine.Object.Instantiate<GameObject>(this.genericImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
					}
					if (index == 1)
					{
						global::UnityEngine.Object.Instantiate<GameObject>(this.bulletHole, hitPoint, Quaternion.LookRotation(hitNormal), parent);
						goto IL_06C1;
					}
					goto IL_06C1;
				}
			}
			else if (surface == "Footsteps/Concrete/PleinAir")
			{
				if (index == 0)
				{
					global::UnityEngine.Object.Instantiate<GameObject>(this.genericImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
				}
				if (index == 1)
				{
					global::UnityEngine.Object.Instantiate<GameObject>(this.bulletHole, hitPoint, Quaternion.LookRotation(hitNormal), parent);
					goto IL_06C1;
				}
				goto IL_06C1;
			}
		}
		else if (num <= 2833365437U)
		{
			if (num <= 2180110808U)
			{
				if (num != 1954265137U)
				{
					if (num == 2180110808U)
					{
						if (surface == "Footsteps/Dirt")
						{
							if (index == 0)
							{
								global::UnityEngine.Object.Instantiate<GameObject>(this.genericImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
							}
							if (index == 1)
							{
								global::UnityEngine.Object.Instantiate<GameObject>(this.dirtHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
								goto IL_06C1;
							}
							goto IL_06C1;
						}
					}
				}
				else if (surface == "NoSound")
				{
					goto IL_06C1;
				}
			}
			else if (num != 2251139421U)
			{
				if (num != 2400559108U)
				{
					if (num == 2833365437U)
					{
						if (surface == "Footsteps/Metal/Pipe2")
						{
							if (index == 0)
							{
								global::UnityEngine.Object.Instantiate<GameObject>(this.genericImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
							}
							if (index == 1)
							{
								global::UnityEngine.Object.Instantiate<GameObject>(this.metalHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
								goto IL_06C1;
							}
							goto IL_06C1;
						}
					}
				}
				else if (surface == "Footsteps/Matelas")
				{
					if (index == 0)
					{
						global::UnityEngine.Object.Instantiate<GameObject>(this.genericImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
					}
					if (index == 1)
					{
						global::UnityEngine.Object.Instantiate<GameObject>(this.softbodyHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
						goto IL_06C1;
					}
					goto IL_06C1;
				}
			}
			else if (surface == "Footsteps/Metal/Pipe")
			{
				if (index == 0)
				{
					global::UnityEngine.Object.Instantiate<GameObject>(this.genericImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
				}
				if (index == 1)
				{
					global::UnityEngine.Object.Instantiate<GameObject>(this.tauleHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
					goto IL_06C1;
				}
				goto IL_06C1;
			}
		}
		else if (num <= 3455850406U)
		{
			if (num != 3075599786U)
			{
				if (num == 3455850406U)
				{
					if (surface == "Footsteps/Graviers")
					{
						if (index == 0)
						{
							global::UnityEngine.Object.Instantiate<GameObject>(this.genericImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
						}
						if (index == 1)
						{
							global::UnityEngine.Object.Instantiate<GameObject>(this.bulletHole, hitPoint, Quaternion.LookRotation(hitNormal), parent);
							goto IL_06C1;
						}
						goto IL_06C1;
					}
				}
			}
			else if (surface == "Footsteps/Wood/Sec")
			{
				if (index == 0)
				{
					global::UnityEngine.Object.Instantiate<GameObject>(this.genericImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
				}
				if (index == 1)
				{
					global::UnityEngine.Object.Instantiate<GameObject>(this.woodHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
					goto IL_06C1;
				}
				goto IL_06C1;
			}
		}
		else if (num != 3492154005U)
		{
			if (num != 3807801418U)
			{
				if (num == 3848897750U)
				{
					if (surface == "Mine")
					{
						goto IL_06C1;
					}
				}
			}
			else if (surface == "Footsteps/Metal/Grille")
			{
				if (index == 0)
				{
					global::UnityEngine.Object.Instantiate<GameObject>(this.genericImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
				}
				if (index == 1)
				{
					global::UnityEngine.Object.Instantiate<GameObject>(this.tauleHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
					goto IL_06C1;
				}
				goto IL_06C1;
			}
		}
		else if (surface == "Footsteps/Grass")
		{
			if (index == 0)
			{
				global::UnityEngine.Object.Instantiate<GameObject>(this.genericImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
			}
			if (index == 1)
			{
				global::UnityEngine.Object.Instantiate<GameObject>(this.dirtHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
				goto IL_06C1;
			}
			goto IL_06C1;
		}
		if (index == 0)
		{
			global::UnityEngine.Object.Instantiate<GameObject>(this.genericImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
		}
		if (index == 1)
		{
			global::UnityEngine.Object.Instantiate<GameObject>(this.bulletHole, hitPoint, Quaternion.LookRotation(hitNormal), parent);
		}
		IL_06C1:
		if (index == 2)
		{
			global::UnityEngine.Object.Instantiate<GameObject>(this.bodyImpact, hitPoint, Quaternion.identity, parent);
		}
	}

	// Token: 0x060006B5 RID: 1717 RVA: 0x0002D4B4 File Offset: 0x0002B6B4
	private void RpcReader___Observers_SpawnVFX_606331033(PooledReader PooledReader0, Channel channel)
	{
		int num = PooledReader0.ReadInt32(AutoPackType.Packed);
		Vector3 vector = PooledReader0.ReadVector3();
		Vector3 vector2 = PooledReader0.ReadVector3();
		string text = PooledReader0.ReadString();
		Transform transform = PooledReader0.ReadTransform();
		if (!base.IsClient)
		{
			return;
		}
		if (base.IsHost)
		{
			return;
		}
		this.RpcLogic___SpawnVFX_606331033(num, vector, vector2, text, transform);
	}

	// Token: 0x060006B6 RID: 1718 RVA: 0x0002D538 File Offset: 0x0002B738
	private void RpcWriter___Server_BeamEffectServer_3316948804(int i)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteInt32(i, AutoPackType.Packed);
		base.SendServerRpc(30U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x060006B7 RID: 1719 RVA: 0x0002D63E File Offset: 0x0002B83E
	private void RpcLogic___BeamEffectServer_3316948804(int i)
	{
		this.BeamEffectObservers(i);
	}

	// Token: 0x060006B8 RID: 1720 RVA: 0x0002D648 File Offset: 0x0002B848
	private void RpcReader___Server_BeamEffectServer_3316948804(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		int num = PooledReader0.ReadInt32(AutoPackType.Packed);
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___BeamEffectServer_3316948804(num);
	}

	// Token: 0x060006B9 RID: 1721 RVA: 0x0002D69C File Offset: 0x0002B89C
	private void RpcWriter___Observers_BeamEffectObservers_3316948804(int i)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteInt32(i, AutoPackType.Packed);
		base.SendObserversRpc(31U, writer, channel, DataOrderType.Default, false, false, true);
		writer.Store();
	}

	// Token: 0x060006BA RID: 1722 RVA: 0x0002D757 File Offset: 0x0002B957
	private void RpcLogic___BeamEffectObservers_3316948804(int i)
	{
		if (i == 0)
		{
			this.tempBeam.transform.SetParent(null);
		}
		if (i == 1)
		{
			this.tempBeam.gameObject.SetActive(false);
		}
	}

	// Token: 0x060006BB RID: 1723 RVA: 0x0002D784 File Offset: 0x0002B984
	private void RpcReader___Observers_BeamEffectObservers_3316948804(PooledReader PooledReader0, Channel channel)
	{
		int num = PooledReader0.ReadInt32(AutoPackType.Packed);
		if (!base.IsClient)
		{
			return;
		}
		if (base.IsHost)
		{
			return;
		}
		this.RpcLogic___BeamEffectObservers_3316948804(num);
	}

	// Token: 0x060006BC RID: 1724 RVA: 0x0002D7C4 File Offset: 0x0002B9C4
	private void RpcWriter___Server_SupressionServer_3068987916(Transform supp)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteTransform(supp);
		base.SendServerRpc(32U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x060006BD RID: 1725 RVA: 0x0002D8C5 File Offset: 0x0002BAC5
	private void RpcLogic___SupressionServer_3068987916(Transform supp)
	{
		this.SuppressionTarget(supp);
	}

	// Token: 0x060006BE RID: 1726 RVA: 0x0002D8D0 File Offset: 0x0002BAD0
	private void RpcReader___Server_SupressionServer_3068987916(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		Transform transform = PooledReader0.ReadTransform();
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		this.RpcLogic___SupressionServer_3068987916(transform);
	}

	// Token: 0x060006BF RID: 1727 RVA: 0x0002D914 File Offset: 0x0002BB14
	private void RpcWriter___Observers_SuppressionTarget_3068987916(Transform supp)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteTransform(supp);
		base.SendObserversRpc(33U, writer, channel, DataOrderType.Default, false, false, false);
		writer.Store();
	}

	// Token: 0x060006C0 RID: 1728 RVA: 0x00027B2A File Offset: 0x00025D2A
	private void RpcLogic___SuppressionTarget_3068987916(Transform supp)
	{
		supp.GetComponent<Suppression>().SuppressionTrigger();
	}

	// Token: 0x060006C1 RID: 1729 RVA: 0x0002D9CC File Offset: 0x0002BBCC
	private void RpcReader___Observers_SuppressionTarget_3068987916(PooledReader PooledReader0, Channel channel)
	{
		Transform transform = PooledReader0.ReadTransform();
		if (!base.IsClient)
		{
			return;
		}
		this.RpcLogic___SuppressionTarget_3068987916(transform);
	}

	// Token: 0x060006C2 RID: 1730 RVA: 0x0002DA00 File Offset: 0x0002BC00
	private void RpcWriter___Server_ServerFX_3848837105(Vector3 position, Quaternion rotation)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteVector3(position);
		writer.WriteQuaternion(rotation, AutoPackType.Packed);
		base.SendServerRpc(34U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x060006C3 RID: 1731 RVA: 0x0002DB13 File Offset: 0x0002BD13
	public void RpcLogic___ServerFX_3848837105(Vector3 position, Quaternion rotation)
	{
		this.ObserversFX(position, rotation);
	}

	// Token: 0x060006C4 RID: 1732 RVA: 0x0002DB20 File Offset: 0x0002BD20
	private void RpcReader___Server_ServerFX_3848837105(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		Vector3 vector = PooledReader0.ReadVector3();
		Quaternion quaternion = PooledReader0.ReadQuaternion(AutoPackType.Packed);
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___ServerFX_3848837105(vector, quaternion);
	}

	// Token: 0x060006C5 RID: 1733 RVA: 0x0002DB88 File Offset: 0x0002BD88
	private void RpcWriter___Observers_ObserversFX_3848837105(Vector3 position, Quaternion rotation)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteVector3(position);
		writer.WriteQuaternion(rotation, AutoPackType.Packed);
		base.SendObserversRpc(35U, writer, channel, DataOrderType.Default, false, false, true);
		writer.Store();
	}

	// Token: 0x060006C6 RID: 1734 RVA: 0x000272E4 File Offset: 0x000254E4
	private void RpcLogic___ObserversFX_3848837105(Vector3 position, Quaternion rotation)
	{
		global::UnityEngine.Object.Instantiate<GameObject>(this.bloodSplatter, position, rotation);
	}

	// Token: 0x060006C7 RID: 1735 RVA: 0x0002DC50 File Offset: 0x0002BE50
	private void RpcReader___Observers_ObserversFX_3848837105(PooledReader PooledReader0, Channel channel)
	{
		Vector3 vector = PooledReader0.ReadVector3();
		Quaternion quaternion = PooledReader0.ReadQuaternion(AutoPackType.Packed);
		if (!base.IsClient)
		{
			return;
		}
		if (base.IsHost)
		{
			return;
		}
		this.RpcLogic___ObserversFX_3848837105(vector, quaternion);
	}

	// Token: 0x060006C8 RID: 1736 RVA: 0x0002DCA1 File Offset: 0x0002BEA1
	public override void Awake()
	{
		this.NetworkInitialize___Early();
		base.Awake();
		this.NetworkInitialize__Late();
	}

	// Token: 0x060006C9 RID: 1737 RVA: 0x000023D6 File Offset: 0x000005D6
	public override void Awake___UserLogic()
	{
	}

	// Token: 0x04000650 RID: 1616
	[Header("Weapon Specials")]
	private float accumulatedPower;

	// Token: 0x04000651 RID: 1617
	[SerializeField]
	private float maxChargeTime;

	// Token: 0x04000652 RID: 1618
	[SerializeField]
	private bool hasIntermediateStates;

	// Token: 0x04000653 RID: 1619
	[SerializeField]
	private float radius = 0.1f;

	// Token: 0x04000654 RID: 1620
	[SerializeField]
	private float playerKnockback = 2f;

	// Token: 0x04000655 RID: 1621
	[SerializeField]
	private AudioSource newSource;

	// Token: 0x04000656 RID: 1622
	private float fireTimer;

	// Token: 0x04000657 RID: 1623
	private bool touched;

	// Token: 0x04000658 RID: 1624
	private NetworkAudioSource networkAudioSource;

	// Token: 0x04000659 RID: 1625
	private bool hitOK;

	// Token: 0x0400065A RID: 1626
	private ParticleSystem tempBeam;

	// Token: 0x0400065B RID: 1627
	private bool NetworkInitializeEarly_ChargeGun_Assembly-CSharp.dll;

	// Token: 0x0400065C RID: 1628
	private bool NetworkInitializeLate_ChargeGun_Assembly-CSharp.dll;
}

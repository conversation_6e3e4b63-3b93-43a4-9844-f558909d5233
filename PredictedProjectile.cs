﻿using System;
using System.Collections.Generic;
using DG.Tweening;
using FishNet;
using UnityEngine;
using UnityEngine.UI;

// Token: 0x020000A2 RID: 162
public class PredictedProjectile : MonoBehaviour
{
	// Token: 0x060008D2 RID: 2258 RVA: 0x0003F654 File Offset: 0x0003D854
	private void OnEnable()
	{
		PauseManager.OnBeforeSpawn += this.StartNewRound;
	}

	// Token: 0x060008D3 RID: 2259 RVA: 0x0003F667 File Offset: 0x0003D867
	private void OnDisable()
	{
		PauseManager.OnBeforeSpawn -= this.StartNewRound;
	}

	// Token: 0x060008D4 RID: 2260 RVA: 0x00002E03 File Offset: 0x00001003
	private void StartNewRound()
	{
		global::UnityEngine.Object.Destroy(base.gameObject);
	}

	// Token: 0x060008D5 RID: 2261 RVA: 0x0003F67A File Offset: 0x0003D87A
	private void Awake()
	{
		this.audio = base.GetComponent<AudioSource>();
		this._gravity = this.gravityStart;
	}

	// Token: 0x060008D6 RID: 2262 RVA: 0x0003F694 File Offset: 0x0003D894
	public void Initialize(Vector3 direction, float force, float passedTime, GameObject rootObject, GameObject gun)
	{
		this._direction = direction;
		this._passedTime = passedTime;
		this._rootObject = rootObject;
		this._force = force;
		this._gun = gun;
		base.transform.rotation = Quaternion.LookRotation(-direction);
		base.transform.position += rootObject.transform.forward * this.launchOffset;
	}

	// Token: 0x060008D7 RID: 2263 RVA: 0x0003F709 File Offset: 0x0003D909
	private void Update()
	{
		this.currentPosition = base.transform.position;
		this.Move();
		this.HandleCollision();
		this.lastPosition = this.currentPosition;
	}

	// Token: 0x060008D8 RID: 2264 RVA: 0x0003F734 File Offset: 0x0003D934
	private void Move()
	{
		this.velocity = this.lastPosition - this.currentPosition;
		if (!this.useGravity)
		{
			base.transform.rotation = Quaternion.LookRotation(this._direction);
		}
		else if (this.velocity != Vector3.zero)
		{
			base.transform.rotation = Quaternion.LookRotation(-this.velocity.normalized);
		}
		float deltaTime = Time.deltaTime;
		float num = 0f;
		if (this._passedTime > 0f)
		{
			float num2 = this._passedTime * 0.08f;
			this._passedTime -= num2;
			if (this._passedTime <= deltaTime / 2f)
			{
				num2 += this._passedTime;
				this._passedTime = 0f;
			}
			num = num2;
		}
		this.actualPassedTime += deltaTime + num;
		if (this.useGravity)
		{
			this._gravity += this.gravity * (deltaTime + num);
		}
		if (this.usePhysics)
		{
			this._force -= this.friction * (deltaTime + num);
		}
		base.transform.position += this._direction * ((this.usePhysics ? this._force : this.MOVE_RATE) * (deltaTime + num));
		this.distanceTraveled += (this.usePhysics ? this._force : this.MOVE_RATE) * (deltaTime + num);
		if (this.distanceTraveled > 150f && this.destroySelf)
		{
			global::UnityEngine.Object.Destroy(base.gameObject, 3f);
			base.enabled = false;
			this.graph.SetActive(false);
		}
		if (this.useGravity)
		{
			base.transform.position -= Vector3.up * (this._gravity * (deltaTime + num));
		}
	}

	// Token: 0x060008D9 RID: 2265 RVA: 0x0003F920 File Offset: 0x0003DB20
	private void HandleCollision()
	{
		if (this.prophet && this.prophetHit)
		{
			return;
		}
		if (!this.skipSafeStartTimer && this.actualPassedTime < 0.1f)
		{
			return;
		}
		RaycastHit raycastHit;
		this.backupRaycast = Physics.Raycast(base.transform.position - base.transform.forward, base.transform.forward, out raycastHit, this.backupRayLength, this.playerLayer);
		Collider[] array = Physics.OverlapSphere(base.transform.position, this.radius, this.playerLayer);
		RaycastHit raycastHit2;
		if (Physics.Raycast(base.transform.position, new Vector3(base.transform.forward.x, 0f, base.transform.forward.z), out raycastHit2, 2f, this.headLayer) && (raycastHit2.transform.gameObject.name == "Head_Col" || raycastHit2.transform.gameObject.name == "Neck_1_Col"))
		{
			this.headshot = true;
		}
		Debug.DrawRay(base.transform.position, new Vector3(base.transform.forward.x, 0f, base.transform.forward.z), Color.red, 1f);
		if (array.Length != 0)
		{
			this.isGlass = false;
			bool flag = false;
			for (int i = 0; i < array.Length; i++)
			{
				if (array[i].transform.tag == "ShatterableGlass")
				{
					this.isGlass = true;
					if (array[i].gameObject.GetComponent<ShatterableGlass>() != null)
					{
						array[i].gameObject.GetComponent<ShatterableGlass>().Shatter3D(array[i].transform.position, array[i].transform.position - base.transform.position);
					}
				}
			}
			for (int j = 0; j < array.Length; j++)
			{
				flag = array[j].gameObject == this._gun;
			}
			bool flag2 = false;
			for (int k = 0; k < array.Length; k++)
			{
				flag2 = array[k].gameObject == this._rootObject;
			}
			if (!flag && !flag2)
			{
				PlayerHealth componentInParent = array[0].GetComponentInParent<PlayerHealth>();
				if (componentInParent != null && (!this.shouldDestroy || componentInParent != this._rootObject.GetComponent<PlayerHealth>()))
				{
					if (this.isOwner && !componentInParent.SyncAccessor_isKilled)
					{
						if (this.headshot)
						{
							this.damage *= 2f;
						}
						if (this.headshot)
						{
							Settings.Instance.IncreaseHeadshotsAmount();
						}
						else
						{
							Settings.Instance.IncreaseBodyshotsAmount();
						}
						if (componentInParent.SyncAccessor_health - this.damage <= 0f)
						{
							componentInParent.ChangeKilledState(true);
						}
						componentInParent.SetKiller(this._rootObject.transform);
						if (componentInParent.SyncAccessor_health - this.damage <= 0f)
						{
							componentInParent.Explode(false, true, componentInParent.gameObject.name, componentInParent.transform.position - base.transform.position, this.ragdollEjectForce, base.transform.position);
							this.KillShockWave();
							this.SendKillLog(componentInParent);
						}
						componentInParent.RemoveHealth(this.damage);
						this.HitMarker(this.headshot);
						global::UnityEngine.Object.Instantiate<GameObject>(this.bloodVfx, base.transform.position, Quaternion.identity);
						if (this.headshot && this.headBloodVfx)
						{
							global::UnityEngine.Object.Instantiate<GameObject>(this.headBloodVfx, base.transform.position, Quaternion.identity);
						}
					}
					if (this.bloodSplatter)
					{
						global::UnityEngine.Object.Instantiate<GameObject>(this.bloodSplatter, componentInParent.transform.position, Quaternion.Euler(0f, (float)global::UnityEngine.Random.Range(0, 360), 0f));
					}
					if (this.shouldDestroy)
					{
						global::UnityEngine.Object.Destroy(base.gameObject, 3f);
						base.enabled = false;
						this.graph.SetActive(false);
						GameObject[] array2 = GameObject.FindGameObjectsWithTag("Player");
						for (int l = 0; l < array2.Length; l++)
						{
							float num = Vector3.Distance(base.transform.position, array2[l].transform.position);
							array2[l].GetComponent<PlayerHealth>().LocalScreenshake(this.duration, Mathf.Lerp(this.maxStrength, this.minStrength, Mathf.Clamp(num / this.maxDistance, 0f, 1f)), this.vibrato, this.randomness, this.shakeEase);
						}
					}
					this.audio.Play();
				}
				RaycastHit raycastHit3;
				if (InstanceFinder.IsClient && !flag && !flag2 && this.shouldDestroy && !this.isGlass && componentInParent == null && Physics.SphereCast(base.transform.position - base.transform.forward, this.radius * 1.4f, base.transform.forward, out raycastHit3, 1f, this.playerLayer))
				{
					global::UnityEngine.Object.Instantiate<GameObject>(this.hitVfx, base.transform.position, this.orientateVfx ? Quaternion.LookRotation(raycastHit3.normal) : Quaternion.identity);
					if (this.SurfacesImpact)
					{
						this.SpawnVFX(0, base.transform.position, raycastHit3.normal, raycastHit3.transform.tag, raycastHit3.transform);
					}
					if (this.SurfacesVFX)
					{
						this.SpawnVFX(1, base.transform.position, raycastHit3.normal, raycastHit3.transform.tag, raycastHit3.transform);
					}
				}
				Collider[] array3 = Physics.OverlapSphere(base.transform.position, this.explosionRadius, this.bodyLayer);
				if (array3.Length != 0 && !this.isGlass && this.explosionRadius > 0f)
				{
					this.Glass();
					List<PlayerHealth> list = new List<PlayerHealth>();
					for (int m = 0; m < array3.Length; m++)
					{
						if (array3[m].GetComponentInParent<PlayerHealth>() != null && !list.Contains(array3[m].GetComponentInParent<PlayerHealth>()))
						{
							list.Add(array3[m].GetComponentInParent<PlayerHealth>());
						}
					}
					this.ph2 = list.ToArray();
					for (int n = 0; n < this.ph2.Length; n++)
					{
						if (this.ph2[n] != null && !this.ph2[n].SyncAccessor_isKilled)
						{
							if (this.isOwner)
							{
								this.ph2[n].SetKiller(this._rootObject.transform);
							}
							if (this.bloodSplatter)
							{
								global::UnityEngine.Object.Instantiate<GameObject>(this.bloodSplatter, this.ph2[n].transform.position, Quaternion.Euler(0f, (float)global::UnityEngine.Random.Range(0, 360), 0f));
							}
							if (this.ph2[n].SyncAccessor_health - this.damage <= 0f && this.isOwner)
							{
								if (this.ph2[n].SyncAccessor_playerValues.SyncAccessor_playerClient == this.weapon.playerValues.SyncAccessor_playerClient)
								{
									this.ph2[n].ChangeKilledState(true);
									this.ph2[n].suicide = true;
									Settings.Instance.IncreaseSuicidesAmount();
								}
								else if (this.ph2[n].SyncAccessor_playerValues.SyncAccessor_playerClient != this.weapon.playerValues.SyncAccessor_playerClient)
								{
									this.ph2[n].ChangeKilledState(true);
									this.ph2[n].Explode(false, true, this.ph2[n].gameObject.name, this.ph2[n].transform.position - base.transform.position, this.ragdollEjectForce, base.transform.position);
									this.KillShockWave();
									this.SendKillLog(this.ph2[n]);
								}
							}
							if (this.isOwner)
							{
								this.ph2[n].RemoveHealth(this.damage);
							}
							if (this.isOwner)
							{
								this.HitMarker(this.headshot);
							}
							this.touched2 = true;
							this.touched = true;
						}
					}
				}
			}
			if (!flag && !flag2 && this.shouldDestroy && !this.isGlass)
			{
				global::UnityEngine.Object.Destroy(base.gameObject, 3f);
				if (this.explosionDecal)
				{
					global::UnityEngine.Object.Instantiate<GameObject>(this.explosionDecal, base.transform.position, Quaternion.identity);
				}
				base.enabled = false;
				this.graph.SetActive(false);
				this.audio.Play();
				GameObject[] array4 = GameObject.FindGameObjectsWithTag("Player");
				for (int num2 = 0; num2 < array4.Length; num2++)
				{
					float num3 = Vector3.Distance(base.transform.position, array4[num2].transform.position);
					array4[num2].GetComponent<PlayerHealth>().LocalScreenshake(this.duration, Mathf.Lerp(this.maxStrength, this.minStrength, Mathf.Clamp(num3 / this.maxDistance, 0f, 1f)), this.vibrato, this.randomness, this.shakeEase);
				}
				return;
			}
		}
		else if (this.backupRaycast && !this.touched && !this.touched2 && this.shouldDestroy)
		{
			this.isGlass = false;
			if (raycastHit.transform.gameObject.tag == "ShatterableGlass")
			{
				this.isGlass = true;
				if (raycastHit.transform.gameObject.GetComponent<ShatterableGlass>() != null)
				{
					raycastHit.transform.gameObject.GetComponent<ShatterableGlass>().Shatter3D(raycastHit.point, raycastHit.point - base.transform.position);
				}
			}
			bool flag3 = raycastHit.transform.gameObject == this._gun;
			bool flag4 = raycastHit.transform.gameObject == this._rootObject;
			if (!flag3 && !flag4)
			{
				PlayerHealth componentInParent2 = raycastHit.transform.GetComponentInParent<PlayerHealth>();
				if (componentInParent2 != null && (!this.shouldDestroy || componentInParent2 != this._rootObject.GetComponent<PlayerHealth>()))
				{
					if (this.isOwner && !componentInParent2.SyncAccessor_isKilled)
					{
						if (this.headshot)
						{
							this.damage *= 2f;
						}
						if (this.headshot)
						{
							Settings.Instance.IncreaseHeadshotsAmount();
						}
						else
						{
							Settings.Instance.IncreaseBodyshotsAmount();
						}
						if (componentInParent2.SyncAccessor_health - this.damage <= 0f)
						{
							componentInParent2.ChangeKilledState(true);
						}
						componentInParent2.SetKiller(this._rootObject.transform);
						if (componentInParent2.SyncAccessor_health - this.damage <= 0f)
						{
							componentInParent2.Explode(false, true, componentInParent2.gameObject.name, componentInParent2.transform.position - base.transform.position, this.ragdollEjectForce, base.transform.position);
							this.KillShockWave();
							this.SendKillLog(componentInParent2);
						}
						componentInParent2.RemoveHealth(this.damage);
						this.HitMarker(this.headshot);
						global::UnityEngine.Object.Instantiate<GameObject>(this.bloodVfx, base.transform.position, Quaternion.identity);
						if (this.headshot && this.headBloodVfx)
						{
							global::UnityEngine.Object.Instantiate<GameObject>(this.headBloodVfx, base.transform.position, Quaternion.identity);
						}
					}
					if (this.bloodSplatter)
					{
						global::UnityEngine.Object.Instantiate<GameObject>(this.bloodSplatter, componentInParent2.transform.position, Quaternion.Euler(0f, (float)global::UnityEngine.Random.Range(0, 360), 0f));
					}
					if (this.shouldDestroy)
					{
						global::UnityEngine.Object.Destroy(base.gameObject, 3f);
						base.enabled = false;
						this.graph.SetActive(false);
						GameObject[] array5 = GameObject.FindGameObjectsWithTag("Player");
						for (int num4 = 0; num4 < array5.Length; num4++)
						{
							float num5 = Vector3.Distance(base.transform.position, array5[num4].transform.position);
							array5[num4].GetComponent<PlayerHealth>().LocalScreenshake(this.duration, Mathf.Lerp(this.maxStrength, this.minStrength, Mathf.Clamp(num5 / this.maxDistance, 0f, 1f)), this.vibrato, this.randomness, this.shakeEase);
						}
					}
					this.audio.Play();
				}
				if (InstanceFinder.IsClient && !flag3 && !flag4 && this.shouldDestroy && !this.isGlass && componentInParent2 == null)
				{
					global::UnityEngine.Object.Instantiate<GameObject>(this.hitVfx, base.transform.position, this.orientateVfx ? Quaternion.LookRotation(raycastHit.normal) : Quaternion.identity);
					if (this.SurfacesImpact)
					{
						this.SpawnVFX(0, base.transform.position, raycastHit.normal, raycastHit.transform.tag, raycastHit.transform);
					}
					if (this.SurfacesVFX)
					{
						this.SpawnVFX(1, base.transform.position + base.transform.forward * 1.5f, raycastHit.normal, raycastHit.transform.tag, raycastHit.transform);
					}
				}
				Collider[] array6 = Physics.OverlapSphere(base.transform.position, this.explosionRadius, this.bodyLayer);
				if (array6.Length != 0 && !this.isGlass && this.explosionRadius > 0f)
				{
					this.Glass();
					List<PlayerHealth> list2 = new List<PlayerHealth>();
					for (int num6 = 0; num6 < array6.Length; num6++)
					{
						if (array6[num6].GetComponentInParent<PlayerHealth>() != null && !list2.Contains(array6[num6].GetComponentInParent<PlayerHealth>()))
						{
							list2.Add(array6[num6].GetComponentInParent<PlayerHealth>());
						}
					}
					this.ph2 = list2.ToArray();
					for (int num7 = 0; num7 < this.ph2.Length; num7++)
					{
						if (this.ph2[num7] != null && !this.ph2[num7].SyncAccessor_isKilled)
						{
							if (this.isOwner)
							{
								this.ph2[num7].SetKiller(this._rootObject.transform);
							}
							if (this.bloodSplatter)
							{
								global::UnityEngine.Object.Instantiate<GameObject>(this.bloodSplatter, this.ph2[num7].transform.position, Quaternion.Euler(0f, (float)global::UnityEngine.Random.Range(0, 360), 0f));
							}
							if (this.ph2[num7].SyncAccessor_health - this.damage <= 0f && this.isOwner)
							{
								if (this.ph2[num7].SyncAccessor_playerValues.SyncAccessor_playerClient == this.weapon.playerValues.SyncAccessor_playerClient)
								{
									this.ph2[num7].ChangeKilledState(true);
									this.ph2[num7].suicide = true;
									Settings.Instance.IncreaseSuicidesAmount();
								}
								else if (this.ph2[num7].SyncAccessor_playerValues.SyncAccessor_playerClient != this.weapon.playerValues.SyncAccessor_playerClient)
								{
									this.ph2[num7].ChangeKilledState(true);
									this.ph2[num7].Explode(false, true, this.ph2[num7].gameObject.name, this.ph2[num7].transform.position - base.transform.position, this.ragdollEjectForce, base.transform.position);
									this.KillShockWave();
									this.SendKillLog(this.ph2[num7]);
								}
							}
							if (this.isOwner)
							{
								this.ph2[num7].RemoveHealth(this.damage);
							}
							if (this.isOwner)
							{
								this.HitMarker(this.headshot);
							}
							this.touched2 = true;
							this.touched = true;
						}
					}
				}
			}
			if (!flag3 && !flag4 && this.shouldDestroy && !this.isGlass)
			{
				global::UnityEngine.Object.Destroy(base.gameObject, 3f);
				base.enabled = false;
				this.graph.SetActive(false);
				this.audio.Play();
				GameObject[] array7 = GameObject.FindGameObjectsWithTag("Player");
				for (int num8 = 0; num8 < array7.Length; num8++)
				{
					float num9 = Vector3.Distance(base.transform.position, array7[num8].transform.position);
					array7[num8].GetComponent<PlayerHealth>().LocalScreenshake(this.duration, Mathf.Lerp(this.maxStrength, this.minStrength, Mathf.Clamp(num9 / this.maxDistance, 0f, 1f)), this.vibrato, this.randomness, this.shakeEase);
				}
			}
		}
	}

	// Token: 0x060008DA RID: 2266 RVA: 0x00040B1C File Offset: 0x0003ED1C
	private void HitMarker(bool head)
	{
		this.audio.PlayOneShot(this.hitSfx);
		if (head)
		{
			this.audio.PlayOneShot(Crosshair.Instance.headshotHitClip);
		}
		if (this.marker == null)
		{
			this.marker = global::UnityEngine.Object.Instantiate<GameObject>(this.hitMarker, Crosshair.Instance.transform.position, Quaternion.identity, PauseManager.Instance.transform);
			this.marker.transform.DOPunchScale(head ? new Vector3(2.5f, 2.5f, 2.5f) : Vector3.one, 0.3f, 8, 2f);
			if (head)
			{
				this.marker.GetComponent<Image>().color = Color.red;
			}
			global::UnityEngine.Object.Destroy(this.marker, 0.3f);
			return;
		}
		global::UnityEngine.Object.Destroy(this.marker);
		this.marker = global::UnityEngine.Object.Instantiate<GameObject>(this.hitMarker, Crosshair.Instance.transform.position, Quaternion.identity, PauseManager.Instance.transform);
		this.marker.transform.DOPunchScale(head ? new Vector3(2.5f, 2.5f, 2.5f) : Vector3.one, 0.3f, 8, 2f);
		if (head)
		{
			this.marker.GetComponent<Image>().color = Color.red;
		}
		global::UnityEngine.Object.Destroy(this.marker, 0.3f);
	}

	// Token: 0x060008DB RID: 2267 RVA: 0x00040C94 File Offset: 0x0003EE94
	private void SendKillLog(PlayerHealth enemyHealth)
	{
		this.sendKillLog = true;
		PauseManager.Instance.WriteLog(string.Concat(new string[]
		{
			"<b><color=#",
			PauseManager.Instance.selfNameLogColor,
			">",
			enemyHealth.SyncAccessor_playerValues.SyncAccessor_playerClient.PlayerName,
			"</color></b> was ",
			this.headshot ? "headshot" : "killed",
			" with ",
			this.StartsWithVowel(this.weapon.transform.GetComponent<ItemBehaviour>().weaponName) ? "an" : "a",
			" <b><color=white>",
			this.weapon.transform.GetComponent<ItemBehaviour>().weaponName,
			"</color></b> by <b><color=#",
			PauseManager.Instance.enemyNameLogColor,
			">",
			ClientInstance.Instance.PlayerName,
			"</color></b>"
		}));
	}

	// Token: 0x060008DC RID: 2268 RVA: 0x00040D9C File Offset: 0x0003EF9C
	public void KillShockWave()
	{
		Settings.Instance.IncreaseKillsAmount();
		this._rootObject.GetComponent<FirstPersonController>().lensDistortion.intensity.value = this._rootObject.GetComponent<FirstPersonController>().killShockWaveStrength;
		this._rootObject.GetComponent<FirstPersonController>().colorGrading.saturation.value = -100f;
	}

	// Token: 0x060008DD RID: 2269 RVA: 0x00040DFC File Offset: 0x0003EFFC
	private void Glass()
	{
		Collider[] array = Physics.OverlapSphere(base.transform.position, this.explosionRadius, this.playerLayer);
		for (int i = 0; i < array.Length; i++)
		{
			if (array[i].transform.tag == "ShatterableGlass" && array[i].gameObject.GetComponent<ShatterableGlass>() != null)
			{
				array[i].gameObject.GetComponent<ShatterableGlass>().Shatter3D(array[i].transform.position, array[i].transform.position - base.transform.position);
			}
		}
	}

	// Token: 0x060008DE RID: 2270 RVA: 0x00040EA4 File Offset: 0x0003F0A4
	private void SpawnVFX(int index, Vector3 hitPoint, Vector3 hitNormal, string surface, Transform parent)
	{
		if (Settings.Instance.reduceVFX)
		{
			return;
		}
		if (parent.gameObject.layer == 3)
		{
			return;
		}
		uint num = <PrivateImplementationDetails>.ComputeStringHash(surface);
		if (num <= 1825421690U)
		{
			if (num <= 1378315797U)
			{
				if (num <= 464173256U)
				{
					if (num != 456440475U)
					{
						if (num == 464173256U)
						{
							if (surface == "Footsteps/Concrete/Dalles")
							{
								if (index == 0)
								{
									global::UnityEngine.Object.Instantiate<GameObject>(this.concreteHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
								}
								if (index == 1)
								{
									global::UnityEngine.Object.Instantiate<GameObject>(this.bulletHole, hitPoint, Quaternion.LookRotation(hitNormal), parent);
									return;
								}
								return;
							}
						}
					}
					else if (surface == "Footsteps/Sand")
					{
						if (index == 0)
						{
							global::UnityEngine.Object.Instantiate<GameObject>(this.sandHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
						}
						if (index == 1)
						{
							global::UnityEngine.Object.Instantiate<GameObject>(this.sandHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
							return;
						}
						return;
					}
				}
				else if (num != 913360285U)
				{
					if (num != 977466297U)
					{
						if (num == 1378315797U)
						{
							if (surface == "Footsteps/Moquette")
							{
								if (index == 0)
								{
									global::UnityEngine.Object.Instantiate<GameObject>(this.softbodyHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
								}
								if (index == 1)
								{
									global::UnityEngine.Object.Instantiate<GameObject>(this.softbodyHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
									return;
								}
								return;
							}
						}
					}
					else if (surface == "Footsteps/Concrete/Solide")
					{
						if (index == 0)
						{
							global::UnityEngine.Object.Instantiate<GameObject>(this.concreteHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
						}
						if (index == 1)
						{
							global::UnityEngine.Object.Instantiate<GameObject>(this.bulletHole, hitPoint, Quaternion.LookRotation(hitNormal), parent);
							return;
						}
						return;
					}
				}
				else if (surface == "Grenade")
				{
					return;
				}
			}
			else if (num <= 1430892386U)
			{
				if (num != 1429664136U)
				{
					if (num == 1430892386U)
					{
						if (surface == "Hat")
						{
							return;
						}
					}
				}
				else if (surface == "Footsteps/Water")
				{
					if (index == 0)
					{
						global::UnityEngine.Object.Instantiate<GameObject>(this.waterHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
					}
					if (index == 1)
					{
						global::UnityEngine.Object.Instantiate<GameObject>(this.waterHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
						return;
					}
					return;
				}
			}
			else if (num != 1610969363U)
			{
				if (num != 1624496828U)
				{
					if (num == 1825421690U)
					{
						if (surface == "Footsteps/Wood/Creux")
						{
							if (index == 0)
							{
								global::UnityEngine.Object.Instantiate<GameObject>(this.woodHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
							}
							if (index == 1)
							{
								global::UnityEngine.Object.Instantiate<GameObject>(this.woodHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
								return;
							}
							return;
						}
					}
				}
				else if (surface == "Footsteps/Concrete/Default")
				{
					if (index == 0)
					{
						global::UnityEngine.Object.Instantiate<GameObject>(this.concreteHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
					}
					if (index == 1)
					{
						global::UnityEngine.Object.Instantiate<GameObject>(this.bulletHole, hitPoint, Quaternion.LookRotation(hitNormal), parent);
						return;
					}
					return;
				}
			}
			else if (surface == "Footsteps/Concrete/PleinAir")
			{
				if (index == 0)
				{
					global::UnityEngine.Object.Instantiate<GameObject>(this.concreteHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
				}
				if (index == 1)
				{
					global::UnityEngine.Object.Instantiate<GameObject>(this.bulletHole, hitPoint, Quaternion.LookRotation(hitNormal), parent);
					return;
				}
				return;
			}
		}
		else if (num <= 2833365437U)
		{
			if (num <= 2180110808U)
			{
				if (num != 1954265137U)
				{
					if (num == 2180110808U)
					{
						if (surface == "Footsteps/Dirt")
						{
							if (index == 0)
							{
								global::UnityEngine.Object.Instantiate<GameObject>(this.dirtHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
							}
							if (index == 1)
							{
								global::UnityEngine.Object.Instantiate<GameObject>(this.dirtHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
								return;
							}
							return;
						}
					}
				}
				else if (surface == "NoSound")
				{
					return;
				}
			}
			else if (num != 2251139421U)
			{
				if (num != 2400559108U)
				{
					if (num == 2833365437U)
					{
						if (surface == "Footsteps/Metal/Pipe2")
						{
							if (index == 0)
							{
								global::UnityEngine.Object.Instantiate<GameObject>(this.metalHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
							}
							if (index == 1)
							{
								global::UnityEngine.Object.Instantiate<GameObject>(this.metalHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
								return;
							}
							return;
						}
					}
				}
				else if (surface == "Footsteps/Matelas")
				{
					if (index == 0)
					{
						global::UnityEngine.Object.Instantiate<GameObject>(this.softbodyHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
					}
					if (index == 1)
					{
						global::UnityEngine.Object.Instantiate<GameObject>(this.softbodyHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
						return;
					}
					return;
				}
			}
			else if (surface == "Footsteps/Metal/Pipe")
			{
				if (index == 0)
				{
					global::UnityEngine.Object.Instantiate<GameObject>(this.tauleHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
				}
				if (index == 1)
				{
					global::UnityEngine.Object.Instantiate<GameObject>(this.tauleHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
					return;
				}
				return;
			}
		}
		else if (num <= 3455850406U)
		{
			if (num != 3075599786U)
			{
				if (num == 3455850406U)
				{
					if (surface == "Footsteps/Graviers")
					{
						if (index == 0)
						{
							global::UnityEngine.Object.Instantiate<GameObject>(this.concreteHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
						}
						if (index == 1)
						{
							global::UnityEngine.Object.Instantiate<GameObject>(this.bulletHole, hitPoint, Quaternion.LookRotation(hitNormal), parent);
							return;
						}
						return;
					}
				}
			}
			else if (surface == "Footsteps/Wood/Sec")
			{
				if (index == 0)
				{
					global::UnityEngine.Object.Instantiate<GameObject>(this.woodHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
				}
				if (index == 1)
				{
					global::UnityEngine.Object.Instantiate<GameObject>(this.woodHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
					return;
				}
				return;
			}
		}
		else if (num != 3492154005U)
		{
			if (num != 3807801418U)
			{
				if (num == 3848897750U)
				{
					if (surface == "Mine")
					{
						return;
					}
				}
			}
			else if (surface == "Footsteps/Metal/Grille")
			{
				if (index == 0)
				{
					global::UnityEngine.Object.Instantiate<GameObject>(this.tauleHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
				}
				if (index == 1)
				{
					global::UnityEngine.Object.Instantiate<GameObject>(this.tauleHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
					return;
				}
				return;
			}
		}
		else if (surface == "Footsteps/Grass")
		{
			if (index == 0)
			{
				global::UnityEngine.Object.Instantiate<GameObject>(this.dirtHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
			}
			if (index == 1)
			{
				global::UnityEngine.Object.Instantiate<GameObject>(this.dirtHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
				return;
			}
			return;
		}
		if (index == 0)
		{
			global::UnityEngine.Object.Instantiate<GameObject>(this.concreteHitImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
		}
		if (index == 1)
		{
			global::UnityEngine.Object.Instantiate<GameObject>(this.bulletHole, hitPoint, Quaternion.LookRotation(hitNormal), parent);
		}
	}

	// Token: 0x060008DF RID: 2271 RVA: 0x00041548 File Offset: 0x0003F748
	public bool StartsWithVowel(string name)
	{
		return name.StartsWith("a") || name.StartsWith("e") || name.StartsWith("i") || name.StartsWith("o") || name.StartsWith("u") || name.StartsWith("y");
	}

	// Token: 0x040007D3 RID: 2003
	[HideInInspector]
	public bool isOwner;

	// Token: 0x040007D4 RID: 2004
	private Vector3 _direction;

	// Token: 0x040007D5 RID: 2005
	private float _passedTime;

	// Token: 0x040007D6 RID: 2006
	private float actualPassedTime;

	// Token: 0x040007D7 RID: 2007
	[SerializeField]
	private float MOVE_RATE = 5f;

	// Token: 0x040007D8 RID: 2008
	[SerializeField]
	private float radius = 0.2f;

	// Token: 0x040007D9 RID: 2009
	[SerializeField]
	private float explosionRadius = 1f;

	// Token: 0x040007DA RID: 2010
	[SerializeField]
	private bool explosionBullet = true;

	// Token: 0x040007DB RID: 2011
	[SerializeField]
	private float bumpForce;

	// Token: 0x040007DC RID: 2012
	[SerializeField]
	private bool orientateVfx = true;

	// Token: 0x040007DD RID: 2013
	[SerializeField]
	private float launchOffset;

	// Token: 0x040007DE RID: 2014
	[SerializeField]
	private bool shouldDestroy = true;

	// Token: 0x040007DF RID: 2015
	[SerializeField]
	private float damage = 100f;

	// Token: 0x040007E0 RID: 2016
	[SerializeField]
	private bool skipSafeStartTimer;

	// Token: 0x040007E1 RID: 2017
	[SerializeField]
	private bool destroySelf;

	// Token: 0x040007E2 RID: 2018
	[SerializeField]
	private float ragdollEjectForce = 4f;

	// Token: 0x040007E3 RID: 2019
	[SerializeField]
	private LayerMask playerLayer;

	// Token: 0x040007E4 RID: 2020
	[SerializeField]
	private LayerMask bodyLayer;

	// Token: 0x040007E5 RID: 2021
	[SerializeField]
	private LayerMask headLayer;

	// Token: 0x040007E6 RID: 2022
	[SerializeField]
	private AudioClip hitClip;

	// Token: 0x040007E7 RID: 2023
	[SerializeField]
	private AudioClip launchClip;

	// Token: 0x040007E8 RID: 2024
	[SerializeField]
	private GameObject hitVfx;

	// Token: 0x040007E9 RID: 2025
	[SerializeField]
	private GameObject bloodVfx;

	// Token: 0x040007EA RID: 2026
	[SerializeField]
	private GameObject headBloodVfx;

	// Token: 0x040007EB RID: 2027
	[SerializeField]
	private GameObject bloodSplatter;

	// Token: 0x040007EC RID: 2028
	private GameObject _rootObject;

	// Token: 0x040007ED RID: 2029
	private AudioSource audio;

	// Token: 0x040007EE RID: 2030
	[SerializeField]
	private bool useGravity;

	// Token: 0x040007EF RID: 2031
	[SerializeField]
	private float gravityStart;

	// Token: 0x040007F0 RID: 2032
	[SerializeField]
	private float gravity;

	// Token: 0x040007F1 RID: 2033
	private float _gravity;

	// Token: 0x040007F2 RID: 2034
	[SerializeField]
	private bool usePhysics;

	// Token: 0x040007F3 RID: 2035
	[SerializeField]
	private float friction;

	// Token: 0x040007F4 RID: 2036
	private float _force;

	// Token: 0x040007F5 RID: 2037
	[Header("Screenshake values")]
	[SerializeField]
	private float duration;

	// Token: 0x040007F6 RID: 2038
	[SerializeField]
	private float minStrength;

	// Token: 0x040007F7 RID: 2039
	[SerializeField]
	private float maxStrength;

	// Token: 0x040007F8 RID: 2040
	[SerializeField]
	private int vibrato;

	// Token: 0x040007F9 RID: 2041
	[SerializeField]
	private float randomness;

	// Token: 0x040007FA RID: 2042
	[SerializeField]
	private Ease shakeEase;

	// Token: 0x040007FB RID: 2043
	[SerializeField]
	private float maxDistance;

	// Token: 0x040007FC RID: 2044
	private GameObject _gun;

	// Token: 0x040007FD RID: 2045
	[SerializeField]
	private GameObject graph;

	// Token: 0x040007FE RID: 2046
	private PlayerHealth[] ph2;

	// Token: 0x040007FF RID: 2047
	private PlayerHealth[] ph3;

	// Token: 0x04000800 RID: 2048
	private bool backupRaycast;

	// Token: 0x04000801 RID: 2049
	[HideInInspector]
	public Weapon weapon;

	// Token: 0x04000802 RID: 2050
	[Space]
	[SerializeField]
	private GameObject explosionDecal;

	// Token: 0x04000803 RID: 2051
	[SerializeField]
	private bool SurfacesImpact;

	// Token: 0x04000804 RID: 2052
	[SerializeField]
	private GameObject concreteHitImpact;

	// Token: 0x04000805 RID: 2053
	[SerializeField]
	private GameObject sandHitImpact;

	// Token: 0x04000806 RID: 2054
	[SerializeField]
	private GameObject dirtHitImpact;

	// Token: 0x04000807 RID: 2055
	[SerializeField]
	private GameObject metalHitImpact;

	// Token: 0x04000808 RID: 2056
	[SerializeField]
	private GameObject tauleHitImpact;

	// Token: 0x04000809 RID: 2057
	[SerializeField]
	private GameObject waterHitImpact;

	// Token: 0x0400080A RID: 2058
	[SerializeField]
	private GameObject woodHitImpact;

	// Token: 0x0400080B RID: 2059
	[SerializeField]
	private GameObject softbodyHitImpact;

	// Token: 0x0400080C RID: 2060
	[SerializeField]
	private bool SurfacesVFX;

	// Token: 0x0400080D RID: 2061
	[SerializeField]
	private GameObject sandHitFx;

	// Token: 0x0400080E RID: 2062
	[SerializeField]
	private GameObject dirtHitFx;

	// Token: 0x0400080F RID: 2063
	[SerializeField]
	private GameObject metalHitFx;

	// Token: 0x04000810 RID: 2064
	[SerializeField]
	private GameObject tauleHitFx;

	// Token: 0x04000811 RID: 2065
	[SerializeField]
	private GameObject waterHitFx;

	// Token: 0x04000812 RID: 2066
	[SerializeField]
	private GameObject woodHitFx;

	// Token: 0x04000813 RID: 2067
	[SerializeField]
	private GameObject softbodyHitFx;

	// Token: 0x04000814 RID: 2068
	[SerializeField]
	private GameObject bulletHole;

	// Token: 0x04000815 RID: 2069
	private Vector3 currentPosition;

	// Token: 0x04000816 RID: 2070
	private Vector3 lastPosition;

	// Token: 0x04000817 RID: 2071
	private Vector3 velocity;

	// Token: 0x04000818 RID: 2072
	private bool headshot;

	// Token: 0x04000819 RID: 2073
	private bool isGlass;

	// Token: 0x0400081A RID: 2074
	private float distanceTraveled;

	// Token: 0x0400081B RID: 2075
	[SerializeField]
	private float backupRayLength = 1.5f;

	// Token: 0x0400081C RID: 2076
	private bool touched;

	// Token: 0x0400081D RID: 2077
	private bool touched2;

	// Token: 0x0400081E RID: 2078
	[SerializeField]
	private bool prophet;

	// Token: 0x0400081F RID: 2079
	private bool prophetHit;

	// Token: 0x04000820 RID: 2080
	[SerializeField]
	private GameObject hitMarker;

	// Token: 0x04000821 RID: 2081
	[SerializeField]
	private AudioClip hitSfx;

	// Token: 0x04000822 RID: 2082
	private GameObject marker;

	// Token: 0x04000823 RID: 2083
	private bool sendKillLog;
}

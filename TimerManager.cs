﻿using System;
using System.Collections;
using System.Collections.Generic;
using HeathenEngineering.DEMO;
using Newtonsoft.Json.Linq;
using TMPro;
using UnityEngine;
using UnityEngine.SceneManagement;

// Token: 0x02000007 RID: 7
public class TimerManager : MonoBehaviour, ISaveable
{
	// Token: 0x06000030 RID: 48 RVA: 0x00003160 File Offset: 0x00001360
	private void Start()
	{
		this.timeUI = base.transform.GetChild(0).GetComponent<TextMeshProUGUI>();
		this.timeUIPrevAndPB = base.transform.GetChild(1).GetComponent<TextMeshProUGUI>();
		this.courseName = base.transform.GetChild(2).GetComponent<TextMeshProUGUI>();
		this.courseName.text = "";
		this.timeUI.text = TimerManager.FormatTime(this.time);
		this.beep = base.transform.GetChild(3).GetComponent<AudioSource>();
		this.checkpointSound = base.transform.GetChild(4).GetComponent<AudioSource>();
		this.pbSound = base.transform.GetChild(5).GetComponent<AudioSource>();
		this.canvas = base.GetComponent<Canvas>();
		this.canvas.enabled = false;
		this.triggers = global::UnityEngine.Object.FindObjectsOfType<TimerTrigger>();
		base.transform.localScale = Vector3.one;
		SaveLoadSystem.Instance.Load();
		this.CheckEnabled();
	}

	// Token: 0x06000031 RID: 49 RVA: 0x00003260 File Offset: 0x00001460
	public void Enable(bool f)
	{
		this.enabled = f;
		this.CheckEnabled();
	}

	// Token: 0x06000032 RID: 50 RVA: 0x00003270 File Offset: 0x00001470
	private void CheckEnabled()
	{
		if (this.enabled)
		{
			using (IEnumerator enumerator = this.triggers.GetEnumerator())
			{
				while (enumerator.MoveNext())
				{
					object obj = enumerator.Current;
					((TimerTrigger)obj).transform.gameObject.SetActive(true);
				}
				return;
			}
		}
		foreach (object obj2 in this.triggers)
		{
			((TimerTrigger)obj2).transform.gameObject.SetActive(false);
		}
	}

	// Token: 0x06000033 RID: 51 RVA: 0x0000332C File Offset: 0x0000152C
	private void Update()
	{
		if (this.currentStartPoint != null && Input.GetKey(KeyCode.LeftControl) && Input.GetKeyDown(KeyCode.R))
		{
			this.player.Teleport(this.currentStartPoint.position, this.currentStartPoint.eulerAngles.y, false, this.currentStartPoint, 0f, 0f, false);
		}
		if (!this.timerStarted)
		{
			return;
		}
		this.timeUI.text = TimerManager.FormatTime(this.time);
		this.time += Time.deltaTime;
	}

	// Token: 0x06000034 RID: 52 RVA: 0x000033C8 File Offset: 0x000015C8
	public void TStart(string course)
	{
		base.CancelInvoke("HideCanvas");
		this.canvas.enabled = true;
		foreach (object obj in this.triggers)
		{
			TimerTrigger timerTrigger = (TimerTrigger)obj;
			if (timerTrigger.transform.parent.gameObject.name == course)
			{
				timerTrigger.ResetMat();
			}
			else
			{
				timerTrigger.HideMat();
			}
		}
		if (course != this.previousCourse)
		{
			this.previousTime = -1f;
			this.previousCourse = course;
		}
		this.prevCheckpoint = 0;
		this.canFinish = false;
		this.mapAndCourse = SceneManager.GetActiveScene().name + "_" + course;
		base.StartCoroutine(this.BeepTimer(1));
		this.courseName.text = "\"" + course + "\"";
		this.time = 0f;
		this.timerStarted = true;
		this.UpdatePrevAndPB();
	}

	// Token: 0x06000035 RID: 53 RVA: 0x000034EC File Offset: 0x000016EC
	public void TCheckpoint(object[] info)
	{
		if (!this.timerStarted)
		{
			return;
		}
		string text = (string)info[0];
		int num = (int)info[1];
		int num2 = (int)info[2];
		if (num != this.prevCheckpoint + 1)
		{
			return;
		}
		foreach (object obj in this.triggers)
		{
			((TimerTrigger)obj).SetMatActive(text, num + 1);
		}
		this.prevCheckpoint = num;
		this.canFinish = num == num2;
		this.checkpointSound.Play();
		Debug.Log("Checkpoint" + num.ToString() + "/" + num2.ToString());
	}

	// Token: 0x06000036 RID: 54 RVA: 0x000035B8 File Offset: 0x000017B8
	public void TEnd()
	{
		if (!this.canFinish)
		{
			return;
		}
		if (!this.timerStarted)
		{
			return;
		}
		this.timerStarted = false;
		this.LogTime();
		bool flag = this.previousTime > 0f && this.time < this.previousTime;
		if (flag)
		{
			this.pbSound.Play();
		}
		this.previousTime = this.time;
		this.UpdatePrevAndPB();
		SaveLoadSystem.Instance.Save();
		foreach (object obj in this.triggers)
		{
			((TimerTrigger)obj).HideMat();
		}
		base.Invoke("HideCanvas", 5f);
		base.StartCoroutine(this.BlinkTimer());
		base.StartCoroutine(this.BeepTimer(4));
		string text;
		if (flag)
		{
			text = " set a PB on " + this.courseName.text + " with a time of " + TimerManager.FormatTime(this.time);
		}
		else
		{
			text = " completed " + this.courseName.text + " with a time of " + TimerManager.FormatTime(this.time);
		}
		this.AnnounceFinish(text);
	}

	// Token: 0x06000037 RID: 55 RVA: 0x00003700 File Offset: 0x00001900
	private void AnnounceFinish(string a)
	{
		GameObject.Find("LobbyController").GetComponent<LobbyChatUILogic>().SendChatMessage(a);
	}

	// Token: 0x06000038 RID: 56 RVA: 0x00003718 File Offset: 0x00001918
	private void UpdatePrevAndPB()
	{
		float num = -1f;
		if (this.courseTimes.ContainsKey(this.mapAndCourse))
		{
			num = this.courseTimes[this.mapAndCourse];
		}
		if (num < 0f)
		{
			this.timeUIPrevAndPB.text = "\nPersonal Best: -";
		}
		else
		{
			this.timeUIPrevAndPB.text = "\nPersonal Best: " + TimerManager.FormatTime(num);
		}
		if (this.previousTime < 0f)
		{
			TextMeshProUGUI textMeshProUGUI = this.timeUIPrevAndPB;
			textMeshProUGUI.text += "\nPrevious Time: -";
			return;
		}
		TextMeshProUGUI textMeshProUGUI2 = this.timeUIPrevAndPB;
		textMeshProUGUI2.text = textMeshProUGUI2.text + "\nPrevious Time: " + TimerManager.FormatTime(this.previousTime);
	}

	// Token: 0x06000039 RID: 57 RVA: 0x000037D4 File Offset: 0x000019D4
	public void LogTime()
	{
		if (!this.courseTimes.ContainsKey(this.mapAndCourse))
		{
			this.courseTimes.Add(this.mapAndCourse, 0f);
		}
		if (this.time < this.courseTimes[this.mapAndCourse] || Mathf.Approximately(this.courseTimes[this.mapAndCourse], 0f))
		{
			this.courseTimes[this.mapAndCourse] = this.time;
		}
		Debug.Log("Course Times:");
		foreach (KeyValuePair<string, float> keyValuePair in this.courseTimes)
		{
			Debug.Log(string.Format("Key: {0}, Value: {1}", keyValuePair.Key, keyValuePair.Value));
		}
	}

	// Token: 0x0600003A RID: 58 RVA: 0x000038C4 File Offset: 0x00001AC4
	public void TShowUI(string course)
	{
		this.mapAndCourse = SceneManager.GetActiveScene().name + "_" + course;
		base.CancelInvoke("HideCanvas");
		this.canvas.enabled = true;
		if (course != this.previousCourse)
		{
			this.previousTime = -1f;
			this.previousCourse = course;
		}
		foreach (object obj in this.triggers)
		{
			TimerTrigger timerTrigger = (TimerTrigger)obj;
			if (timerTrigger.transform.parent.gameObject.name == course)
			{
				timerTrigger.ResetMat();
			}
			else
			{
				timerTrigger.HideMat();
			}
		}
		this.courseName.text = "\"" + course + "\"";
		this.timeUI.text = TimerManager.FormatTime(0f);
		this.UpdatePrevAndPB();
	}

	// Token: 0x0600003B RID: 59 RVA: 0x000039CC File Offset: 0x00001BCC
	public void THideUI(string course)
	{
		if (this.timerStarted)
		{
			return;
		}
		foreach (object obj in this.triggers)
		{
			((TimerTrigger)obj).HideMat();
		}
		base.CancelInvoke("HideCanvas");
		this.canvas.enabled = false;
	}

	// Token: 0x0600003C RID: 60 RVA: 0x00003A44 File Offset: 0x00001C44
	private IEnumerator BlinkTimer()
	{
		int num;
		for (int i = 0; i < 5; i = num + 1)
		{
			this.timeUI.gameObject.SetActive(false);
			yield return new WaitForSeconds(0.2f);
			this.timeUI.gameObject.SetActive(true);
			yield return new WaitForSeconds(0.35f);
			num = i;
		}
		yield break;
	}

	// Token: 0x0600003D RID: 61 RVA: 0x00003A53 File Offset: 0x00001C53
	private IEnumerator BeepTimer(int nb)
	{
		int num;
		for (int i = 0; i < nb; i = num + 1)
		{
			this.beep.Play();
			yield return new WaitForSeconds(0.1f);
			num = i;
		}
		yield break;
	}

	// Token: 0x0600003E RID: 62 RVA: 0x00003A69 File Offset: 0x00001C69
	private void HideCanvas()
	{
		this.canvas.enabled = false;
	}

	// Token: 0x0600003F RID: 63 RVA: 0x00003A78 File Offset: 0x00001C78
	public static string FormatTime(float totalSeconds)
	{
		int num = Mathf.FloorToInt(totalSeconds / 60f);
		int num2 = Mathf.FloorToInt(totalSeconds % 60f);
		int num3 = Mathf.FloorToInt(totalSeconds * 1000f % 1000f);
		return string.Format("{0:00}:{1:00}:{2:000}", num, num2, num3);
	}

	// Token: 0x06000040 RID: 64 RVA: 0x00003AD0 File Offset: 0x00001CD0
	public object SaveState()
	{
		return new TimerManager.SaveData
		{
			courseTimes = this.courseTimes
		};
	}

	// Token: 0x06000041 RID: 65 RVA: 0x00003AF8 File Offset: 0x00001CF8
	public void LoadState(JObject state)
	{
		TimerManager.SaveData saveData = state.ToObject<TimerManager.SaveData>();
		this.courseTimes = saveData.courseTimes;
	}

	// Token: 0x04000046 RID: 70
	public new bool enabled = true;

	// Token: 0x04000047 RID: 71
	public Material invisibleMat;

	// Token: 0x04000048 RID: 72
	private TextMeshProUGUI timeUI;

	// Token: 0x04000049 RID: 73
	private TextMeshProUGUI timeUIPrevAndPB;

	// Token: 0x0400004A RID: 74
	private TextMeshProUGUI courseName;

	// Token: 0x0400004B RID: 75
	private AudioSource beep;

	// Token: 0x0400004C RID: 76
	private AudioSource checkpointSound;

	// Token: 0x0400004D RID: 77
	private AudioSource pbSound;

	// Token: 0x0400004E RID: 78
	private Canvas canvas;

	// Token: 0x0400004F RID: 79
	private Array triggers;

	// Token: 0x04000050 RID: 80
	[HideInInspector]
	public FirstPersonController player;

	// Token: 0x04000051 RID: 81
	[HideInInspector]
	public Transform currentStartPoint;

	// Token: 0x04000052 RID: 82
	private float time;

	// Token: 0x04000053 RID: 83
	private float previousTime = -1f;

	// Token: 0x04000054 RID: 84
	private string previousCourse = "";

	// Token: 0x04000055 RID: 85
	private string mapAndCourse = "";

	// Token: 0x04000056 RID: 86
	public bool timerStarted;

	// Token: 0x04000057 RID: 87
	private Dictionary<string, float> courseTimes = new Dictionary<string, float>();

	// Token: 0x04000058 RID: 88
	private bool canFinish;

	// Token: 0x04000059 RID: 89
	private int prevCheckpoint;

	// Token: 0x02000008 RID: 8
	[Serializable]
	private struct SaveData
	{
		// Token: 0x0400005A RID: 90
		public Dictionary<string, float> courseTimes;
	}
}

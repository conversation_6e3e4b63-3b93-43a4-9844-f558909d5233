﻿using System;
using TMPro;
using UnityEngine;

// Token: 0x02000120 RID: 288
public class AlphaYoyo : MonoBehaviour
{
	// Token: 0x06000E1A RID: 3610 RVA: 0x0005E5D5 File Offset: 0x0005C7D5
	private void Start()
	{
		this.text = base.GetComponent<TextMeshProUGUI>();
		this.initialColor = this.text.color;
	}

	// Token: 0x06000E1B RID: 3611 RVA: 0x0005E5F4 File Offset: 0x0005C7F4
	private void Update()
	{
		if (!this.enabled)
		{
			return;
		}
		this.timer += Time.deltaTime * this.speed;
		this.alpha = Mathf.Lerp(this.floorLimit, this.ceilLimit, Mathf.Abs(Mathf.Cos(this.timer)));
		this.text.color = new Color(this.text.color.r, this.text.color.g, this.text.color.b, this.alpha);
	}

	// Token: 0x06000E1C RID: 3612 RVA: 0x0005E690 File Offset: 0x0005C890
	public void InvertState()
	{
		this.enabled = !this.enabled;
		if (!this.enabled)
		{
			this.text.color = this.initialColor;
		}
	}

	// Token: 0x06000E1D RID: 3613 RVA: 0x0005E6BA File Offset: 0x0005C8BA
	public void SetState(bool state)
	{
		this.enabled = state;
		if (!this.enabled)
		{
			this.text.color = this.initialColor;
		}
	}

	// Token: 0x04000C8A RID: 3210
	[SerializeField]
	private float speed = 1f;

	// Token: 0x04000C8B RID: 3211
	[SerializeField]
	private float floorLimit = 0.5f;

	// Token: 0x04000C8C RID: 3212
	[SerializeField]
	private float ceilLimit = 1f;

	// Token: 0x04000C8D RID: 3213
	private TextMeshProUGUI text;

	// Token: 0x04000C8E RID: 3214
	private float alpha;

	// Token: 0x04000C8F RID: 3215
	private float timer;

	// Token: 0x04000C90 RID: 3216
	private new bool enabled = true;

	// Token: 0x04000C91 RID: 3217
	private Color initialColor;
}

﻿using System;
using UnityEngine;

// Token: 0x02000172 RID: 370
public class GunView : MonoBehaviour
{
	// Token: 0x06000F78 RID: 3960 RVA: 0x00065973 File Offset: 0x00063B73
	private void OnEnable()
	{
		base.transform.localScale = this.baseScale;
		if (SystemInfo.deviceType == DeviceType.Handheld)
		{
			this.isPhone = true;
		}
		this.isFirst = true;
	}

	// Token: 0x06000F79 RID: 3961 RVA: 0x0006599C File Offset: 0x00063B9C
	private void Update()
	{
		if (this.isPhone)
		{
			if (Input.touchCount > 0)
			{
				if (this.isFirst)
				{
					this.isFirst = false;
					this.startPos = Input.GetTouch(0).position;
					return;
				}
				this.zRot += (Input.GetTouch(0).position.y - this.startPos.y) * 0.09f;
				this.zRot = Mathf.Clamp(this.zRot, -55f, 55f);
				this.yRot += (Input.GetTouch(0).position.x - this.startPos.x) * 0.25f;
				this.startPos = Input.GetTouch(0).position;
				this.ScaleUp();
			}
			else
			{
				this.isFirst = true;
				this.zRot = Mathf.Lerp(this.zRot, 0f, 0.08f);
				this.yRot = Mathf.Lerp(this.yRot, 0f, 0.08f);
				this.ScaleDown();
			}
			base.transform.eulerAngles = new Vector3(0f, 90f + this.yRot, this.zRot);
			return;
		}
		if (Input.GetMouseButton(0))
		{
			if (this.isFirst)
			{
				this.isFirst = false;
				this.startPos = Input.mousePosition;
				return;
			}
			this.zRot += (Input.mousePosition.y - this.startPos.y) * 0.1f;
			this.zRot = Mathf.Clamp(this.zRot, -55f, 55f);
			this.yRot += (Input.mousePosition.x - this.startPos.x) * 0.3f;
			this.startPos = Input.mousePosition;
			this.ScaleUp();
		}
		else
		{
			this.isFirst = true;
			this.zRot = Mathf.Lerp(this.zRot, 0f, 0.2f);
			this.yRot = Mathf.Lerp(this.yRot, 0f, 0.2f);
			this.ScaleDown();
		}
		base.transform.eulerAngles = new Vector3(0f, 90f + this.yRot, this.zRot);
	}

	// Token: 0x06000F7A RID: 3962 RVA: 0x00065C03 File Offset: 0x00063E03
	private void ScaleUp()
	{
		base.transform.localScale = Vector3.Lerp(base.transform.localScale, Vector3.one, 0.075f);
	}

	// Token: 0x06000F7B RID: 3963 RVA: 0x00065C2A File Offset: 0x00063E2A
	private void ScaleDown()
	{
		base.transform.localScale = Vector3.Lerp(base.transform.localScale, this.baseScale, 0.075f);
	}

	// Token: 0x04000E40 RID: 3648
	private Vector3 baseScale = new Vector3(0.7f, 0.7f, 0.7f);

	// Token: 0x04000E41 RID: 3649
	private bool isPhone;

	// Token: 0x04000E42 RID: 3650
	private bool isFirst = true;

	// Token: 0x04000E43 RID: 3651
	private float zRot;

	// Token: 0x04000E44 RID: 3652
	private float yRot;

	// Token: 0x04000E45 RID: 3653
	private Vector3 startPos;
}

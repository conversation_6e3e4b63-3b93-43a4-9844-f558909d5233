﻿using System;
using FishNet.Component.Transforming;
using FishNet.Object;
using UnityEngine;

// Token: 0x02000061 RID: 97
public class MovingPlatformParent : NetworkBehaviour
{
	// Token: 0x06000420 RID: 1056 RVA: 0x0001D346 File Offset: 0x0001B546
	private void Start()
	{
		if (base.GetComponent<NetworkTransform>() != null)
		{
			base.GetComponent<NetworkTransform>().enabled = false;
		}
	}

	// Token: 0x06000421 RID: 1057 RVA: 0x0001D362 File Offset: 0x0001B562
	private void Update()
	{
		this.movingVector = base.transform.position - this.previousPosition;
		this.previousPosition = base.transform.position;
	}

	// Token: 0x06000423 RID: 1059 RVA: 0x0001D3A0 File Offset: 0x0001B5A0
	public virtual void NetworkInitialize___Early()
	{
		if (this.NetworkInitializeEarly_MovingPlatformParent_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeEarly_MovingPlatformParent_Assembly-CSharp.dll = true;
	}

	// Token: 0x06000424 RID: 1060 RVA: 0x0001D3B3 File Offset: 0x0001B5B3
	public virtual void NetworkInitialize__Late()
	{
		if (this.NetworkInitializeLate_MovingPlatformParent_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeLate_MovingPlatformParent_Assembly-CSharp.dll = true;
	}

	// Token: 0x06000425 RID: 1061 RVA: 0x0001D3C6 File Offset: 0x0001B5C6
	public override void NetworkInitializeIfDisabled()
	{
		this.NetworkInitialize___Early();
		this.NetworkInitialize__Late();
	}

	// Token: 0x06000426 RID: 1062 RVA: 0x0001D3C6 File Offset: 0x0001B5C6
	public virtual void Awake()
	{
		this.NetworkInitialize___Early();
		this.NetworkInitialize__Late();
	}

	// Token: 0x06000427 RID: 1063 RVA: 0x000023D6 File Offset: 0x000005D6
	public virtual void Awake___UserLogic()
	{
	}

	// Token: 0x0400046F RID: 1135
	public bool doesEject = true;

	// Token: 0x04000470 RID: 1136
	public Vector3 movingVector;

	// Token: 0x04000471 RID: 1137
	private Vector3 previousPosition;

	// Token: 0x04000472 RID: 1138
	private bool NetworkInitializeEarly_MovingPlatformParent_Assembly-CSharp.dll;

	// Token: 0x04000473 RID: 1139
	private bool NetworkInitializeLate_MovingPlatformParent_Assembly-CSharp.dll;
}

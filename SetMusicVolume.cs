﻿using System;
using UnityEngine;

// Token: 0x0200010B RID: 267
public class SetMusicVolume : MonoBehaviour
{
	// Token: 0x06000D3D RID: 3389 RVA: 0x00059EC1 File Offset: 0x000580C1
	private void Awake()
	{
		this.source = SoundManager.Instance._musicSource;
		this.audio = base.GetComponent<AudioSource>();
	}

	// Token: 0x06000D3E RID: 3390 RVA: 0x00059EDF File Offset: 0x000580DF
	private void Update()
	{
		if (this.audio.volume != this.source.volume)
		{
			this.audio.volume = this.source.volume;
		}
	}

	// Token: 0x04000B79 RID: 2937
	private AudioSource source;

	// Token: 0x04000B7A RID: 2938
	private AudioSource audio;
}

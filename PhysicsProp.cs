﻿using System;
using FishNet;
using FishNet.Connection;
using FishNet.Managing;
using FishNet.Object;
using FishNet.Object.Delegating;
using FishNet.Object.Synchronizing;
using FishNet.Serializing;
using FishNet.Serializing.Generated;
using FishNet.Transporting;
using UnityEngine;

// Token: 0x02000063 RID: 99
public class PhysicsProp : InteractEnvironment
{
	// Token: 0x0600042C RID: 1068 RVA: 0x0001D44A File Offset: 0x0001B64A
	private void Start()
	{
		this.audio = base.GetComponent<AudioSource>();
		this.physics = base.GetComponent<CharacterController>();
		this.col = base.GetComponent<BoxCollider>();
	}

	// Token: 0x0600042D RID: 1069 RVA: 0x0001D470 File Offset: 0x0001B670
	public override void OnFocus()
	{
		PauseManager.Instance.interactPopup.gameObject.SetActive(true);
		PauseManager.Instance.interactPopup.text = string.Concat(new string[]
		{
			this.SyncAccessor_grabbed ? "drop" : "grab",
			" ",
			this.popupText.ToLower(),
			" [",
			PauseManager.Instance.InteractPromptLetter.ToLower(),
			"]"
		});
	}

	// Token: 0x0600042E RID: 1070 RVA: 0x0001D4FC File Offset: 0x0001B6FC
	public override void OnInteract(Transform player)
	{
		if (this.SyncAccessor_grabbed && this.SyncAccessor_localPlayer != player)
		{
			return;
		}
		this.pickup = player.GetComponent<PlayerPickup>();
		if (!this.pickup.heldEnvironmentInteractable)
		{
			this.pickup.heldEnvironmentInteractable = this;
		}
		else if (this.pickup.heldEnvironmentInteractable != this)
		{
			return;
		}
		this.shouldPlaySfx2 = true;
		this.localLocalPlayer = player;
		if (this.SyncAccessor_grabbed && this.SyncAccessor_localPlayer == player)
		{
			this.AddForce(this.cam.forward, this.launchForce);
		}
		this.throwTimer = 0.3f;
		this.CmdInteract(player);
		this.cam = player.GetComponentInChildren<Camera>().transform;
	}

	// Token: 0x0600042F RID: 1071 RVA: 0x000023D6 File Offset: 0x000005D6
	public override void OnLoseFocus()
	{
	}

	// Token: 0x06000430 RID: 1072 RVA: 0x0001D5BE File Offset: 0x0001B7BE
	[ServerRpc(RunLocally = true, RequireOwnership = false)]
	private void CmdInteract(Transform player)
	{
		this.RpcWriter___Server_CmdInteract_3068987916(player);
		this.RpcLogic___CmdInteract_3068987916(player);
	}

	// Token: 0x06000431 RID: 1073 RVA: 0x0001D5D4 File Offset: 0x0001B7D4
	[ServerRpc(RunLocally = true, RequireOwnership = false)]
	private void SetGrabbed(bool newGrabbed)
	{
		this.RpcWriter___Server_SetGrabbed_1140765316(newGrabbed);
		this.RpcLogic___SetGrabbed_1140765316(newGrabbed);
	}

	// Token: 0x06000432 RID: 1074 RVA: 0x0001D5EA File Offset: 0x0001B7EA
	[ServerRpc(RunLocally = true, RequireOwnership = false)]
	private void SetPlayer(Transform player)
	{
		this.RpcWriter___Server_SetPlayer_3068987916(player);
		this.RpcLogic___SetPlayer_3068987916(player);
	}

	// Token: 0x06000433 RID: 1075 RVA: 0x0001D600 File Offset: 0x0001B800
	[ServerRpc(RunLocally = true)]
	private void PlaySfx()
	{
		this.RpcWriter___Server_PlaySfx_2166136261();
		this.RpcLogic___PlaySfx_2166136261();
	}

	// Token: 0x06000434 RID: 1076 RVA: 0x0001D60E File Offset: 0x0001B80E
	[ObserversRpc(RunLocally = true, ExcludeOwner = true)]
	private void PlaySfxObservers()
	{
		this.RpcWriter___Observers_PlaySfxObservers_2166136261();
		this.RpcLogic___PlaySfxObservers_2166136261();
	}

	// Token: 0x06000435 RID: 1077 RVA: 0x0001D61C File Offset: 0x0001B81C
	private void Update()
	{
		this.throwTimer -= Time.deltaTime;
		this.canThrow = this.throwTimer < 0f;
		if (this.col.isTrigger != this.SyncAccessor_grabbed)
		{
			this.col.isTrigger = this.SyncAccessor_grabbed;
		}
		if (this.physics.enabled == this.SyncAccessor_grabbed)
		{
			this.physics.enabled = !this.SyncAccessor_grabbed;
		}
		if (!base.IsOwner)
		{
			return;
		}
		if (this.SyncAccessor_grabbed)
		{
			if (this.SyncAccessor_localPlayer)
			{
				PlayerHealth component = this.SyncAccessor_localPlayer.GetComponent<PlayerHealth>();
				if (component && component.SyncAccessor_isKilled)
				{
					this.SetGrabbed(false);
					this.SetPlayer(null);
				}
			}
			else
			{
				this.SetGrabbed(false);
			}
			RaycastHit raycastHit;
			if (Physics.Raycast(this.cam.position, this.cam.forward, out raycastHit, this.distanceFromPlayer, this.defaultMask))
			{
				if (this.shouldPlaySfx)
				{
					this.shouldPlaySfx = false;
					this.PlaySfx();
				}
				base.transform.position = raycastHit.point - this.cam.forward * (this.col.size.z / 2f);
			}
			else
			{
				base.transform.position = this.cam.position + this.cam.forward * this.distanceFromPlayer;
				this.shouldPlaySfx = true;
			}
			this.moveDirection = Vector3.zero;
			this.customForceFinal = Vector3.zero;
			this.canHit = true;
			if (this.canThrow && (Input.GetMouseButton(0) || InputManager.inputActions.Player.Interact.WasPerformedThisFrame()))
			{
				this.OnInteract(this.SyncAccessor_localPlayer);
				this.throwTimer = 0.3f;
				return;
			}
		}
		else
		{
			if (!this.physics.isGrounded)
			{
				this.moveDirection.y = this.moveDirection.y - this.gravity * Time.deltaTime;
			}
			this.physics.Move((this.moveDirection + this.customForceFinal) * Time.deltaTime);
			if (this.impact.magnitude > 0.2f)
			{
				this.customForceFinal = this.impact;
			}
			else
			{
				this.customForceFinal = Vector3.zero;
			}
			if (this.physics.isGrounded)
			{
				this.impact.y = 0f;
			}
			this.impact = Vector3.Lerp(this.impact, Vector3.zero, ((!this.physics.isGrounded) ? this.airdeceleration : this.deceleration) * Time.deltaTime);
			if (this.pickup && this.pickup.heldEnvironmentInteractable == this)
			{
				this.pickup.heldEnvironmentInteractable = null;
				this.pickup = null;
			}
		}
	}

	// Token: 0x06000436 RID: 1078 RVA: 0x0001D910 File Offset: 0x0001BB10
	private void OnControllerColliderHit(ControllerColliderHit collision)
	{
		this.customForceFinal = Vector3.zero;
		if (collision.transform.GetComponentInParent<PlayerHealth>() && this.canHit && this.impact.magnitude > 2.5f)
		{
			this.canHit = false;
			global::UnityEngine.Object.Instantiate<GameObject>(this.bloodImpact, collision.point, Quaternion.LookRotation(collision.normal));
			if (collision.transform.GetComponentInParent<PlayerHealth>().SyncAccessor_health - this.damage <= 0f)
			{
				Settings.Instance.propKills += 1f;
				this.KillShockWave();
				this.SendKillLog(collision.transform.GetComponentInParent<PlayerHealth>());
			}
			collision.transform.GetComponentInParent<PlayerHealth>().RemoveHealth(this.damage);
			this.BumpPlayerServer(this.impact, 120f, collision.transform.GetComponentInParent<PlayerHealth>());
		}
		if (this.shouldPlaySfx2)
		{
			this.shouldPlaySfx2 = false;
			this.PlaySfx();
		}
	}

	// Token: 0x06000437 RID: 1079 RVA: 0x0001DA14 File Offset: 0x0001BC14
	private void SendKillLog(PlayerHealth enemyHealth)
	{
		if (this.sendKillLog)
		{
			return;
		}
		this.sendKillLog = true;
		PauseManager.Instance.WriteLog(string.Concat(new string[]
		{
			"<b><color=#",
			PauseManager.Instance.selfNameLogColor,
			">",
			enemyHealth.SyncAccessor_playerValues.SyncAccessor_playerClient.PlayerName,
			"</color></b> was killed with a <b><color=white>",
			this.popupText.ToLower(),
			"</color></b> by <b><color=#",
			PauseManager.Instance.enemyNameLogColor,
			">",
			ClientInstance.Instance.PlayerName,
			"</color></b>"
		}));
	}

	// Token: 0x06000438 RID: 1080 RVA: 0x0001DAC4 File Offset: 0x0001BCC4
	public void KillShockWave()
	{
		if (!this.increaseKillAmount)
		{
			Settings.Instance.IncreaseKillsAmount();
			this.increaseKillAmount = true;
		}
		this.localLocalPlayer.GetComponent<FirstPersonController>().lensDistortion.intensity.value = this.localLocalPlayer.GetComponent<FirstPersonController>().killShockWaveStrength;
		this.localLocalPlayer.GetComponent<FirstPersonController>().colorGrading.saturation.value = -100f;
	}

	// Token: 0x06000439 RID: 1081 RVA: 0x0001DB33 File Offset: 0x0001BD33
	[ServerRpc]
	private void BumpPlayerServer(Vector3 direction, float force, PlayerHealth ph)
	{
		this.RpcWriter___Server_BumpPlayerServer_1076951378(direction, force, ph);
	}

	// Token: 0x0600043A RID: 1082 RVA: 0x0001DB47 File Offset: 0x0001BD47
	[TargetRpc]
	private void BumpPlayer(NetworkConnection conn, PlayerHealth enemyHealth, float force, Vector3 direction)
	{
		this.RpcWriter___Target_BumpPlayer_2429708885(conn, enemyHealth, force, direction);
	}

	// Token: 0x0600043B RID: 1083 RVA: 0x0001DB5F File Offset: 0x0001BD5F
	public void AddForce(Vector3 dir, float force)
	{
		dir.Normalize();
		this.impact += dir.normalized * force / this.mass;
	}

	// Token: 0x0600043D RID: 1085 RVA: 0x0001DC00 File Offset: 0x0001BE00
	public override void NetworkInitialize___Early()
	{
		if (this.NetworkInitializeEarly_PhysicsProp_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeEarly_PhysicsProp_Assembly-CSharp.dll = true;
		base.NetworkInitialize___Early();
		this.syncVar___localPlayer = new SyncVar<Transform>(this, 1U, WritePermission.ClientUnsynchronized, ReadPermission.Observers, -1f, Channel.Reliable, this.localPlayer);
		this.syncVar___grabbed = new SyncVar<bool>(this, 0U, WritePermission.ClientUnsynchronized, ReadPermission.Observers, -1f, Channel.Reliable, this.grabbed);
		base.RegisterServerRpc(0U, new ServerRpcDelegate(this.RpcReader___Server_CmdInteract_3068987916));
		base.RegisterServerRpc(1U, new ServerRpcDelegate(this.RpcReader___Server_SetGrabbed_1140765316));
		base.RegisterServerRpc(2U, new ServerRpcDelegate(this.RpcReader___Server_SetPlayer_3068987916));
		base.RegisterServerRpc(3U, new ServerRpcDelegate(this.RpcReader___Server_PlaySfx_2166136261));
		base.RegisterObserversRpc(4U, new ClientRpcDelegate(this.RpcReader___Observers_PlaySfxObservers_2166136261));
		base.RegisterServerRpc(5U, new ServerRpcDelegate(this.RpcReader___Server_BumpPlayerServer_1076951378));
		base.RegisterTargetRpc(6U, new ClientRpcDelegate(this.RpcReader___Target_BumpPlayer_2429708885));
		base.RegisterSyncVarRead(new SyncVarReadDelegate(this.ReadSyncVar___PhysicsProp));
	}

	// Token: 0x0600043E RID: 1086 RVA: 0x0001DD2D File Offset: 0x0001BF2D
	public override void NetworkInitialize__Late()
	{
		if (this.NetworkInitializeLate_PhysicsProp_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeLate_PhysicsProp_Assembly-CSharp.dll = true;
		base.NetworkInitialize__Late();
		this.syncVar___localPlayer.SetRegistered();
		this.syncVar___grabbed.SetRegistered();
	}

	// Token: 0x0600043F RID: 1087 RVA: 0x0001DD5C File Offset: 0x0001BF5C
	public override void NetworkInitializeIfDisabled()
	{
		this.NetworkInitialize___Early();
		this.NetworkInitialize__Late();
	}

	// Token: 0x06000440 RID: 1088 RVA: 0x0001DD6C File Offset: 0x0001BF6C
	private void RpcWriter___Server_CmdInteract_3068987916(Transform player)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteTransform(player);
		base.SendServerRpc(0U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000441 RID: 1089 RVA: 0x0001DE13 File Offset: 0x0001C013
	private void RpcLogic___CmdInteract_3068987916(Transform player)
	{
		this.SetGrabbed(!this.SyncAccessor_grabbed);
		this.SetPlayer(this.SyncAccessor_grabbed ? player : null);
	}

	// Token: 0x06000442 RID: 1090 RVA: 0x0001DE38 File Offset: 0x0001C038
	private void RpcReader___Server_CmdInteract_3068987916(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		Transform transform = PooledReader0.ReadTransform();
		if (!base.IsServer)
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___CmdInteract_3068987916(transform);
	}

	// Token: 0x06000443 RID: 1091 RVA: 0x0001DE78 File Offset: 0x0001C078
	private void RpcWriter___Server_SetGrabbed_1140765316(bool newGrabbed)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteBoolean(newGrabbed);
		base.SendServerRpc(1U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000444 RID: 1092 RVA: 0x0001DF1F File Offset: 0x0001C11F
	private void RpcLogic___SetGrabbed_1140765316(bool newGrabbed)
	{
		this.sync___set_value_grabbed(newGrabbed, true);
	}

	// Token: 0x06000445 RID: 1093 RVA: 0x0001DF2C File Offset: 0x0001C12C
	private void RpcReader___Server_SetGrabbed_1140765316(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		bool flag = PooledReader0.ReadBoolean();
		if (!base.IsServer)
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___SetGrabbed_1140765316(flag);
	}

	// Token: 0x06000446 RID: 1094 RVA: 0x0001DF6C File Offset: 0x0001C16C
	private void RpcWriter___Server_SetPlayer_3068987916(Transform player)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteTransform(player);
		base.SendServerRpc(2U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000447 RID: 1095 RVA: 0x0001E013 File Offset: 0x0001C213
	private void RpcLogic___SetPlayer_3068987916(Transform player)
	{
		this.sync___set_value_localPlayer(player, true);
	}

	// Token: 0x06000448 RID: 1096 RVA: 0x0001E020 File Offset: 0x0001C220
	private void RpcReader___Server_SetPlayer_3068987916(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		Transform transform = PooledReader0.ReadTransform();
		if (!base.IsServer)
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___SetPlayer_3068987916(transform);
	}

	// Token: 0x06000449 RID: 1097 RVA: 0x0001E060 File Offset: 0x0001C260
	private void RpcWriter___Server_PlaySfx_2166136261()
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendServerRpc(3U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x0600044A RID: 1098 RVA: 0x0001E154 File Offset: 0x0001C354
	private void RpcLogic___PlaySfx_2166136261()
	{
		this.PlaySfxObservers();
	}

	// Token: 0x0600044B RID: 1099 RVA: 0x0001E15C File Offset: 0x0001C35C
	private void RpcReader___Server_PlaySfx_2166136261(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___PlaySfx_2166136261();
	}

	// Token: 0x0600044C RID: 1100 RVA: 0x0001E19C File Offset: 0x0001C39C
	private void RpcWriter___Observers_PlaySfxObservers_2166136261()
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendObserversRpc(4U, writer, channel, DataOrderType.Default, false, false, true);
		writer.Store();
	}

	// Token: 0x0600044D RID: 1101 RVA: 0x0001E245 File Offset: 0x0001C445
	private void RpcLogic___PlaySfxObservers_2166136261()
	{
		this.audio.PlayOneShot(this.hitClips[Mathf.RoundToInt((float)global::UnityEngine.Random.Range(0, this.hitClips.Length))]);
	}

	// Token: 0x0600044E RID: 1102 RVA: 0x0001E270 File Offset: 0x0001C470
	private void RpcReader___Observers_PlaySfxObservers_2166136261(PooledReader PooledReader0, Channel channel)
	{
		if (!base.IsClient)
		{
			return;
		}
		if (base.IsHost)
		{
			return;
		}
		this.RpcLogic___PlaySfxObservers_2166136261();
	}

	// Token: 0x0600044F RID: 1103 RVA: 0x0001E29C File Offset: 0x0001C49C
	private void RpcWriter___Server_BumpPlayerServer_1076951378(Vector3 direction, float force, PlayerHealth ph)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteVector3(direction);
		writer.WriteSingle(force, AutoPackType.Unpacked);
		writer.Write___PlayerHealthFishNet.Serializing.Generated(ph);
		base.SendServerRpc(5U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000450 RID: 1104 RVA: 0x0001E3BC File Offset: 0x0001C5BC
	private void RpcLogic___BumpPlayerServer_1076951378(Vector3 direction, float force, PlayerHealth ph)
	{
		this.BumpPlayer(ph.SyncAccessor_playerValues.SyncAccessor_playerClient.transform.GetComponent<NetworkObject>().Owner, ph, force, direction);
	}

	// Token: 0x06000451 RID: 1105 RVA: 0x0001E3E4 File Offset: 0x0001C5E4
	private void RpcReader___Server_BumpPlayerServer_1076951378(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		Vector3 vector = PooledReader0.ReadVector3();
		float num = PooledReader0.ReadSingle(AutoPackType.Unpacked);
		PlayerHealth playerHealth = global::FishNet.Serializing.Generated.GeneratedReaders___Internal.Read___PlayerHealthFishNet.Serializing.Generateds(PooledReader0);
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		this.RpcLogic___BumpPlayerServer_1076951378(vector, num, playerHealth);
	}

	// Token: 0x06000452 RID: 1106 RVA: 0x0001E450 File Offset: 0x0001C650
	private void RpcWriter___Target_BumpPlayer_2429708885(NetworkConnection conn, PlayerHealth enemyHealth, float force, Vector3 direction)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.Write___PlayerHealthFishNet.Serializing.Generated(enemyHealth);
		writer.WriteSingle(force, AutoPackType.Unpacked);
		writer.WriteVector3(direction);
		base.SendTargetRpc(6U, writer, channel, DataOrderType.Default, conn, false, true);
		writer.Store();
	}

	// Token: 0x06000453 RID: 1107 RVA: 0x0001E524 File Offset: 0x0001C724
	private void RpcLogic___BumpPlayer_2429708885(NetworkConnection conn, PlayerHealth enemyHealth, float force, Vector3 direction)
	{
		enemyHealth.bounceDirection = direction;
		enemyHealth.bounceForce = force;
		enemyHealth.shouldBounce = true;
		enemyHealth.shouldDropWeapon = true;
	}

	// Token: 0x06000454 RID: 1108 RVA: 0x0001E544 File Offset: 0x0001C744
	private void RpcReader___Target_BumpPlayer_2429708885(PooledReader PooledReader0, Channel channel)
	{
		PlayerHealth playerHealth = global::FishNet.Serializing.Generated.GeneratedReaders___Internal.Read___PlayerHealthFishNet.Serializing.Generateds(PooledReader0);
		float num = PooledReader0.ReadSingle(AutoPackType.Unpacked);
		Vector3 vector = PooledReader0.ReadVector3();
		if (!base.IsClient)
		{
			return;
		}
		this.RpcLogic___BumpPlayer_2429708885(base.LocalConnection, playerHealth, num, vector);
	}

	// Token: 0x1700006A RID: 106
	// (get) Token: 0x06000455 RID: 1109 RVA: 0x0001E5A2 File Offset: 0x0001C7A2
	// (set) Token: 0x06000456 RID: 1110 RVA: 0x0001E5AA File Offset: 0x0001C7AA
	public bool SyncAccessor_grabbed
	{
		get
		{
			return this.grabbed;
		}
		set
		{
			if (value || !base.IsServer)
			{
				this.grabbed = value;
			}
			this.syncVar___grabbed.SetValue(value, value);
		}
	}

	// Token: 0x06000457 RID: 1111 RVA: 0x0001E5E0 File Offset: 0x0001C7E0
	public virtual bool ReadSyncVar___PhysicsProp(PooledReader PooledReader0, uint UInt321, bool Boolean2)
	{
		if (UInt321 == 1U)
		{
			if (PooledReader0 == null)
			{
				this.sync___set_value_localPlayer(this.syncVar___localPlayer.GetValue(true), true);
				return true;
			}
			Transform transform = PooledReader0.ReadTransform();
			this.sync___set_value_localPlayer(transform, Boolean2);
			return true;
		}
		else
		{
			if (UInt321 != 0U)
			{
				return false;
			}
			if (PooledReader0 == null)
			{
				this.sync___set_value_grabbed(this.syncVar___grabbed.GetValue(true), true);
				return true;
			}
			bool flag = PooledReader0.ReadBoolean();
			this.sync___set_value_grabbed(flag, Boolean2);
			return true;
		}
	}

	// Token: 0x1700006B RID: 107
	// (get) Token: 0x06000458 RID: 1112 RVA: 0x0001E676 File Offset: 0x0001C876
	// (set) Token: 0x06000459 RID: 1113 RVA: 0x0001E67E File Offset: 0x0001C87E
	public Transform SyncAccessor_localPlayer
	{
		get
		{
			return this.localPlayer;
		}
		set
		{
			if (value || !base.IsServer)
			{
				this.localPlayer = value;
			}
			this.syncVar___localPlayer.SetValue(value, value);
		}
	}

	// Token: 0x0600045A RID: 1114 RVA: 0x0001E6B3 File Offset: 0x0001C8B3
	public override void Awake()
	{
		this.NetworkInitialize___Early();
		base.Awake();
		this.NetworkInitialize__Late();
	}

	// Token: 0x0600045B RID: 1115 RVA: 0x000023D6 File Offset: 0x000005D6
	public override void Awake___UserLogic()
	{
	}

	// Token: 0x04000475 RID: 1141
	[SyncVar(WritePermissions = WritePermission.ClientUnsynchronized)]
	public bool grabbed;

	// Token: 0x04000476 RID: 1142
	private CharacterController physics;

	// Token: 0x04000477 RID: 1143
	[SerializeField]
	private LayerMask defaultMask;

	// Token: 0x04000478 RID: 1144
	[SerializeField]
	private float launchForce = 25f;

	// Token: 0x04000479 RID: 1145
	[SerializeField]
	private float distanceFromPlayer = 2.5f;

	// Token: 0x0400047A RID: 1146
	[SerializeField]
	private float damage = 1f;

	// Token: 0x0400047B RID: 1147
	[SerializeField]
	private AudioClip[] hitClips;

	// Token: 0x0400047C RID: 1148
	[SerializeField]
	private GameObject bloodImpact;

	// Token: 0x0400047D RID: 1149
	[SyncVar(WritePermissions = WritePermission.ClientUnsynchronized)]
	public Transform localPlayer;

	// Token: 0x0400047E RID: 1150
	public Transform localLocalPlayer;

	// Token: 0x0400047F RID: 1151
	private Transform cam;

	// Token: 0x04000480 RID: 1152
	[Space]
	[SerializeField]
	private float gravity = 20f;

	// Token: 0x04000481 RID: 1153
	private Vector3 moveDirection;

	// Token: 0x04000482 RID: 1154
	private AudioSource audio;

	// Token: 0x04000483 RID: 1155
	private BoxCollider col;

	// Token: 0x04000484 RID: 1156
	private bool canHit;

	// Token: 0x04000485 RID: 1157
	private bool canThrow;

	// Token: 0x04000486 RID: 1158
	private float throwTimer;

	// Token: 0x04000487 RID: 1159
	private PlayerPickup pickup;

	// Token: 0x04000488 RID: 1160
	private bool shouldPlaySfx;

	// Token: 0x04000489 RID: 1161
	private bool shouldPlaySfx2;

	// Token: 0x0400048A RID: 1162
	private bool sendKillLog;

	// Token: 0x0400048B RID: 1163
	private bool increaseKillAmount;

	// Token: 0x0400048C RID: 1164
	[SerializeField]
	private float mass = 1f;

	// Token: 0x0400048D RID: 1165
	private Vector3 impact = Vector3.zero;

	// Token: 0x0400048E RID: 1166
	private Vector3 customForceFinal;

	// Token: 0x0400048F RID: 1167
	[SerializeField]
	private float airdeceleration = 3f;

	// Token: 0x04000490 RID: 1168
	[SerializeField]
	private float deceleration = 5f;

	// Token: 0x04000491 RID: 1169
	public SyncVar<bool> syncVar___grabbed;

	// Token: 0x04000492 RID: 1170
	public SyncVar<Transform> syncVar___localPlayer;

	// Token: 0x04000493 RID: 1171
	private bool NetworkInitializeEarly_PhysicsProp_Assembly-CSharp.dll;

	// Token: 0x04000494 RID: 1172
	private bool NetworkInitializeLate_PhysicsProp_Assembly-CSharp.dll;
}

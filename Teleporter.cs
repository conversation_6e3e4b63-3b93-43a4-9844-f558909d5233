﻿using System;
using UnityEngine;

// Token: 0x02000073 RID: 115
public class Teleporter : MonoBehaviour
{
	// Token: 0x060004EF RID: 1263 RVA: 0x00020ED8 File Offset: 0x0001F0D8
	private void Start()
	{
		if (this.anglesDifference == 0f && this.selfOrientation != null)
		{
			this.anglesDifference = Mathf.Abs(this.selfOrientation.eulerAngles.y) - Mathf.Abs(this.teleportPoint.eulerAngles.y);
		}
	}

	// Token: 0x040004E7 RID: 1255
	public bool dontTranslateRotation;

	// Token: 0x040004E8 RID: 1256
	[Space]
	public Transform teleportPoint;

	// Token: 0x040004E9 RID: 1257
	public Transform selfOrientation;

	// Token: 0x040004EA RID: 1258
	public float propulsionPower = 10f;

	// Token: 0x040004EB RID: 1259
	public float propulsionDecel = 8f;

	// Token: 0x040004EC RID: 1260
	public float anglesDifference;
}

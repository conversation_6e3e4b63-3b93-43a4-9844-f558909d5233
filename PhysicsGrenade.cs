﻿using System;
using System.Collections.Generic;
using DG.Tweening;
using UnityEngine;

// Token: 0x020000A1 RID: 161
public class PhysicsGrenade : MonoBehaviour
{
	// Token: 0x060008C5 RID: 2245 RVA: 0x0003EDFD File Offset: 0x0003CFFD
	private void OnEnable()
	{
		PauseManager.OnBeforeSpawn += this.StartNewRound;
	}

	// Token: 0x060008C6 RID: 2246 RVA: 0x0003EE10 File Offset: 0x0003D010
	private void OnDisable()
	{
		PauseManager.OnBeforeSpawn -= this.StartNewRound;
	}

	// Token: 0x060008C7 RID: 2247 RVA: 0x00002E03 File Offset: 0x00001003
	private void StartNewRound()
	{
		global::UnityEngine.Object.Destroy(base.gameObject);
	}

	// Token: 0x060008C8 RID: 2248 RVA: 0x0003EE23 File Offset: 0x0003D023
	private void Awake()
	{
		this.audio = base.GetComponent<AudioSource>();
	}

	// Token: 0x060008C9 RID: 2249 RVA: 0x0003EE31 File Offset: 0x0003D031
	public void Initialize(GameObject rootObject, GameObject gun, float passedTime, float grenadeOpenSince)
	{
		this._rootObject = rootObject;
		this._passedTime = passedTime;
		this._gun = gun;
		this.explosionTimer = grenadeOpenSince;
		this.audio.PlayOneShot(this.swooshClip);
	}

	// Token: 0x060008CA RID: 2250 RVA: 0x0003EE64 File Offset: 0x0003D064
	private void Update()
	{
		float deltaTime = Time.deltaTime;
		float num = 0f;
		this.velocity = this.lastPosition - this.currentPosition;
		this.explosionTimer -= deltaTime + num;
		this.graph.Rotate(this.rotateAxis * this.rotateSpeed * Time.deltaTime);
		this.HandleExplosion();
		this.lastPosition = this.currentPosition;
	}

	// Token: 0x060008CB RID: 2251 RVA: 0x0003EEDC File Offset: 0x0003D0DC
	private void HandleExplosion()
	{
		if (this.explosionTimer < 0f && this.explosionTimer > -2f)
		{
			Collider[] array = new Collider[1];
			List<Collider> list = new List<Collider>();
			if (!this.fragGrenade)
			{
				array = Physics.OverlapSphere(base.transform.position, this.explosionRadius, this.bodyLayer);
			}
			else
			{
				int num = 0;
				while ((float)num < this.numberOfRays)
				{
					Vector3 normalized = global::UnityEngine.Random.onUnitSphere.normalized;
					RaycastHit raycastHit;
					if (Physics.Raycast(base.transform.position, normalized, out raycastHit, this.explosionRadius, this.bodyLayer))
					{
						list.Add(raycastHit.transform.GetComponent<Collider>());
					}
					this.RenderObject(normalized, (raycastHit.collider == null) ? this.explosionRadius : Vector3.Distance(base.transform.position, raycastHit.point));
					num++;
				}
				array = new Collider[list.Count];
				for (int i = 0; i < list.Count; i++)
				{
					array[i] = list[i];
				}
			}
			if (array.Length != 0)
			{
				this.ph2 = new PlayerHealth[array.Length];
				for (int j = 0; j < array.Length; j++)
				{
					if (array[j].transform.tag == "ShatterableGlass" && array[j].gameObject.GetComponent<ShatterableGlass>() != null)
					{
						array[j].gameObject.GetComponent<ShatterableGlass>().Shatter3D(array[j].transform.position, array[j].transform.position - base.transform.position);
					}
					if (array[j].GetComponentInParent<PlayerHealth>() != null)
					{
						this.ph2[j] = array[j].GetComponentInParent<PlayerHealth>();
						if (this.bloodSplatter && this.makeBlood && !this.stunGrenade)
						{
							global::UnityEngine.Object.Instantiate<GameObject>(this.bloodSplatter, array[j].transform.position, Quaternion.Euler(0f, (float)global::UnityEngine.Random.Range(0, 360), 0f));
							this.makeBlood = false;
						}
					}
				}
				for (int k = 0; k < this.ph2.Length; k++)
				{
					if (this.ph2[k] != null && this.isOwner && !this.ph2[k].SyncAccessor_isKilled)
					{
						if (!this.stunGrenade)
						{
							this.ph2[k].ChangeKilledState(true);
							this.ph2[k].RemoveHealth(10f);
							if (this.ph2[k].transform.gameObject == this._rootObject)
							{
								this.IncreaseSuicidesAmount();
								this.ph2[k].suicide = true;
							}
							else
							{
								this.KillShockWave();
								this.SendKillLog(this.ph2[k]);
							}
							this.ph2[k].Explode(false, true, this.ph2[k].gameObject.name, this.ph2[k].transform.position - base.transform.position, this.ragdollEjectForce, base.transform.position);
							this.ph2[k].SetKiller(this._rootObject.transform);
						}
						if (this.stunGrenade)
						{
							this.ph2[k].TaserEnemy(this.ph2[k], this.stunTime);
						}
					}
				}
			}
			GameObject[] array2 = GameObject.FindGameObjectsWithTag("Player");
			for (int l = 0; l < array2.Length; l++)
			{
				float num2 = Vector3.Distance(base.transform.position, array2[l].transform.position);
				array2[l].GetComponent<PlayerHealth>().LocalScreenshake(this.duration, Mathf.Lerp(this.maxStrength, this.minStrength, Mathf.Clamp(num2 / this.maxDistance, 0f, 1f)), this.vibrato, this.randomness, this.shakeEase);
			}
			global::UnityEngine.Object.Destroy(base.gameObject, 3f);
			base.enabled = false;
			this.graph.gameObject.SetActive(false);
			if (!this.fragGrenade)
			{
				GameObject gameObject = global::UnityEngine.Object.Instantiate<GameObject>(this.explosionVfx, base.transform.position, Quaternion.identity);
				if (gameObject.transform.Find("ball") != null)
				{
					gameObject.transform.Find("ball").localScale = this.vfxScale;
				}
			}
			global::UnityEngine.Object.Instantiate<GameObject>(this.explosionDecal, base.transform.position, Quaternion.identity);
			this.audio.Play();
		}
	}

	// Token: 0x060008CC RID: 2252 RVA: 0x0003F3B4 File Offset: 0x0003D5B4
	private void SendKillLog(PlayerHealth enemyHealth)
	{
		this.sendKillLog = true;
		PauseManager.Instance.WriteLog(string.Concat(new string[]
		{
			"<b><color=#",
			PauseManager.Instance.selfNameLogColor,
			">",
			enemyHealth.SyncAccessor_playerValues.SyncAccessor_playerClient.PlayerName,
			"</color></b> was killed with a <b><color=white>",
			this.weaponName,
			"</color></b> by <b><color=#",
			PauseManager.Instance.enemyNameLogColor,
			">",
			ClientInstance.Instance.PlayerName,
			"</color></b>"
		}));
	}

	// Token: 0x060008CD RID: 2253 RVA: 0x0003F453 File Offset: 0x0003D653
	private void IncreaseSuicidesAmount()
	{
		if (this.suicide)
		{
			this.suicide = false;
			Settings.Instance.IncreaseSuicidesAmount();
		}
	}

	// Token: 0x060008CE RID: 2254 RVA: 0x0003F470 File Offset: 0x0003D670
	public void KillShockWave()
	{
		Settings.Instance.IncreaseKillsAmount();
		this.increaseKillAmount = true;
		this._rootObject.GetComponent<FirstPersonController>().lensDistortion.intensity.value = this._rootObject.GetComponent<FirstPersonController>().killShockWaveStrength;
		this._rootObject.GetComponent<FirstPersonController>().colorGrading.saturation.value = -100f;
	}

	// Token: 0x060008CF RID: 2255 RVA: 0x0003F4D8 File Offset: 0x0003D6D8
	private void RenderObject(Vector3 direction, float maxDistance)
	{
		GameObject gameObject = new GameObject("RaycastLine");
		LineRenderer lineRenderer = gameObject.AddComponent<LineRenderer>();
		LineFade lineFade = gameObject.AddComponent<LineFade>();
		lineFade.decreaseInSize = false;
		lineFade.decreaseInSizeSpeed = 10f;
		lineFade.color = Color.white;
		lineFade.speed = 9f;
		lineRenderer.startWidth = 0.02f;
		lineRenderer.endWidth = 0.01f;
		lineRenderer.material = new Material(Shader.Find("Sprites/Default"));
		lineRenderer.startColor = Color.white;
		lineRenderer.endColor = Color.white;
		Vector3 position = base.transform.position;
		lineRenderer.SetPosition(0, position);
		lineRenderer.SetPosition(1, position + direction * maxDistance);
		lineRenderer.startColor = Color.white;
		lineRenderer.endColor = Color.white;
		global::UnityEngine.Object.Destroy(gameObject, 2.5f);
	}

	// Token: 0x060008D0 RID: 2256 RVA: 0x0003F5B0 File Offset: 0x0003D7B0
	private void OnCollisionEnter(Collision col)
	{
		if (this.hits > this.maxHits)
		{
			return;
		}
		if (this.explosionTimer < 0f)
		{
			return;
		}
		this.audio.PlayOneShot(this.hitClip, 0.56f);
		this.hits++;
	}

	// Token: 0x0400079F RID: 1951
	[SerializeField]
	private string weaponName;

	// Token: 0x040007A0 RID: 1952
	public bool isOwner;

	// Token: 0x040007A1 RID: 1953
	private Vector3 impact = Vector3.zero;

	// Token: 0x040007A2 RID: 1954
	private CharacterController character;

	// Token: 0x040007A3 RID: 1955
	[SerializeField]
	private float ragdollEjectForce;

	// Token: 0x040007A4 RID: 1956
	[SerializeField]
	private LayerMask playerLayer;

	// Token: 0x040007A5 RID: 1957
	[SerializeField]
	private LayerMask bodyLayer;

	// Token: 0x040007A6 RID: 1958
	[SerializeField]
	private GameObject hitVfx;

	// Token: 0x040007A7 RID: 1959
	[SerializeField]
	private GameObject explosionVfx;

	// Token: 0x040007A8 RID: 1960
	[SerializeField]
	private AudioClip hitClip;

	// Token: 0x040007A9 RID: 1961
	[SerializeField]
	private AudioClip explosionClip;

	// Token: 0x040007AA RID: 1962
	[SerializeField]
	private AudioClip swooshClip;

	// Token: 0x040007AB RID: 1963
	[SerializeField]
	private float rotateSpeed;

	// Token: 0x040007AC RID: 1964
	[SerializeField]
	private Vector3 rotateAxis;

	// Token: 0x040007AD RID: 1965
	[SerializeField]
	private Vector3 vfxScale = Vector3.one;

	// Token: 0x040007AE RID: 1966
	[SerializeField]
	private Transform graph;

	// Token: 0x040007AF RID: 1967
	[SerializeField]
	private GameObject explosionDecal;

	// Token: 0x040007B0 RID: 1968
	[SerializeField]
	private GameObject bloodSplatter;

	// Token: 0x040007B1 RID: 1969
	[SerializeField]
	private float timeBeforeExplosion = 2f;

	// Token: 0x040007B2 RID: 1970
	[SerializeField]
	private float explosionRadius = 3f;

	// Token: 0x040007B3 RID: 1971
	private float explosionTimer;

	// Token: 0x040007B4 RID: 1972
	[SerializeField]
	private float rebondForce;

	// Token: 0x040007B5 RID: 1973
	private bool isGrounded;

	// Token: 0x040007B6 RID: 1974
	[Header("Screenshake values")]
	[SerializeField]
	private float duration;

	// Token: 0x040007B7 RID: 1975
	[SerializeField]
	private float minStrength;

	// Token: 0x040007B8 RID: 1976
	[SerializeField]
	private float maxStrength;

	// Token: 0x040007B9 RID: 1977
	[SerializeField]
	private int vibrato;

	// Token: 0x040007BA RID: 1978
	[SerializeField]
	private float randomness;

	// Token: 0x040007BB RID: 1979
	[SerializeField]
	private Ease shakeEase;

	// Token: 0x040007BC RID: 1980
	[SerializeField]
	private float maxDistance;

	// Token: 0x040007BD RID: 1981
	private bool touched;

	// Token: 0x040007BE RID: 1982
	private bool touched2;

	// Token: 0x040007BF RID: 1983
	private GameObject _gun;

	// Token: 0x040007C0 RID: 1984
	private PlayerHealth[] ph2;

	// Token: 0x040007C1 RID: 1985
	private float _passedTime;

	// Token: 0x040007C2 RID: 1986
	private GameObject _rootObject;

	// Token: 0x040007C3 RID: 1987
	private AudioSource audio;

	// Token: 0x040007C4 RID: 1988
	[HideInInspector]
	public Weapon weapon;

	// Token: 0x040007C5 RID: 1989
	private Vector3 currentPosition;

	// Token: 0x040007C6 RID: 1990
	private Vector3 lastPosition;

	// Token: 0x040007C7 RID: 1991
	private Vector3 velocity;

	// Token: 0x040007C8 RID: 1992
	[SerializeField]
	private bool fragGrenade;

	// Token: 0x040007C9 RID: 1993
	[SerializeField]
	private float numberOfRays;

	// Token: 0x040007CA RID: 1994
	[SerializeField]
	private bool stunGrenade;

	// Token: 0x040007CB RID: 1995
	[SerializeField]
	private float stunTime;

	// Token: 0x040007CC RID: 1996
	private float safeTimer;

	// Token: 0x040007CD RID: 1997
	private bool makeBlood = true;

	// Token: 0x040007CE RID: 1998
	private bool sendKillLog;

	// Token: 0x040007CF RID: 1999
	private bool suicide = true;

	// Token: 0x040007D0 RID: 2000
	private bool increaseKillAmount;

	// Token: 0x040007D1 RID: 2001
	private int maxHits = 5;

	// Token: 0x040007D2 RID: 2002
	private int hits;
}

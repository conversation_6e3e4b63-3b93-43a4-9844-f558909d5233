﻿using System;
using UnityEngine;

// Token: 0x0200005B RID: 91
public class ItemSpawner : Spawner
{
	// Token: 0x06000406 RID: 1030 RVA: 0x0001C93D File Offset: 0x0001AB3D
	private void OnEnable()
	{
		PauseManager.OnBeforeSpawn += this.StartNewRound;
	}

	// Token: 0x06000407 RID: 1031 RVA: 0x0001C950 File Offset: 0x0001AB50
	private void OnDisable()
	{
		PauseManager.OnBeforeSpawn -= this.StartNewRound;
	}

	// Token: 0x06000408 RID: 1032 RVA: 0x0001C963 File Offset: 0x0001AB63
	public void StartNewRound()
	{
		this.countdownTimer = 0f;
	}

	// Token: 0x06000409 RID: 1033 RVA: 0x0001C970 File Offset: 0x0001AB70
	public override void TrySpawn()
	{
		if (this.SpawnedItem != null && !this.SpawnedItem.GetComponent<ItemBehaviour>().isTaken)
		{
			return;
		}
		if (SpawnerManager.Instance != null && SpawnerManager.Instance.SyncAccessor_randomiseWeapons)
		{
			this.itemToSpawn = SpawnerManager.Instance.GetRandomSpawnableWeapon();
		}
		this.countdownTimer = this.weaponRespawnTimeInSeconds;
		Vector3 vector = base.transform.position + Vector3.up * 0.5f;
		Quaternion quaternion = Quaternion.LookRotation(base.transform.right);
		GameObject gameObject = global::UnityEngine.Object.Instantiate<GameObject>(this.itemToSpawn, vector, quaternion, base.transform);
		base.ServerManager.Spawn(gameObject, null);
		this.SpawnedItem = gameObject;
	}

	// Token: 0x0600040B RID: 1035 RVA: 0x0001CA34 File Offset: 0x0001AC34
	public override void NetworkInitialize___Early()
	{
		if (this.NetworkInitializeEarly_ItemSpawner_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeEarly_ItemSpawner_Assembly-CSharp.dll = true;
		base.NetworkInitialize___Early();
	}

	// Token: 0x0600040C RID: 1036 RVA: 0x0001CA4D File Offset: 0x0001AC4D
	public override void NetworkInitialize__Late()
	{
		if (this.NetworkInitializeLate_ItemSpawner_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeLate_ItemSpawner_Assembly-CSharp.dll = true;
		base.NetworkInitialize__Late();
	}

	// Token: 0x0600040D RID: 1037 RVA: 0x0001CA66 File Offset: 0x0001AC66
	public override void NetworkInitializeIfDisabled()
	{
		this.NetworkInitialize___Early();
		this.NetworkInitialize__Late();
	}

	// Token: 0x0600040E RID: 1038 RVA: 0x0001CA74 File Offset: 0x0001AC74
	public override void Awake()
	{
		this.NetworkInitialize___Early();
		base.Awake();
		this.NetworkInitialize__Late();
	}

	// Token: 0x0600040F RID: 1039 RVA: 0x000023D6 File Offset: 0x000005D6
	public override void Awake___UserLogic()
	{
	}

	// Token: 0x04000453 RID: 1107
	public GameObject itemToSpawn;

	// Token: 0x04000454 RID: 1108
	private bool NetworkInitializeEarly_ItemSpawner_Assembly-CSharp.dll;

	// Token: 0x04000455 RID: 1109
	private bool NetworkInitializeLate_ItemSpawner_Assembly-CSharp.dll;
}

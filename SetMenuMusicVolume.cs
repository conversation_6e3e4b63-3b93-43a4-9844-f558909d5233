﻿using System;
using TMPro;
using UnityEngine;
using UnityEngine.SceneManagement;

// Token: 0x0200010A RID: 266
public class SetMenuMusicVolume : MonoBehaviour
{
	// Token: 0x06000D33 RID: 3379 RVA: 0x000596EA File Offset: 0x000578EA
	private void Awake()
	{
		this.audio = base.GetComponent<AudioSource>();
		this.customListener = base.GetComponent<AudioListener>();
	}

	// Token: 0x06000D34 RID: 3380 RVA: 0x00059704 File Offset: 0x00057904
	private void Start()
	{
		this.settings = Settings.Instance;
		this.soundManager = SoundManager.Instance;
		this.Shuffle(this.menuTracks);
	}

	// Token: 0x06000D35 RID: 3381 RVA: 0x00059728 File Offset: 0x00057928
	private void Update()
	{
		if (PauseManager.Instance.inMainMenu)
		{
			this.musicPlayerTransform.position = ((this.optionsMenu.activeSelf || this.mapsPanel.transform.localScale != Vector3.zero) ? this.optionsPosition.position : this.playMenuPosition.position);
			this.musicPlayerTransform.localScale = ((this.optionsMenu.activeSelf || this.mapsPanel.transform.localScale != Vector3.zero) ? Vector3.one : new Vector3(0.75f, 0.75f, 0.75f));
			this.musicPlayerTransform.SetParent((this.optionsMenu.activeSelf || this.mapsPanel.transform.localScale != Vector3.zero) ? this.optionsPosition : this.playMenuPosition);
		}
		else
		{
			if (this.musicPlayerTransform.position != this.optionsPosition.position)
			{
				this.musicPlayerTransform.position = this.optionsPosition.position;
			}
			if (this.musicPlayerTransform.localScale != Vector3.one)
			{
				this.musicPlayerTransform.localScale = Vector3.one;
			}
			if (this.musicPlayerTransform.parent != this.optionsPosition)
			{
				this.musicPlayerTransform.SetParent(this.optionsPosition);
			}
		}
		if (this.audio.volume != this.soundManager.menuMusicVolume)
		{
			this.audio.volume = this.soundManager.menuMusicVolume;
		}
		this.audio.mute = SceneManager.GetActiveScene().name == "VictoryScene" || SceneManager.GetActiveScene().name == "EndGame";
		this.customListener.enabled = SceneManager.GetActiveScene().name == "MovedObjectsHolder";
		if (!this.audio.isPlaying && SceneManager.GetActiveScene().name == "MainMenu" && !this.pause)
		{
			this.currentTrackId = Mathf.RoundToInt((float)global::UnityEngine.Random.Range(0, this.menuTracks.Length));
			this.audio.clip = this.menuTracks[this.currentTrackId];
			this.audio.Play();
			this.trackTextInPlayer.text = this.audio.clip.name + " - " + this.artistNames[this.currentTrackId];
			this.trackText.text = "music playing : " + this.audio.clip.name + " - " + this.artistNames[this.currentTrackId];
		}
		else if (this.audio.isPlaying && SceneManager.GetActiveScene().name != "MainMenu" && !this.settings.inGameMusic)
		{
			this.audio.Stop();
		}
		if (this.audio.time >= this.audio.clip.length - 0.75f)
		{
			Debug.Log("Change Track");
			this.NextTrack();
		}
		this.playbackTimeText.text = string.Concat(new string[]
		{
			((int)(this.audio.time / 60f)).ToString(),
			":",
			(this.audio.time % 60f < 10f) ? "0" : "",
			((int)(this.audio.time % 60f)).ToString(),
			" / ",
			((int)(this.audio.clip.length / 60f)).ToString(),
			":",
			(this.audio.clip.length % 60f < 10f) ? "0" : "",
			((int)(this.audio.clip.length % 60f)).ToString()
		});
	}

	// Token: 0x06000D36 RID: 3382 RVA: 0x00059B88 File Offset: 0x00057D88
	public void NextTrack()
	{
		if (SceneManager.GetActiveScene().name != "MainMenu" && !this.settings.inGameMusic)
		{
			return;
		}
		this.audio.time = 0f;
		this.audio.Stop();
		this.currentTrackId = ((this.currentTrackId + 1 >= this.menuTracks.Length) ? 0 : (this.currentTrackId + 1));
		this.audio.clip = this.menuTracks[this.currentTrackId];
		this.audio.Play();
		this.pauseButton.SetActive(true);
		this.playButton.SetActive(false);
		this.trackTextInPlayer.text = this.audio.clip.name + " - " + this.artistNames[this.currentTrackId];
		this.trackText.text = "music playing : " + this.audio.clip.name + " - " + this.artistNames[this.currentTrackId];
	}

	// Token: 0x06000D37 RID: 3383 RVA: 0x00059CA0 File Offset: 0x00057EA0
	public void PreviousTrack()
	{
		if (SceneManager.GetActiveScene().name != "MainMenu" && !this.settings.inGameMusic)
		{
			return;
		}
		this.audio.time = 0f;
		this.audio.Stop();
		this.currentTrackId = ((this.currentTrackId - 1 < 0) ? (this.menuTracks.Length - 1) : (this.currentTrackId - 1));
		this.audio.clip = this.menuTracks[this.currentTrackId];
		this.audio.Play();
		this.pauseButton.SetActive(true);
		this.playButton.SetActive(false);
		this.trackTextInPlayer.text = this.audio.clip.name + " - " + this.artistNames[this.currentTrackId];
		this.trackText.text = "music playing : " + this.audio.clip.name + " - " + this.artistNames[this.currentTrackId];
	}

	// Token: 0x06000D38 RID: 3384 RVA: 0x00059DB8 File Offset: 0x00057FB8
	public void Pause()
	{
		this.audio.Pause();
		this.pause = true;
		this.pauseButton.SetActive(false);
		this.playButton.SetActive(true);
	}

	// Token: 0x06000D39 RID: 3385 RVA: 0x00059DE4 File Offset: 0x00057FE4
	public void Play()
	{
		if (SceneManager.GetActiveScene().name != "MainMenu" && !this.settings.inGameMusic)
		{
			return;
		}
		this.audio.Play();
		this.pause = false;
		this.pauseButton.SetActive(true);
		this.playButton.SetActive(false);
	}

	// Token: 0x06000D3A RID: 3386 RVA: 0x00059E42 File Offset: 0x00058042
	public void SetAudioPosition(float value)
	{
		this.audio.time = Mathf.Lerp(0f, this.audio.clip.length, value);
	}

	// Token: 0x06000D3B RID: 3387 RVA: 0x00059E6C File Offset: 0x0005806C
	public void Shuffle(AudioClip[] texts)
	{
		for (int i = 0; i < texts.Length; i++)
		{
			AudioClip audioClip = texts[i];
			string text = this.artistNames[i];
			int num = global::UnityEngine.Random.Range(i, texts.Length);
			texts[i] = texts[num];
			this.artistNames[i] = this.artistNames[num];
			texts[num] = audioClip;
			this.artistNames[num] = text;
		}
	}

	// Token: 0x04000B67 RID: 2919
	public AudioClip[] menuTracks;

	// Token: 0x04000B68 RID: 2920
	public string[] artistNames;

	// Token: 0x04000B69 RID: 2921
	[SerializeField]
	private TextMeshProUGUI trackText;

	// Token: 0x04000B6A RID: 2922
	[SerializeField]
	private TextMeshProUGUI trackTextInPlayer;

	// Token: 0x04000B6B RID: 2923
	[SerializeField]
	private TextMeshProUGUI playbackTimeText;

	// Token: 0x04000B6C RID: 2924
	[SerializeField]
	private GameObject pauseButton;

	// Token: 0x04000B6D RID: 2925
	[SerializeField]
	private GameObject playButton;

	// Token: 0x04000B6E RID: 2926
	[Space]
	[SerializeField]
	private Transform musicPlayerTransform;

	// Token: 0x04000B6F RID: 2927
	[SerializeField]
	private Transform optionsPosition;

	// Token: 0x04000B70 RID: 2928
	[SerializeField]
	private Transform playMenuPosition;

	// Token: 0x04000B71 RID: 2929
	[SerializeField]
	private GameObject optionsMenu;

	// Token: 0x04000B72 RID: 2930
	[SerializeField]
	private GameObject mapsPanel;

	// Token: 0x04000B73 RID: 2931
	[Space]
	[HideInInspector]
	public AudioSource audio;

	// Token: 0x04000B74 RID: 2932
	private Settings settings;

	// Token: 0x04000B75 RID: 2933
	private SoundManager soundManager;

	// Token: 0x04000B76 RID: 2934
	private AudioListener customListener;

	// Token: 0x04000B77 RID: 2935
	private bool pause;

	// Token: 0x04000B78 RID: 2936
	public int currentTrackId;
}

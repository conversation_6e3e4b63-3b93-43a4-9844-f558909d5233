﻿using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.AI;

namespace ch.sycoforge.Decal.Demo
{
	// Token: 0x02000183 RID: 387
	[RequireComponent(typeof(NavMeshAgent))]
	[RequireComponent(typeof(LineRenderer))]
	public class PathAgent : MonoBehaviour
	{
		// Token: 0x06000FC5 RID: 4037 RVA: 0x000676E4 File Offset: 0x000658E4
		private void Start()
		{
			this.TargetAimDecal.gameObject.SetActive(false);
			this.agent = base.GetComponent<NavMeshAgent>();
			this.lineRenderer = base.GetComponent<LineRenderer>();
		}

		// Token: 0x06000FC6 RID: 4038 RVA: 0x00067710 File Offset: 0x00065910
		private void Update()
		{
			Ray ray = Camera.main.ScreenPointToRay(Input.mousePosition);
			this.CreatePath(ray);
			this.SetTarget(ray);
		}

		// Token: 0x06000FC7 RID: 4039 RVA: 0x0006773C File Offset: 0x0006593C
		private void SetTarget(Ray mouseRay)
		{
			RaycastHit raycastHit;
			if (Input.GetMouseButtonUp(0) && Physics.Raycast(mouseRay, out raycastHit, 50f))
			{
				this.agent.SetDestination(raycastHit.point);
				EasyDecal.ProjectAt(this.TargetPointDecalPrefab, raycastHit.collider.gameObject, raycastHit.point + this.decalOffset, Quaternion.identity, true);
			}
		}

		// Token: 0x06000FC8 RID: 4040 RVA: 0x000677A4 File Offset: 0x000659A4
		private void CreatePath(Ray mouseRay)
		{
			RaycastHit raycastHit;
			if (Physics.Raycast(mouseRay, out raycastHit, 50f))
			{
				Vector3 position = base.transform.position;
				Vector3 point = raycastHit.point;
				this.path.Clear();
				NavMeshPath navMeshPath = new NavMeshPath();
				if (NavMesh.CalculatePath(position, point, -1, navMeshPath) && navMeshPath.status == NavMeshPathStatus.PathComplete)
				{
					int num = navMeshPath.corners.Length;
					Vector3 vector = base.transform.up;
					for (int i = 0; i < num; i++)
					{
						RaycastHit raycastHit2;
						if (i > 0 && this.NormalPathOffset > 0f && Physics.Raycast(navMeshPath.corners[i], Vector3.down, out raycastHit2, this.NormalPathOffset * 10f))
						{
							vector = raycastHit.normal;
						}
						Vector3 vector2 = navMeshPath.corners[i] + vector * this.NormalPathOffset;
						this.path.Add(vector2);
					}
					Vector3[] array = BezierUtil.InterpolatePath(this.path, 10, this.Radius, this.AngleThreshold).ToArray();
					this.lineRenderer.SetVertexCount(array.Length);
					this.lineRenderer.SetPositions(array);
					this.TargetAimDecal.gameObject.SetActive(true);
					this.TargetAimDecal.gameObject.transform.position = navMeshPath.corners[num - 1] + this.decalOffset;
					return;
				}
			}
			this.TargetAimDecal.gameObject.SetActive(false);
		}

		// Token: 0x06000FC9 RID: 4041 RVA: 0x00067924 File Offset: 0x00065B24
		private void OnDrawGizmos()
		{
			if (this.DrawGizmos)
			{
				Gizmos.color = Color.red;
				foreach (Vector3 vector in this.path)
				{
					Gizmos.DrawSphere(vector, 0.05f);
				}
			}
		}

		// Token: 0x04000EA7 RID: 3751
		public float PathThickness = 1f;

		// Token: 0x04000EA8 RID: 3752
		[Tooltip("Distance from the ground.")]
		public float NormalPathOffset;

		// Token: 0x04000EA9 RID: 3753
		[Tooltip("Max radius between segments.")]
		[Range(0.001f, 0.5f)]
		public float Radius = 0.25f;

		// Token: 0x04000EAA RID: 3754
		[Tooltip("Discard segments when their angle is smaller than this value.")]
		public float AngleThreshold = 5f;

		// Token: 0x04000EAB RID: 3755
		public bool DrawGizmos;

		// Token: 0x04000EAC RID: 3756
		public EasyDecal TargetAimDecal;

		// Token: 0x04000EAD RID: 3757
		public GameObject TargetPointDecalPrefab;

		// Token: 0x04000EAE RID: 3758
		private List<Vector3> path = new List<Vector3>();

		// Token: 0x04000EAF RID: 3759
		private NavMeshAgent agent;

		// Token: 0x04000EB0 RID: 3760
		private LineRenderer lineRenderer;

		// Token: 0x04000EB1 RID: 3761
		private Vector3 decalOffset = Vector3.up * 0.5f;

		// Token: 0x04000EB2 RID: 3762
		private const int MAXDISTANCE = 50;
	}
}

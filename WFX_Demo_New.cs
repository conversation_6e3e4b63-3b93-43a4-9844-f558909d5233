﻿using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

// Token: 0x02000165 RID: 357
public class WFX_Demo_New : MonoBehaviour
{
	// Token: 0x06000F2C RID: 3884 RVA: 0x000641F8 File Offset: 0x000623F8
	private void Awake()
	{
		List<GameObject> list = new List<GameObject>();
		int childCount = base.transform.childCount;
		for (int i = 0; i < childCount; i++)
		{
			GameObject gameObject = base.transform.GetChild(i).gameObject;
			list.Add(gameObject);
		}
		list.AddRange(this.AdditionalEffects);
		this.ParticleExamples = list.ToArray();
		this.defaultCamPosition = Camera.main.transform.position;
		this.defaultCamRotation = Camera.main.transform.rotation;
		base.StartCoroutine("CheckForDeletedParticles");
		this.UpdateUI();
	}

	// Token: 0x06000F2D RID: 3885 RVA: 0x00064290 File Offset: 0x00062490
	private void Update()
	{
		if (Input.GetKeyDown(KeyCode.LeftArrow))
		{
			this.prevParticle();
		}
		else if (Input.GetKeyDown(KeyCode.RightArrow))
		{
			this.nextParticle();
		}
		else if (Input.GetKeyDown(KeyCode.Delete))
		{
			this.destroyParticles();
		}
		if (Input.GetMouseButtonDown(0))
		{
			RaycastHit raycastHit = default(RaycastHit);
			if (this.groundCollider.Raycast(Camera.main.ScreenPointToRay(Input.mousePosition), out raycastHit, 9999f))
			{
				GameObject gameObject = this.spawnParticle();
				if (!gameObject.name.StartsWith("WFX_MF"))
				{
					gameObject.transform.position = raycastHit.point + gameObject.transform.position;
				}
			}
		}
		float axis = Input.GetAxis("Mouse ScrollWheel");
		if (axis != 0f)
		{
			Camera.main.transform.Translate(Vector3.forward * ((axis < 0f) ? (-1f) : 1f), Space.Self);
		}
		if (Input.GetMouseButtonDown(2))
		{
			Camera.main.transform.position = this.defaultCamPosition;
			Camera.main.transform.rotation = this.defaultCamRotation;
		}
	}

	// Token: 0x06000F2E RID: 3886 RVA: 0x000643B8 File Offset: 0x000625B8
	public void OnToggleGround()
	{
		Color white = Color.white;
		this.groundRenderer.enabled = !this.groundRenderer.enabled;
		white.a = (this.groundRenderer.enabled ? 1f : 0.33f);
		this.groundBtn.color = white;
		this.groundLabel.color = white;
	}

	// Token: 0x06000F2F RID: 3887 RVA: 0x0006441C File Offset: 0x0006261C
	public void OnToggleCamera()
	{
		Color white = Color.white;
		CFX_Demo_RotateCamera.rotating = !CFX_Demo_RotateCamera.rotating;
		white.a = (CFX_Demo_RotateCamera.rotating ? 1f : 0.33f);
		this.camRotBtn.color = white;
		this.camRotLabel.color = white;
	}

	// Token: 0x06000F30 RID: 3888 RVA: 0x00064470 File Offset: 0x00062670
	public void OnToggleSlowMo()
	{
		Color white = Color.white;
		this.slowMo = !this.slowMo;
		if (this.slowMo)
		{
			Time.timeScale = 0.33f;
			white.a = 1f;
		}
		else
		{
			Time.timeScale = 1f;
			white.a = 0.33f;
		}
		this.slowMoBtn.color = white;
		this.slowMoLabel.color = white;
	}

	// Token: 0x06000F31 RID: 3889 RVA: 0x000644E0 File Offset: 0x000626E0
	public void OnPreviousEffect()
	{
		this.prevParticle();
	}

	// Token: 0x06000F32 RID: 3890 RVA: 0x000644E8 File Offset: 0x000626E8
	public void OnNextEffect()
	{
		this.nextParticle();
	}

	// Token: 0x06000F33 RID: 3891 RVA: 0x000644F0 File Offset: 0x000626F0
	private void UpdateUI()
	{
		this.EffectLabel.text = this.ParticleExamples[this.exampleIndex].name;
		this.EffectIndexLabel.text = string.Format("{0}/{1}", (this.exampleIndex + 1).ToString("00"), this.ParticleExamples.Length.ToString("00"));
	}

	// Token: 0x06000F34 RID: 3892 RVA: 0x0006455C File Offset: 0x0006275C
	public GameObject spawnParticle()
	{
		GameObject gameObject = global::UnityEngine.Object.Instantiate<GameObject>(this.ParticleExamples[this.exampleIndex]);
		gameObject.transform.position = new Vector3(0f, gameObject.transform.position.y, 0f);
		gameObject.SetActive(true);
		if (gameObject.name.StartsWith("WFX_MF"))
		{
			gameObject.transform.parent = this.ParticleExamples[this.exampleIndex].transform.parent;
			gameObject.transform.localPosition = this.ParticleExamples[this.exampleIndex].transform.localPosition;
			gameObject.transform.localRotation = this.ParticleExamples[this.exampleIndex].transform.localRotation;
		}
		else if (gameObject.name.Contains("Hole"))
		{
			gameObject.transform.parent = this.bulletholes.transform;
		}
		ParticleSystem component = gameObject.GetComponent<ParticleSystem>();
		if (component != null && component.main.loop)
		{
			component.gameObject.AddComponent<CFX_AutoStopLoopedEffect>();
			component.gameObject.AddComponent<CFX_AutoDestructShuriken>();
		}
		this.onScreenParticles.Add(gameObject);
		return gameObject;
	}

	// Token: 0x06000F35 RID: 3893 RVA: 0x00064694 File Offset: 0x00062894
	private IEnumerator CheckForDeletedParticles()
	{
		for (;;)
		{
			yield return new WaitForSeconds(5f);
			for (int i = this.onScreenParticles.Count - 1; i >= 0; i--)
			{
				if (this.onScreenParticles[i] == null)
				{
					this.onScreenParticles.RemoveAt(i);
				}
			}
		}
		yield break;
	}

	// Token: 0x06000F36 RID: 3894 RVA: 0x000646A3 File Offset: 0x000628A3
	private void prevParticle()
	{
		this.exampleIndex--;
		if (this.exampleIndex < 0)
		{
			this.exampleIndex = this.ParticleExamples.Length - 1;
		}
		this.UpdateUI();
		this.showHideStuff();
	}

	// Token: 0x06000F37 RID: 3895 RVA: 0x000646D8 File Offset: 0x000628D8
	private void nextParticle()
	{
		this.exampleIndex++;
		if (this.exampleIndex >= this.ParticleExamples.Length)
		{
			this.exampleIndex = 0;
		}
		this.UpdateUI();
		this.showHideStuff();
	}

	// Token: 0x06000F38 RID: 3896 RVA: 0x0006470C File Offset: 0x0006290C
	private void destroyParticles()
	{
		for (int i = this.onScreenParticles.Count - 1; i >= 0; i--)
		{
			if (this.onScreenParticles[i] != null)
			{
				global::UnityEngine.Object.Destroy(this.onScreenParticles[i]);
			}
			this.onScreenParticles.RemoveAt(i);
		}
	}

	// Token: 0x06000F39 RID: 3897 RVA: 0x00064764 File Offset: 0x00062964
	private void prevTexture()
	{
		int num = this.groundTextures.IndexOf(this.groundTextureStr);
		num--;
		if (num < 0)
		{
			num = this.groundTextures.Count - 1;
		}
		this.groundTextureStr = this.groundTextures[num];
		this.selectMaterial();
	}

	// Token: 0x06000F3A RID: 3898 RVA: 0x000647B4 File Offset: 0x000629B4
	private void nextTexture()
	{
		int num = this.groundTextures.IndexOf(this.groundTextureStr);
		num++;
		if (num >= this.groundTextures.Count)
		{
			num = 0;
		}
		this.groundTextureStr = this.groundTextures[num];
		this.selectMaterial();
	}

	// Token: 0x06000F3B RID: 3899 RVA: 0x00064800 File Offset: 0x00062A00
	private void selectMaterial()
	{
		string text = this.groundTextureStr;
		if (text == "Concrete")
		{
			this.ground.GetComponent<Renderer>().material = this.concrete;
			this.walls.transform.GetChild(0).GetComponent<Renderer>().material = this.concreteWall;
			this.walls.transform.GetChild(1).GetComponent<Renderer>().material = this.concreteWall;
			return;
		}
		if (text == "Wood")
		{
			this.ground.GetComponent<Renderer>().material = this.wood;
			this.walls.transform.GetChild(0).GetComponent<Renderer>().material = this.woodWall;
			this.walls.transform.GetChild(1).GetComponent<Renderer>().material = this.woodWall;
			return;
		}
		if (text == "Metal")
		{
			this.ground.GetComponent<Renderer>().material = this.metal;
			this.walls.transform.GetChild(0).GetComponent<Renderer>().material = this.metalWall;
			this.walls.transform.GetChild(1).GetComponent<Renderer>().material = this.metalWall;
			return;
		}
		if (!(text == "Checker"))
		{
			return;
		}
		this.ground.GetComponent<Renderer>().material = this.checker;
		this.walls.transform.GetChild(0).GetComponent<Renderer>().material = this.checkerWall;
		this.walls.transform.GetChild(1).GetComponent<Renderer>().material = this.checkerWall;
	}

	// Token: 0x06000F3C RID: 3900 RVA: 0x000649B4 File Offset: 0x00062BB4
	private void showHideStuff()
	{
		if (this.ParticleExamples[this.exampleIndex].name.StartsWith("WFX_MF Spr"))
		{
			this.m4.GetComponent<Renderer>().enabled = true;
			Camera.main.transform.position = new Vector3(-2.482457f, 3.263842f, -0.004924395f);
			Camera.main.transform.eulerAngles = new Vector3(20f, 90f, 0f);
		}
		else
		{
			this.m4.GetComponent<Renderer>().enabled = false;
		}
		if (this.ParticleExamples[this.exampleIndex].name.StartsWith("WFX_MF FPS"))
		{
			this.m4fps.GetComponent<Renderer>().enabled = true;
		}
		else
		{
			this.m4fps.GetComponent<Renderer>().enabled = false;
		}
		if (this.ParticleExamples[this.exampleIndex].name.StartsWith("WFX_BImpact"))
		{
			this.walls.SetActive(true);
			Renderer[] array = this.bulletholes.GetComponentsInChildren<Renderer>();
			for (int i = 0; i < array.Length; i++)
			{
				array[i].enabled = true;
			}
		}
		else
		{
			this.walls.SetActive(false);
			Renderer[] array = this.bulletholes.GetComponentsInChildren<Renderer>();
			for (int i = 0; i < array.Length; i++)
			{
				array[i].enabled = false;
			}
		}
		if (this.ParticleExamples[this.exampleIndex].name.Contains("Wood"))
		{
			this.groundTextureStr = "Wood";
			this.selectMaterial();
			return;
		}
		if (this.ParticleExamples[this.exampleIndex].name.Contains("Concrete"))
		{
			this.groundTextureStr = "Concrete";
			this.selectMaterial();
			return;
		}
		if (this.ParticleExamples[this.exampleIndex].name.Contains("Metal"))
		{
			this.groundTextureStr = "Metal";
			this.selectMaterial();
			return;
		}
		if (this.ParticleExamples[this.exampleIndex].name.Contains("Dirt") || this.ParticleExamples[this.exampleIndex].name.Contains("Sand") || this.ParticleExamples[this.exampleIndex].name.Contains("SoftBody"))
		{
			this.groundTextureStr = "Checker";
			this.selectMaterial();
			return;
		}
		if (this.ParticleExamples[this.exampleIndex].name == "WFX_Explosion")
		{
			this.groundTextureStr = "Checker";
			this.selectMaterial();
		}
	}

	// Token: 0x04000DF0 RID: 3568
	public Renderer groundRenderer;

	// Token: 0x04000DF1 RID: 3569
	public Collider groundCollider;

	// Token: 0x04000DF2 RID: 3570
	[Space]
	[Space]
	public Image slowMoBtn;

	// Token: 0x04000DF3 RID: 3571
	public Text slowMoLabel;

	// Token: 0x04000DF4 RID: 3572
	public Image camRotBtn;

	// Token: 0x04000DF5 RID: 3573
	public Text camRotLabel;

	// Token: 0x04000DF6 RID: 3574
	public Image groundBtn;

	// Token: 0x04000DF7 RID: 3575
	public Text groundLabel;

	// Token: 0x04000DF8 RID: 3576
	[Space]
	public Text EffectLabel;

	// Token: 0x04000DF9 RID: 3577
	public Text EffectIndexLabel;

	// Token: 0x04000DFA RID: 3578
	public GameObject[] AdditionalEffects;

	// Token: 0x04000DFB RID: 3579
	public GameObject ground;

	// Token: 0x04000DFC RID: 3580
	public GameObject walls;

	// Token: 0x04000DFD RID: 3581
	public GameObject bulletholes;

	// Token: 0x04000DFE RID: 3582
	public GameObject m4;

	// Token: 0x04000DFF RID: 3583
	public GameObject m4fps;

	// Token: 0x04000E00 RID: 3584
	public Material wood;

	// Token: 0x04000E01 RID: 3585
	public Material concrete;

	// Token: 0x04000E02 RID: 3586
	public Material metal;

	// Token: 0x04000E03 RID: 3587
	public Material checker;

	// Token: 0x04000E04 RID: 3588
	public Material woodWall;

	// Token: 0x04000E05 RID: 3589
	public Material concreteWall;

	// Token: 0x04000E06 RID: 3590
	public Material metalWall;

	// Token: 0x04000E07 RID: 3591
	public Material checkerWall;

	// Token: 0x04000E08 RID: 3592
	private string groundTextureStr = "Checker";

	// Token: 0x04000E09 RID: 3593
	private List<string> groundTextures = new List<string>(new string[] { "Concrete", "Wood", "Metal", "Checker" });

	// Token: 0x04000E0A RID: 3594
	private GameObject[] ParticleExamples;

	// Token: 0x04000E0B RID: 3595
	private int exampleIndex;

	// Token: 0x04000E0C RID: 3596
	private bool slowMo;

	// Token: 0x04000E0D RID: 3597
	private Vector3 defaultCamPosition;

	// Token: 0x04000E0E RID: 3598
	private Quaternion defaultCamRotation;

	// Token: 0x04000E0F RID: 3599
	private List<GameObject> onScreenParticles = new List<GameObject>();
}

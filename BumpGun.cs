﻿using System;
using FishNet;
using FishNet.Connection;
using FishNet.Managing;
using FishNet.Object;
using FishNet.Object.Delegating;
using FishNet.Serializing;
using FishNet.Transporting;
using UnityEngine;

// Token: 0x02000088 RID: 136
public class BumpGun : Weapon
{
	// Token: 0x06000655 RID: 1621 RVA: 0x0002A2A0 File Offset: 0x000284A0
	private void Update()
	{
		base.WeaponUpdate();
		if (this.fireTimer > 0f)
		{
			this.fireTimer -= Time.deltaTime;
		}
		if (base.gameObject.layer == 7)
		{
			return;
		}
		if (!this.onePressShoot)
		{
			if (this.fire1.ReadValue<float>() > 0.1f && this.inRightHand)
			{
				this.Fire();
			}
			if (this.fire2.ReadValue<float>() > 0.1f && this.inLeftHand)
			{
				this.Fire();
			}
		}
		else
		{
			if (this.fire1.ReadValue<float>() < 0.1f && this.inRightHand)
			{
				this.isClicked = true;
			}
			if (this.fire2.ReadValue<float>() < 0.1f && this.inLeftHand)
			{
				this.isClicked = true;
			}
		}
		if (this.isClicked && this.onePressShoot)
		{
			if (this.fire1.ReadValue<float>() > 0.1f && this.inRightHand)
			{
				this.Fire();
				this.isClicked = false;
			}
			if (this.fire2.ReadValue<float>() > 0.1f && this.inLeftHand)
			{
				this.Fire();
				this.isClicked = false;
			}
		}
	}

	// Token: 0x06000656 RID: 1622 RVA: 0x0002A3CC File Offset: 0x000285CC
	private void Fire()
	{
		if (!this.playerController.IsOwner || !this.playerController.SyncAccessor_canMove)
		{
			return;
		}
		if (this.fireTimer > 0f)
		{
			return;
		}
		this.fireTimer = this.timeBetweenFire;
		if (base.SyncAccessor_currentAmmo <= 0)
		{
			this.audio.PlayOneShot(this.nobulletClip);
		}
		if (base.SyncAccessor_currentAmmo <= 0)
		{
			return;
		}
		base.sync___set_value_currentAmmo(base.SyncAccessor_currentAmmo - 1, true);
		Vector3 position = this.cam.transform.position;
		Vector3 forward = this.cam.transform.forward;
		this.SpawnProjectile(position, forward, 0f);
		this.ServerFire(position, forward, base.TimeManager.Tick);
		this.playerController.BForce(-this.cam.transform.forward, this.playerKnockback, true, false, 4f, true);
		base.CameraAnimation();
		base.WeaponAnimation();
	}

	// Token: 0x06000657 RID: 1623 RVA: 0x0002A4BD File Offset: 0x000286BD
	private void SpawnProjectile(Vector3 position, Vector3 direction, float passedTime)
	{
		global::UnityEngine.Object.Instantiate<BumpBullet>(this._projectile, position, Quaternion.identity).Initialize(direction, this.launchForce, passedTime, this.rootObject, base.gameObject);
	}

	// Token: 0x06000658 RID: 1624 RVA: 0x0002A4EC File Offset: 0x000286EC
	[ServerRpc(RunLocally = true)]
	private void ServerFire(Vector3 position, Vector3 direction, uint tick)
	{
		this.RpcWriter___Server_ServerFire_2754081237(position, direction, tick);
		this.RpcLogic___ServerFire_2754081237(position, direction, tick);
	}

	// Token: 0x06000659 RID: 1625 RVA: 0x0002A520 File Offset: 0x00028720
	[ObserversRpc(ExcludeOwner = true)]
	private void ObserversFire(Vector3 position, Vector3 direction, uint tick)
	{
		this.RpcWriter___Observers_ObserversFire_2754081237(position, direction, tick);
	}

	// Token: 0x0600065A RID: 1626 RVA: 0x0002A540 File Offset: 0x00028740
	[ObserversRpc(RunLocally = true, ExcludeOwner = true)]
	private void ShootObserversEffect()
	{
		this.RpcWriter___Observers_ShootObserversEffect_2166136261();
		this.RpcLogic___ShootObserversEffect_2166136261();
	}

	// Token: 0x0600065B RID: 1627 RVA: 0x00026D1D File Offset: 0x00024F1D
	private void LocalSound(int index)
	{
		if (index == 0)
		{
			this.audio.PlayOneShot(this.headHitClip);
		}
	}

	// Token: 0x0600065D RID: 1629 RVA: 0x0002A578 File Offset: 0x00028778
	public override void NetworkInitialize___Early()
	{
		if (this.NetworkInitializeEarly_BumpGun_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeEarly_BumpGun_Assembly-CSharp.dll = true;
		base.NetworkInitialize___Early();
		base.RegisterServerRpc(15U, new ServerRpcDelegate(this.RpcReader___Server_ServerFire_2754081237));
		base.RegisterObserversRpc(16U, new ClientRpcDelegate(this.RpcReader___Observers_ObserversFire_2754081237));
		base.RegisterObserversRpc(17U, new ClientRpcDelegate(this.RpcReader___Observers_ShootObserversEffect_2166136261));
	}

	// Token: 0x0600065E RID: 1630 RVA: 0x0002A5E1 File Offset: 0x000287E1
	public override void NetworkInitialize__Late()
	{
		if (this.NetworkInitializeLate_BumpGun_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeLate_BumpGun_Assembly-CSharp.dll = true;
		base.NetworkInitialize__Late();
	}

	// Token: 0x0600065F RID: 1631 RVA: 0x0002A5FA File Offset: 0x000287FA
	public override void NetworkInitializeIfDisabled()
	{
		this.NetworkInitialize___Early();
		this.NetworkInitialize__Late();
	}

	// Token: 0x06000660 RID: 1632 RVA: 0x0002A608 File Offset: 0x00028808
	private void RpcWriter___Server_ServerFire_2754081237(Vector3 position, Vector3 direction, uint tick)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteVector3(position);
		writer.WriteVector3(direction);
		writer.WriteUInt32(tick, AutoPackType.Packed);
		base.SendServerRpc(15U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000661 RID: 1633 RVA: 0x0002A728 File Offset: 0x00028928
	private void RpcLogic___ServerFire_2754081237(Vector3 position, Vector3 direction, uint tick)
	{
		float num = (float)base.TimeManager.TimePassed(tick, false);
		num = Mathf.Min(0.15f, num);
		this.ShootObserversEffect();
		if (!base.IsServer)
		{
			this.SpawnProjectile(position, direction, num);
		}
		this.ObserversFire(position, direction, tick);
	}

	// Token: 0x06000662 RID: 1634 RVA: 0x0002A770 File Offset: 0x00028970
	private void RpcReader___Server_ServerFire_2754081237(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		Vector3 vector = PooledReader0.ReadVector3();
		Vector3 vector2 = PooledReader0.ReadVector3();
		uint num = PooledReader0.ReadUInt32(AutoPackType.Packed);
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___ServerFire_2754081237(vector, vector2, num);
	}

	// Token: 0x06000663 RID: 1635 RVA: 0x0002A7E8 File Offset: 0x000289E8
	private void RpcWriter___Observers_ObserversFire_2754081237(Vector3 position, Vector3 direction, uint tick)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteVector3(position);
		writer.WriteVector3(direction);
		writer.WriteUInt32(tick, AutoPackType.Packed);
		base.SendObserversRpc(16U, writer, channel, DataOrderType.Default, false, false, true);
		writer.Store();
	}

	// Token: 0x06000664 RID: 1636 RVA: 0x0002A8C0 File Offset: 0x00028AC0
	private void RpcLogic___ObserversFire_2754081237(Vector3 position, Vector3 direction, uint tick)
	{
		float num = (float)base.TimeManager.TimePassed(tick, false);
		num = Mathf.Min(0.3f, num);
		this.SpawnProjectile(position, direction, num);
	}

	// Token: 0x06000665 RID: 1637 RVA: 0x0002A8F4 File Offset: 0x00028AF4
	private void RpcReader___Observers_ObserversFire_2754081237(PooledReader PooledReader0, Channel channel)
	{
		Vector3 vector = PooledReader0.ReadVector3();
		Vector3 vector2 = PooledReader0.ReadVector3();
		uint num = PooledReader0.ReadUInt32(AutoPackType.Packed);
		if (!base.IsClient)
		{
			return;
		}
		this.RpcLogic___ObserversFire_2754081237(vector, vector2, num);
	}

	// Token: 0x06000666 RID: 1638 RVA: 0x0002A94C File Offset: 0x00028B4C
	private void RpcWriter___Observers_ShootObserversEffect_2166136261()
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendObserversRpc(17U, writer, channel, DataOrderType.Default, false, false, true);
		writer.Store();
	}

	// Token: 0x06000667 RID: 1639 RVA: 0x0002A9F8 File Offset: 0x00028BF8
	private void RpcLogic___ShootObserversEffect_2166136261()
	{
		if (Settings.Instance.reduceVFX)
		{
			return;
		}
		this.audio.PlayOneShot(this.fireClip);
		global::UnityEngine.Object.Instantiate<GameObject>(this.muzzleFlash, this.muzzleFlashPoint.position, this.shootPoint.rotation).GetComponent<ParticleSystem>().Play();
	}

	// Token: 0x06000668 RID: 1640 RVA: 0x0002AA50 File Offset: 0x00028C50
	private void RpcReader___Observers_ShootObserversEffect_2166136261(PooledReader PooledReader0, Channel channel)
	{
		if (!base.IsClient)
		{
			return;
		}
		if (base.IsHost)
		{
			return;
		}
		this.RpcLogic___ShootObserversEffect_2166136261();
	}

	// Token: 0x06000669 RID: 1641 RVA: 0x0002AA7A File Offset: 0x00028C7A
	public override void Awake()
	{
		this.NetworkInitialize___Early();
		base.Awake();
		this.NetworkInitialize__Late();
	}

	// Token: 0x0600066A RID: 1642 RVA: 0x000023D6 File Offset: 0x000005D6
	public override void Awake___UserLogic()
	{
	}

	// Token: 0x04000649 RID: 1609
	[Header("Weapon Specials")]
	[SerializeField]
	private float launchForce = 12f;

	// Token: 0x0400064A RID: 1610
	[SerializeField]
	private float playerKnockback = 2f;

	// Token: 0x0400064B RID: 1611
	[SerializeField]
	private BumpBullet _projectile;

	// Token: 0x0400064C RID: 1612
	private const float MAX_PASSED_TIME = 0.3f;

	// Token: 0x0400064D RID: 1613
	private float fireTimer;

	// Token: 0x0400064E RID: 1614
	private bool NetworkInitializeEarly_BumpGun_Assembly-CSharp.dll;

	// Token: 0x0400064F RID: 1615
	private bool NetworkInitializeLate_BumpGun_Assembly-CSharp.dll;
}

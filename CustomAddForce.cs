﻿using System;
using UnityEngine;

// Token: 0x0200001F RID: 31
public class CustomAddForce : MonoBehaviour
{
	// Token: 0x060000E9 RID: 233 RVA: 0x000067E4 File Offset: 0x000049E4
	private void Start()
	{
		this.character = base.GetComponent<CharacterController>();
		this.controller = base.GetComponent<FirstPersonController>();
	}

	// Token: 0x060000EA RID: 234 RVA: 0x00006800 File Offset: 0x00004A00
	private void Update()
	{
		if (this.impact.magnitude > 0.2f)
		{
			this.controller.customForceFinal = this.impact;
		}
		else
		{
			this.controller.customForceFinal = Vector3.zero;
		}
		if (this.controller.isGrounded)
		{
			this.impact.y = 0f;
		}
		this.impact = Vector3.Lerp(this.impact, Vector3.zero, ((!this.controller.isGrounded) ? this.airdeceleration : this.deceleration) * Time.deltaTime);
	}

	// Token: 0x060000EB RID: 235 RVA: 0x00006896 File Offset: 0x00004A96
	public void AddForce(Vector3 dir, float force)
	{
		dir.Normalize();
		this.impact += dir.normalized * force / this.mass;
	}

	// Token: 0x040000D5 RID: 213
	[SerializeField]
	private float mass = 3f;

	// Token: 0x040000D6 RID: 214
	private Vector3 impact = Vector3.zero;

	// Token: 0x040000D7 RID: 215
	private CharacterController character;

	// Token: 0x040000D8 RID: 216
	private FirstPersonController controller;

	// Token: 0x040000D9 RID: 217
	[SerializeField]
	private float airdeceleration = 0.5f;

	// Token: 0x040000DA RID: 218
	[SerializeField]
	private float deceleration = 5f;
}

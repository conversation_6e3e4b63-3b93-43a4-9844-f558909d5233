﻿using System;
using FishNet.Object;
using UnityEngine;

// Token: 0x0200006B RID: 107
public class PropDamage : NetworkBehaviour
{
	// Token: 0x060004B6 RID: 1206 RVA: 0x00020384 File Offset: 0x0001E584
	public void Damage()
	{
		this.index++;
		if (this.index >= this.states.Length)
		{
			return;
		}
		base.GetComponent<AudioSource>().PlayOneShot(this.hitClips[Mathf.RoundToInt((float)global::UnityEngine.Random.Range(0, this.hitClips.Length))]);
		this.states[this.index - 1].SetActive(false);
		this.states[this.index].SetActive(true);
	}

	// Token: 0x060004B8 RID: 1208 RVA: 0x000203FE File Offset: 0x0001E5FE
	public virtual void NetworkInitialize___Early()
	{
		if (this.NetworkInitializeEarly_PropDamage_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeEarly_PropDamage_Assembly-CSharp.dll = true;
	}

	// Token: 0x060004B9 RID: 1209 RVA: 0x00020411 File Offset: 0x0001E611
	public virtual void NetworkInitialize__Late()
	{
		if (this.NetworkInitializeLate_PropDamage_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeLate_PropDamage_Assembly-CSharp.dll = true;
	}

	// Token: 0x060004BA RID: 1210 RVA: 0x00020424 File Offset: 0x0001E624
	public override void NetworkInitializeIfDisabled()
	{
		this.NetworkInitialize___Early();
		this.NetworkInitialize__Late();
	}

	// Token: 0x060004BB RID: 1211 RVA: 0x00020424 File Offset: 0x0001E624
	public virtual void Awake()
	{
		this.NetworkInitialize___Early();
		this.NetworkInitialize__Late();
	}

	// Token: 0x060004BC RID: 1212 RVA: 0x000023D6 File Offset: 0x000005D6
	public virtual void Awake___UserLogic()
	{
	}

	// Token: 0x040004CE RID: 1230
	[SerializeField]
	private GameObject[] states;

	// Token: 0x040004CF RID: 1231
	[SerializeField]
	private AudioClip[] hitClips;

	// Token: 0x040004D0 RID: 1232
	private int index;

	// Token: 0x040004D1 RID: 1233
	private bool NetworkInitializeEarly_PropDamage_Assembly-CSharp.dll;

	// Token: 0x040004D2 RID: 1234
	private bool NetworkInitializeLate_PropDamage_Assembly-CSharp.dll;
}

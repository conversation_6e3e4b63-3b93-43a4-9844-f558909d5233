﻿using System;
using UnityEngine;

// Token: 0x0200015B RID: 347
[RequireComponent(typeof(CharacterController))]
public class SimpleCharacterMotor : MonoBehaviour
{
	// Token: 0x06000F05 RID: 3845 RVA: 0x000630A0 File Offset: 0x000612A0
	private void Awake()
	{
		this.controller = base.GetComponent<CharacterController>();
		Cursor.lockState = this.cursorLockMode;
		Cursor.visible = this.cursorVisible;
		this.targetRotation = (this.targetPivotRotation = Quaternion.identity);
	}

	// Token: 0x06000F06 RID: 3846 RVA: 0x000630E3 File Offset: 0x000612E3
	private void Update()
	{
		this.UpdateTranslation();
		this.UpdateLookRotation();
	}

	// Token: 0x06000F07 RID: 3847 RVA: 0x000630F4 File Offset: 0x000612F4
	private void UpdateLookRotation()
	{
		float num = Input.GetAxis("Mouse Y");
		float axis = Input.GetAxis("Mouse X");
		num *= (float)(this.invertY ? (-1) : 1);
		this.targetRotation = base.transform.localRotation * Quaternion.AngleAxis(axis * this.lookSpeed * Time.deltaTime, Vector3.up);
		this.targetPivotRotation = this.cameraPivot.localRotation * Quaternion.AngleAxis(num * this.lookSpeed * Time.deltaTime, Vector3.right);
		base.transform.localRotation = this.targetRotation;
		this.cameraPivot.localRotation = this.targetPivotRotation;
	}

	// Token: 0x06000F08 RID: 3848 RVA: 0x000631A8 File Offset: 0x000613A8
	private void UpdateTranslation()
	{
		if (this.controller.isGrounded)
		{
			float axis = Input.GetAxis("Horizontal");
			float axis2 = Input.GetAxis("Vertical");
			bool key = Input.GetKey(KeyCode.LeftShift);
			Vector3 vector = new Vector3(axis, 0f, axis2);
			this.speed = (key ? this.runSpeed : this.walkSpeed);
			this.movement = base.transform.TransformDirection(vector * this.speed);
		}
		else
		{
			this.movement.y = this.movement.y - this.gravity * Time.deltaTime;
		}
		this.finalMovement = Vector3.Lerp(this.finalMovement, this.movement, Time.deltaTime * this.movementAcceleration);
		this.controller.Move(this.finalMovement * Time.deltaTime);
	}

	// Token: 0x04000DAF RID: 3503
	public CursorLockMode cursorLockMode = CursorLockMode.Locked;

	// Token: 0x04000DB0 RID: 3504
	public bool cursorVisible;

	// Token: 0x04000DB1 RID: 3505
	[Header("Movement")]
	public float walkSpeed = 2f;

	// Token: 0x04000DB2 RID: 3506
	public float runSpeed = 4f;

	// Token: 0x04000DB3 RID: 3507
	public float gravity = 9.8f;

	// Token: 0x04000DB4 RID: 3508
	[Space]
	[Header("Look")]
	public Transform cameraPivot;

	// Token: 0x04000DB5 RID: 3509
	public float lookSpeed = 45f;

	// Token: 0x04000DB6 RID: 3510
	public bool invertY = true;

	// Token: 0x04000DB7 RID: 3511
	[Space]
	[Header("Smoothing")]
	public float movementAcceleration = 1f;

	// Token: 0x04000DB8 RID: 3512
	private CharacterController controller;

	// Token: 0x04000DB9 RID: 3513
	private Vector3 movement;

	// Token: 0x04000DBA RID: 3514
	private Vector3 finalMovement;

	// Token: 0x04000DBB RID: 3515
	private float speed;

	// Token: 0x04000DBC RID: 3516
	private Quaternion targetRotation;

	// Token: 0x04000DBD RID: 3517
	private Quaternion targetPivotRotation;
}

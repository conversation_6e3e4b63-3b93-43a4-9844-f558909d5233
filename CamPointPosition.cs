﻿using System;
using UnityEngine;

// Token: 0x0200001E RID: 30
public class CamPointPosition : MonoBehaviour
{
	// Token: 0x060000E6 RID: 230 RVA: 0x000023D6 File Offset: 0x000005D6
	private void Start()
	{
	}

	// Token: 0x060000E7 RID: 231 RVA: 0x000067A8 File Offset: 0x000049A8
	private void Update()
	{
		base.transform.position = this.cam.transform.position + this.cam.transform.forward * 2f;
	}

	// Token: 0x040000D4 RID: 212
	[SerializeField]
	private Transform cam;
}

﻿using System;
using System.Collections;
using DG.Tweening;
using DG.Tweening.Core;
using DG.Tweening.Plugins.Options;
using FishNet;
using FishNet.Connection;
using FishNet.Managing;
using FishNet.Object;
using FishNet.Object.Delegating;
using FishNet.Object.Synchronizing;
using FishNet.Serializing;
using FishNet.Transporting;
using TMPro;
using UnityEngine;
using UnityEngine.InputSystem;

// Token: 0x02000081 RID: 129
public class Weapon : NetworkBehaviour
{
	// Token: 0x0600056C RID: 1388 RVA: 0x000236B0 File Offset: 0x000218B0
	public virtual void Awake()
	{
		this.NetworkInitialize___Early();
		this.Awake___UserLogic();
		this.NetworkInitialize__Late();
	}

	// Token: 0x0600056D RID: 1389 RVA: 0x000236CF File Offset: 0x000218CF
	private void OnDisable()
	{
		base.transform.DOKill(false);
	}

	// Token: 0x0600056E RID: 1390 RVA: 0x000236E0 File Offset: 0x000218E0
	public void WeaponUpdate()
	{
		this.rootObject = this.behaviour.rootObject;
		this.lastPlayerHolder = this.behaviour.lastPlayerHolder;
		this.playerController = this.behaviour.playerController;
		this.cam = this.behaviour.cam;
		this.caseSoundTimer -= Time.deltaTime;
		if (this.SyncAccessor_currentAmmo <= 0)
		{
			this.cantTakeSafeBool = true;
		}
		if (this.SyncAccessor_currentAmmo <= 0 && this.SyncAccessor_currentAmmo > -100 && base.gameObject.layer == 7)
		{
			base.Invoke("DespawnObject", 0.65f);
			this.sync___set_value_currentAmmo(-103, true);
		}
		else if (this.heldOnce && this.lastPlayerHolder == null)
		{
			this.heldOnce = false;
			base.gameObject.SetActive(false);
			this.DespawnObjectServer();
		}
		else if (this.inHandDespawn && this.SyncAccessor_currentAmmo <= 0 && this.SyncAccessor_currentAmmo > -100 && base.gameObject.layer != 7)
		{
			if (this.inRightHand)
			{
				this.behaviour.playerPickup.RightHandDrop();
			}
			else if (this.inLeftHand)
			{
				this.behaviour.playerPickup.LeftHandDrop();
			}
			this.DespawnObjectServer();
			base.gameObject.SetActive(false);
			this.sync___set_value_currentAmmo(-103, true);
		}
		if (base.gameObject.layer == 7)
		{
			return;
		}
		this.fpArms.GetComponent<FPArms>().heavy = this.behaviour.heavy;
		this.fpArms.GetComponent<FPArms>().vertical = this.behaviour.vertical;
		if (this.inRightHand)
		{
			this.playerController.movementFactor = this.movementFactor;
			this.playerController.jumpFactor = this.jumpFactor;
			this.playerController.maxWallJumps = this.maxWallJumps;
			this.playerController.wallJumpFactor = this.wallJumpFactor;
		}
		if (this.playerController)
		{
			this.fire1 = this.playerController.fire1;
			this.fire2 = this.playerController.fire2;
			this.reload = this.playerController.reload;
			this.jump = this.playerController.jump;
		}
		if (base.IsOwner)
		{
			if (this.inRightHand && this.behaviour.playerPickup.SyncAccessor_objInHand != base.gameObject)
			{
				this.DespawnObject();
				base.gameObject.layer = 0;
				this.behaviour.playerPickup.HandsReconstruct();
			}
			if (this.inLeftHand && this.behaviour.playerPickup.SyncAccessor_objInLeftHand != base.gameObject)
			{
				this.DespawnObject();
				base.gameObject.layer = 0;
				this.behaviour.playerPickup.HandsReconstruct();
			}
		}
		if (this.rootObject.layer != 6)
		{
			this.SetLayerAllChildren(base.transform, 9);
		}
		if (this.elbowPivot)
		{
			this.elbowPivot.transform.localRotation = Quaternion.Slerp(this.elbowPivot.transform.localRotation, Quaternion.identity, this.foreArmAnimationSpeed * Time.deltaTime);
		}
		if (base.IsOwner)
		{
			this.pauseManager.ChangeAmmoText(this.needsAmmo ? Mathf.Clamp(this.SyncAccessor_currentAmmo, 0, this.SyncAccessor_currentAmmo).ToString() : "∞", this.reloadWeapon ? (this.chargedBullets.ToString() + " / ") : "", this.inRightHand);
		}
		if (base.IsOwner)
		{
			base.GetComponent<AudioSource>().spatialBlend = 0f;
		}
		else
		{
			base.GetComponent<AudioSource>().spatialBlend = 1f;
		}
		if (base.gameObject.layer == 8 && base.IsOwner)
		{
			this.invertFire = this.behaviour.playerPickup.SyncAccessor_hasObjectInHand && this.behaviour.playerPickup.SyncAccessor_hasObjectInLeftHand && Settings.Instance.inverseFireBinding;
		}
	}

	// Token: 0x0600056F RID: 1391 RVA: 0x00023AF0 File Offset: 0x00021CF0
	private void DespawnObject()
	{
		if (base.gameObject.layer == 8 || base.gameObject.layer == 9)
		{
			this.sync___set_value_currentAmmo(-50, true);
			return;
		}
		this.SpawnEffect(this.behaviour.depopVFX);
		this.DespawnObjectServer();
	}

	// Token: 0x06000570 RID: 1392 RVA: 0x00023B30 File Offset: 0x00021D30
	[ServerRpc(RequireOwnership = false)]
	private void DespawnObjectServer()
	{
		this.RpcWriter___Server_DespawnObjectServer_2166136261();
	}

	// Token: 0x06000571 RID: 1393 RVA: 0x00023B43 File Offset: 0x00021D43
	private void SpawnEffect(GameObject fx)
	{
		global::UnityEngine.Object.Instantiate<GameObject>(fx, base.transform.position, Quaternion.identity);
		base.gameObject.SetActive(false);
	}

	// Token: 0x06000572 RID: 1394 RVA: 0x00023B68 File Offset: 0x00021D68
	public void CameraAnimation()
	{
		base.transform.DOKill(false);
		this.camAnimScript.baseSpeed = this.cameraLerpSpeed;
		if (this.tween == null)
		{
			this.tween = this.cam.DOShakeRotation(this.duration, this.strength, this.vibrato, this.randomness, this.fadeOut, this.randomnessMode).SetEase(this.shakeEase);
			return;
		}
		this.tween = this.cam.DOShakeRotation(this.duration, this.strength, this.vibrato, this.randomness, this.fadeOut, this.randomnessMode).SetEase(this.shakeEase);
	}

	// Token: 0x06000573 RID: 1395 RVA: 0x00023C1C File Offset: 0x00021E1C
	private void OnShoot()
	{
		if (this.fireSlowDown)
		{
			this.playerController.SetSpeed(this.fireSlowDownFactor, this.fireSlowDownDuration);
		}
		if (this.EjectVFX)
		{
			GameObject gameObject = global::UnityEngine.Object.Instantiate<GameObject>(this.ejectCaseVfx, this.ejectCasePoint.position, Quaternion.identity);
			gameObject.transform.forward = -this.ejectCasePoint.right;
			if (this.caseSoundTimer > 0f)
			{
				float num = (float)global::UnityEngine.Random.Range(0, 100);
				gameObject.GetComponent<EjectedCaseScript>().shouldPlaySound = num > 60f;
			}
			else
			{
				gameObject.GetComponent<EjectedCaseScript>().shouldPlaySound = true;
			}
			gameObject.GetComponent<EjectedCaseScript>().ejectCaseIndex = this.ejectCaseIndex;
		}
		this.caseSoundTimer = 0.3f;
	}

	// Token: 0x06000574 RID: 1396 RVA: 0x00023CE4 File Offset: 0x00021EE4
	public void OnReload()
	{
		if (this.reloadEjectVfx)
		{
			GameObject gameObject = global::UnityEngine.Object.Instantiate<GameObject>(this.reloadEjectVfx, this.ejectCasePoint.position, Quaternion.identity);
			gameObject.transform.forward = -this.ejectCasePoint.right;
			if (this.caseSoundTimer > 0f)
			{
				float num = (float)global::UnityEngine.Random.Range(0, 100);
				gameObject.GetComponent<EjectedCaseScript>().shouldPlaySound = num > 60f;
			}
			else
			{
				gameObject.GetComponent<EjectedCaseScript>().shouldPlaySound = true;
			}
			gameObject.GetComponent<EjectedCaseScript>().ejectCaseIndex = this.ejectCaseIndex;
		}
		this.caseSoundTimer = 0.3f;
	}

	// Token: 0x06000575 RID: 1397 RVA: 0x00023D90 File Offset: 0x00021F90
	public void CameraRevolverAnimation()
	{
		base.transform.DOKill(false);
		this.camAnimScript.baseSpeed = this.cameraLerpSpeed;
		if (!this.akAnim)
		{
			if (this.tween == null)
			{
				this.tween = this.cam.transform.DOPunchRotation(-this.recoil, this.duration, this.vibrato, this.elasticity).SetEase(this.shakeEase);
				return;
			}
			this.tween = this.cam.transform.DOPunchRotation(-this.recoil, this.duration, this.vibrato, this.elasticity).SetEase(this.shakeEase);
			return;
		}
		else
		{
			if (this.tween == null)
			{
				this.tween = this.cam.transform.DOLocalRotate(this.cam.transform.localEulerAngles - this.recoil, this.duration, RotateMode.Fast).SetEase(this.shakeEase);
				return;
			}
			this.tween = this.cam.transform.DOLocalRotate(this.cam.transform.localEulerAngles - this.recoil, this.duration, RotateMode.Fast).SetEase(this.shakeEase);
			return;
		}
	}

	// Token: 0x06000576 RID: 1398 RVA: 0x00023EDC File Offset: 0x000220DC
	public void AltCameraRevolverAnimation()
	{
		base.transform.DOKill(false);
		this.camAnimScript.baseSpeed = this.cameraLerpSpeed;
		if (!this.akAnim)
		{
			if (this.tween == null)
			{
				this.tween = this.cam.transform.DOPunchRotation(-this.recoil * 3f, this.duration, this.vibrato, this.elasticity).SetEase(this.shakeEase);
				return;
			}
			this.tween = this.cam.transform.DOPunchRotation(-this.recoil * 3f, this.duration, this.vibrato, this.elasticity).SetEase(this.shakeEase);
			return;
		}
		else
		{
			if (this.tween == null)
			{
				this.tween = this.cam.transform.DOLocalRotate(this.cam.transform.localEulerAngles - this.recoil * 3f, this.duration, RotateMode.Fast).SetEase(this.shakeEase);
				return;
			}
			this.tween = this.cam.transform.DOLocalRotate(this.cam.transform.localEulerAngles - this.recoil * 3f, this.duration, RotateMode.Fast).SetEase(this.shakeEase);
			return;
		}
	}

	// Token: 0x06000577 RID: 1399 RVA: 0x00024050 File Offset: 0x00022250
	public void WeaponAnimation()
	{
		this.OnShoot();
		if (this.supplementAnimator)
		{
			this.animator.SetTrigger("Shoot");
		}
		if (this.instantComebackOnFire)
		{
			base.transform.DOKill(false);
			this.behaviour.InstantComeBackOnFire();
		}
		if (this.holdback)
		{
			base.transform.DOLocalMove(base.transform.localPosition - this.animationPunch, 0f, false);
			base.transform.DOLocalMove(base.transform.localPosition - this.animationPunch - base.transform.forward * 0.01f, this.animationDuration, false);
			return;
		}
		if (this.instantPush)
		{
			if (this.horizontalAnimation)
			{
				if (!this.playerController.isAiming && this.elbowPivot)
				{
					this.elbowPivot.transform.DOLocalRotate(-this.foreArmAnimationPunch, this.animationDuration, RotateMode.Fast);
				}
				base.transform.DOLocalMove(base.transform.localPosition - this.animationPunch, this.animationDuration, false);
				return;
			}
			if (this.requireBothHands)
			{
				this.fpArms.DOLocalRotate(this.fpArms.localEulerAngles - this.animationPunch, this.animationDuration, RotateMode.Fast);
				return;
			}
			if (!this.playerController.isAiming && this.elbowPivot)
			{
				this.elbowPivot.transform.DOLocalRotate(-this.foreArmAnimationPunch, this.animationDuration, RotateMode.Fast);
			}
			base.transform.DOLocalRotate(-this.animationPunch, this.animationDuration, RotateMode.Fast);
			return;
		}
		else
		{
			if (this.horizontalAnimation)
			{
				if (!this.playerController.isAiming && this.elbowPivot)
				{
					this.elbowPivot.transform.DOPunchRotation(-this.foreArmAnimationPunch, this.animationDuration, this.animationVibrato, this.animationElasticity);
				}
				base.transform.DOPunchPosition(-this.animationPunch, this.animationDuration, this.animationVibrato, this.animationElasticity, false);
				return;
			}
			if (this.requireBothHands)
			{
				this.fpArms.DOPunchRotation(-this.animationPunch, this.animationDuration, this.animationVibrato, this.animationElasticity);
				return;
			}
			if (!this.playerController.isAiming && this.elbowPivot)
			{
				this.elbowPivot.transform.DOPunchRotation(-this.foreArmAnimationPunch, this.animationDuration, this.animationVibrato, this.animationElasticity);
			}
			base.transform.DOPunchRotation(-this.animationPunch, this.animationDuration, this.animationVibrato, this.animationElasticity);
			return;
		}
	}

	// Token: 0x06000578 RID: 1400 RVA: 0x00024335 File Offset: 0x00022535
	private IEnumerator ComebackFromHoldbackAnimation()
	{
		yield return new WaitForSeconds(this.animationDuration);
		yield break;
	}

	// Token: 0x06000579 RID: 1401 RVA: 0x00024344 File Offset: 0x00022544
	public void KillShockWave()
	{
		this.playerController.lensDistortion.intensity.value = this.playerController.killShockWaveStrength;
		Settings.Instance.IncreaseKillsAmount();
		this.playerController.colorGrading.saturation.value = -100f;
	}

	// Token: 0x0600057A RID: 1402 RVA: 0x00024398 File Offset: 0x00022598
	public void TriggerEnvironment(GameObject obj, Vector3 hitPoint, Vector3 direction, Vector3 hitNormal)
	{
		if (obj.CompareTag("Mine") && obj.transform.root.GetComponent<ProximityMine>().canExplode)
		{
			obj.transform.root.GetComponent<ProximityMine>().ChangeState();
		}
		if (obj.CompareTag("Claymore") && obj.transform.root.GetComponent<Claymore>().canExplode)
		{
			obj.transform.root.GetComponent<Claymore>().ChangeState();
		}
		if (obj.CompareTag("Hat"))
		{
			obj.transform.SetParent(null);
			if (!obj.GetComponent<Rigidbody>())
			{
				obj.AddComponent<Rigidbody>();
			}
			Rigidbody component = obj.GetComponent<Rigidbody>();
			component.collisionDetectionMode = CollisionDetectionMode.ContinuousDynamic;
			component.drag = 0f;
			component.interpolation = RigidbodyInterpolation.Interpolate;
			component.AddForce(direction * 10f, ForceMode.Impulse);
			global::UnityEngine.Random.Range(-1, 1);
			component.AddTorque(this.cam.transform.forward * 40f + base.transform.right * 40f, ForceMode.Impulse);
		}
		if (obj.layer == LayerMask.NameToLayer("Ragdoll"))
		{
			Rigidbody[] componentsInChildren = obj.transform.root.GetComponentsInChildren<Rigidbody>();
			global::UnityEngine.Object.Instantiate<GameObject>(this.bodyImpact, hitPoint, Quaternion.LookRotation(hitNormal));
			global::UnityEngine.Object.Instantiate<GameObject>(this.bloodSplatter, hitPoint, Quaternion.LookRotation(hitNormal));
			Rigidbody[] array = componentsInChildren;
			for (int i = 0; i < array.Length; i++)
			{
				array[i].AddExplosionForce(this.ragdollEjectForce, hitPoint - direction, 100f, 1f, ForceMode.Impulse);
			}
		}
		if (obj.CompareTag("Grenade"))
		{
			this.CmdExplodeGrenade(obj);
		}
		if (obj.CompareTag("Pig"))
		{
			this.CmdKillPig(obj);
		}
		if (obj.CompareTag("DetachableObject"))
		{
			this.CmdDetachObject(obj, direction, hitNormal);
		}
		if (obj.CompareTag("NoSound"))
		{
			if (obj.GetComponent<Pot>() != null)
			{
				Settings.Instance.potsBroken += 1f;
				this.CmdBreakPot(obj);
			}
			if (obj.GetComponent<PropDamage>() != null)
			{
				this.CmdDamageProp(obj);
			}
			if (obj.GetComponent<Pigeon>() != null)
			{
				this.CmdKillBird(obj);
			}
		}
	}

	// Token: 0x0600057B RID: 1403 RVA: 0x000245D4 File Offset: 0x000227D4
	public void BreakGlassServer(Vector3 hitPoint, Vector3 direction, GameObject obj)
	{
		Settings.Instance.windowsBroken += 1f;
		this.CmdBreakGlassServer(hitPoint, direction, obj);
	}

	// Token: 0x0600057C RID: 1404 RVA: 0x000245F5 File Offset: 0x000227F5
	[ServerRpc(RequireOwnership = false, RunLocally = true)]
	public void CmdBreakGlassServer(Vector3 hitPoint, Vector3 direction, GameObject obj)
	{
		this.RpcWriter___Server_CmdBreakGlassServer_4203392553(hitPoint, direction, obj);
		this.RpcLogic___CmdBreakGlassServer_4203392553(hitPoint, direction, obj);
	}

	// Token: 0x0600057D RID: 1405 RVA: 0x0002461B File Offset: 0x0002281B
	[ObserversRpc(RunLocally = true, ExcludeOwner = true)]
	private void BreakGlassObservers(Vector3 hitPoint, Vector3 direction, GameObject obj)
	{
		this.RpcWriter___Observers_BreakGlassObservers_4203392553(hitPoint, direction, obj);
		this.RpcLogic___BreakGlassObservers_4203392553(hitPoint, direction, obj);
	}

	// Token: 0x0600057E RID: 1406 RVA: 0x00024641 File Offset: 0x00022841
	[ServerRpc(RequireOwnership = false, RunLocally = true)]
	private void CmdExplodeGrenade(GameObject obj)
	{
		this.RpcWriter___Server_CmdExplodeGrenade_1934289915(obj);
		this.RpcLogic___CmdExplodeGrenade_1934289915(obj);
	}

	// Token: 0x0600057F RID: 1407 RVA: 0x00024657 File Offset: 0x00022857
	[ObserversRpc(RunLocally = true, ExcludeOwner = true)]
	private void ExplodeGrenadeObservers(GameObject obj)
	{
		this.RpcWriter___Observers_ExplodeGrenadeObservers_1934289915(obj);
		this.RpcLogic___ExplodeGrenadeObservers_1934289915(obj);
	}

	// Token: 0x06000580 RID: 1408 RVA: 0x0002466D File Offset: 0x0002286D
	[ServerRpc(RequireOwnership = false, RunLocally = true)]
	private void CmdKillPig(GameObject obj)
	{
		this.RpcWriter___Server_CmdKillPig_1934289915(obj);
		this.RpcLogic___CmdKillPig_1934289915(obj);
	}

	// Token: 0x06000581 RID: 1409 RVA: 0x00024683 File Offset: 0x00022883
	[ObserversRpc(RunLocally = true, ExcludeOwner = true)]
	private void KillPigObservers(GameObject obj)
	{
		this.RpcWriter___Observers_KillPigObservers_1934289915(obj);
		this.RpcLogic___KillPigObservers_1934289915(obj);
	}

	// Token: 0x06000582 RID: 1410 RVA: 0x00024699 File Offset: 0x00022899
	[ServerRpc(RequireOwnership = false, RunLocally = true)]
	private void CmdDetachObject(GameObject obj, Vector3 direction, Vector3 hitNormal)
	{
		this.RpcWriter___Server_CmdDetachObject_3779971553(obj, direction, hitNormal);
		this.RpcLogic___CmdDetachObject_3779971553(obj, direction, hitNormal);
	}

	// Token: 0x06000583 RID: 1411 RVA: 0x000246BF File Offset: 0x000228BF
	[ObserversRpc(RunLocally = true, ExcludeOwner = true)]
	private void DetachObjectObservers(GameObject obj, Vector3 direction, Vector3 hitNormal)
	{
		this.RpcWriter___Observers_DetachObjectObservers_3779971553(obj, direction, hitNormal);
		this.RpcLogic___DetachObjectObservers_3779971553(obj, direction, hitNormal);
	}

	// Token: 0x06000584 RID: 1412 RVA: 0x000246E5 File Offset: 0x000228E5
	[ServerRpc(RequireOwnership = false, RunLocally = true)]
	private void CmdKillBird(GameObject obj)
	{
		this.RpcWriter___Server_CmdKillBird_1934289915(obj);
		this.RpcLogic___CmdKillBird_1934289915(obj);
	}

	// Token: 0x06000585 RID: 1413 RVA: 0x000246FB File Offset: 0x000228FB
	[ObserversRpc(RunLocally = true, ExcludeOwner = true)]
	private void KillBirdObservers(GameObject obj)
	{
		this.RpcWriter___Observers_KillBirdObservers_1934289915(obj);
		this.RpcLogic___KillBirdObservers_1934289915(obj);
	}

	// Token: 0x06000586 RID: 1414 RVA: 0x00024711 File Offset: 0x00022911
	[ServerRpc(RequireOwnership = false, RunLocally = true)]
	private void CmdBreakPot(GameObject obj)
	{
		this.RpcWriter___Server_CmdBreakPot_1934289915(obj);
		this.RpcLogic___CmdBreakPot_1934289915(obj);
	}

	// Token: 0x06000587 RID: 1415 RVA: 0x00024727 File Offset: 0x00022927
	[ObserversRpc(RunLocally = true, ExcludeOwner = true)]
	private void BreakPotObservers(GameObject obj)
	{
		this.RpcWriter___Observers_BreakPotObservers_1934289915(obj);
		this.RpcLogic___BreakPotObservers_1934289915(obj);
	}

	// Token: 0x06000588 RID: 1416 RVA: 0x0002473D File Offset: 0x0002293D
	[ServerRpc(RequireOwnership = false, RunLocally = true)]
	private void CmdDamageProp(GameObject obj)
	{
		this.RpcWriter___Server_CmdDamageProp_1934289915(obj);
		this.RpcLogic___CmdDamageProp_1934289915(obj);
	}

	// Token: 0x06000589 RID: 1417 RVA: 0x00024753 File Offset: 0x00022953
	[ObserversRpc(RunLocally = true, ExcludeOwner = true)]
	private void CmdDamagePropObservers(GameObject obj)
	{
		this.RpcWriter___Observers_CmdDamagePropObservers_1934289915(obj);
		this.RpcLogic___CmdDamagePropObservers_1934289915(obj);
	}

	// Token: 0x0600058A RID: 1418 RVA: 0x0002476C File Offset: 0x0002296C
	private void OnTriggerEnter(Collider col)
	{
		if (col.CompareTag("Teleport"))
		{
			base.transform.position = col.GetComponent<Teleporter>().teleportPoint.position;
		}
		if (col.CompareTag("Killz"))
		{
			base.Invoke("DespawnObject", 0.65f);
		}
	}

	// Token: 0x0600058B RID: 1419 RVA: 0x000247C0 File Offset: 0x000229C0
	public void SetLayerAllChildren(Transform root, int layer)
	{
		Transform[] componentsInChildren = root.GetComponentsInChildren<Transform>(true);
		for (int i = 0; i < componentsInChildren.Length; i++)
		{
			componentsInChildren[i].gameObject.layer = layer;
		}
	}

	// Token: 0x0600058C RID: 1420 RVA: 0x000247F4 File Offset: 0x000229F4
	public bool StartsWithVowel(string name)
	{
		name = name.ToLower();
		return name.StartsWith("a") || name.StartsWith("e") || name.StartsWith("i") || name.StartsWith("o") || name.StartsWith("u") || name.StartsWith("y");
	}

	// Token: 0x0600058D RID: 1421 RVA: 0x0002485C File Offset: 0x00022A5C
	public bool FriendlyFireCheck(PlayerHealth enemyHealth)
	{
		if (GameManager.Instance.SyncAccessor_FriendlyFireEnabled)
		{
			return false;
		}
		int teamId = ScoreManager.Instance.GetTeamId(enemyHealth.SyncAccessor_playerValues.SyncAccessor_playerClient.PlayerId);
		int teamId2 = ScoreManager.Instance.GetTeamId(this.playerValues.SyncAccessor_playerClient.PlayerId);
		return teamId == teamId2;
	}

	// Token: 0x0600058F RID: 1423 RVA: 0x000249A0 File Offset: 0x00022BA0
	public virtual void NetworkInitialize___Early()
	{
		if (this.NetworkInitializeEarly_Weapon_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeEarly_Weapon_Assembly-CSharp.dll = true;
		this.syncVar___currentAmmo = new SyncVar<int>(this, 0U, WritePermission.ClientUnsynchronized, ReadPermission.Observers, -1f, Channel.Reliable, this.currentAmmo);
		base.RegisterServerRpc(0U, new ServerRpcDelegate(this.RpcReader___Server_DespawnObjectServer_2166136261));
		base.RegisterServerRpc(1U, new ServerRpcDelegate(this.RpcReader___Server_CmdBreakGlassServer_4203392553));
		base.RegisterObserversRpc(2U, new ClientRpcDelegate(this.RpcReader___Observers_BreakGlassObservers_4203392553));
		base.RegisterServerRpc(3U, new ServerRpcDelegate(this.RpcReader___Server_CmdExplodeGrenade_1934289915));
		base.RegisterObserversRpc(4U, new ClientRpcDelegate(this.RpcReader___Observers_ExplodeGrenadeObservers_1934289915));
		base.RegisterServerRpc(5U, new ServerRpcDelegate(this.RpcReader___Server_CmdKillPig_1934289915));
		base.RegisterObserversRpc(6U, new ClientRpcDelegate(this.RpcReader___Observers_KillPigObservers_1934289915));
		base.RegisterServerRpc(7U, new ServerRpcDelegate(this.RpcReader___Server_CmdDetachObject_3779971553));
		base.RegisterObserversRpc(8U, new ClientRpcDelegate(this.RpcReader___Observers_DetachObjectObservers_3779971553));
		base.RegisterServerRpc(9U, new ServerRpcDelegate(this.RpcReader___Server_CmdKillBird_1934289915));
		base.RegisterObserversRpc(10U, new ClientRpcDelegate(this.RpcReader___Observers_KillBirdObservers_1934289915));
		base.RegisterServerRpc(11U, new ServerRpcDelegate(this.RpcReader___Server_CmdBreakPot_1934289915));
		base.RegisterObserversRpc(12U, new ClientRpcDelegate(this.RpcReader___Observers_BreakPotObservers_1934289915));
		base.RegisterServerRpc(13U, new ServerRpcDelegate(this.RpcReader___Server_CmdDamageProp_1934289915));
		base.RegisterObserversRpc(14U, new ClientRpcDelegate(this.RpcReader___Observers_CmdDamagePropObservers_1934289915));
		base.RegisterSyncVarRead(new SyncVarReadDelegate(this.ReadSyncVar___Weapon));
	}

	// Token: 0x06000590 RID: 1424 RVA: 0x00024B54 File Offset: 0x00022D54
	public virtual void NetworkInitialize__Late()
	{
		if (this.NetworkInitializeLate_Weapon_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeLate_Weapon_Assembly-CSharp.dll = true;
		this.syncVar___currentAmmo.SetRegistered();
	}

	// Token: 0x06000591 RID: 1425 RVA: 0x00024B72 File Offset: 0x00022D72
	public override void NetworkInitializeIfDisabled()
	{
		this.NetworkInitialize___Early();
		this.NetworkInitialize__Late();
	}

	// Token: 0x06000592 RID: 1426 RVA: 0x00024B80 File Offset: 0x00022D80
	private void RpcWriter___Server_DespawnObjectServer_2166136261()
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendServerRpc(0U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000593 RID: 1427 RVA: 0x00024C1C File Offset: 0x00022E1C
	private void RpcLogic___DespawnObjectServer_2166136261()
	{
		base.transform.DOKill(false);
		base.Despawn(null);
	}

	// Token: 0x06000594 RID: 1428 RVA: 0x00024C48 File Offset: 0x00022E48
	private void RpcReader___Server_DespawnObjectServer_2166136261(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		if (!base.IsServer)
		{
			return;
		}
		this.RpcLogic___DespawnObjectServer_2166136261();
	}

	// Token: 0x06000595 RID: 1429 RVA: 0x00024C68 File Offset: 0x00022E68
	private void RpcWriter___Server_CmdBreakGlassServer_4203392553(Vector3 hitPoint, Vector3 direction, GameObject obj)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteVector3(hitPoint);
		writer.WriteVector3(direction);
		writer.WriteGameObject(obj);
		base.SendServerRpc(1U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000596 RID: 1430 RVA: 0x00024D29 File Offset: 0x00022F29
	public void RpcLogic___CmdBreakGlassServer_4203392553(Vector3 hitPoint, Vector3 direction, GameObject obj)
	{
		this.BreakGlassObservers(hitPoint, direction, obj);
	}

	// Token: 0x06000597 RID: 1431 RVA: 0x00024D34 File Offset: 0x00022F34
	private void RpcReader___Server_CmdBreakGlassServer_4203392553(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		Vector3 vector = PooledReader0.ReadVector3();
		Vector3 vector2 = PooledReader0.ReadVector3();
		GameObject gameObject = PooledReader0.ReadGameObject();
		if (!base.IsServer)
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___CmdBreakGlassServer_4203392553(vector, vector2, gameObject);
	}

	// Token: 0x06000598 RID: 1432 RVA: 0x00024D94 File Offset: 0x00022F94
	private void RpcWriter___Observers_BreakGlassObservers_4203392553(Vector3 hitPoint, Vector3 direction, GameObject obj)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteVector3(hitPoint);
		writer.WriteVector3(direction);
		writer.WriteGameObject(obj);
		base.SendObserversRpc(2U, writer, channel, DataOrderType.Default, false, false, true);
		writer.Store();
	}

	// Token: 0x06000599 RID: 1433 RVA: 0x00024E64 File Offset: 0x00023064
	private void RpcLogic___BreakGlassObservers_4203392553(Vector3 hitPoint, Vector3 direction, GameObject obj)
	{
		if (obj == null)
		{
			return;
		}
		if (obj.GetComponent<ShatterableGlass>() != null)
		{
			obj.GetComponent<ShatterableGlass>().Shatter3D(hitPoint, direction);
		}
	}

	// Token: 0x0600059A RID: 1434 RVA: 0x00024E8C File Offset: 0x0002308C
	private void RpcReader___Observers_BreakGlassObservers_4203392553(PooledReader PooledReader0, Channel channel)
	{
		Vector3 vector = PooledReader0.ReadVector3();
		Vector3 vector2 = PooledReader0.ReadVector3();
		GameObject gameObject = PooledReader0.ReadGameObject();
		if (!base.IsClient)
		{
			return;
		}
		if (base.IsHost)
		{
			return;
		}
		this.RpcLogic___BreakGlassObservers_4203392553(vector, vector2, gameObject);
	}

	// Token: 0x0600059B RID: 1435 RVA: 0x00024EEC File Offset: 0x000230EC
	private void RpcWriter___Server_CmdExplodeGrenade_1934289915(GameObject obj)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteGameObject(obj);
		base.SendServerRpc(3U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x0600059C RID: 1436 RVA: 0x00024F93 File Offset: 0x00023193
	private void RpcLogic___CmdExplodeGrenade_1934289915(GameObject obj)
	{
		this.ExplodeGrenadeObservers(obj);
	}

	// Token: 0x0600059D RID: 1437 RVA: 0x00024F9C File Offset: 0x0002319C
	private void RpcReader___Server_CmdExplodeGrenade_1934289915(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		GameObject gameObject = PooledReader0.ReadGameObject();
		if (!base.IsServer)
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___CmdExplodeGrenade_1934289915(gameObject);
	}

	// Token: 0x0600059E RID: 1438 RVA: 0x00024FDC File Offset: 0x000231DC
	private void RpcWriter___Observers_ExplodeGrenadeObservers_1934289915(GameObject obj)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteGameObject(obj);
		base.SendObserversRpc(4U, writer, channel, DataOrderType.Default, false, false, true);
		writer.Store();
	}

	// Token: 0x0600059F RID: 1439 RVA: 0x00025092 File Offset: 0x00023292
	private void RpcLogic___ExplodeGrenadeObservers_1934289915(GameObject obj)
	{
		obj.transform.root.GetComponent<HandGrenade>().explosionTimer = 0f;
	}

	// Token: 0x060005A0 RID: 1440 RVA: 0x000250B0 File Offset: 0x000232B0
	private void RpcReader___Observers_ExplodeGrenadeObservers_1934289915(PooledReader PooledReader0, Channel channel)
	{
		GameObject gameObject = PooledReader0.ReadGameObject();
		if (!base.IsClient)
		{
			return;
		}
		if (base.IsHost)
		{
			return;
		}
		this.RpcLogic___ExplodeGrenadeObservers_1934289915(gameObject);
	}

	// Token: 0x060005A1 RID: 1441 RVA: 0x000250EC File Offset: 0x000232EC
	private void RpcWriter___Server_CmdKillPig_1934289915(GameObject obj)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteGameObject(obj);
		base.SendServerRpc(5U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x060005A2 RID: 1442 RVA: 0x00025193 File Offset: 0x00023393
	private void RpcLogic___CmdKillPig_1934289915(GameObject obj)
	{
		this.KillPigObservers(obj);
	}

	// Token: 0x060005A3 RID: 1443 RVA: 0x0002519C File Offset: 0x0002339C
	private void RpcReader___Server_CmdKillPig_1934289915(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		GameObject gameObject = PooledReader0.ReadGameObject();
		if (!base.IsServer)
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___CmdKillPig_1934289915(gameObject);
	}

	// Token: 0x060005A4 RID: 1444 RVA: 0x000251DC File Offset: 0x000233DC
	private void RpcWriter___Observers_KillPigObservers_1934289915(GameObject obj)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteGameObject(obj);
		base.SendObserversRpc(6U, writer, channel, DataOrderType.Default, false, false, true);
		writer.Store();
	}

	// Token: 0x060005A5 RID: 1445 RVA: 0x00025292 File Offset: 0x00023492
	private void RpcLogic___KillPigObservers_1934289915(GameObject obj)
	{
		obj.GetComponent<Pig>().FallFar();
	}

	// Token: 0x060005A6 RID: 1446 RVA: 0x000252A0 File Offset: 0x000234A0
	private void RpcReader___Observers_KillPigObservers_1934289915(PooledReader PooledReader0, Channel channel)
	{
		GameObject gameObject = PooledReader0.ReadGameObject();
		if (!base.IsClient)
		{
			return;
		}
		if (base.IsHost)
		{
			return;
		}
		this.RpcLogic___KillPigObservers_1934289915(gameObject);
	}

	// Token: 0x060005A7 RID: 1447 RVA: 0x000252DC File Offset: 0x000234DC
	private void RpcWriter___Server_CmdDetachObject_3779971553(GameObject obj, Vector3 direction, Vector3 hitNormal)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteGameObject(obj);
		writer.WriteVector3(direction);
		writer.WriteVector3(hitNormal);
		base.SendServerRpc(7U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x060005A8 RID: 1448 RVA: 0x0002539D File Offset: 0x0002359D
	private void RpcLogic___CmdDetachObject_3779971553(GameObject obj, Vector3 direction, Vector3 hitNormal)
	{
		this.DetachObjectObservers(obj, direction, hitNormal);
	}

	// Token: 0x060005A9 RID: 1449 RVA: 0x000253A8 File Offset: 0x000235A8
	private void RpcReader___Server_CmdDetachObject_3779971553(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		GameObject gameObject = PooledReader0.ReadGameObject();
		Vector3 vector = PooledReader0.ReadVector3();
		Vector3 vector2 = PooledReader0.ReadVector3();
		if (!base.IsServer)
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___CmdDetachObject_3779971553(gameObject, vector, vector2);
	}

	// Token: 0x060005AA RID: 1450 RVA: 0x00025408 File Offset: 0x00023608
	private void RpcWriter___Observers_DetachObjectObservers_3779971553(GameObject obj, Vector3 direction, Vector3 hitNormal)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteGameObject(obj);
		writer.WriteVector3(direction);
		writer.WriteVector3(hitNormal);
		base.SendObserversRpc(8U, writer, channel, DataOrderType.Default, false, false, true);
		writer.Store();
	}

	// Token: 0x060005AB RID: 1451 RVA: 0x000254D8 File Offset: 0x000236D8
	private void RpcLogic___DetachObjectObservers_3779971553(GameObject obj, Vector3 direction, Vector3 hitNormal)
	{
		obj.GetComponent<DetachableObject>().Detach(hitNormal, direction);
	}

	// Token: 0x060005AC RID: 1452 RVA: 0x000254E8 File Offset: 0x000236E8
	private void RpcReader___Observers_DetachObjectObservers_3779971553(PooledReader PooledReader0, Channel channel)
	{
		GameObject gameObject = PooledReader0.ReadGameObject();
		Vector3 vector = PooledReader0.ReadVector3();
		Vector3 vector2 = PooledReader0.ReadVector3();
		if (!base.IsClient)
		{
			return;
		}
		if (base.IsHost)
		{
			return;
		}
		this.RpcLogic___DetachObjectObservers_3779971553(gameObject, vector, vector2);
	}

	// Token: 0x060005AD RID: 1453 RVA: 0x00025548 File Offset: 0x00023748
	private void RpcWriter___Server_CmdKillBird_1934289915(GameObject obj)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteGameObject(obj);
		base.SendServerRpc(9U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x060005AE RID: 1454 RVA: 0x000255EF File Offset: 0x000237EF
	private void RpcLogic___CmdKillBird_1934289915(GameObject obj)
	{
		this.KillBirdObservers(obj);
	}

	// Token: 0x060005AF RID: 1455 RVA: 0x000255F8 File Offset: 0x000237F8
	private void RpcReader___Server_CmdKillBird_1934289915(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		GameObject gameObject = PooledReader0.ReadGameObject();
		if (!base.IsServer)
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___CmdKillBird_1934289915(gameObject);
	}

	// Token: 0x060005B0 RID: 1456 RVA: 0x00025638 File Offset: 0x00023838
	private void RpcWriter___Observers_KillBirdObservers_1934289915(GameObject obj)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteGameObject(obj);
		base.SendObserversRpc(10U, writer, channel, DataOrderType.Default, false, false, true);
		writer.Store();
	}

	// Token: 0x060005B1 RID: 1457 RVA: 0x000256EE File Offset: 0x000238EE
	private void RpcLogic___KillBirdObservers_1934289915(GameObject obj)
	{
		obj.GetComponent<Pigeon>().Die();
	}

	// Token: 0x060005B2 RID: 1458 RVA: 0x000256FC File Offset: 0x000238FC
	private void RpcReader___Observers_KillBirdObservers_1934289915(PooledReader PooledReader0, Channel channel)
	{
		GameObject gameObject = PooledReader0.ReadGameObject();
		if (!base.IsClient)
		{
			return;
		}
		if (base.IsHost)
		{
			return;
		}
		this.RpcLogic___KillBirdObservers_1934289915(gameObject);
	}

	// Token: 0x060005B3 RID: 1459 RVA: 0x00025738 File Offset: 0x00023938
	private void RpcWriter___Server_CmdBreakPot_1934289915(GameObject obj)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteGameObject(obj);
		base.SendServerRpc(11U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x060005B4 RID: 1460 RVA: 0x000257DF File Offset: 0x000239DF
	private void RpcLogic___CmdBreakPot_1934289915(GameObject obj)
	{
		this.BreakPotObservers(obj);
	}

	// Token: 0x060005B5 RID: 1461 RVA: 0x000257E8 File Offset: 0x000239E8
	private void RpcReader___Server_CmdBreakPot_1934289915(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		GameObject gameObject = PooledReader0.ReadGameObject();
		if (!base.IsServer)
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___CmdBreakPot_1934289915(gameObject);
	}

	// Token: 0x060005B6 RID: 1462 RVA: 0x00025828 File Offset: 0x00023A28
	private void RpcWriter___Observers_BreakPotObservers_1934289915(GameObject obj)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteGameObject(obj);
		base.SendObserversRpc(12U, writer, channel, DataOrderType.Default, false, false, true);
		writer.Store();
	}

	// Token: 0x060005B7 RID: 1463 RVA: 0x000258DE File Offset: 0x00023ADE
	private void RpcLogic___BreakPotObservers_1934289915(GameObject obj)
	{
		obj.GetComponent<Pot>().Die();
	}

	// Token: 0x060005B8 RID: 1464 RVA: 0x000258EC File Offset: 0x00023AEC
	private void RpcReader___Observers_BreakPotObservers_1934289915(PooledReader PooledReader0, Channel channel)
	{
		GameObject gameObject = PooledReader0.ReadGameObject();
		if (!base.IsClient)
		{
			return;
		}
		if (base.IsHost)
		{
			return;
		}
		this.RpcLogic___BreakPotObservers_1934289915(gameObject);
	}

	// Token: 0x060005B9 RID: 1465 RVA: 0x00025928 File Offset: 0x00023B28
	private void RpcWriter___Server_CmdDamageProp_1934289915(GameObject obj)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteGameObject(obj);
		base.SendServerRpc(13U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x060005BA RID: 1466 RVA: 0x000259CF File Offset: 0x00023BCF
	private void RpcLogic___CmdDamageProp_1934289915(GameObject obj)
	{
		this.CmdDamagePropObservers(obj);
	}

	// Token: 0x060005BB RID: 1467 RVA: 0x000259D8 File Offset: 0x00023BD8
	private void RpcReader___Server_CmdDamageProp_1934289915(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		GameObject gameObject = PooledReader0.ReadGameObject();
		if (!base.IsServer)
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___CmdDamageProp_1934289915(gameObject);
	}

	// Token: 0x060005BC RID: 1468 RVA: 0x00025A18 File Offset: 0x00023C18
	private void RpcWriter___Observers_CmdDamagePropObservers_1934289915(GameObject obj)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteGameObject(obj);
		base.SendObserversRpc(14U, writer, channel, DataOrderType.Default, false, false, true);
		writer.Store();
	}

	// Token: 0x060005BD RID: 1469 RVA: 0x00025ACE File Offset: 0x00023CCE
	private void RpcLogic___CmdDamagePropObservers_1934289915(GameObject obj)
	{
		obj.GetComponent<PropDamage>().Damage();
	}

	// Token: 0x060005BE RID: 1470 RVA: 0x00025ADC File Offset: 0x00023CDC
	private void RpcReader___Observers_CmdDamagePropObservers_1934289915(PooledReader PooledReader0, Channel channel)
	{
		GameObject gameObject = PooledReader0.ReadGameObject();
		if (!base.IsClient)
		{
			return;
		}
		if (base.IsHost)
		{
			return;
		}
		this.RpcLogic___CmdDamagePropObservers_1934289915(gameObject);
	}

	// Token: 0x17000073 RID: 115
	// (get) Token: 0x060005BF RID: 1471 RVA: 0x00025B17 File Offset: 0x00023D17
	// (set) Token: 0x060005C0 RID: 1472 RVA: 0x00025B1F File Offset: 0x00023D1F
	public int SyncAccessor_currentAmmo
	{
		get
		{
			return this.currentAmmo;
		}
		set
		{
			if (value || !base.IsServer)
			{
				this.currentAmmo = value;
			}
			this.syncVar___currentAmmo.SetValue(value, value);
		}
	}

	// Token: 0x060005C1 RID: 1473 RVA: 0x00025B54 File Offset: 0x00023D54
	public virtual bool ReadSyncVar___Weapon(PooledReader PooledReader0, uint UInt321, bool Boolean2)
	{
		if (UInt321 != 0U)
		{
			return false;
		}
		if (PooledReader0 == null)
		{
			this.sync___set_value_currentAmmo(this.syncVar___currentAmmo.GetValue(true), true);
			return true;
		}
		int num = PooledReader0.ReadInt32(AutoPackType.Packed);
		this.sync___set_value_currentAmmo(num, Boolean2);
		return true;
	}

	// Token: 0x060005C2 RID: 1474 RVA: 0x00025BAC File Offset: 0x00023DAC
	public virtual void Awake___UserLogic()
	{
		this.playerControls = new PlayerControls();
		this.behaviour = base.GetComponent<ItemBehaviour>();
		this.audio = base.GetComponent<AudioSource>();
		if (base.GetComponent<Animator>() != null)
		{
			this.animator = base.GetComponent<Animator>();
		}
		if (base.GetComponentInChildren<ShootPointObject>())
		{
			this.shootPoint = base.GetComponentInChildren<ShootPointObject>().transform;
		}
		if (base.GetComponentInChildren<FlashPointObject>())
		{
			this.muzzleFlashPoint = base.GetComponentInChildren<FlashPointObject>().transform;
		}
		if (base.GetComponentInChildren<EjectCasePointObject>())
		{
			this.ejectCasePoint = base.GetComponentInChildren<EjectCasePointObject>().transform;
		}
		this.pauseManager = PauseManager.Instance;
		if (base.GetComponentInChildren<ElbowPivotPoint>() != null)
		{
			this.elbowPivot = base.GetComponentInChildren<ElbowPivotPoint>().transform;
		}
	}

	// Token: 0x04000576 RID: 1398
	[Header("Weapon Stats")]
	[SyncVar(WritePermissions = WritePermission.ClientUnsynchronized)]
	public int currentAmmo = 32;

	// Token: 0x04000577 RID: 1399
	public float timeBetweenFire = 0.2f;

	// Token: 0x04000578 RID: 1400
	public float damage = 1f;

	// Token: 0x04000579 RID: 1401
	public float headMultiplier = 2f;

	// Token: 0x0400057A RID: 1402
	public float movementFactor = 1f;

	// Token: 0x0400057B RID: 1403
	public int maxWallJumps = 1;

	// Token: 0x0400057C RID: 1404
	public float jumpFactor = 1f;

	// Token: 0x0400057D RID: 1405
	public float wallJumpFactor = 1f;

	// Token: 0x0400057E RID: 1406
	public bool fireSlowDown;

	// Token: 0x0400057F RID: 1407
	public float fireSlowDownFactor = 0.8f;

	// Token: 0x04000580 RID: 1408
	public float fireSlowDownDuration = 0.15f;

	// Token: 0x04000581 RID: 1409
	public float minSpread;

	// Token: 0x04000582 RID: 1410
	public float maxSpread = 0.2f;

	// Token: 0x04000583 RID: 1411
	public float standingAccuracy = 1f;

	// Token: 0x04000584 RID: 1412
	public float walkAccuracy = 0.75f;

	// Token: 0x04000585 RID: 1413
	public float sprintAccuracy = 0.5f;

	// Token: 0x04000586 RID: 1414
	public bool ScopeAimWeapon;

	// Token: 0x04000587 RID: 1415
	public float notAimingAccuracy = 0.5f;

	// Token: 0x04000588 RID: 1416
	public bool requireBothHands;

	// Token: 0x04000589 RID: 1417
	public bool needsAmmo = true;

	// Token: 0x0400058A RID: 1418
	public bool onePressShoot;

	// Token: 0x0400058B RID: 1419
	public bool changePitchOnShoot;

	// Token: 0x0400058C RID: 1420
	[SerializeField]
	private bool inHandDespawn;

	// Token: 0x0400058D RID: 1421
	public bool burstGun;

	// Token: 0x0400058E RID: 1422
	public bool aimBurstGun;

	// Token: 0x0400058F RID: 1423
	public int bulletsAmount;

	// Token: 0x04000590 RID: 1424
	public float timeBetweenBullets;

	// Token: 0x04000591 RID: 1425
	public float additionalPrecision = 1f;

	// Token: 0x04000592 RID: 1426
	[Header("SFX")]
	[HideInInspector]
	public AudioSource audio;

	// Token: 0x04000593 RID: 1427
	public AudioClip fireClip;

	// Token: 0x04000594 RID: 1428
	public AudioClip nobulletClip;

	// Token: 0x04000595 RID: 1429
	public AudioClip headHitClip;

	// Token: 0x04000596 RID: 1430
	public AudioClip bodyHitClip;

	// Token: 0x04000597 RID: 1431
	public AudioClip deathClip;

	// Token: 0x04000598 RID: 1432
	[Header("VFX")]
	public GameObject muzzleFlash;

	// Token: 0x04000599 RID: 1433
	public float lightIntensity = 5f;

	// Token: 0x0400059A RID: 1434
	public GameObject headImpact;

	// Token: 0x0400059B RID: 1435
	public GameObject bodyImpact;

	// Token: 0x0400059C RID: 1436
	public GameObject genericBodyImpact;

	// Token: 0x0400059D RID: 1437
	public bool playGenericBodyImpactOnBody;

	// Token: 0x0400059E RID: 1438
	public GameObject bloodSplatter;

	// Token: 0x0400059F RID: 1439
	public GameObject genericImpact;

	// Token: 0x040005A0 RID: 1440
	public GameObject bulletHole;

	// Token: 0x040005A1 RID: 1441
	public LineRenderer bulletTrailLocal;

	// Token: 0x040005A2 RID: 1442
	public GameObject hitMarker;

	// Token: 0x040005A3 RID: 1443
	[HideInInspector]
	public GameObject marker;

	// Token: 0x040005A4 RID: 1444
	public bool SurfacesImpact;

	// Token: 0x040005A5 RID: 1445
	public GameObject concreteHitImpact;

	// Token: 0x040005A6 RID: 1446
	public GameObject sandHitImpact;

	// Token: 0x040005A7 RID: 1447
	public GameObject dirtHitImpact;

	// Token: 0x040005A8 RID: 1448
	public GameObject metalHitImpact;

	// Token: 0x040005A9 RID: 1449
	public GameObject tauleHitImpact;

	// Token: 0x040005AA RID: 1450
	public GameObject waterHitImpact;

	// Token: 0x040005AB RID: 1451
	public GameObject woodHitImpact;

	// Token: 0x040005AC RID: 1452
	public GameObject softbodyHitImpact;

	// Token: 0x040005AD RID: 1453
	public bool SurfacesVFX;

	// Token: 0x040005AE RID: 1454
	public GameObject sandHitFx;

	// Token: 0x040005AF RID: 1455
	public GameObject dirtHitFx;

	// Token: 0x040005B0 RID: 1456
	public GameObject metalHitFx;

	// Token: 0x040005B1 RID: 1457
	public GameObject tauleHitFx;

	// Token: 0x040005B2 RID: 1458
	public GameObject waterHitFx;

	// Token: 0x040005B3 RID: 1459
	public GameObject woodHitFx;

	// Token: 0x040005B4 RID: 1460
	public GameObject softbodyHitFx;

	// Token: 0x040005B5 RID: 1461
	public bool EjectVFX;

	// Token: 0x040005B6 RID: 1462
	public GameObject ejectCaseVfx;

	// Token: 0x040005B7 RID: 1463
	[SerializeField]
	private GameObject reloadEjectVfx;

	// Token: 0x040005B8 RID: 1464
	private Transform ejectCasePoint;

	// Token: 0x040005B9 RID: 1465
	[Header("Layers")]
	public LayerMask defaultLayer;

	// Token: 0x040005BA RID: 1466
	public LayerMask playerLayer;

	// Token: 0x040005BB RID: 1467
	public LayerMask supLayer;

	// Token: 0x040005BC RID: 1468
	public float duration;

	// Token: 0x040005BD RID: 1469
	public int vibrato;

	// Token: 0x040005BE RID: 1470
	public Vector3 strength;

	// Token: 0x040005BF RID: 1471
	public float randomness;

	// Token: 0x040005C0 RID: 1472
	public bool fadeOut;

	// Token: 0x040005C1 RID: 1473
	public ShakeRandomnessMode randomnessMode;

	// Token: 0x040005C2 RID: 1474
	public Ease shakeEase;

	// Token: 0x040005C3 RID: 1475
	public bool revolverShake;

	// Token: 0x040005C4 RID: 1476
	public bool akAnim;

	// Token: 0x040005C5 RID: 1477
	[SerializeField]
	private float cameraLerpSpeed = 3f;

	// Token: 0x040005C6 RID: 1478
	[SerializeField]
	private Vector3 recoil;

	// Token: 0x040005C7 RID: 1479
	[SerializeField]
	private float elasticity;

	// Token: 0x040005C8 RID: 1480
	[SerializeField]
	private bool instantPush;

	// Token: 0x040005C9 RID: 1481
	[SerializeField]
	private bool holdback;

	// Token: 0x040005CA RID: 1482
	[SerializeField]
	private bool instantComebackOnFire;

	// Token: 0x040005CB RID: 1483
	[SerializeField]
	private bool horizontalAnimation;

	// Token: 0x040005CC RID: 1484
	[SerializeField]
	private bool supplementAnimator;

	// Token: 0x040005CD RID: 1485
	public Animator animator;

	// Token: 0x040005CE RID: 1486
	[SerializeField]
	private Vector3 animationPunch;

	// Token: 0x040005CF RID: 1487
	[SerializeField]
	private Vector3 foreArmAnimationPunch;

	// Token: 0x040005D0 RID: 1488
	public float foreArmAnimationSpeed = 10f;

	// Token: 0x040005D1 RID: 1489
	[SerializeField]
	private float animationDuration;

	// Token: 0x040005D2 RID: 1490
	[SerializeField]
	private int animationVibrato;

	// Token: 0x040005D3 RID: 1491
	[SerializeField]
	private float animationElasticity;

	// Token: 0x040005D4 RID: 1492
	[Header("State")]
	public bool inRightHand;

	// Token: 0x040005D5 RID: 1493
	public bool inLeftHand;

	// Token: 0x040005D6 RID: 1494
	private PlayerControls playerControls;

	// Token: 0x040005D7 RID: 1495
	[HideInInspector]
	public InputAction fire1;

	// Token: 0x040005D8 RID: 1496
	[HideInInspector]
	public InputAction fire2;

	// Token: 0x040005D9 RID: 1497
	[HideInInspector]
	public InputAction reload;

	// Token: 0x040005DA RID: 1498
	[HideInInspector]
	public InputAction jump;

	// Token: 0x040005DB RID: 1499
	[HideInInspector]
	public ItemBehaviour behaviour;

	// Token: 0x040005DC RID: 1500
	[HideInInspector]
	public FirstPersonController playerController;

	// Token: 0x040005DD RID: 1501
	[HideInInspector]
	public GameObject rootObject;

	// Token: 0x040005DE RID: 1502
	[HideInInspector]
	public GameObject lastPlayerHolder;

	// Token: 0x040005DF RID: 1503
	[HideInInspector]
	public CameraShakeConstrains camAnimScript;

	// Token: 0x040005E0 RID: 1504
	private TextMeshProUGUI ammoDisplay;

	// Token: 0x040005E1 RID: 1505
	[HideInInspector]
	public Transform shootPoint;

	// Token: 0x040005E2 RID: 1506
	[HideInInspector]
	public Transform muzzleFlashPoint;

	// Token: 0x040005E3 RID: 1507
	[HideInInspector]
	public Camera cam;

	// Token: 0x040005E4 RID: 1508
	[HideInInspector]
	public Tween tween;

	// Token: 0x040005E5 RID: 1509
	[HideInInspector]
	public bool isClicked;

	// Token: 0x040005E6 RID: 1510
	[HideInInspector]
	public Transform fpArms;

	// Token: 0x040005E7 RID: 1511
	[HideInInspector]
	public PlayerValues playerValues;

	// Token: 0x040005E8 RID: 1512
	[HideInInspector]
	public bool shot;

	// Token: 0x040005E9 RID: 1513
	[HideInInspector]
	public bool heldOnce;

	// Token: 0x040005EA RID: 1514
	[HideInInspector]
	public int noAmmoClicks;

	// Token: 0x040005EB RID: 1515
	[HideInInspector]
	public Transform elbowPivot;

	// Token: 0x040005EC RID: 1516
	private float caseSoundTimer;

	// Token: 0x040005ED RID: 1517
	public bool reloadWeapon;

	// Token: 0x040005EE RID: 1518
	[HideInInspector]
	public bool isReloading;

	// Token: 0x040005EF RID: 1519
	public int ammoCharge;

	// Token: 0x040005F0 RID: 1520
	[SerializeField]
	private int ejectCaseIndex;

	// Token: 0x040005F1 RID: 1521
	[HideInInspector]
	public float chargedBullets;

	// Token: 0x040005F2 RID: 1522
	public float ragdollEjectForce = 50f;

	// Token: 0x040005F3 RID: 1523
	private PauseManager pauseManager;

	// Token: 0x040005F4 RID: 1524
	private Vector3 initialLocalPos;

	// Token: 0x040005F5 RID: 1525
	[HideInInspector]
	public bool cantTakeSafeBool;

	// Token: 0x040005F6 RID: 1526
	[HideInInspector]
	public bool invertFire;

	// Token: 0x040005F7 RID: 1527
	public SyncVar<int> syncVar___currentAmmo;

	// Token: 0x040005F8 RID: 1528
	private bool NetworkInitializeEarly_Weapon_Assembly-CSharp.dll;

	// Token: 0x040005F9 RID: 1529
	private bool NetworkInitializeLate_Weapon_Assembly-CSharp.dll;
}

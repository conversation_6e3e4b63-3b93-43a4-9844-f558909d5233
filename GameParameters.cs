﻿using System;
using FishNet;
using UnityEngine;

// Token: 0x02000054 RID: 84
public class GameParameters : MonoBehaviour
{
	// Token: 0x060003D3 RID: 979 RVA: 0x0001BD28 File Offset: 0x00019F28
	private void Update()
	{
		if (!InstanceFinder.NetworkManager.IsServer)
		{
			this.display.transform.localScale = Vector3.zero;
			return;
		}
		if (this.mapSelectionWindow.localScale == Vector3.one)
		{
			this.display.transform.localScale = Vector3.zero;
			return;
		}
		this.display.transform.localScale = Vector3.one;
	}

	// Token: 0x060003D4 RID: 980 RVA: 0x0001BD99 File Offset: 0x00019F99
	public void ToggleOutlines(bool state)
	{
		if (GameManager.Instance.IsServer)
		{
			GameManager.Instance.sync___set_value_EnemyOutlinesEnabled(state, true);
		}
	}

	// Token: 0x060003D5 RID: 981 RVA: 0x0001BDB3 File Offset: 0x00019FB3
	public void ToggleFriendlyFire(bool state)
	{
		if (GameManager.Instance.IsServer)
		{
			GameManager.Instance.sync___set_value_FriendlyFireEnabled(state, true);
		}
	}

	// Token: 0x04000426 RID: 1062
	[SerializeField]
	private GameObject display;

	// Token: 0x04000427 RID: 1063
	[SerializeField]
	private Transform mapSelectionWindow;
}

﻿using System;
using UnityEngine;

namespace ch.sycoforge.Decal.Demo
{
	// Token: 0x0200017F RID: 383
	public class BasicBulletHoles : MonoBehaviour
	{
		// Token: 0x06000FBA RID: 4026 RVA: 0x00067164 File Offset: 0x00065364
		public void Start()
		{
			if (this.DecalPrefab == null)
			{
				Debug.LogError("The DynamicDemo script has no decal prefab attached.");
			}
		}

		// Token: 0x06000FBB RID: 4027 RVA: 0x00067180 File Offset: 0x00065380
		public void Update()
		{
			if (Input.GetMouseButtonUp(0))
			{
				Ray ray = Camera.main.ScreenPointToRay(Input.mousePosition);
				RaycastHit raycastHit;
				if (Physics.Raycast(ray, out raycastHit, 200f))
				{
					Debug.DrawLine(ray.origin, raycastHit.point, Color.red);
					EasyDecal easyDecal = EasyDecal.ProjectAt(this.DecalPrefab.gameObject, raycastHit.collider.gameObject, raycastHit.point, raycastHit.normal, true);
					this.t = !this.t;
					if (this.t)
					{
						easyDecal.CancelFade();
					}
				}
			}
		}

		// Token: 0x04000E9F RID: 3743
		public EasyDecal DecalPrefab;

		// Token: 0x04000EA0 RID: 3744
		private bool t;
	}
}

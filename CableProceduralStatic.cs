﻿using System;
using UnityEngine;

// Token: 0x0200001A RID: 26
[RequireComponent(typeof(LineRenderer))]
[ExecuteAlways]
public class CableProceduralStatic : MonoBehaviour
{
	// Token: 0x060000D9 RID: 217 RVA: 0x00006348 File Offset: 0x00004548
	private void Start()
	{
		this.line = base.GetComponent<LineRenderer>();
		if (!this.endPointTransform)
		{
			Debug.LogError("No Endpoint Transform assigned to Cable_Procedural component attached to " + base.gameObject.name);
			return;
		}
		this.vectorFromStartToEnd = this.endPointTransform.position - base.transform.position;
		base.transform.forward = this.vectorFromStartToEnd.normalized;
		this.pointsInLineRenderer = Mathf.FloorToInt(this.pointDensity * this.vectorFromStartToEnd.magnitude);
		this.line.positionCount = this.pointsInLineRenderer;
		this.sagDirection = Physics.gravity.normalized;
		this.Draw();
	}

	// Token: 0x060000DA RID: 218 RVA: 0x00006408 File Offset: 0x00004608
	private void Draw()
	{
		for (int i = 0; i < this.pointsInLineRenderer; i++)
		{
			float num = (float)i / (float)(this.pointsInLineRenderer - 1);
			float num2 = Mathf.Sin(num * 3.1415927f);
			Vector3 vector = this.vectorFromStartToEnd * num;
			Vector3 vector2 = this.sagDirection * this.sagAmplitude;
			Vector3 vector3 = base.transform.position + vector + Vector3.ClampMagnitude(vector2, this.sagAmplitude) * num2;
			this.line.SetPosition(i, vector3);
		}
	}

	// Token: 0x040000BE RID: 190
	private LineRenderer line;

	// Token: 0x040000BF RID: 191
	[SerializeField]
	private Transform endPointTransform;

	// Token: 0x040000C0 RID: 192
	[SerializeField]
	[Tooltip("Number of points per unit length, using the straight line from the start to the end transform.")]
	private float pointDensity = 3f;

	// Token: 0x040000C1 RID: 193
	[SerializeField]
	private float sagAmplitude = 1f;

	// Token: 0x040000C2 RID: 194
	private int pointsInLineRenderer;

	// Token: 0x040000C3 RID: 195
	private Vector3 vectorFromStartToEnd;

	// Token: 0x040000C4 RID: 196
	private Vector3 sagDirection;
}

﻿using System;
using FishNet;
using FishNet.Managing;
using FishNet.Object;
using FishNet.Object.Delegating;
using FishNet.Serializing;
using FishNet.Transporting;
using UnityEngine;
using UnityEngine.Events;

// Token: 0x0200004F RID: 79
public class EventInteract : BaseEventInteract
{
	// Token: 0x060003B8 RID: 952 RVA: 0x0001B908 File Offset: 0x00019B08
	[ObserversRpc]
	protected override void OnInteractObserverRpc()
	{
		this.RpcWriter___Observers_OnInteractObserverRpc_2166136261();
	}

	// Token: 0x060003BA RID: 954 RVA: 0x0001B918 File Offset: 0x00019B18
	public override void NetworkInitialize___Early()
	{
		if (this.NetworkInitializeEarly_EventInteract_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeEarly_EventInteract_Assembly-CSharp.dll = true;
		base.NetworkInitialize___Early();
		base.RegisterObserversRpc(5U, new ClientRpcDelegate(this.RpcReader___Observers_OnInteractObserverRpc_2166136261));
	}

	// Token: 0x060003BB RID: 955 RVA: 0x0001B948 File Offset: 0x00019B48
	public override void NetworkInitialize__Late()
	{
		if (this.NetworkInitializeLate_EventInteract_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeLate_EventInteract_Assembly-CSharp.dll = true;
		base.NetworkInitialize__Late();
	}

	// Token: 0x060003BC RID: 956 RVA: 0x0001B961 File Offset: 0x00019B61
	public override void NetworkInitializeIfDisabled()
	{
		this.NetworkInitialize___Early();
		this.NetworkInitialize__Late();
	}

	// Token: 0x060003BD RID: 957 RVA: 0x0001B970 File Offset: 0x00019B70
	private void RpcWriter___Observers_OnInteractObserverRpc_2166136261()
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendObserversRpc(5U, writer, channel, DataOrderType.Default, false, false, false);
		writer.Store();
	}

	// Token: 0x060003BE RID: 958 RVA: 0x0001BA19 File Offset: 0x00019C19
	protected virtual void RpcLogic___OnInteractObserverRpc_2166136261()
	{
		UnityEvent unityEvent = this.onInteractEvent;
		if (unityEvent == null)
		{
			return;
		}
		unityEvent.Invoke();
	}

	// Token: 0x060003BF RID: 959 RVA: 0x0001BA2C File Offset: 0x00019C2C
	private void RpcReader___Observers_OnInteractObserverRpc_2166136261(PooledReader PooledReader0, Channel channel)
	{
		if (!base.IsClient)
		{
			return;
		}
		this.RpcLogic___OnInteractObserverRpc_2166136261();
	}

	// Token: 0x060003C0 RID: 960 RVA: 0x0001BA4C File Offset: 0x00019C4C
	public override void Awake()
	{
		this.NetworkInitialize___Early();
		base.Awake();
		this.NetworkInitialize__Late();
	}

	// Token: 0x060003C1 RID: 961 RVA: 0x000023D6 File Offset: 0x000005D6
	public override void Awake___UserLogic()
	{
	}

	// Token: 0x04000414 RID: 1044
	[Header("This event triggers when the player interacts with this object.")]
	[SerializeField]
	private UnityEvent onInteractEvent;

	// Token: 0x04000415 RID: 1045
	private bool NetworkInitializeEarly_EventInteract_Assembly-CSharp.dll;

	// Token: 0x04000416 RID: 1046
	private bool NetworkInitializeLate_EventInteract_Assembly-CSharp.dll;
}

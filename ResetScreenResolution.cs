﻿using System;
using TMPro;
using UnityEngine;

// Token: 0x02000133 RID: 307
public class ResetScreenResolution : MonoBehaviour
{
	// Token: 0x06000E74 RID: 3700 RVA: 0x000023D6 File Offset: 0x000005D6
	private void Start()
	{
	}

	// Token: 0x06000E75 RID: 3701 RVA: 0x0005FA41 File Offset: 0x0005DC41
	private void Update()
	{
		if (Settings.Instance.resolution != 0)
		{
			base.transform.localScale = Vector3.one;
			return;
		}
		base.transform.localScale = Vector3.zero;
	}

	// Token: 0x06000E76 RID: 3702 RVA: 0x0005FA70 File Offset: 0x0005DC70
	public void ResetResolution()
	{
		this.resolutionDropdown.value = 0;
		this.resolutionDropdown.GetComponent<SettingsResolution>().SetResolutionReal(this.resolutionDropdown);
	}

	// Token: 0x04000D00 RID: 3328
	[SerializeField]
	private TMP_Dropdown resolutionDropdown;
}

﻿using System;
using UnityEngine;

// Token: 0x02000153 RID: 339
public class InfoBubble : MonoBehaviour
{
	// Token: 0x06000EED RID: 3821 RVA: 0x00062850 File Offset: 0x00060A50
	private void Start()
	{
		this.startOffsetTarget = base.transform.position - this.TrackTarget.position;
	}

	// Token: 0x06000EEE RID: 3822 RVA: 0x00062874 File Offset: 0x00060A74
	private void Update()
	{
		Vector3 vector = Mathf.Sin(this.WobbleFrequency * Time.timeSinceLevelLoad) * this.WobbleAxis * this.WobbleAmplitude;
		base.transform.Rotate(vector);
		base.transform.position = this.TrackTarget.position + this.startOffsetTarget;
	}

	// Token: 0x04000D8F RID: 3471
	public Vector3 WobbleAxis = Vector3.one;

	// Token: 0x04000D90 RID: 3472
	public float WobbleFrequency = 1f;

	// Token: 0x04000D91 RID: 3473
	public float WobbleAmplitude = 0.25f;

	// Token: 0x04000D92 RID: 3474
	public Transform TrackTarget;

	// Token: 0x04000D93 RID: 3475
	private Vector3 startOffsetTarget;
}

﻿using System;
using FishNet.Object;
using FishNet.Object.Delegating;
using FishNet.Object.Synchronizing;
using FishNet.Serializing;
using FishNet.Serializing.Generated;
using FishNet.Transporting;
using UnityEngine;

// Token: 0x02000068 RID: 104
public class PlayerValues : NetworkBehaviour
{
	// Token: 0x060004A5 RID: 1189 RVA: 0x0002006C File Offset: 0x0001E26C
	private void Start()
	{
		this.setup = base.GetComponent<PlayerSetup>();
	}

	// Token: 0x060004A6 RID: 1190 RVA: 0x0002007C File Offset: 0x0001E27C
	private void Update()
	{
		if (!SteamLobby.Instance)
		{
			return;
		}
		if (!base.IsOwner && this.voiceChatSource.mute != this.SyncAccessor_playerClient.voiceChatSource.mute)
		{
			this.voiceChatSource.mute = this.SyncAccessor_playerClient.voiceChatSource.mute;
		}
		if (!this.setup.hat)
		{
			return;
		}
		if (!this.setup.hat.activeSelf)
		{
			this.setup.hat.SetActive(true);
		}
	}

	// Token: 0x060004A7 RID: 1191 RVA: 0x0002010C File Offset: 0x0001E30C
	private void SyncedItemRaycast()
	{
		this.syncFixTimer -= Time.deltaTime;
		RaycastHit raycastHit;
		if (Physics.Raycast(this.head.position, this.head.forward, out raycastHit, this.raycastLength, this.itemLayer))
		{
			if (raycastHit.transform.gameObject.layer != 7)
			{
				if (this.currentInteractable != null)
				{
					this.currentInteractable.canTake = false;
				}
				this.currentInteractable = null;
				return;
			}
			if (raycastHit.transform.GetComponent<Interactable>() != null)
			{
				this.currentInteractable = raycastHit.transform.GetComponent<Interactable>();
				this.currentInteractable.canTake = false;
				return;
			}
		}
		else
		{
			if (this.currentInteractable != null)
			{
				this.currentInteractable.canTake = true;
			}
			this.currentInteractable = null;
		}
	}

	// Token: 0x060004A9 RID: 1193 RVA: 0x000201E8 File Offset: 0x0001E3E8
	public virtual void NetworkInitialize___Early()
	{
		if (this.NetworkInitializeEarly_PlayerValues_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeEarly_PlayerValues_Assembly-CSharp.dll = true;
		this.syncVar___playerClient = new SyncVar<ClientInstance>(this, 0U, WritePermission.ServerOnly, ReadPermission.Observers, -1f, Channel.Reliable, this.playerClient);
		base.RegisterSyncVarRead(new SyncVarReadDelegate(this.ReadSyncVar___PlayerValues));
	}

	// Token: 0x060004AA RID: 1194 RVA: 0x00020243 File Offset: 0x0001E443
	public virtual void NetworkInitialize__Late()
	{
		if (this.NetworkInitializeLate_PlayerValues_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeLate_PlayerValues_Assembly-CSharp.dll = true;
		this.syncVar___playerClient.SetRegistered();
	}

	// Token: 0x060004AB RID: 1195 RVA: 0x00020261 File Offset: 0x0001E461
	public override void NetworkInitializeIfDisabled()
	{
		this.NetworkInitialize___Early();
		this.NetworkInitialize__Late();
	}

	// Token: 0x17000070 RID: 112
	// (get) Token: 0x060004AC RID: 1196 RVA: 0x0002026F File Offset: 0x0001E46F
	// (set) Token: 0x060004AD RID: 1197 RVA: 0x00020277 File Offset: 0x0001E477
	public ClientInstance SyncAccessor_playerClient
	{
		get
		{
			return this.playerClient;
		}
		set
		{
			if (value || !base.IsServer)
			{
				this.playerClient = value;
			}
			this.syncVar___playerClient.SetValue(value, value);
		}
	}

	// Token: 0x060004AE RID: 1198 RVA: 0x000202AC File Offset: 0x0001E4AC
	public virtual bool ReadSyncVar___PlayerValues(PooledReader PooledReader0, uint UInt321, bool Boolean2)
	{
		if (UInt321 != 0U)
		{
			return false;
		}
		if (PooledReader0 == null)
		{
			this.sync___set_value_playerClient(this.syncVar___playerClient.GetValue(true), true);
			return true;
		}
		ClientInstance clientInstance = FishNet.Serializing.Generated.GeneratedReaders___Internal.Read___ClientInstanceFishNet.Serializing.Generateds(PooledReader0);
		this.sync___set_value_playerClient(clientInstance, Boolean2);
		return true;
	}

	// Token: 0x060004AF RID: 1199 RVA: 0x00020261 File Offset: 0x0001E461
	public virtual void Awake()
	{
		this.NetworkInitialize___Early();
		this.NetworkInitialize__Late();
	}

	// Token: 0x060004B0 RID: 1200 RVA: 0x000023D6 File Offset: 0x000005D6
	public virtual void Awake___UserLogic()
	{
	}

	// Token: 0x040004C0 RID: 1216
	[SyncVar]
	public ClientInstance playerClient;

	// Token: 0x040004C1 RID: 1217
	private PlayerSetup setup;

	// Token: 0x040004C2 RID: 1218
	[SerializeField]
	private AudioSource voiceChatSource;

	// Token: 0x040004C3 RID: 1219
	[Space]
	[Header("Grab Sync Fix")]
	[SerializeField]
	private LayerMask itemLayer;

	// Token: 0x040004C4 RID: 1220
	[SerializeField]
	private float syncFixTimer;

	// Token: 0x040004C5 RID: 1221
	[SerializeField]
	private float raycastLength;

	// Token: 0x040004C6 RID: 1222
	[SerializeField]
	private Transform head;

	// Token: 0x040004C7 RID: 1223
	public Interactable currentInteractable;

	// Token: 0x040004C8 RID: 1224
	public SyncVar<ClientInstance> syncVar___playerClient;

	// Token: 0x040004C9 RID: 1225
	private bool NetworkInitializeEarly_PlayerValues_Assembly-CSharp.dll;

	// Token: 0x040004CA RID: 1226
	private bool NetworkInitializeLate_PlayerValues_Assembly-CSharp.dll;
}

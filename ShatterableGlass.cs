﻿using System;
using System.Collections.Generic;
using UnityEngine;

// Token: 0x0200014D RID: 333
public class ShatterableGlass : MonoBehaviour
{
	// Token: 0x06000EDA RID: 3802 RVA: 0x000618F0 File Offset: 0x0005FAF0
	private void Awake()
	{
		this.source = SoundManager.Instance._effectsSource;
	}

	// Token: 0x06000EDB RID: 3803 RVA: 0x00061904 File Offset: 0x0005FB04
	private void Update()
	{
		if (this.SoundEmitter.volume != this.source.volume)
		{
			this.SoundEmitter.volume = this.source.volume;
		}
		if (this.canBreak)
		{
			this.canBreak = false;
			this.Shatter3DLocal(this.TempHitPoint, this.TempDirection);
		}
	}

	// Token: 0x06000EDC RID: 3804 RVA: 0x00061960 File Offset: 0x0005FB60
	private void Start()
	{
		this.col = base.GetComponent<BoxCollider>();
		float num = Mathf.Abs(this.col.size.x / 2f);
		float num2 = Mathf.Abs(this.col.size.y / 2f);
		this.Area = num * num2;
		this.Bounds[0] = new Vector2(num, num2);
		this.Bounds[1] = new Vector2(-num, num2);
		this.Bounds[2] = new Vector2(-num, -num2);
		this.Bounds[3] = new Vector2(num, -num2);
		this.SoundEmitter = base.GetComponent<AudioSource>();
		if (base.GetComponent<Renderer>() == null || base.GetComponent<MeshFilter>() == null)
		{
			Debug.LogError(base.gameObject.name + ": No Renderer and/or MeshFilter components!");
			global::UnityEngine.Object.Destroy(base.gameObject);
			return;
		}
		this.GlassMaterial = base.GetComponent<Renderer>().material;
		if (this.GlassSides && this.GlassSidesMaterial == null)
		{
			Debug.LogError(base.gameObject.name + ": GlassSide material must be assigned! Glass will be destroyed.");
			global::UnityEngine.Object.Destroy(base.gameObject);
		}
	}

	// Token: 0x06000EDD RID: 3805 RVA: 0x00061AA4 File Offset: 0x0005FCA4
	public void Shatter2D(Vector2 HitPoint)
	{
		this.Shatter(HitPoint, base.transform.forward);
	}

	// Token: 0x06000EDE RID: 3806 RVA: 0x00061AB8 File Offset: 0x0005FCB8
	public void Shatter3D(Vector3 hitPoint, Vector3 direction)
	{
		this.canBreak = true;
		this.TempHitPoint = hitPoint;
		this.TempDirection = direction;
	}

	// Token: 0x06000EDF RID: 3807 RVA: 0x00061AD0 File Offset: 0x0005FCD0
	public void Shatter3DLocal(Vector3 hitPoint, Vector3 direction)
	{
		ShatterableGlass.ShatterableGlassInfo shatterableGlassInfo = new ShatterableGlass.ShatterableGlassInfo(hitPoint, direction);
		Transform transform = base.gameObject.transform.parent;
		bool flag = true;
		while (transform != null)
		{
			if (transform.localScale.x != 1f || transform.localScale.y != 1f || transform.localScale.y != 1f)
			{
				flag = false;
			}
			transform = transform.parent;
		}
		if (!flag)
		{
			Debug.LogWarning(base.gameObject.name + ": scale of all parents in hierarchy recommended to be {1, 1, 1}. Glass may shatter weirdly.");
		}
		Vector3 vector = base.transform.TransformPoint(new Vector3(-1f, -1f));
		Vector3 vector2 = base.transform.TransformPoint(new Vector3(1f, -1f));
		float num = Vector3.Distance(shatterableGlassInfo.HitPoint, vector);
		float num2 = Vector3.Distance(vector2, vector);
		float num3 = Vector3.Distance(shatterableGlassInfo.HitPoint, vector2);
		float num4 = (num3 + num + num2) / 2f;
		float num5 = Mathf.Sqrt(num4 * (num4 - num3) * (num4 - num) * (num4 - num2));
		float num6 = 2f / num2 * num5;
		float num7 = Mathf.Sqrt(num * num - num6 * num6);
		num6 -= Mathf.Abs(this.col.size.y / 2f);
		num7 -= Mathf.Abs(this.col.size.x / 2f);
		this.Shatter(new Vector2(num7 * Mathf.Sign(this.col.size.x), num6 * Mathf.Sign(this.col.size.y)), shatterableGlassInfo.HitDirrection);
	}

	// Token: 0x06000EE0 RID: 3808 RVA: 0x00061C8C File Offset: 0x0005FE8C
	public void Shatter(Vector2 HitPoint, Vector3 ForceDirrection)
	{
		int num = 4 + (this.Sectors - 1) * 4;
		ShatterableGlass.BaseLine[] array = new ShatterableGlass.BaseLine[num];
		for (int i = 0; i < 4; i++)
		{
			array[i * this.Sectors] = new ShatterableGlass.BaseLine(HitPoint, this.Bounds[i], this.DetailsPerSector);
			float num2 = 1f / (float)this.Sectors;
			float num3 = num2;
			for (int j = 1; j < this.Sectors; j++)
			{
				array[i * this.Sectors + j] = new ShatterableGlass.BaseLine(HitPoint, Vector2.Lerp(this.Bounds[i], this.Bounds[(i + 1) % 4], num3), this.DetailsPerSector);
				num3 += num2;
			}
		}
		List<ShatterableGlass.Figure> list = new List<ShatterableGlass.Figure>();
		for (int k = 0; k < num; k++)
		{
			int num4 = (k + 1) % num;
			float num5 = Vector2.Distance(HitPoint, array[k].Points[this.DetailsPerSector]);
			float num6 = Vector2.Distance(HitPoint, array[num4].Points[this.DetailsPerSector]);
			float num7 = Vector2.Distance(array[k].Points[this.DetailsPerSector], array[num4].Points[this.DetailsPerSector]);
			float num8 = (num5 + num6 + num7) * 0.5f;
			if (Mathf.Sqrt(num8 * (num8 - num5) * (num8 - num6) * (num8 - num7)) < this.Area * this.SimplifyThreshold)
			{
				list.Add(new ShatterableGlass.Figure(new Vector2[]
				{
					array[k].Points[this.DetailsPerSector],
					array[num4].Points[this.DetailsPerSector],
					HitPoint
				}, this.DetailsPerSector / 2));
			}
			else
			{
				list.Add(new ShatterableGlass.Figure(new Vector2[]
				{
					array[k].Points[1],
					array[num4].Points[1],
					HitPoint
				}, 1));
				for (int l = 1; l < this.DetailsPerSector; l++)
				{
					list.Add(new ShatterableGlass.Figure(new Vector2[]
					{
						array[k].Points[l],
						array[(k + 1) % num].Points[l],
						array[k].Points[l + 1],
						array[(k + 1) % num].Points[l + 1]
					}, k + 1));
				}
			}
		}
		foreach (ShatterableGlass.Figure figure in list)
		{
			GameObject gameObject = new GameObject("GlassGib");
			gameObject.transform.rotation = base.transform.rotation;
			gameObject.transform.position = base.transform.position;
			if (this.AdoptFragments)
			{
				gameObject.transform.parent = base.transform.parent;
			}
			MeshFilter meshFilter = gameObject.AddComponent<MeshFilter>();
			MeshRenderer meshRenderer = gameObject.AddComponent<MeshRenderer>();
			if (this.GlassSides)
			{
				meshRenderer.materials = new Material[] { this.GlassMaterial, this.GlassSidesMaterial };
			}
			else
			{
				meshRenderer.material = this.GlassMaterial;
			}
			Mesh mesh = figure.GenerateMesh(this.GlassSides, this.GlassThickness / 2f, new Vector2(this.col.size.x, this.col.size.y));
			meshFilter.sharedMesh = mesh;
			if (!this.ShatterButNotBreak)
			{
				figure.GenerateCollider(this.GlassThickness, gameObject);
				Rigidbody rigidbody = gameObject.AddComponent<Rigidbody>();
				rigidbody.collisionDetectionMode = CollisionDetectionMode.ContinuousDynamic;
				rigidbody.AddForce(ForceDirrection * global::UnityEngine.Random.Range(this.Force, this.Force * 1.5f) / (float)figure.ForceScale);
				if (this.GibsOnSeparateLayer)
				{
					gameObject.layer = this.GibsLayer;
				}
				if (this.DestroyGibs)
				{
					float num9 = this.AfterSeconds * 0.1f;
					global::UnityEngine.Object.Destroy(gameObject, global::UnityEngine.Random.Range(this.AfterSeconds - num9, this.AfterSeconds + num9));
				}
			}
			else if (this.SlightlyRotateGibs)
			{
				gameObject.transform.Rotate(new Vector3(global::UnityEngine.Random.Range(-0.5f, 0.5f), global::UnityEngine.Random.Range(-0.5f, 0.5f), global::UnityEngine.Random.Range(-0.5f, 0.5f)));
			}
		}
		if (this.SoundEmitter)
		{
			this.SoundEmitter.Play();
		}
		global::UnityEngine.Object.Destroy(base.GetComponent<Renderer>());
		global::UnityEngine.Object.Destroy(base.GetComponent<MeshFilter>());
		global::UnityEngine.Object.Destroy(base.GetComponent<ShatterableGlass>());
		if (this.ShatterButNotBreak)
		{
			base.gameObject.tag = "Untagged";
			return;
		}
		if (base.GetComponent<MeshCollider>())
		{
			global::UnityEngine.Object.Destroy(base.GetComponent<MeshCollider>());
		}
		if (base.GetComponent<BoxCollider>())
		{
			global::UnityEngine.Object.Destroy(base.GetComponent<BoxCollider>());
		}
		if (!this.SoundEmitter)
		{
			global::UnityEngine.Object.Destroy(base.gameObject);
			return;
		}
		if (this.SoundEmitter.clip)
		{
			global::UnityEngine.Object.Destroy(base.gameObject, this.SoundEmitter.clip.length);
			return;
		}
		Debug.Log(base.gameObject.name + ": AudioSource component is present, but SoundClip is not set.");
	}

	// Token: 0x04000D71 RID: 3441
	private bool canBreak;

	// Token: 0x04000D72 RID: 3442
	private Vector3 TempHitPoint;

	// Token: 0x04000D73 RID: 3443
	private Vector3 TempDirection;

	// Token: 0x04000D74 RID: 3444
	public int Sectors = 3;

	// Token: 0x04000D75 RID: 3445
	public int DetailsPerSector = 3;

	// Token: 0x04000D76 RID: 3446
	public float SimplifyThreshold = 0.05f;

	// Token: 0x04000D77 RID: 3447
	public bool GlassSides = true;

	// Token: 0x04000D78 RID: 3448
	public Material GlassSidesMaterial;

	// Token: 0x04000D79 RID: 3449
	public float GlassThickness = 0.01f;

	// Token: 0x04000D7A RID: 3450
	public bool ShatterButNotBreak;

	// Token: 0x04000D7B RID: 3451
	public bool SlightlyRotateGibs = true;

	// Token: 0x04000D7C RID: 3452
	public bool DestroyGibs = true;

	// Token: 0x04000D7D RID: 3453
	public float AfterSeconds = 5f;

	// Token: 0x04000D7E RID: 3454
	public bool GibsOnSeparateLayer;

	// Token: 0x04000D7F RID: 3455
	public int GibsLayer;

	// Token: 0x04000D80 RID: 3456
	public float Force = 500f;

	// Token: 0x04000D81 RID: 3457
	public bool AdoptFragments;

	// Token: 0x04000D82 RID: 3458
	private Vector2[] Bounds = new Vector2[4];

	// Token: 0x04000D83 RID: 3459
	private float Area = 1f;

	// Token: 0x04000D84 RID: 3460
	private Material GlassMaterial;

	// Token: 0x04000D85 RID: 3461
	private AudioSource SoundEmitter;

	// Token: 0x04000D86 RID: 3462
	private BoxCollider col;

	// Token: 0x04000D87 RID: 3463
	private AudioSource source;

	// Token: 0x0200014E RID: 334
	private struct ShatterableGlassInfo
	{
		// Token: 0x06000EE2 RID: 3810 RVA: 0x000622CD File Offset: 0x000604CD
		public ShatterableGlassInfo(Vector3 HitPoint, Vector3 HitDirrection)
		{
			this.HitPoint = HitPoint;
			this.HitDirrection = HitDirrection;
		}

		// Token: 0x04000D88 RID: 3464
		public Vector3 HitPoint;

		// Token: 0x04000D89 RID: 3465
		public Vector3 HitDirrection;
	}

	// Token: 0x0200014F RID: 335
	private class Figure
	{
		// Token: 0x06000EE3 RID: 3811 RVA: 0x000622DD File Offset: 0x000604DD
		public Figure(Vector2[] Points, int ForceScale)
		{
			this.Points = Points;
			this.ForceScale = ForceScale;
		}

		// Token: 0x06000EE4 RID: 3812 RVA: 0x000622F4 File Offset: 0x000604F4
		public void GenerateCollider(float GlassThickness, GameObject Obj)
		{
			BoxCollider boxCollider = Obj.AddComponent<BoxCollider>();
			float num = Vector2.Distance(this.Points[2], this.Points[0]);
			float num2 = Vector2.Distance(this.Points[2], this.Points[1]);
			float num3 = Vector2.Distance(this.Points[1], this.Points[0]);
			float num4 = num + num2 + num3;
			float num5 = (num * this.Points[0].x + num2 * this.Points[1].x + num3 * this.Points[2].x) / num4;
			float num6 = (num * this.Points[0].y + num2 * this.Points[1].y + num3 * this.Points[2].y) / num4;
			num4 /= 2f;
			float num7 = Mathf.Sqrt((num4 - num) * (num4 - num2) * (num4 - num3) / num4);
			num7 *= Mathf.Sqrt(2f);
			boxCollider.center = new Vector3(num5, num6, 0f);
			boxCollider.size = new Vector3(num7, num7, GlassThickness);
		}

		// Token: 0x06000EE5 RID: 3813 RVA: 0x00062434 File Offset: 0x00060634
		public Mesh GenerateMesh(bool GenerateGlassSides, float GlassHalfThickness, Vector2 UVScale)
		{
			Mesh mesh = new Mesh();
			mesh.name = "GlassGib";
			if (GenerateGlassSides)
			{
				mesh.subMeshCount = 2;
			}
			bool flag = this.Points.Length == 3;
			Vector3[] array = new Vector3[flag ? (GenerateGlassSides ? 9 : 3) : (GenerateGlassSides ? 12 : 4)];
			Vector2[] array2 = new Vector2[array.Length];
			for (int i = 0; i < this.Points.Length; i++)
			{
				array[i] = this.Points[i];
				array2[i] = new Vector2(this.Points[i].x / UVScale.x, this.Points[i].y / UVScale.y) + new Vector2(0.5f, 0.5f);
			}
			int[] array4;
			if (flag)
			{
				int[] array3 = new int[3];
				array3[0] = 2;
				array3[1] = 1;
				array4 = array3;
			}
			else
			{
				array4 = new int[] { 0, 1, 2, 3, 2, 1 };
			}
			if (GenerateGlassSides)
			{
				int[] array5;
				if (flag)
				{
					for (int j = 0; j < 3; j++)
					{
						this.GlassSideVertex(this.Points[j], ref array[j * 2 + 3], ref array[j * 2 + 4], GlassHalfThickness);
					}
					array5 = new int[]
					{
						3, 4, 5, 4, 6, 5, 3, 4, 7, 7,
						8, 4, 5, 6, 8, 8, 7, 5
					};
				}
				else
				{
					for (int k = 0; k < 4; k++)
					{
						this.GlassSideVertex(this.Points[k], ref array[k * 2 + 4], ref array[k * 2 + 5], GlassHalfThickness);
					}
					array5 = new int[]
					{
						7, 5, 4, 6, 7, 4, 11, 7, 6, 10,
						11, 6, 10, 11, 9, 9, 8, 10, 8, 9,
						5, 8, 4, 5
					};
				}
				mesh.vertices = array;
				mesh.SetTriangles(array4, 0);
				mesh.SetTriangles(array5, 1);
			}
			else
			{
				mesh.vertices = array;
				mesh.triangles = array4;
			}
			mesh.uv = array2;
			return mesh;
		}

		// Token: 0x06000EE6 RID: 3814 RVA: 0x00062615 File Offset: 0x00060815
		private void GlassSideVertex(Vector2 Ref, ref Vector3 A, ref Vector3 B, float GlassHalfThickness)
		{
			A = new Vector3(Ref.x, Ref.y, GlassHalfThickness);
			B = new Vector3(Ref.x, Ref.y, -GlassHalfThickness);
		}

		// Token: 0x04000D8A RID: 3466
		public Vector2[] Points;

		// Token: 0x04000D8B RID: 3467
		public int ForceScale;
	}

	// Token: 0x02000150 RID: 336
	private class BaseLine
	{
		// Token: 0x06000EE7 RID: 3815 RVA: 0x0006264C File Offset: 0x0006084C
		public BaseLine(Vector2 HitPoint, Vector2 End, int Count)
		{
			this.Points = new Vector2[Count + 1];
			this.Points[0] = HitPoint;
			this.Points[Count] = End;
			float num = 1f / (float)Count;
			float num2 = num;
			float num3 = Mathf.Atan2(Mathf.Max(HitPoint.y, End.y) - Mathf.Min(HitPoint.y, End.y), Mathf.Max(End.x, HitPoint.x) - Mathf.Min(HitPoint.x, End.x));
			float num4 = 0.7853982f;
			float num5 = 1.5707964f;
			if (num3 > num4)
			{
				num3 = num5 - num3;
			}
			float num6 = num3 / num4;
			for (int i = 0; i < Count - 1; i++)
			{
				this.Points[i + 1] = Vector2.Lerp(HitPoint, End, num2 * Mathf.Lerp(1f, Mathf.Sqrt(2f) / 2f, num6));
				num2 += num;
			}
		}

		// Token: 0x04000D8C RID: 3468
		public Vector2[] Points;
	}
}

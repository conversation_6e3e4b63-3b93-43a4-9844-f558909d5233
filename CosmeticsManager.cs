﻿using System;
using System.Collections;
using Newtonsoft.Json.Linq;
using UnityEngine;

// Token: 0x02000047 RID: 71
public class CosmeticsManager : MonoBehaviour, ISaveable
{
	// Token: 0x06000362 RID: 866 RVA: 0x0001A59E File Offset: 0x0001879E
	private void Awake()
	{
		if (CosmeticsManager.Instance == null)
		{
			CosmeticsManager.Instance = this;
			return;
		}
		global::UnityEngine.Object.Destroy(base.gameObject);
	}

	// Token: 0x06000363 RID: 867 RVA: 0x0001A5C0 File Offset: 0x000187C0
	private void Start()
	{
		this.hatsChildren = this.hatsParent.GetComponentsInChildren<CosmeticInstance>();
		this.hats = new GameObject[this.hatsChildren.Length];
		for (int i = 0; i < this.hats.Length; i++)
		{
			this.hats[i] = this.hatsChildren[i].hat;
			this.hatsChildren[i].index = i;
		}
		this.suitsChildren = this.suitsParent.GetComponentsInChildren<CosmeticInstance>();
		this.cigsChildren = this.cigsParent.GetComponentsInChildren<CosmeticInstance>();
		base.StartCoroutine(this.DressAboubi());
	}

	// Token: 0x06000364 RID: 868 RVA: 0x0001A658 File Offset: 0x00018858
	public void UpdateUnlockable()
	{
		ProgressManager instance = ProgressManager.Instance;
		foreach (ProgressInstance progressInstance in instance.instances)
		{
			if (progressInstance.cosmetic != null && (float)progressInstance.xpToUnlock <= instance.xp)
			{
				progressInstance.cosmetic.Unlock();
			}
		}
	}

	// Token: 0x06000365 RID: 869 RVA: 0x0001A6AC File Offset: 0x000188AC
	private IEnumerator DressAboubi()
	{
		yield return new WaitForSeconds(1f);
		this.LoadDress();
		yield break;
	}

	// Token: 0x06000366 RID: 870 RVA: 0x0001A6BB File Offset: 0x000188BB
	[ContextMenu("Load Dress")]
	private void LoadDress()
	{
		this.ChangeDress(this.suitsChildren[this.suitIndex]);
		this.ChangeDress(this.hatsChildren[this.hatIndex]);
		this.ChangeDress(this.cigsChildren[this.cigIndex]);
	}

	// Token: 0x06000367 RID: 871 RVA: 0x0001A6F6 File Offset: 0x000188F6
	private void Update()
	{
		if (PauseManager.Instance.inMainMenu && !this.inMenu)
		{
			this.inMenu = true;
			base.StartCoroutine(this.DressAboubi());
			return;
		}
		if (!PauseManager.Instance.inMainMenu)
		{
			this.inMenu = false;
		}
	}

	// Token: 0x06000368 RID: 872 RVA: 0x0001A734 File Offset: 0x00018934
	public void ChangeDress(CosmeticInstance cosmeticInstance)
	{
		if (cosmeticInstance.isHat)
		{
			this.currenthat = cosmeticInstance.hat;
			this.hatIndex = cosmeticInstance.index;
		}
		else if (cosmeticInstance.isCig)
		{
			this.currentcigIndex = cosmeticInstance.index;
			this.cigIndex = cosmeticInstance.index;
		}
		else
		{
			this.currentsuitIndex = cosmeticInstance.index;
			this.suitIndex = cosmeticInstance.index;
		}
		this.hatIndex = (this.hatIndex + this.hatsChildren.Length) % this.hatsChildren.Length;
		this.suitIndex = (this.suitIndex + this.suitsChildren.Length) % this.suitsChildren.Length;
		this.cigIndex = (this.cigIndex + this.cigsChildren.Length) % this.cigsChildren.Length;
		AboubiPreview.Instance.ChangeDress(this.hatsChildren[this.hatIndex].hat, this.mats[this.suitIndex], this.cigs[this.cigIndex]);
		if (LobbyController.Instance.LocalPlayerController != null)
		{
			LobbyController.Instance.LocalPlayerController.DressAboubi(this.currenthat, this.currentsuitIndex, this.currentcigIndex, LobbyController.Instance.LocalPlayerController.PlayerId);
		}
		SaveLoadSystem.Instance.Save();
	}

	// Token: 0x06000369 RID: 873 RVA: 0x0001A87C File Offset: 0x00018A7C
	public object SaveState()
	{
		return new CosmeticsManager.SaveData
		{
			hatIndexSaved = this.hatIndex,
			suitIndexSaved = this.suitIndex,
			cigIndexSaved = this.cigIndex
		};
	}

	// Token: 0x0600036A RID: 874 RVA: 0x0001A8C0 File Offset: 0x00018AC0
	public void LoadState(JObject state)
	{
		CosmeticsManager.SaveData saveData = state.ToObject<CosmeticsManager.SaveData>();
		this.hatIndex = saveData.hatIndexSaved;
		this.suitIndex = saveData.suitIndexSaved;
		this.cigIndex = saveData.cigIndexSaved;
	}

	// Token: 0x040003CB RID: 971
	public static CosmeticsManager Instance;

	// Token: 0x040003CC RID: 972
	[SerializeField]
	private Transform suitsParent;

	// Token: 0x040003CD RID: 973
	[SerializeField]
	private Transform hatsParent;

	// Token: 0x040003CE RID: 974
	[SerializeField]
	private Transform cigsParent;

	// Token: 0x040003CF RID: 975
	public Material[] mats;

	// Token: 0x040003D0 RID: 976
	public Material[] fparmsMats;

	// Token: 0x040003D1 RID: 977
	public GameObject[] hats;

	// Token: 0x040003D2 RID: 978
	public GameObject[] cigs;

	// Token: 0x040003D3 RID: 979
	public int suitIndex;

	// Token: 0x040003D4 RID: 980
	public int hatIndex;

	// Token: 0x040003D5 RID: 981
	public int cigIndex;

	// Token: 0x040003D6 RID: 982
	private CosmeticInstance[] suitsChildren;

	// Token: 0x040003D7 RID: 983
	private CosmeticInstance[] hatsChildren;

	// Token: 0x040003D8 RID: 984
	private CosmeticInstance[] cigsChildren;

	// Token: 0x040003D9 RID: 985
	private bool inMenu;

	// Token: 0x040003DA RID: 986
	private bool activate;

	// Token: 0x040003DB RID: 987
	public GameObject currenthat;

	// Token: 0x040003DC RID: 988
	public int currentsuitIndex;

	// Token: 0x040003DD RID: 989
	public int currentcigIndex;

	// Token: 0x02000048 RID: 72
	[Serializable]
	private struct SaveData
	{
		// Token: 0x040003DE RID: 990
		public int hatIndexSaved;

		// Token: 0x040003DF RID: 991
		public int suitIndexSaved;

		// Token: 0x040003E0 RID: 992
		public int cigIndexSaved;
	}
}

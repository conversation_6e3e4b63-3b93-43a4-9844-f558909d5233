﻿using System;
using System.Collections.Generic;
using Newtonsoft.Json.Linq;
using UnityEngine;

// Token: 0x02000101 RID: 257
public class SaveableEntity : MonoBehaviour
{
	// Token: 0x170000C6 RID: 198
	// (get) Token: 0x06000CFA RID: 3322 RVA: 0x0005889E File Offset: 0x00056A9E
	public string Id
	{
		get
		{
			return this.id;
		}
	}

	// Token: 0x06000CFB RID: 3323 RVA: 0x000588A8 File Offset: 0x00056AA8
	[ContextMenu("Generate Id")]
	private void GenerateId()
	{
		this.id = Guid.NewGuid().ToString();
	}

	// Token: 0x06000CFC RID: 3324 RVA: 0x000588D0 File Offset: 0x00056AD0
	public object SaveState()
	{
		Dictionary<string, object> dictionary = new Dictionary<string, object>();
		foreach (ISaveable saveable in base.GetComponents<ISaveable>())
		{
			dictionary[saveable.GetType().ToString()] = saveable.SaveState();
		}
		return dictionary;
	}

	// Token: 0x06000CFD RID: 3325 RVA: 0x00058914 File Offset: 0x00056B14
	public void LoadState(JObject state)
	{
		Dictionary<string, JObject> dictionary = state.ToObject<Dictionary<string, JObject>>();
		foreach (ISaveable saveable in base.GetComponents<ISaveable>())
		{
			string text = saveable.GetType().ToString();
			JObject jobject;
			if (dictionary.TryGetValue(text, out jobject))
			{
				saveable.LoadState(jobject);
			}
		}
	}

	// Token: 0x04000B43 RID: 2883
	[SerializeField]
	private string id;
}

﻿using System;
using UnityEngine;

// Token: 0x02000003 RID: 3
public class FlingTrigger : MonoBehaviour
{
	// Token: 0x0600000C RID: 12 RVA: 0x00002793 File Offset: 0x00000993
	private void Update()
	{
		if (!this.active)
		{
			this.cooldownTimer += Time.deltaTime;
			if (this.cooldownTimer > this.cooldown)
			{
				this.active = true;
				this.cooldownTimer = 0f;
			}
		}
	}

	// Token: 0x0600000D RID: 13 RVA: 0x000027D0 File Offset: 0x000009D0
	private void OnTriggerEnter(Collider other)
	{
		if (!this.active)
		{
			return;
		}
		if (other.gameObject.tag == "Player")
		{
			this.c = other;
			this.pig.Fling();
			base.Invoke("Fling", 0.25f);
			this.active = false;
		}
	}

	// Token: 0x0600000E RID: 14 RVA: 0x00002826 File Offset: 0x00000A26
	private void Fling()
	{
		if (this.c == null)
		{
			return;
		}
		this.c.gameObject.GetComponent<FirstPersonController>().AddVerticalForce(Vector3.up, 25f);
	}

	// Token: 0x04000016 RID: 22
	public float cooldown = 2f;

	// Token: 0x04000017 RID: 23
	private float cooldownTimer;

	// Token: 0x04000018 RID: 24
	private bool active = true;

	// Token: 0x04000019 RID: 25
	public Pig pig;

	// Token: 0x0400001A RID: 26
	private Collider c;
}

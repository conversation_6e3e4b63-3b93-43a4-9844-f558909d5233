﻿using System;
using UnityEngine;

// Token: 0x02000130 RID: 304
public class OnePlayerActive : MonoBehaviour
{
	// Token: 0x06000E6D RID: 3693 RVA: 0x0005F989 File Offset: 0x0005DB89
	private void Start()
	{
		this.manager = SteamLobby.Instance;
		this.initScale = base.transform.localScale;
	}

	// Token: 0x06000E6E RID: 3694 RVA: 0x0005F9A7 File Offset: 0x0005DBA7
	private void Update()
	{
		base.transform.localScale = ((this.manager.players.Count > 1) ? Vector3.zero : this.initScale);
	}

	// Token: 0x04000CFD RID: 3325
	private SteamLobby manager;

	// Token: 0x04000CFE RID: 3326
	private Vector3 initScale;
}

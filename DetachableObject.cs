﻿using System;
using FishNet.Object;
using UnityEngine;

// Token: 0x0200004A RID: 74
public class DetachableObject : NetworkObject
{
	// Token: 0x06000372 RID: 882 RVA: 0x0001A962 File Offset: 0x00018B62
	private new void Start()
	{
		this.rb = base.GetComponent<Rigidbody>();
		this.rb.isKinematic = true;
	}

	// Token: 0x06000373 RID: 883 RVA: 0x0001A97C File Offset: 0x00018B7C
	public void Detach(Vector3 hitNormal, Vector3 direction)
	{
		this.rb.isKinematic = false;
		base.gameObject.layer = 18;
		base.transform.SetParent(null);
		global::UnityEngine.Object.Instantiate<GameObject>(this.vfx, base.transform.position, Quaternion.LookRotation(hitNormal));
		this.rb.AddForce(direction * this.ejectForce, ForceMode.Impulse);
	}

	// Token: 0x040003E4 RID: 996
	[SerializeField]
	private GameObject vfx;

	// Token: 0x040003E5 RID: 997
	[SerializeField]
	private float ejectForce = 4f;

	// Token: 0x040003E6 RID: 998
	private Rigidbody rb;
}

﻿using System;

// Token: 0x020000BA RID: 186
[Serializable]
public struct WeaponData
{
	// Token: 0x06000A59 RID: 2649 RVA: 0x0004B6D0 File Offset: 0x000498D0
	public WeaponData(string weaponName, uint spawnChance, bool isSpawnable)
	{
		this.WeaponName = weaponName;
		this.SpawnChance = spawnChance;
		this.IsSpawnable = isSpawnable;
	}

	// Token: 0x04000917 RID: 2327
	public string WeaponName;

	// Token: 0x04000918 RID: 2328
	public uint SpawnChance;

	// Token: 0x04000919 RID: 2329
	public bool IsSpawnable;
}

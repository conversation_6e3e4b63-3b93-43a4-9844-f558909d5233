﻿using System;
using UnityEngine;

// Token: 0x0200014A RID: 330
public class Player : MonoBehaviour
{
	// Token: 0x06000ECF RID: 3791 RVA: 0x0006153C File Offset: 0x0005F73C
	private float Avg(float[] AvgValues, float New)
	{
		if (!this.Smooth)
		{
			return New;
		}
		float num = New;
		for (int i = 31; i > 0; i--)
		{
			AvgValues[i] = AvgValues[i - 1];
			num += AvgValues[i - 1];
		}
		AvgValues[0] = New;
		return num / 32f;
	}

	// Token: 0x06000ED0 RID: 3792 RVA: 0x0006157D File Offset: 0x0005F77D
	private void Start()
	{
		this.CrosshairSize = Screen.height / 30;
		Cursor.lockState = CursorLockMode.Locked;
	}

	// Token: 0x06000ED1 RID: 3793 RVA: 0x00061594 File Offset: 0x0005F794
	private void OnGUI()
	{
		GUI.DrawTexture(new Rect((float)(Screen.width / 2 - this.CrosshairSize), (float)(Screen.height / 2 - this.CrosshairSize), (float)(this.CrosshairSize * 2), (float)(this.CrosshairSize * 2)), this.Crosshair);
	}

	// Token: 0x06000ED2 RID: 3794 RVA: 0x000615E4 File Offset: 0x0005F7E4
	private void Update()
	{
		CharacterController component = base.GetComponent<CharacterController>();
		if (component.isGrounded)
		{
			this.MoveDirection = new Vector3(Input.GetAxis("Horizontal"), 0f, Input.GetAxis("Vertical"));
			this.MoveDirection = base.transform.TransformDirection(this.MoveDirection);
			this.MoveDirection *= this.MoveSpeed;
			if (Input.GetButton("Jump"))
			{
				this.MoveDirection.y = this.JumpSpeed;
			}
		}
		this.MoveDirection.y = this.MoveDirection.y - this.FallSpeed * Time.deltaTime;
		component.Move(this.MoveDirection * Time.deltaTime);
		this.Yaw += this.Sensitivity * this.Avg(this.YawAvg, Input.GetAxis("Mouse X"));
		this.Pitch -= this.Sensitivity * this.Avg(this.PitchAvg, Input.GetAxis("Mouse Y"));
		Camera.main.transform.eulerAngles = new Vector3(this.Pitch, this.Yaw, 0f);
		base.transform.eulerAngles = new Vector3(0f, this.Yaw, 0f);
		bool key = Input.GetKey(KeyCode.E);
		if (key && !this.PrevUse)
		{
			UseArea[] useAreas = this.UseAreas;
			for (int i = 0; i < useAreas.Length; i++)
			{
				useAreas[i].Use();
			}
		}
		this.PrevUse = key;
		bool key2 = Input.GetKey(KeyCode.Q);
		if (key2 && !this.PrevSlowMotion)
		{
			Time.timeScale = 0.25f;
			Time.fixedDeltaTime /= 4f;
		}
		if (!key2 && this.PrevSlowMotion)
		{
			Time.timeScale = 1f;
			Time.fixedDeltaTime *= 4f;
		}
		this.PrevSlowMotion = key2;
	}

	// Token: 0x04000D5F RID: 3423
	public float MoveSpeed = 6f;

	// Token: 0x04000D60 RID: 3424
	public float JumpSpeed = 8f;

	// Token: 0x04000D61 RID: 3425
	public float FallSpeed = 20f;

	// Token: 0x04000D62 RID: 3426
	private Vector3 MoveDirection = Vector3.zero;

	// Token: 0x04000D63 RID: 3427
	public float Sensitivity = 2f;

	// Token: 0x04000D64 RID: 3428
	public bool Smooth;

	// Token: 0x04000D65 RID: 3429
	public Texture Crosshair;

	// Token: 0x04000D66 RID: 3430
	private int CrosshairSize;

	// Token: 0x04000D67 RID: 3431
	public UseArea[] UseAreas;

	// Token: 0x04000D68 RID: 3432
	private float Yaw;

	// Token: 0x04000D69 RID: 3433
	private float Pitch;

	// Token: 0x04000D6A RID: 3434
	private float[] YawAvg = new float[32];

	// Token: 0x04000D6B RID: 3435
	private float[] PitchAvg = new float[32];

	// Token: 0x04000D6C RID: 3436
	private bool PrevUse;

	// Token: 0x04000D6D RID: 3437
	private bool PrevSlowMotion;
}

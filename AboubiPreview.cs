﻿using System;
using FishNet.Object;
using UnityEngine;

// Token: 0x0200003D RID: 61
public class AboubiPreview : MonoBehaviour
{
	// Token: 0x06000310 RID: 784 RVA: 0x000194E0 File Offset: 0x000176E0
	private void Awake()
	{
		if (AboubiPreview.Instance == null)
		{
			AboubiPreview.Instance = this;
		}
	}

	// Token: 0x06000311 RID: 785 RVA: 0x000194F8 File Offset: 0x000176F8
	public void ChangeDress(GameObject hat, Material mat, GameObject cig)
	{
		if (hat != null)
		{
			if (this.currentHat != null)
			{
				global::UnityEngine.Object.Destroy(this.currentHat);
			}
			this.currentHat = global::UnityEngine.Object.Instantiate<GameObject>(hat, this.hatToWearPosition.position, this.hatToWearPosition.rotation, this.hatToWearPosition);
			this.currentHat.AddComponent<HatPosition>();
			this.currentHat.GetComponent<HatPosition>().reference = this.hatToWearPosition;
			this.currentHat.transform.localPosition = Vector3.zero;
			global::UnityEngine.Object.Destroy(this.currentHat.GetComponent<NetworkObject>());
			this.currentHat.SetActive(true);
			this.SetGameLayerRecursive(this.currentHat, 23);
		}
		if (cig != null)
		{
			if (this.currentCig != null)
			{
				global::UnityEngine.Object.Destroy(this.currentCig);
			}
			this.currentCig = global::UnityEngine.Object.Instantiate<GameObject>(cig, this.hatToWearPosition.position, this.hatToWearPosition.rotation, this.hatToWearPosition);
			this.currentCig.AddComponent<HatPosition>();
			this.currentCig.GetComponent<HatPosition>().reference = this.hatToWearPosition;
			this.currentCig.transform.localPosition = Vector3.zero;
			global::UnityEngine.Object.Destroy(this.currentCig.GetComponent<NetworkObject>());
			this.currentCig.SetActive(true);
			this.SetGameLayerRecursive(this.currentCig, 23);
		}
		if (mat != null)
		{
			GameObject[] array = this.meshesToChange;
			for (int i = 0; i < array.Length; i++)
			{
				array[i].GetComponent<SkinnedMeshRenderer>().material = mat;
			}
		}
	}

	// Token: 0x06000312 RID: 786 RVA: 0x00019690 File Offset: 0x00017890
	private void SetGameLayerRecursive(GameObject _go, int _layer)
	{
		_go.layer = _layer;
		foreach (object obj in _go.transform)
		{
			Transform transform = (Transform)obj;
			transform.gameObject.layer = _layer;
			if (transform.GetComponentInChildren<Transform>() != null)
			{
				this.SetGameLayerRecursive(transform.gameObject, _layer);
			}
		}
	}

	// Token: 0x06000313 RID: 787 RVA: 0x00019710 File Offset: 0x00017910
	private void Update()
	{
		base.transform.Rotate(0f, this.rotateSpeed * Time.deltaTime, 0f);
	}

	// Token: 0x04000399 RID: 921
	public static AboubiPreview Instance;

	// Token: 0x0400039A RID: 922
	[SerializeField]
	private float rotateSpeed = 20f;

	// Token: 0x0400039B RID: 923
	[SerializeField]
	private GameObject[] meshesToChange;

	// Token: 0x0400039C RID: 924
	[SerializeField]
	private Transform hatToWearPosition;

	// Token: 0x0400039D RID: 925
	private GameObject currentHat;

	// Token: 0x0400039E RID: 926
	private GameObject currentCig;
}

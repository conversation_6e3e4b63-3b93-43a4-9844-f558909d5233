﻿using System;
using UnityEngine;
using UnityEngine.EventSystems;

// Token: 0x0200012A RID: 298
public class MapInstanceButton : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IPointerEnterHandler, IEventSystemHandler, IPointerExitHandler
{
	// Token: 0x06000E49 RID: 3657 RVA: 0x0005F25B File Offset: 0x0005D45B
	public void OnPointerEnter(PointerEventData eventData)
	{
		if (this.isExplore)
		{
			FloatingName.Instance.nameToShow = "Click to explore map";
			return;
		}
		FloatingName.Instance.nameToShow = (this.parentScript.selected ? "Click to remove map" : "Click to add map");
	}

	// Token: 0x06000E4A RID: 3658 RVA: 0x0001A57E File Offset: 0x0001877E
	public void OnPointerExit(PointerEventData eventData)
	{
		FloatingName.Instance.nameToShow = "";
	}

	// Token: 0x04000CDA RID: 3290
	[SerializeField]
	private bool isExplore;

	// Token: 0x04000CDB RID: 3291
	[SerializeField]
	private MapInstance parentScript;
}

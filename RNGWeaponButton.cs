﻿using System;
using UnityEngine;
using UnityEngine.UI;

// Token: 0x02000010 RID: 16
public class RNGWeaponButton : MonoBehaviour
{
	// Token: 0x0600006A RID: 106 RVA: 0x00004193 File Offset: 0x00002393
	private void Awake()
	{
		base.gameObject.SetActive(SteamLobby.ownDlc0);
		RNGWeaponButton.toggle = base.GetComponent<Toggle>();
	}

	// Token: 0x0600006B RID: 107 RVA: 0x000041B0 File Offset: 0x000023B0
	public void Toggle(bool state)
	{
		if (SpawnerManager.Instance && SpawnerManager.Instance.IsServer)
		{
			SpawnerManager.Instance.sync___set_value_randomiseWeapons(state, true);
		}
	}

	// Token: 0x04000079 RID: 121
	public static Toggle toggle;
}

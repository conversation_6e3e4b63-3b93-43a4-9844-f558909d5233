﻿using System;
using UnityEngine;

namespace ch.sycoforge.Decal.Demo
{
	// Token: 0x0200017E RID: 382
	public class AdvancedBulletHoles : MonoBehaviour
	{
		// Token: 0x06000FB7 RID: 4023 RVA: 0x00067010 File Offset: 0x00065210
		private void Start()
		{
			if (this.DecalPrefab == null)
			{
				Debug.LogError("The AdvancedBulletHoles script has no decal prefab attached.");
			}
			EasyDecal.HideMesh = false;
		}

		// Token: 0x06000FB8 RID: 4024 RVA: 0x00067030 File Offset: 0x00065230
		private void Update()
		{
			if (Input.GetMouseButtonUp(0))
			{
				Ray ray = Camera.main.ScreenPointToRay(Input.mousePosition);
				RaycastHit raycastHit;
				if (Physics.Raycast(ray, out raycastHit, 200f))
				{
					GameObject gameObject = raycastHit.collider.gameObject;
					Vector3 point = raycastHit.point;
					RaycastHit[] array = Physics.SphereCastAll(ray, this.CastRadius, Vector3.Distance(Camera.main.transform.position, point) + 2f);
					Vector3 vector = raycastHit.normal;
					if (array.Length != 0)
					{
						foreach (RaycastHit raycastHit2 in array)
						{
							Debug.DrawLine(ray.origin, raycastHit2.point, Color.red);
							vector += raycastHit2.normal;
						}
					}
					vector /= (float)(array.Length + 1);
					EasyDecal.ProjectAt(this.DecalPrefab.gameObject, gameObject, point, vector, true);
					if (this.ImpactParticles != null)
					{
						Quaternion quaternion = Quaternion.FromToRotation(Vector3.up, vector);
						global::UnityEngine.Object.Instantiate<GameObject>(this.ImpactParticles, point, quaternion);
					}
				}
			}
		}

		// Token: 0x04000E9C RID: 3740
		public EasyDecal DecalPrefab;

		// Token: 0x04000E9D RID: 3741
		public GameObject ImpactParticles;

		// Token: 0x04000E9E RID: 3742
		public float CastRadius = 0.25f;
	}
}

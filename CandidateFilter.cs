﻿using System;
using System.Collections.Generic;
using ch.sycoforge.Decal;
using ch.sycoforge.Decal.Projectors;
using UnityEngine;

// Token: 0x02000151 RID: 337
[RequireComponent(typeof(EasyDecal))]
public class CandidateFilter : MonoBehaviour
{
	// Token: 0x06000EE8 RID: 3816 RVA: 0x00062744 File Offset: 0x00060944
	private void Start()
	{
		this.decal = base.GetComponent<EasyDecal>();
		ch.sycoforge.Decal.Projectors.Projector projector = this.decal.Projector;
		if (projector != null && projector is BoxProjector)
		{
			(projector as BoxProjector).OnCandidatesProcessed += this.bp_OnCandidatesProcessed;
		}
	}

	// Token: 0x06000EE9 RID: 3817 RVA: 0x0006278C File Offset: 0x0006098C
	private void bp_OnCandidatesProcessed(List<Collider> colliders)
	{
		List<Collider> list = new List<Collider>();
		foreach (Collider collider in colliders)
		{
			if (!collider.gameObject.Equals(this.ExclusiveReceiver))
			{
				list.Add(collider);
			}
		}
		foreach (Collider collider2 in list)
		{
			colliders.Remove(collider2);
		}
	}

	// Token: 0x04000D8D RID: 3469
	public GameObject ExclusiveReceiver;

	// Token: 0x04000D8E RID: 3470
	private EasyDecal decal;
}

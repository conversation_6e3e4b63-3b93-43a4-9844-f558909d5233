﻿using System;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

// Token: 0x02000126 RID: 294
public class DlcOutButton : MonoBehaviour, IPointerEnterHandler, IEventSystemHandler, IPointerExitHandler
{
	// Token: 0x06000E34 RID: 3636 RVA: 0x0005EE25 File Offset: 0x0005D025
	private void Awake()
	{
		this.button = base.GetComponent<Button>();
	}

	// Token: 0x06000E35 RID: 3637 RVA: 0x000023D6 File Offset: 0x000005D6
	private void Start()
	{
	}

	// Token: 0x06000E36 RID: 3638 RVA: 0x000023D6 File Offset: 0x000005D6
	public void OnPointerEnter(PointerEventData eventData)
	{
	}

	// Token: 0x06000E37 RID: 3639 RVA: 0x000023D6 File Offset: 0x000005D6
	public void OnPointerExit(PointerEventData eventData)
	{
	}

	// Token: 0x04000CC2 RID: 3266
	private Button button;
}

﻿using System;
using FishNet;
using FishNet.Connection;
using FishNet.Managing;
using FishNet.Object;
using FishNet.Object.Delegating;
using FishNet.Serializing;
using FishNet.Transporting;
using UnityEngine;
using UnityEngine.InputSystem;

// Token: 0x02000032 RID: 50
public class PlayerShoot : NetworkBehaviour
{
	// Token: 0x060002B7 RID: 695 RVA: 0x00017A68 File Offset: 0x00015C68
	public virtual void Awake()
	{
		this.NetworkInitialize___Early();
		this.Awake___UserLogic();
		this.NetworkInitialize__Late();
	}

	// Token: 0x060002B8 RID: 696 RVA: 0x00017A7C File Offset: 0x00015C7C
	private void OnEnable()
	{
		this.fire1 = this.playerControls.Player.LeftClick;
		this.fire1.Enable();
		this.fire1.performed += this.Fire;
		this.fire2 = this.playerControls.Player.RightClick;
		this.fire2.Enable();
		this.fire2.performed += this.Fire;
	}

	// Token: 0x060002B9 RID: 697 RVA: 0x00017B00 File Offset: 0x00015D00
	private void OnDisable()
	{
		this.fire1.Disable();
		this.fire1.performed -= this.Fire;
		this.fire2.Disable();
		this.fire2.performed -= this.Fire;
	}

	// Token: 0x060002BA RID: 698 RVA: 0x00017B51 File Offset: 0x00015D51
	private void Update()
	{
		if (this.fireTimer > 0f)
		{
			this.fireTimer -= Time.deltaTime;
		}
	}

	// Token: 0x060002BB RID: 699 RVA: 0x00017B74 File Offset: 0x00015D74
	private void Fire(InputAction.CallbackContext ctx)
	{
		if (!base.IsOwner)
		{
			return;
		}
		if (this.fireTimer > 0f)
		{
			return;
		}
		this.ShootServer((float)this.damage, this.cam.transform.position, this.cam.transform.forward);
		this.fireTimer = this.timeBetweenFire;
	}

	// Token: 0x060002BC RID: 700 RVA: 0x00017BD4 File Offset: 0x00015DD4
	[ServerRpc(RequireOwnership = false)]
	private void ShootServer(float damageToGive, Vector3 position, Vector3 direction)
	{
		this.RpcWriter___Server_ShootServer_1867452766(damageToGive, position, direction);
	}

	// Token: 0x060002BE RID: 702 RVA: 0x00017C0D File Offset: 0x00015E0D
	public virtual void NetworkInitialize___Early()
	{
		if (this.NetworkInitializeEarly_PlayerShoot_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeEarly_PlayerShoot_Assembly-CSharp.dll = true;
		base.RegisterServerRpc(0U, new ServerRpcDelegate(this.RpcReader___Server_ShootServer_1867452766));
	}

	// Token: 0x060002BF RID: 703 RVA: 0x00017C37 File Offset: 0x00015E37
	public virtual void NetworkInitialize__Late()
	{
		if (this.NetworkInitializeLate_PlayerShoot_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeLate_PlayerShoot_Assembly-CSharp.dll = true;
	}

	// Token: 0x060002C0 RID: 704 RVA: 0x00017C4A File Offset: 0x00015E4A
	public override void NetworkInitializeIfDisabled()
	{
		this.NetworkInitialize___Early();
		this.NetworkInitialize__Late();
	}

	// Token: 0x060002C1 RID: 705 RVA: 0x00017C58 File Offset: 0x00015E58
	private void RpcWriter___Server_ShootServer_1867452766(float damageToGive, Vector3 position, Vector3 direction)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteSingle(damageToGive, AutoPackType.Unpacked);
		writer.WriteVector3(position);
		writer.WriteVector3(direction);
		base.SendServerRpc(0U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x060002C2 RID: 706 RVA: 0x00017D20 File Offset: 0x00015F20
	private void RpcLogic___ShootServer_1867452766(float damageToGive, Vector3 position, Vector3 direction)
	{
		RaycastHit raycastHit;
		PlayerHealth playerHealth;
		if (Physics.Raycast(position, direction, out raycastHit, float.PositiveInfinity) && raycastHit.transform.TryGetComponent<PlayerHealth>(out playerHealth) && raycastHit.transform.gameObject != base.gameObject)
		{
			PlayerHealth playerHealth2 = playerHealth;
			playerHealth2.sync___set_value_health(playerHealth2.SyncAccessor_health - damageToGive, true);
		}
		RaycastHit raycastHit2;
		DollHealth dollHealth;
		if (Physics.Raycast(position, direction, out raycastHit2, float.PositiveInfinity) && raycastHit2.transform.TryGetComponent<DollHealth>(out dollHealth))
		{
			dollHealth.health -= damageToGive;
		}
	}

	// Token: 0x060002C3 RID: 707 RVA: 0x00017DA4 File Offset: 0x00015FA4
	private void RpcReader___Server_ShootServer_1867452766(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		float num = PooledReader0.ReadSingle(AutoPackType.Unpacked);
		Vector3 vector = PooledReader0.ReadVector3();
		Vector3 vector2 = PooledReader0.ReadVector3();
		if (!base.IsServer)
		{
			return;
		}
		this.RpcLogic___ShootServer_1867452766(num, vector, vector2);
	}

	// Token: 0x060002C4 RID: 708 RVA: 0x00017DFC File Offset: 0x00015FFC
	public virtual void Awake___UserLogic()
	{
		this.playerControls = InputManager.inputActions;
		this.cam = base.transform.GetChild(0).GetComponent<Camera>();
	}

	// Token: 0x04000318 RID: 792
	public PlayerControls playerControls;

	// Token: 0x04000319 RID: 793
	private InputAction fire1;

	// Token: 0x0400031A RID: 794
	private InputAction fire2;

	// Token: 0x0400031B RID: 795
	[SerializeField]
	private LayerMask playerLayer;

	// Token: 0x0400031C RID: 796
	public float timeBetweenFire = 0.2f;

	// Token: 0x0400031D RID: 797
	public int damage = 1;

	// Token: 0x0400031E RID: 798
	private float fireTimer;

	// Token: 0x0400031F RID: 799
	private Camera cam;

	// Token: 0x04000320 RID: 800
	private bool NetworkInitializeEarly_PlayerShoot_Assembly-CSharp.dll;

	// Token: 0x04000321 RID: 801
	private bool NetworkInitializeLate_PlayerShoot_Assembly-CSharp.dll;
}

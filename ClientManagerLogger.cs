﻿using System;
using FishNet.Managing.Client;
using FishNet.Transporting;
using UnityEngine;

// Token: 0x020000C6 RID: 198
[RequireComponent(typeof(ClientManager))]
public class ClientManagerLogger : MonoBehaviour
{
	// Token: 0x06000B2A RID: 2858 RVA: 0x0004F048 File Offset: 0x0004D248
	private void Awake()
	{
		this.ClientManager = base.GetComponent<ClientManager>();
		this.ClientManager.OnClientConnectionState += this.OnClientConnectionStateChanged;
		this.ClientManager.OnRemoteConnectionState += this.OnRemoteConnectionStateChanged;
		this.ClientManager.OnAuthenticated += this.OnAuthenticated;
	}

	// Token: 0x06000B2B RID: 2859 RVA: 0x0004F0A8 File Offset: 0x0004D2A8
	private void OnDestroy()
	{
		if (this.ClientManager == null)
		{
			return;
		}
		this.ClientManager.OnClientConnectionState -= this.OnClientConnectionStateChanged;
		this.ClientManager.OnRemoteConnectionState -= this.OnRemoteConnectionStateChanged;
		this.ClientManager.OnAuthenticated -= this.OnAuthenticated;
	}

	// Token: 0x06000B2C RID: 2860 RVA: 0x0004F10C File Offset: 0x0004D30C
	private void OnClientConnectionStateChanged(ClientConnectionStateArgs args)
	{
		switch (args.ConnectionState)
		{
		case LocalConnectionState.Stopped:
			Debug.LogWarning("[FishNet] Client stopped.");
			return;
		case LocalConnectionState.Starting:
			Debug.Log("[FishNet] Client is starting.");
			return;
		case LocalConnectionState.Started:
			Debug.Log("[FishNet] Client started.");
			return;
		case LocalConnectionState.Stopping:
			Debug.LogWarning("[FishNet] Client is stopping.");
			return;
		default:
			return;
		}
	}

	// Token: 0x06000B2D RID: 2861 RVA: 0x0004F164 File Offset: 0x0004D364
	private void OnRemoteConnectionStateChanged(RemoteConnectionStateArgs args)
	{
		Debug.Log(string.Format("[FishNet] Remote client {0} state changed: {1}.", args.ConnectionId, args.ConnectionState));
		if (args.ConnectionState == RemoteConnectionState.Stopped)
		{
			Debug.LogWarning(string.Format("[FishNet] Remote client {0} disconnected.", args.ConnectionId));
		}
	}

	// Token: 0x06000B2E RID: 2862 RVA: 0x0004F1B8 File Offset: 0x0004D3B8
	private void OnAuthenticated()
	{
		Debug.Log("[FishNet] Client successfully authenticated.");
	}

	// Token: 0x04000963 RID: 2403
	private ClientManager ClientManager;
}

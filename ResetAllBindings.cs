﻿using System;
using UnityEngine;

// Token: 0x020000FD RID: 253
public class ResetAllBindings : MonoBehaviour
{
	// Token: 0x06000CDC RID: 3292 RVA: 0x000023D6 File Offset: 0x000005D6
	private void Start()
	{
	}

	// Token: 0x06000CDD RID: 3293 RVA: 0x000574D8 File Offset: 0x000556D8
	public void ResetAll()
	{
		ReBindUI[] componentsInChildren = base.GetComponentsInChildren<ReBindUI>();
		for (int i = 0; i < componentsInChildren.Length; i++)
		{
			componentsInChildren[i].ResetBinding();
		}
	}
}

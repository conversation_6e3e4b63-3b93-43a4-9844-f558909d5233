﻿using System;
using System.Collections;
using DG.Tweening;
using FishNet;
using FishNet.Component.Utility;
using FishNet.Connection;
using FishNet.Managing;
using FishNet.Object;
using FishNet.Object.Delegating;
using FishNet.Object.Synchronizing;
using FishNet.Serializing;
using FishNet.Serializing.Generated;
using FishNet.Transporting;
using TMPro;
using UnityEngine;

// Token: 0x02000028 RID: 40
public class PlayerHealth : NetworkBehaviour
{
	// Token: 0x060001BA RID: 442 RVA: 0x00010628 File Offset: 0x0000E828
	public virtual void Awake()
	{
		this.NetworkInitialize___Early();
		this.Awake___UserLogic();
		this.NetworkInitialize__Late();
	}

	// Token: 0x060001BB RID: 443 RVA: 0x00010647 File Offset: 0x0000E847
	private IEnumerator Start()
	{
		for (;;)
		{
			this.count = 1f / Time.unscaledDeltaTime;
			yield return new WaitForSeconds(0.1f);
		}
		yield break;
	}

	// Token: 0x060001BC RID: 444 RVA: 0x00010658 File Offset: 0x0000E858
	private void Update()
	{
		if (!base.IsOwner)
		{
			return;
		}
		this.tweenScript.health = this.SyncAccessor_health;
		if (this.SyncAccessor_health <= 0f && this.mainCamObject != null)
		{
			this.KillCam();
			this.killCamScript.isDead = true;
			this.mainCamObject.GetComponent<Camera>().enabled = true;
			this.mainCamObject.GetComponent<AudioListener>().enabled = true;
		}
		if (this.SyncAccessor_health <= 0f && this.SyncAccessor_health > -1000f && this.SyncAccessor_isKilled)
		{
			if (!GameManager.Instance.SyncAccessor_roundWasWon)
			{
				GameManager.Instance.PlayerDied(this.SyncAccessor_playerValues.SyncAccessor_playerClient.PlayerId);
				if (this.fellVoid)
				{
					PauseManager.Instance.WriteLog(string.Concat(new string[]
					{
						"<b><color=#",
						PauseManager.Instance.selfNameLogColor,
						">",
						this.SyncAccessor_playerValues.SyncAccessor_playerClient.PlayerName,
						"</color></b> fell into the void"
					}));
				}
				else if (this.suicide)
				{
					PauseManager.Instance.WriteLog(string.Concat(new string[]
					{
						"<b><color=#",
						PauseManager.Instance.selfNameLogColor,
						">",
						this.SyncAccessor_playerValues.SyncAccessor_playerClient.PlayerName,
						"</color></b> commited suicide"
					}));
				}
				this.shouldDropWeapon = true;
			}
			this.DespawnObject();
			this.sync___set_value_health(-2000f, true);
			this.sync___set_value_isKilled(false, true);
		}
		else if (this.SyncAccessor_health <= 0f && this.SyncAccessor_health > -1000f)
		{
			base.StartCoroutine(this.SafeDeathFix());
			this.sync___set_value_health(-2000f, true);
		}
		if (!this.controller.pauseManager.nonSteamworksTransport)
		{
			this.controller.pauseManager.minimalistHealthText.text = "health : " + Mathf.Ceil(this.SyncAccessor_health / 4f * 100f).ToString();
			this.controller.pauseManager.minimalPingText.text = this.PingDisplay.ping.ToString() + " ms";
			this.controller.pauseManager.minimalFpsText.text = Mathf.Round(this.count).ToString() + " fps";
		}
		if (this.shouldBounce)
		{
			this.shouldBounce = false;
			this.AddForce(this.bounceDirection, this.bounceForce);
		}
		if (this.shouldDropWeapon)
		{
			this.shouldDropWeapon = false;
			if (base.GetComponent<PlayerPickup>().SyncAccessor_hasObjectInLeftHand)
			{
				base.GetComponent<PlayerPickup>().LeftHandDrop();
			}
			if (base.GetComponent<PlayerPickup>().SyncAccessor_hasObjectInHand)
			{
				base.GetComponent<PlayerPickup>().RightHandDrop();
			}
		}
		if (this.isDeadFromTargetRpc)
		{
			base.gameObject.SetActive(false);
		}
		if (this.SyncAccessor_health != this.tempHealth)
		{
			this.tempHealth = this.SyncAccessor_health;
			this.tweenScript.health = this.SyncAccessor_health;
			this.tweenScript.ChangeState();
		}
	}

	// Token: 0x060001BD RID: 445 RVA: 0x00010982 File Offset: 0x0000EB82
	public IEnumerator UnfreezePlayer(float time)
	{
		yield return new WaitForSeconds(time);
		this.UnfreezePlayerServer();
		yield break;
	}

	// Token: 0x060001BE RID: 446 RVA: 0x00010998 File Offset: 0x0000EB98
	[ServerRpc]
	private void UnfreezePlayerServer()
	{
		this.RpcWriter___Server_UnfreezePlayerServer_2166136261();
	}

	// Token: 0x060001BF RID: 447 RVA: 0x000109A0 File Offset: 0x0000EBA0
	private IEnumerator SafeDeathFix()
	{
		yield return new WaitForSeconds(0.075f);
		if (!this.SyncAccessor_isShot && !GameManager.Instance.SyncAccessor_roundWasWon)
		{
			GameManager.Instance.PlayerDied(this.SyncAccessor_playerValues.SyncAccessor_playerClient.PlayerId);
		}
		this.DespawnObject();
		this.sync___set_value_isKilled(false, true);
		yield break;
	}

	// Token: 0x060001C0 RID: 448 RVA: 0x000109AF File Offset: 0x0000EBAF
	[ServerRpc(RunLocally = true, RequireOwnership = false)]
	public void RemoveHealth(float damage)
	{
		this.RpcWriter___Server_RemoveHealth_431000436(damage);
		this.RpcLogic___RemoveHealth_431000436(damage);
	}

	// Token: 0x060001C1 RID: 449 RVA: 0x000109C5 File Offset: 0x0000EBC5
	[ObserversRpc]
	private void HitFeedbackObservers()
	{
		this.RpcWriter___Observers_HitFeedbackObservers_2166136261();
	}

	// Token: 0x060001C2 RID: 450 RVA: 0x000109CD File Offset: 0x0000EBCD
	public void BumpPlayer(Vector3 dir, float force)
	{
		this.BumpPlayerObservers(dir, force);
	}

	// Token: 0x060001C3 RID: 451 RVA: 0x000109D7 File Offset: 0x0000EBD7
	private void BumpPlayerObservers(Vector3 dir, float force)
	{
		this.AddForce(dir, force);
	}

	// Token: 0x060001C4 RID: 452 RVA: 0x000109E1 File Offset: 0x0000EBE1
	[ServerRpc(RunLocally = true, RequireOwnership = false)]
	public void SetKiller(Transform tempKiller)
	{
		this.RpcWriter___Server_SetKiller_3068987916(tempKiller);
		this.RpcLogic___SetKiller_3068987916(tempKiller);
	}

	// Token: 0x060001C5 RID: 453 RVA: 0x000109F7 File Offset: 0x0000EBF7
	[ServerRpc(RunLocally = true, RequireOwnership = false)]
	public void ChangeKilledState(bool tempBool)
	{
		this.RpcWriter___Server_ChangeKilledState_1140765316(tempBool);
		this.RpcLogic___ChangeKilledState_1140765316(tempBool);
	}

	// Token: 0x060001C6 RID: 454 RVA: 0x00010A0D File Offset: 0x0000EC0D
	[ObserversRpc]
	private void RemoveHealthObservers()
	{
		this.RpcWriter___Observers_RemoveHealthObservers_2166136261();
	}

	// Token: 0x060001C7 RID: 455 RVA: 0x000023D6 File Offset: 0x000005D6
	public void Dismemberment(string col)
	{
	}

	// Token: 0x060001C8 RID: 456 RVA: 0x00010A15 File Offset: 0x0000EC15
	public void HitFeeback()
	{
		if (base.IsOwner)
		{
			this.camHit.TakeHit();
		}
	}

	// Token: 0x060001C9 RID: 457 RVA: 0x00010A2A File Offset: 0x0000EC2A
	public void Explode(bool explode, bool dismemberment, string memberName, Vector3 ejectForceDir, float force, Vector3 position)
	{
		this.ExplodeServer(explode, dismemberment, memberName, ejectForceDir, force, position);
		this.killCamScript.ragdollCam = true;
	}

	// Token: 0x060001CA RID: 458 RVA: 0x00010A47 File Offset: 0x0000EC47
	[ServerRpc(RunLocally = true, RequireOwnership = false)]
	public void ExplodeServer(bool explode, bool dismemberment, string memberName, Vector3 ejectForceDir, float force, Vector3 position)
	{
		this.RpcWriter___Server_ExplodeServer_576886416(explode, dismemberment, memberName, ejectForceDir, force, position);
		this.RpcLogic___ExplodeServer_576886416(explode, dismemberment, memberName, ejectForceDir, force, position);
	}

	// Token: 0x060001CB RID: 459 RVA: 0x00010A88 File Offset: 0x0000EC88
	[ObserversRpc(RunLocally = true)]
	private void ExplodeForAll(bool explode, bool dismemberment, string memberName, Vector3 ejectForceDir, float force, Vector3 position)
	{
		this.RpcWriter___Observers_ExplodeForAll_576886416(explode, dismemberment, memberName, ejectForceDir, force, position);
		this.RpcLogic___ExplodeForAll_576886416(explode, dismemberment, memberName, ejectForceDir, force, position);
	}

	// Token: 0x060001CC RID: 460 RVA: 0x00010AD1 File Offset: 0x0000ECD1
	[ServerRpc(RequireOwnership = false)]
	public void DisablePlayerObjectWhenKilled()
	{
		this.RpcWriter___Server_DisablePlayerObjectWhenKilled_2166136261();
	}

	// Token: 0x060001CD RID: 461 RVA: 0x00010AD9 File Offset: 0x0000ECD9
	[ObserversRpc]
	private void DisablePlayerObjectForAll()
	{
		this.RpcWriter___Observers_DisablePlayerObjectForAll_2166136261();
	}

	// Token: 0x060001CE RID: 462 RVA: 0x00010AE4 File Offset: 0x0000ECE4
	private void Hat(GameObject obj, Vector3 dir)
	{
		obj.transform.SetParent(null);
		if (!obj.GetComponent<Rigidbody>())
		{
			obj.AddComponent<Rigidbody>();
		}
		Rigidbody component = obj.GetComponent<Rigidbody>();
		component.collisionDetectionMode = CollisionDetectionMode.ContinuousDynamic;
		component.drag = 0f;
		component.interpolation = RigidbodyInterpolation.Interpolate;
		component.AddForce(dir * 1.5f, ForceMode.Impulse);
		global::UnityEngine.Random.Range(-1, 1);
		component.AddTorque(base.transform.forward * 5f + base.transform.right * 5f, ForceMode.Impulse);
		Crosshair.Instance.hatObj = obj;
	}

	// Token: 0x060001CF RID: 463 RVA: 0x00010B8A File Offset: 0x0000ED8A
	[ServerRpc(RunLocally = true)]
	public void DespawnObject()
	{
		this.RpcWriter___Server_DespawnObject_2166136261();
		this.RpcLogic___DespawnObject_2166136261();
	}

	// Token: 0x060001D0 RID: 464 RVA: 0x00010B98 File Offset: 0x0000ED98
	[ObserversRpc(RunLocally = true, ExcludeOwner = true)]
	private void DespawnObjectObservers()
	{
		this.RpcWriter___Observers_DespawnObjectObservers_2166136261();
		this.RpcLogic___DespawnObjectObservers_2166136261();
	}

	// Token: 0x060001D1 RID: 465 RVA: 0x00010BB4 File Offset: 0x0000EDB4
	public void KillCam()
	{
		if (this.SyncAccessor_killer != null)
		{
			this.killCamScript.enemy = this.SyncAccessor_killer;
		}
		if (this.mainCamObject != null)
		{
			this.mainCamObject.transform.position = base.transform.position + new Vector3(0f, base.GetComponent<CharacterController>().height, 0f);
		}
	}

	// Token: 0x060001D2 RID: 466 RVA: 0x00010C28 File Offset: 0x0000EE28
	private void OnDisable()
	{
		this.KillCam();
		PauseManager.Instance.grabPopup.gameObject.SetActive(false);
	}

	// Token: 0x060001D3 RID: 467 RVA: 0x00010C45 File Offset: 0x0000EE45
	public void AddForce(Vector3 force, float factor)
	{
		this.controller.customForceScript.AddForce(force, factor);
	}

	// Token: 0x060001D4 RID: 468 RVA: 0x00010C59 File Offset: 0x0000EE59
	public void LocalScreenshake(float duration, float strength, int vibrato, float randomness, Ease shakeEase)
	{
		this.playerCamera.transform.DOKill(false);
		this.playerCamera.transform.DOShakeRotation(duration, strength, vibrato, randomness, true, ShakeRandomnessMode.Full).SetEase(shakeEase);
	}

	// Token: 0x060001D5 RID: 469 RVA: 0x00010C8C File Offset: 0x0000EE8C
	private GameObject FindRecursive(string name, Transform root)
	{
		Transform[] componentsInChildren = root.GetComponentsInChildren<Transform>(true);
		GameObject gameObject = null;
		foreach (Transform transform in componentsInChildren)
		{
			if (transform.gameObject.name == name)
			{
				gameObject = transform.gameObject;
				break;
			}
		}
		return gameObject;
	}

	// Token: 0x060001D6 RID: 470 RVA: 0x00010CD2 File Offset: 0x0000EED2
	[ServerRpc(RunLocally = true, RequireOwnership = false)]
	public void TaserEnemy(PlayerHealth enemyHealth, float stunTime)
	{
		this.RpcWriter___Server_TaserEnemy_4069068711(enemyHealth, stunTime);
		this.RpcLogic___TaserEnemy_4069068711(enemyHealth, stunTime);
	}

	// Token: 0x060001D7 RID: 471 RVA: 0x00010CF0 File Offset: 0x0000EEF0
	[TargetRpc]
	private void TaserEnemyTarget(NetworkConnection conn, PlayerHealth enemyHealth, float stunTime)
	{
		this.RpcWriter___Target_TaserEnemyTarget_865840858(conn, enemyHealth, stunTime);
	}

	// Token: 0x060001D9 RID: 473 RVA: 0x00010D2C File Offset: 0x0000EF2C
	public virtual void NetworkInitialize___Early()
	{
		if (this.NetworkInitializeEarly_PlayerHealth_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeEarly_PlayerHealth_Assembly-CSharp.dll = true;
		this.syncVar___lastKillDirection = new SyncVar<Vector3>(this, 6U, WritePermission.ServerOnly, ReadPermission.Observers, -1f, Channel.Reliable, this.lastKillDirection);
		this.syncVar___isShot = new SyncVar<bool>(this, 5U, WritePermission.ServerOnly, ReadPermission.Observers, -1f, Channel.Reliable, this.isShot);
		this.syncVar___isKilled = new SyncVar<bool>(this, 4U, WritePermission.ClientUnsynchronized, ReadPermission.Observers, -1f, Channel.Reliable, this.isKilled);
		this.syncVar___killer = new SyncVar<Transform>(this, 3U, WritePermission.ServerOnly, ReadPermission.Observers, -1f, Channel.Reliable, this.killer);
		this.syncVar___playerValues = new SyncVar<PlayerValues>(this, 2U, WritePermission.ServerOnly, ReadPermission.Observers, -1f, Channel.Reliable, this.playerValues);
		this.syncVar___canMove = new SyncVar<bool>(this, 1U, WritePermission.ServerOnly, ReadPermission.Observers, -1f, Channel.Reliable, this.canMove);
		this.syncVar___health = new SyncVar<float>(this, 0U, WritePermission.ServerOnly, ReadPermission.Observers, -1f, Channel.Reliable, this.health);
		base.RegisterServerRpc(0U, new ServerRpcDelegate(this.RpcReader___Server_UnfreezePlayerServer_2166136261));
		base.RegisterServerRpc(1U, new ServerRpcDelegate(this.RpcReader___Server_RemoveHealth_431000436));
		base.RegisterObserversRpc(2U, new ClientRpcDelegate(this.RpcReader___Observers_HitFeedbackObservers_2166136261));
		base.RegisterServerRpc(3U, new ServerRpcDelegate(this.RpcReader___Server_SetKiller_3068987916));
		base.RegisterServerRpc(4U, new ServerRpcDelegate(this.RpcReader___Server_ChangeKilledState_1140765316));
		base.RegisterObserversRpc(5U, new ClientRpcDelegate(this.RpcReader___Observers_RemoveHealthObservers_2166136261));
		base.RegisterServerRpc(6U, new ServerRpcDelegate(this.RpcReader___Server_ExplodeServer_576886416));
		base.RegisterObserversRpc(7U, new ClientRpcDelegate(this.RpcReader___Observers_ExplodeForAll_576886416));
		base.RegisterServerRpc(8U, new ServerRpcDelegate(this.RpcReader___Server_DisablePlayerObjectWhenKilled_2166136261));
		base.RegisterObserversRpc(9U, new ClientRpcDelegate(this.RpcReader___Observers_DisablePlayerObjectForAll_2166136261));
		base.RegisterServerRpc(10U, new ServerRpcDelegate(this.RpcReader___Server_DespawnObject_2166136261));
		base.RegisterObserversRpc(11U, new ClientRpcDelegate(this.RpcReader___Observers_DespawnObjectObservers_2166136261));
		base.RegisterServerRpc(12U, new ServerRpcDelegate(this.RpcReader___Server_TaserEnemy_4069068711));
		base.RegisterTargetRpc(13U, new ClientRpcDelegate(this.RpcReader___Target_TaserEnemyTarget_865840858));
		base.RegisterSyncVarRead(new SyncVarReadDelegate(this.ReadSyncVar___PlayerHealth));
	}

	// Token: 0x060001DA RID: 474 RVA: 0x00010FCC File Offset: 0x0000F1CC
	public virtual void NetworkInitialize__Late()
	{
		if (this.NetworkInitializeLate_PlayerHealth_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeLate_PlayerHealth_Assembly-CSharp.dll = true;
		this.syncVar___lastKillDirection.SetRegistered();
		this.syncVar___isShot.SetRegistered();
		this.syncVar___isKilled.SetRegistered();
		this.syncVar___killer.SetRegistered();
		this.syncVar___playerValues.SetRegistered();
		this.syncVar___canMove.SetRegistered();
		this.syncVar___health.SetRegistered();
	}

	// Token: 0x060001DB RID: 475 RVA: 0x00011037 File Offset: 0x0000F237
	public override void NetworkInitializeIfDisabled()
	{
		this.NetworkInitialize___Early();
		this.NetworkInitialize__Late();
	}

	// Token: 0x060001DC RID: 476 RVA: 0x00011048 File Offset: 0x0000F248
	private void RpcWriter___Server_UnfreezePlayerServer_2166136261()
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendServerRpc(0U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x060001DD RID: 477 RVA: 0x0001113C File Offset: 0x0000F33C
	private void RpcLogic___UnfreezePlayerServer_2166136261()
	{
		this.controller.sync___set_value_canMove(true, true);
	}

	// Token: 0x060001DE RID: 478 RVA: 0x0001114C File Offset: 0x0000F34C
	private void RpcReader___Server_UnfreezePlayerServer_2166136261(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		this.RpcLogic___UnfreezePlayerServer_2166136261();
	}

	// Token: 0x060001DF RID: 479 RVA: 0x00011180 File Offset: 0x0000F380
	private void RpcWriter___Server_RemoveHealth_431000436(float damage)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteSingle(damage, AutoPackType.Unpacked);
		base.SendServerRpc(1U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x060001E0 RID: 480 RVA: 0x0001122C File Offset: 0x0000F42C
	public void RpcLogic___RemoveHealth_431000436(float damage)
	{
		this.sync___set_value_health(this.SyncAccessor_health - damage, true);
		this.HitFeedbackObservers();
	}

	// Token: 0x060001E1 RID: 481 RVA: 0x00011244 File Offset: 0x0000F444
	private void RpcReader___Server_RemoveHealth_431000436(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		float num = PooledReader0.ReadSingle(AutoPackType.Unpacked);
		if (!base.IsServer)
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___RemoveHealth_431000436(num);
	}

	// Token: 0x060001E2 RID: 482 RVA: 0x00011288 File Offset: 0x0000F488
	private void RpcWriter___Observers_HitFeedbackObservers_2166136261()
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendObserversRpc(2U, writer, channel, DataOrderType.Default, false, false, false);
		writer.Store();
	}

	// Token: 0x060001E3 RID: 483 RVA: 0x00011331 File Offset: 0x0000F531
	private void RpcLogic___HitFeedbackObservers_2166136261()
	{
		this.HitFeeback();
	}

	// Token: 0x060001E4 RID: 484 RVA: 0x0001133C File Offset: 0x0000F53C
	private void RpcReader___Observers_HitFeedbackObservers_2166136261(PooledReader PooledReader0, Channel channel)
	{
		if (!base.IsClient)
		{
			return;
		}
		this.RpcLogic___HitFeedbackObservers_2166136261();
	}

	// Token: 0x060001E5 RID: 485 RVA: 0x0001135C File Offset: 0x0000F55C
	private void RpcWriter___Server_SetKiller_3068987916(Transform tempKiller)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteTransform(tempKiller);
		base.SendServerRpc(3U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x060001E6 RID: 486 RVA: 0x00011403 File Offset: 0x0000F603
	public void RpcLogic___SetKiller_3068987916(Transform tempKiller)
	{
		this.sync___set_value_killer(tempKiller, true);
	}

	// Token: 0x060001E7 RID: 487 RVA: 0x00011410 File Offset: 0x0000F610
	private void RpcReader___Server_SetKiller_3068987916(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		Transform transform = PooledReader0.ReadTransform();
		if (!base.IsServer)
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___SetKiller_3068987916(transform);
	}

	// Token: 0x060001E8 RID: 488 RVA: 0x00011450 File Offset: 0x0000F650
	private void RpcWriter___Server_ChangeKilledState_1140765316(bool tempBool)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteBoolean(tempBool);
		base.SendServerRpc(4U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x060001E9 RID: 489 RVA: 0x000114F7 File Offset: 0x0000F6F7
	public void RpcLogic___ChangeKilledState_1140765316(bool tempBool)
	{
		this.sync___set_value_isKilled(tempBool, true);
	}

	// Token: 0x060001EA RID: 490 RVA: 0x00011504 File Offset: 0x0000F704
	private void RpcReader___Server_ChangeKilledState_1140765316(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		bool flag = PooledReader0.ReadBoolean();
		if (!base.IsServer)
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___ChangeKilledState_1140765316(flag);
	}

	// Token: 0x060001EB RID: 491 RVA: 0x00011544 File Offset: 0x0000F744
	private void RpcWriter___Observers_RemoveHealthObservers_2166136261()
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendObserversRpc(5U, writer, channel, DataOrderType.Default, false, false, false);
		writer.Store();
	}

	// Token: 0x060001EC RID: 492 RVA: 0x000115ED File Offset: 0x0000F7ED
	private void RpcLogic___RemoveHealthObservers_2166136261()
	{
		this.KillCam();
	}

	// Token: 0x060001ED RID: 493 RVA: 0x000115F8 File Offset: 0x0000F7F8
	private void RpcReader___Observers_RemoveHealthObservers_2166136261(PooledReader PooledReader0, Channel channel)
	{
		if (!base.IsClient)
		{
			return;
		}
		this.RpcLogic___RemoveHealthObservers_2166136261();
	}

	// Token: 0x060001EE RID: 494 RVA: 0x00011618 File Offset: 0x0000F818
	private void RpcWriter___Server_ExplodeServer_576886416(bool explode, bool dismemberment, string memberName, Vector3 ejectForceDir, float force, Vector3 position)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteBoolean(explode);
		writer.WriteBoolean(dismemberment);
		writer.WriteString(memberName);
		writer.WriteVector3(ejectForceDir);
		writer.WriteSingle(force, AutoPackType.Unpacked);
		writer.WriteVector3(position);
		base.SendServerRpc(6U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x060001EF RID: 495 RVA: 0x00011705 File Offset: 0x0000F905
	public void RpcLogic___ExplodeServer_576886416(bool explode, bool dismemberment, string memberName, Vector3 ejectForceDir, float force, Vector3 position)
	{
		this.ExplodeForAll(explode, dismemberment, memberName, ejectForceDir, force, position);
	}

	// Token: 0x060001F0 RID: 496 RVA: 0x00011718 File Offset: 0x0000F918
	private void RpcReader___Server_ExplodeServer_576886416(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		bool flag = PooledReader0.ReadBoolean();
		bool flag2 = PooledReader0.ReadBoolean();
		string text = PooledReader0.ReadString();
		Vector3 vector = PooledReader0.ReadVector3();
		float num = PooledReader0.ReadSingle(AutoPackType.Unpacked);
		Vector3 vector2 = PooledReader0.ReadVector3();
		if (!base.IsServer)
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___ExplodeServer_576886416(flag, flag2, text, vector, num, vector2);
	}

	// Token: 0x060001F1 RID: 497 RVA: 0x000117B0 File Offset: 0x0000F9B0
	private void RpcWriter___Observers_ExplodeForAll_576886416(bool explode, bool dismemberment, string memberName, Vector3 ejectForceDir, float force, Vector3 position)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteBoolean(explode);
		writer.WriteBoolean(dismemberment);
		writer.WriteString(memberName);
		writer.WriteVector3(ejectForceDir);
		writer.WriteSingle(force, AutoPackType.Unpacked);
		writer.WriteVector3(position);
		base.SendObserversRpc(7U, writer, channel, DataOrderType.Default, false, false, false);
		writer.Store();
	}

	// Token: 0x060001F2 RID: 498 RVA: 0x000118AC File Offset: 0x0000FAAC
	private void RpcLogic___ExplodeForAll_576886416(bool explode, bool dismemberment, string memberName, Vector3 ejectForceDir, float force, Vector3 position)
	{
		if (this.spawnedRagdoll)
		{
			return;
		}
		this.spawnedRagdoll = true;
		if (explode)
		{
			if (base.IsOwner)
			{
				Settings.Instance.IncreaseDeathsAmount();
			}
			for (int i = 0; i < this.bodyParts.Length; i++)
			{
				global::UnityEngine.Object.Instantiate<GameObject>(this.bodyParts[i], base.transform.position + Vector3.up, Quaternion.identity).GetComponent<Rigidbody>().AddForce(new Vector3((float)global::UnityEngine.Random.Range(-1, 1), (float)global::UnityEngine.Random.Range(-1, 1), (float)global::UnityEngine.Random.Range(-1, 1)) * 10f, ForceMode.Impulse);
			}
			return;
		}
		if (base.IsOwner)
		{
			Settings.Instance.IncreaseDeathsAmount();
			SoundManager.Instance.PlaySoundWithPitch(PauseManager.Instance.deathAudioClip[Mathf.RoundToInt((float)global::UnityEngine.Random.Range(0, PauseManager.Instance.deathAudioClip.Length))], global::UnityEngine.Random.Range(0.95f, 1.05f));
		}
		GameObject gameObject = global::UnityEngine.Object.Instantiate<GameObject>(this.aboubiRagdoll, base.transform.position, base.transform.rotation);
		GameObject[] meshesToChange = gameObject.GetComponent<RagdollDress>().meshesToChange;
		for (int j = 0; j < meshesToChange.Length; j++)
		{
			meshesToChange[j].GetComponent<SkinnedMeshRenderer>().material = CosmeticsManager.Instance.mats[base.GetComponent<PlayerSetup>().SyncAccessor_mat];
		}
		if (base.GetComponent<PlayerSetup>().hat)
		{
			this.Hat(base.GetComponent<PlayerSetup>().hat, new Vector3(ejectForceDir.x, 0f, ejectForceDir.z).normalized);
		}
		Rigidbody[] componentsInChildren = gameObject.transform.GetComponentsInChildren<Rigidbody>();
		for (int j = 0; j < componentsInChildren.Length; j++)
		{
			componentsInChildren[j].AddExplosionForce(force * 1.5f, position - ejectForceDir * 1.5f, 100f, 0f, ForceMode.Impulse);
		}
		this.killCamScript.ragdoll = gameObject.transform;
		this.killCamScript.triggerLookAtBody = true;
		Vector3 normalized = new Vector3(ejectForceDir.x, 0f, ejectForceDir.z).normalized;
		this.killCamScript.firstPosition = base.transform.position + this.killCamRagdollOffset + normalized * this.directionOffset + Vector3.right;
		global::UnityEngine.Object.Instantiate<GameObject>(this.bloodFX, position, Quaternion.LookRotation(ejectForceDir), this.FindRecursive("Hips", gameObject.transform).transform);
	}

	// Token: 0x060001F3 RID: 499 RVA: 0x00011B38 File Offset: 0x0000FD38
	private void RpcReader___Observers_ExplodeForAll_576886416(PooledReader PooledReader0, Channel channel)
	{
		bool flag = PooledReader0.ReadBoolean();
		bool flag2 = PooledReader0.ReadBoolean();
		string text = PooledReader0.ReadString();
		Vector3 vector = PooledReader0.ReadVector3();
		float num = PooledReader0.ReadSingle(AutoPackType.Unpacked);
		Vector3 vector2 = PooledReader0.ReadVector3();
		if (!base.IsClient)
		{
			return;
		}
		if (base.IsHost)
		{
			return;
		}
		this.RpcLogic___ExplodeForAll_576886416(flag, flag2, text, vector, num, vector2);
	}

	// Token: 0x060001F4 RID: 500 RVA: 0x00011BD0 File Offset: 0x0000FDD0
	private void RpcWriter___Server_DisablePlayerObjectWhenKilled_2166136261()
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendServerRpc(8U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x060001F5 RID: 501 RVA: 0x00011C6A File Offset: 0x0000FE6A
	public void RpcLogic___DisablePlayerObjectWhenKilled_2166136261()
	{
		this.DisablePlayerObjectForAll();
	}

	// Token: 0x060001F6 RID: 502 RVA: 0x00011C74 File Offset: 0x0000FE74
	private void RpcReader___Server_DisablePlayerObjectWhenKilled_2166136261(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		if (!base.IsServer)
		{
			return;
		}
		this.RpcLogic___DisablePlayerObjectWhenKilled_2166136261();
	}

	// Token: 0x060001F7 RID: 503 RVA: 0x00011C94 File Offset: 0x0000FE94
	private void RpcWriter___Observers_DisablePlayerObjectForAll_2166136261()
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendObserversRpc(9U, writer, channel, DataOrderType.Default, false, false, false);
		writer.Store();
	}

	// Token: 0x060001F8 RID: 504 RVA: 0x00011D3D File Offset: 0x0000FF3D
	private void RpcLogic___DisablePlayerObjectForAll_2166136261()
	{
		this.graphics.SetActive(false);
		this.controller.playerPickupScript.fpArms.gameObject.SetActive(false);
		base.GetComponent<CharacterController>().enabled = false;
	}

	// Token: 0x060001F9 RID: 505 RVA: 0x00011D74 File Offset: 0x0000FF74
	private void RpcReader___Observers_DisablePlayerObjectForAll_2166136261(PooledReader PooledReader0, Channel channel)
	{
		if (!base.IsClient)
		{
			return;
		}
		this.RpcLogic___DisablePlayerObjectForAll_2166136261();
	}

	// Token: 0x060001FA RID: 506 RVA: 0x00011D94 File Offset: 0x0000FF94
	private void RpcWriter___Server_DespawnObject_2166136261()
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendServerRpc(10U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x060001FB RID: 507 RVA: 0x00011E88 File Offset: 0x00010088
	public void RpcLogic___DespawnObject_2166136261()
	{
		this.DespawnObjectObservers();
	}

	// Token: 0x060001FC RID: 508 RVA: 0x00011E90 File Offset: 0x00010090
	private void RpcReader___Server_DespawnObject_2166136261(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___DespawnObject_2166136261();
	}

	// Token: 0x060001FD RID: 509 RVA: 0x00011ED0 File Offset: 0x000100D0
	private void RpcWriter___Observers_DespawnObjectObservers_2166136261()
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendObserversRpc(11U, writer, channel, DataOrderType.Default, false, false, true);
		writer.Store();
	}

	// Token: 0x060001FE RID: 510 RVA: 0x00011F7C File Offset: 0x0001017C
	private void RpcLogic___DespawnObjectObservers_2166136261()
	{
		base.transform.gameObject.SetActive(false);
		this.Explode(false, false, "", -base.transform.forward, 30f, base.transform.position + Vector3.up * 2f + Vector3.right);
	}

	// Token: 0x060001FF RID: 511 RVA: 0x00011FE8 File Offset: 0x000101E8
	private void RpcReader___Observers_DespawnObjectObservers_2166136261(PooledReader PooledReader0, Channel channel)
	{
		if (!base.IsClient)
		{
			return;
		}
		if (base.IsHost)
		{
			return;
		}
		this.RpcLogic___DespawnObjectObservers_2166136261();
	}

	// Token: 0x06000200 RID: 512 RVA: 0x00012014 File Offset: 0x00010214
	private void RpcWriter___Server_TaserEnemy_4069068711(PlayerHealth enemyHealth, float stunTime)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.Write___PlayerHealthFishNet.Serializing.Generated(enemyHealth);
		writer.WriteSingle(stunTime, AutoPackType.Unpacked);
		base.SendServerRpc(12U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000201 RID: 513 RVA: 0x000120CD File Offset: 0x000102CD
	public void RpcLogic___TaserEnemy_4069068711(PlayerHealth enemyHealth, float stunTime)
	{
		enemyHealth.controller.sync___set_value_canMove(false, true);
		this.TaserEnemyTarget(enemyHealth.SyncAccessor_playerValues.SyncAccessor_playerClient.transform.GetComponent<NetworkObject>().Owner, enemyHealth, stunTime);
	}

	// Token: 0x06000202 RID: 514 RVA: 0x00012100 File Offset: 0x00010300
	private void RpcReader___Server_TaserEnemy_4069068711(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		PlayerHealth playerHealth = global::FishNet.Serializing.Generated.GeneratedReaders___Internal.Read___PlayerHealthFishNet.Serializing.Generateds(PooledReader0);
		float num = PooledReader0.ReadSingle(AutoPackType.Unpacked);
		if (!base.IsServer)
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___TaserEnemy_4069068711(playerHealth, num);
	}

	// Token: 0x06000203 RID: 515 RVA: 0x00012154 File Offset: 0x00010354
	private void RpcWriter___Target_TaserEnemyTarget_865840858(NetworkConnection conn, PlayerHealth enemyHealth, float stunTime)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.Write___PlayerHealthFishNet.Serializing.Generated(enemyHealth);
		writer.WriteSingle(stunTime, AutoPackType.Unpacked);
		base.SendTargetRpc(13U, writer, channel, DataOrderType.Default, conn, false, true);
		writer.Store();
	}

	// Token: 0x06000204 RID: 516 RVA: 0x0001221B File Offset: 0x0001041B
	private void RpcLogic___TaserEnemyTarget_865840858(NetworkConnection conn, PlayerHealth enemyHealth, float stunTime)
	{
		enemyHealth.StartCoroutine(enemyHealth.UnfreezePlayer(stunTime));
		Debug.Log("Taser");
	}

	// Token: 0x06000205 RID: 517 RVA: 0x00012238 File Offset: 0x00010438
	private void RpcReader___Target_TaserEnemyTarget_865840858(PooledReader PooledReader0, Channel channel)
	{
		PlayerHealth playerHealth = global::FishNet.Serializing.Generated.GeneratedReaders___Internal.Read___PlayerHealthFishNet.Serializing.Generateds(PooledReader0);
		float num = PooledReader0.ReadSingle(AutoPackType.Unpacked);
		if (!base.IsClient)
		{
			return;
		}
		this.RpcLogic___TaserEnemyTarget_865840858(base.LocalConnection, playerHealth, num);
	}

	// Token: 0x1700003E RID: 62
	// (get) Token: 0x06000206 RID: 518 RVA: 0x00012285 File Offset: 0x00010485
	// (set) Token: 0x06000207 RID: 519 RVA: 0x0001228D File Offset: 0x0001048D
	public float SyncAccessor_health
	{
		get
		{
			return this.health;
		}
		set
		{
			if (value || !base.IsServer)
			{
				this.health = value;
			}
			this.syncVar___health.SetValue(value, value);
		}
	}

	// Token: 0x06000208 RID: 520 RVA: 0x000122C4 File Offset: 0x000104C4
	public virtual bool ReadSyncVar___PlayerHealth(PooledReader PooledReader0, uint UInt321, bool Boolean2)
	{
		if (UInt321 == 6U)
		{
			if (PooledReader0 == null)
			{
				this.sync___set_value_lastKillDirection(this.syncVar___lastKillDirection.GetValue(true), true);
				return true;
			}
			Vector3 vector = PooledReader0.ReadVector3();
			this.sync___set_value_lastKillDirection(vector, Boolean2);
			return true;
		}
		else if (UInt321 == 5U)
		{
			if (PooledReader0 == null)
			{
				this.sync___set_value_isShot(this.syncVar___isShot.GetValue(true), true);
				return true;
			}
			bool flag = PooledReader0.ReadBoolean();
			this.sync___set_value_isShot(flag, Boolean2);
			return true;
		}
		else if (UInt321 == 4U)
		{
			if (PooledReader0 == null)
			{
				this.sync___set_value_isKilled(this.syncVar___isKilled.GetValue(true), true);
				return true;
			}
			bool flag2 = PooledReader0.ReadBoolean();
			this.sync___set_value_isKilled(flag2, Boolean2);
			return true;
		}
		else if (UInt321 == 3U)
		{
			if (PooledReader0 == null)
			{
				this.sync___set_value_killer(this.syncVar___killer.GetValue(true), true);
				return true;
			}
			Transform transform = PooledReader0.ReadTransform();
			this.sync___set_value_killer(transform, Boolean2);
			return true;
		}
		else if (UInt321 == 2U)
		{
			if (PooledReader0 == null)
			{
				this.sync___set_value_playerValues(this.syncVar___playerValues.GetValue(true), true);
				return true;
			}
			PlayerValues playerValues = global::FishNet.Serializing.Generated.GeneratedReaders___Internal.Read___PlayerValuesFishNet.Serializing.Generateds(PooledReader0);
			this.sync___set_value_playerValues(playerValues, Boolean2);
			return true;
		}
		else if (UInt321 == 1U)
		{
			if (PooledReader0 == null)
			{
				this.sync___set_value_canMove(this.syncVar___canMove.GetValue(true), true);
				return true;
			}
			bool flag3 = PooledReader0.ReadBoolean();
			this.sync___set_value_canMove(flag3, Boolean2);
			return true;
		}
		else
		{
			if (UInt321 != 0U)
			{
				return false;
			}
			if (PooledReader0 == null)
			{
				this.sync___set_value_health(this.syncVar___health.GetValue(true), true);
				return true;
			}
			float num = PooledReader0.ReadSingle(AutoPackType.Unpacked);
			this.sync___set_value_health(num, Boolean2);
			return true;
		}
	}

	// Token: 0x1700003F RID: 63
	// (get) Token: 0x06000209 RID: 521 RVA: 0x000124B3 File Offset: 0x000106B3
	// (set) Token: 0x0600020A RID: 522 RVA: 0x000124BB File Offset: 0x000106BB
	public bool SyncAccessor_canMove
	{
		get
		{
			return this.canMove;
		}
		set
		{
			if (value || !base.IsServer)
			{
				this.canMove = value;
			}
			this.syncVar___canMove.SetValue(value, value);
		}
	}

	// Token: 0x17000040 RID: 64
	// (get) Token: 0x0600020B RID: 523 RVA: 0x000124F0 File Offset: 0x000106F0
	// (set) Token: 0x0600020C RID: 524 RVA: 0x000124F8 File Offset: 0x000106F8
	public PlayerValues SyncAccessor_playerValues
	{
		get
		{
			return this.playerValues;
		}
		set
		{
			if (value || !base.IsServer)
			{
				this.playerValues = value;
			}
			this.syncVar___playerValues.SetValue(value, value);
		}
	}

	// Token: 0x17000041 RID: 65
	// (get) Token: 0x0600020D RID: 525 RVA: 0x0001252D File Offset: 0x0001072D
	// (set) Token: 0x0600020E RID: 526 RVA: 0x00012535 File Offset: 0x00010735
	public Transform SyncAccessor_killer
	{
		get
		{
			return this.killer;
		}
		set
		{
			if (value || !base.IsServer)
			{
				this.killer = value;
			}
			this.syncVar___killer.SetValue(value, value);
		}
	}

	// Token: 0x17000042 RID: 66
	// (get) Token: 0x0600020F RID: 527 RVA: 0x0001256A File Offset: 0x0001076A
	// (set) Token: 0x06000210 RID: 528 RVA: 0x00012572 File Offset: 0x00010772
	public bool SyncAccessor_isKilled
	{
		get
		{
			return this.isKilled;
		}
		set
		{
			if (value || !base.IsServer)
			{
				this.isKilled = value;
			}
			this.syncVar___isKilled.SetValue(value, value);
		}
	}

	// Token: 0x17000043 RID: 67
	// (get) Token: 0x06000211 RID: 529 RVA: 0x000125A7 File Offset: 0x000107A7
	// (set) Token: 0x06000212 RID: 530 RVA: 0x000125AF File Offset: 0x000107AF
	public bool SyncAccessor_isShot
	{
		get
		{
			return this.isShot;
		}
		set
		{
			if (value || !base.IsServer)
			{
				this.isShot = value;
			}
			this.syncVar___isShot.SetValue(value, value);
		}
	}

	// Token: 0x17000044 RID: 68
	// (get) Token: 0x06000213 RID: 531 RVA: 0x000125E4 File Offset: 0x000107E4
	// (set) Token: 0x06000214 RID: 532 RVA: 0x000125EC File Offset: 0x000107EC
	public Vector3 SyncAccessor_lastKillDirection
	{
		get
		{
			return this.lastKillDirection;
		}
		set
		{
			if (value || !base.IsServer)
			{
				this.lastKillDirection = value;
			}
			this.syncVar___lastKillDirection.SetValue(value, value);
		}
	}

	// Token: 0x06000215 RID: 533 RVA: 0x00012624 File Offset: 0x00010824
	public virtual void Awake___UserLogic()
	{
		this.fullHealth = this.SyncAccessor_health;
		this.controller = base.GetComponent<FirstPersonController>();
		this.sync___set_value_playerValues(base.GetComponent<PlayerValues>(), true);
		this.healthDisplay = GameObject.FindGameObjectWithTag("HealthDisplay").GetComponent<TMP_Text>();
		this.mainCamObject = GameObject.Find("Main Camera");
		if (this.mainCamObject.GetComponent<KillCam>() != null)
		{
			this.killCamScript = this.mainCamObject.GetComponent<KillCam>();
		}
		this.playerCamera = this.controller.playerCamera.transform;
		this.killCamScript.ragdollCam = false;
		this.killCamScript.isDead = false;
		this.tempHealth = this.SyncAccessor_health;
		this.PingDisplay = InstanceFinder.NetworkManager.GetComponent<PingDisplay>();
	}

	// Token: 0x0400026A RID: 618
	[SyncVar]
	public float health = 10f;

	// Token: 0x0400026B RID: 619
	public float fullHealth;

	// Token: 0x0400026C RID: 620
	public Vector3 spawn;

	// Token: 0x0400026D RID: 621
	[HideInInspector]
	public FirstPersonController controller;

	// Token: 0x0400026E RID: 622
	private float respawnTimer;

	// Token: 0x0400026F RID: 623
	private float sceneTimer;

	// Token: 0x04000270 RID: 624
	[SyncVar]
	public bool canMove = true;

	// Token: 0x04000271 RID: 625
	[SyncVar]
	public PlayerValues playerValues;

	// Token: 0x04000272 RID: 626
	[SyncVar]
	public Transform killer;

	// Token: 0x04000273 RID: 627
	[SyncVar(WritePermissions = WritePermission.ClientUnsynchronized)]
	public bool isKilled;

	// Token: 0x04000274 RID: 628
	[SyncVar]
	public bool isShot;

	// Token: 0x04000275 RID: 629
	[SyncVar]
	public Vector3 lastKillDirection;

	// Token: 0x04000276 RID: 630
	public bool shouldBounce;

	// Token: 0x04000277 RID: 631
	public bool shouldDropWeapon;

	// Token: 0x04000278 RID: 632
	public bool isDeadFromTargetRpc;

	// Token: 0x04000279 RID: 633
	public Vector3 bounceDirection;

	// Token: 0x0400027A RID: 634
	public float bounceForce;

	// Token: 0x0400027B RID: 635
	[SerializeField]
	private GameObject[] bodyParts;

	// Token: 0x0400027C RID: 636
	[SerializeField]
	private GameObject aboubiRagdoll;

	// Token: 0x0400027D RID: 637
	[SerializeField]
	private Vector3 killCamRagdollOffset;

	// Token: 0x0400027E RID: 638
	[SerializeField]
	private float directionOffset = 3f;

	// Token: 0x0400027F RID: 639
	[SerializeField]
	private GameObject bloodFX;

	// Token: 0x04000280 RID: 640
	[SerializeField]
	private HealthTween tweenScript;

	// Token: 0x04000281 RID: 641
	[SerializeField]
	private CameraEffect camHit;

	// Token: 0x04000282 RID: 642
	private TMP_Text healthDisplay;

	// Token: 0x04000283 RID: 643
	public GameObject graphics;

	// Token: 0x04000284 RID: 644
	private GameObject mainCamObject;

	// Token: 0x04000285 RID: 645
	private KillCam killCamScript;

	// Token: 0x04000286 RID: 646
	private Transform playerCamera;

	// Token: 0x04000287 RID: 647
	private float tempHealth;

	// Token: 0x04000288 RID: 648
	private bool spawnedRagdoll;

	// Token: 0x04000289 RID: 649
	[HideInInspector]
	public bool suicide;

	// Token: 0x0400028A RID: 650
	[HideInInspector]
	public bool fellVoid;

	// Token: 0x0400028B RID: 651
	private PingDisplay PingDisplay;

	// Token: 0x0400028C RID: 652
	private float count;

	// Token: 0x0400028D RID: 653
	public SyncVar<float> syncVar___health;

	// Token: 0x0400028E RID: 654
	public SyncVar<bool> syncVar___canMove;

	// Token: 0x0400028F RID: 655
	public SyncVar<PlayerValues> syncVar___playerValues;

	// Token: 0x04000290 RID: 656
	public SyncVar<Transform> syncVar___killer;

	// Token: 0x04000291 RID: 657
	public SyncVar<bool> syncVar___isKilled;

	// Token: 0x04000292 RID: 658
	public SyncVar<bool> syncVar___isShot;

	// Token: 0x04000293 RID: 659
	public SyncVar<Vector3> syncVar___lastKillDirection;

	// Token: 0x04000294 RID: 660
	private bool NetworkInitializeEarly_PlayerHealth_Assembly-CSharp.dll;

	// Token: 0x04000295 RID: 661
	private bool NetworkInitializeLate_PlayerHealth_Assembly-CSharp.dll;
}

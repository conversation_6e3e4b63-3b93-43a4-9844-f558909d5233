﻿using System;
using Steamworks;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

// Token: 0x0200011C RID: 284
public class LobbyDataEntry : MonoBehaviour
{
	// Token: 0x06000E10 RID: 3600 RVA: 0x0005E3FC File Offset: 0x0005C5FC
	public void RenderLobby()
	{
		this.DlcStar.SetActive(false);
		this.DlcFrame.SetActive(false);
		if (this.lobbyID == new CSteamID(SteamLobby.Instance.CurrentLobbyID))
		{
			global::UnityEngine.Object.Destroy(base.gameObject);
		}
		if (this.lobbyName == "private")
		{
			global::UnityEngine.Object.Destroy(base.gameObject);
		}
		this.lobbyNameText.text = ((this.lobbyName == "") ? "Empty" : this.lobbyName);
		if (this.ownDlc0 == "True")
		{
			this.background.color = this.bgColor;
			this.lobbyNameText.color = this.textColor;
			this.DlcStar.SetActive(true);
			this.DlcFrame.SetActive(true);
		}
		this.playerCountText.text = this.playerCount.ToString() + "/" + this.maxPlayers.ToString();
		this.gamemodeText.text = this.gamemode;
	}

	// Token: 0x06000E11 RID: 3601 RVA: 0x0005E517 File Offset: 0x0005C717
	public void JoinLobby()
	{
		SteamLobby.Instance.JoinLobbyAuth(this.lobbyID);
	}

	// Token: 0x04000C76 RID: 3190
	public CSteamID lobbyID;

	// Token: 0x04000C77 RID: 3191
	public string lobbyName;

	// Token: 0x04000C78 RID: 3192
	public string ownDlc0;

	// Token: 0x04000C79 RID: 3193
	public string gamemode;

	// Token: 0x04000C7A RID: 3194
	public int playerCount;

	// Token: 0x04000C7B RID: 3195
	public int maxPlayers;

	// Token: 0x04000C7C RID: 3196
	public TextMeshProUGUI lobbyNameText;

	// Token: 0x04000C7D RID: 3197
	public TextMeshProUGUI playerCountText;

	// Token: 0x04000C7E RID: 3198
	public TextMeshProUGUI gamemodeText;

	// Token: 0x04000C7F RID: 3199
	[SerializeField]
	private Image background;

	// Token: 0x04000C80 RID: 3200
	[SerializeField]
	private Color bgColor;

	// Token: 0x04000C81 RID: 3201
	[SerializeField]
	private Color textColor;

	// Token: 0x04000C82 RID: 3202
	[SerializeField]
	private GameObject DlcStar;

	// Token: 0x04000C83 RID: 3203
	[SerializeField]
	private GameObject DlcFrame;
}

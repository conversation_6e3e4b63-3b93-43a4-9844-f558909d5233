﻿using System;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

// Token: 0x02000140 RID: 320
public class XpPopupInstance : MonoBehaviour
{
	// Token: 0x06000E9D RID: 3741 RVA: 0x0006065C File Offset: 0x0005E85C
	private void Start()
	{
		SoundManager.Instance.PlaySound(this.spawnSfx);
		this.tempVfx = global::UnityEngine.Object.Instantiate<GameObject>(this.spawnVfx, GameObject.Find("XpPopupVfxPos").transform.position, Quaternion.identity);
		if (ProgressManager.Instance.instances[this.index].dlcExlusive)
		{
			this.dlcText.text = "DLC";
		}
		if (ProgressManager.Instance.instances[this.index].cosmetic != null)
		{
			this.titleText.text = "New Hat";
			this.img.texture = ProgressManager.Instance.instances[this.index].cosmetic.sprite.texture;
			this.text.text = ProgressManager.Instance.instances[this.index].cosmetic.cosmeticName;
			this.challengeText.text = (ProgressManager.Instance.instances[this.index].cosmetic.unlockWithXp ? ("Gain " + ProgressManager.Instance.instances[this.index].xpToUnlock.ToString() + " xp") : ProgressManager.Instance.instances[this.index].cosmetic.challengeDescription);
			return;
		}
		this.titleText.text = "New Map";
		this.text.text = ProgressManager.Instance.instances[this.index].maps[this.mapIndex];
		this.challengeText.text = "Gain " + ProgressManager.Instance.instances[this.index].xpToUnlock.ToString() + " xp";
		if (!Application.isEditor)
		{
			this.img.texture = (Texture2D)Resources.Load("MapSprites/" + this.text.text, typeof(Texture2D));
		}
	}

	// Token: 0x06000E9E RID: 3742 RVA: 0x00060865 File Offset: 0x0005EA65
	private void OnDisable()
	{
		if (this.tempVfx != null)
		{
			global::UnityEngine.Object.Destroy(this.tempVfx);
		}
	}

	// Token: 0x06000E9F RID: 3743 RVA: 0x00060880 File Offset: 0x0005EA80
	public void PassPopup()
	{
		this.clicked = true;
	}

	// Token: 0x06000EA0 RID: 3744 RVA: 0x00060889 File Offset: 0x0005EA89
	public void SkipAll()
	{
		ProgressManager.Instance.skipAll = true;
	}

	// Token: 0x04000D34 RID: 3380
	public int index;

	// Token: 0x04000D35 RID: 3381
	public int mapIndex;

	// Token: 0x04000D36 RID: 3382
	public bool clicked;

	// Token: 0x04000D37 RID: 3383
	[SerializeField]
	private RawImage img;

	// Token: 0x04000D38 RID: 3384
	[SerializeField]
	private TextMeshProUGUI text;

	// Token: 0x04000D39 RID: 3385
	[SerializeField]
	private TextMeshProUGUI challengeText;

	// Token: 0x04000D3A RID: 3386
	[SerializeField]
	private TextMeshProUGUI titleText;

	// Token: 0x04000D3B RID: 3387
	[SerializeField]
	private TextMeshProUGUI dlcText;

	// Token: 0x04000D3C RID: 3388
	[SerializeField]
	private GameObject spawnVfx;

	// Token: 0x04000D3D RID: 3389
	[SerializeField]
	private AudioClip spawnSfx;

	// Token: 0x04000D3E RID: 3390
	private GameObject tempVfx;
}

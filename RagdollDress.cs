﻿using System;
using UnityEngine;

// Token: 0x0200006C RID: 108
public class RagdollDress : MonoBehaviour
{
	// Token: 0x060004BD RID: 1213 RVA: 0x00020432 File Offset: 0x0001E632
	private void OnEnable()
	{
		PauseManager.OnRoundStarted += this.StartNewRound;
	}

	// Token: 0x060004BE RID: 1214 RVA: 0x00020445 File Offset: 0x0001E645
	private void OnDisable()
	{
		PauseManager.OnRoundStarted -= this.StartNewRound;
	}

	// Token: 0x060004BF RID: 1215 RVA: 0x00020458 File Offset: 0x0001E658
	private void StartNewRound()
	{
		if (ClientInstance.playerInstances.Count > 2)
		{
			global::UnityEngine.Object.Destroy(base.gameObject);
		}
	}

	// Token: 0x040004D3 RID: 1235
	public GameObject[] meshesToChange;
}

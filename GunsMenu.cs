﻿using System;
using UnityEngine;
using UnityEngine.EventSystems;

// Token: 0x02000171 RID: 369
public class GunsMenu : MonoBehaviour
{
	// Token: 0x06000F73 RID: 3955 RVA: 0x00065834 File Offset: 0x00063A34
	private void Start()
	{
		this.Guns[0].SetActive(true);
	}

	// Token: 0x06000F74 RID: 3956 RVA: 0x00065844 File Offset: 0x00063A44
	public void NextGun()
	{
		this.Guns[this.currentGun].SetActive(false);
		this.currentGun++;
		if (this.currentGun >= this.Guns.Length)
		{
			this.currentGun = 0;
		}
		this.Guns[this.currentGun].SetActive(true);
	}

	// Token: 0x06000F75 RID: 3957 RVA: 0x0006589C File Offset: 0x00063A9C
	public void PreviousGun()
	{
		this.Guns[this.currentGun].SetActive(false);
		this.currentGun--;
		if (this.currentGun < 0)
		{
			this.currentGun = this.Guns.Length - 1;
		}
		this.Guns[this.currentGun].SetActive(true);
	}

	// Token: 0x06000F76 RID: 3958 RVA: 0x000658F8 File Offset: 0x00063AF8
	private void Update()
	{
		if ((Input.GetMouseButtonDown(0) && !EventSystem.current.IsPointerOverGameObject()) || (Input.touchCount > 0 && Input.GetTouch(0).phase == TouchPhase.Began && !EventSystem.current.IsPointerOverGameObject(Input.GetTouch(0).fingerId)))
		{
			this.Buttons.SetActive(false);
			return;
		}
		if (Input.touchCount == 0 && !Input.GetMouseButton(0))
		{
			this.Buttons.SetActive(true);
		}
	}

	// Token: 0x04000E3D RID: 3645
	public GameObject Buttons;

	// Token: 0x04000E3E RID: 3646
	public GameObject[] Guns;

	// Token: 0x04000E3F RID: 3647
	private int currentGun;
}

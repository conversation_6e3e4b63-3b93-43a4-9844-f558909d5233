﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using Newtonsoft.Json.Linq;
using TMPro;
using UnityEngine;
using UnityEngine.SceneManagement;
using UnityEngine.UI;

// Token: 0x020000F1 RID: 241
public class ProgressManager : MonoBehaviour, ISaveable
{
	// Token: 0x06000C89 RID: 3209 RVA: 0x00055E90 File Offset: 0x00054090
	private void Awake()
	{
		if (ProgressManager.Instance == null)
		{
			ProgressManager.Instance = this;
		}
		for (int i = 0; i < this.instances.Length; i++)
		{
			this.instances[i].index = i;
		}
		int num = SceneManager.sceneCountInBuildSettings - 6;
		string[] array = new string[num];
		for (int j = 0; j < num; j++)
		{
			string fileNameWithoutExtension = Path.GetFileNameWithoutExtension(SceneUtility.GetScenePathByBuildIndex(j + 6));
			array[j] = fileNameWithoutExtension;
		}
		List<string> list = new List<string>();
		list.AddRange(this.instances[0].maps);
		foreach (string text in this.instances[0].maps)
		{
			if (text.ToLower().EndsWith("_alt"))
			{
				list.Remove(text);
			}
		}
		foreach (string text2 in array)
		{
			if (text2.ToLower().EndsWith("_alt"))
			{
				list.Add(text2);
			}
		}
		this.instances[0].maps = list.ToArray();
	}

	// Token: 0x06000C8A RID: 3210 RVA: 0x00055FAF File Offset: 0x000541AF
	private void Start()
	{
		this.pauseManager = PauseManager.Instance;
		base.StartCoroutine(this.BootupPopups());
		base.StartCoroutine(this.UIBootupCoroutine());
	}

	// Token: 0x06000C8B RID: 3211 RVA: 0x00055FD6 File Offset: 0x000541D6
	private IEnumerator UIBootupCoroutine()
	{
		yield return new WaitForSeconds(0.1f);
		this.UIBootup();
		yield break;
	}

	// Token: 0x06000C8C RID: 3212 RVA: 0x00055FE5 File Offset: 0x000541E5
	private IEnumerator BootupPopups()
	{
		yield return new WaitForSeconds(2f);
		this.ReturnToMenu();
		yield break;
	}

	// Token: 0x06000C8D RID: 3213 RVA: 0x00055FF4 File Offset: 0x000541F4
	private void Update()
	{
		this.HandleUIUpdate();
		if (!SceneMotor.Instance)
		{
			return;
		}
		if (!this.pauseManager.inMainMenu && !this.pauseManager.inVictoryMenu && !SceneMotor.Instance.testMap && this.xp <= 10000f)
		{
			this.xp += Time.deltaTime * 1.4f;
		}
		if (Input.GetKey(KeyCode.LeftControl) && Input.GetKeyDown(KeyCode.N) && Application.isEditor)
		{
			this.xp = 30000f;
		}
	}

	// Token: 0x06000C8E RID: 3214 RVA: 0x00056088 File Offset: 0x00054288
	[ContextMenu("ActivatePopups")]
	public void ReturnToMenu()
	{
		List<ProgressInstance> list = new List<ProgressInstance>();
		for (int i = 0; i < this.instances.Length; i++)
		{
			if ((float)this.instances[i].xpToUnlock <= this.xp)
			{
				list.Add(this.instances[i]);
			}
		}
		base.StartCoroutine(this.Popups(list));
		this.skipAll = false;
		foreach (XpContentInstance xpContentInstance in this.xpContentInstances)
		{
			xpContentInstance.UpdateUI();
		}
		MapsManager.Instance.UpdateUnlockedMaps();
		CosmeticsManager.Instance.UpdateUnlockable();
		Settings.Instance.mapsUnlocked = (float)MapsManager.Instance.unlockedMaps.Length;
	}

	// Token: 0x06000C8F RID: 3215 RVA: 0x00056158 File Offset: 0x00054358
	private IEnumerator Popups(List<ProgressInstance> instancePopups)
	{
		int num;
		for (int i = 0; i < instancePopups.Count; i = num + 1)
		{
			if (!instancePopups[i].unlocked && (!instancePopups[i].dlcExlusive || SteamLobby.ownDlc0))
			{
				instancePopups[i].unlocked = true;
				this.unlocked[instancePopups[i].index] = true;
				if (instancePopups[i].cosmetic == null)
				{
					for (int j = 0; j < instancePopups[i].maps.Length; j = num + 1)
					{
						yield return base.StartCoroutine(this.GetPopup(instancePopups[i], instancePopups[i].index, j));
						num = j;
					}
				}
				else
				{
					yield return base.StartCoroutine(this.GetPopup(instancePopups[i], instancePopups[i].index, 0));
				}
			}
			else
			{
				yield return null;
			}
			num = i;
		}
		using (List<XpContentInstance>.Enumerator enumerator = this.xpContentInstances.GetEnumerator())
		{
			while (enumerator.MoveNext())
			{
				XpContentInstance xpContentInstance = enumerator.Current;
				xpContentInstance.UpdateUI();
			}
			yield break;
		}
		yield break;
	}

	// Token: 0x06000C90 RID: 3216 RVA: 0x0005616E File Offset: 0x0005436E
	private IEnumerator GetPopup(ProgressInstance ins, int index, int mapIndex)
	{
		if (ins.cosmetic != null)
		{
			GameObject tempPopup = global::UnityEngine.Object.Instantiate<GameObject>(this.xpPopupPrefab, this.parentObject.position, Quaternion.identity, this.parentObject);
			XpPopupInstance popupScript2 = tempPopup.GetComponent<XpPopupInstance>();
			popupScript2.index = index;
			yield return new WaitUntil(() => popupScript2.clicked || this.skipAll);
			if (this.skipAll)
			{
				yield return new WaitForSeconds(0.01f);
			}
			global::UnityEngine.Object.Destroy(tempPopup);
			tempPopup = null;
		}
		else
		{
			GameObject tempPopup = global::UnityEngine.Object.Instantiate<GameObject>(this.xpPopupPrefab, this.parentObject.position, Quaternion.identity, this.parentObject);
			XpPopupInstance popupScript = tempPopup.GetComponent<XpPopupInstance>();
			popupScript.index = index;
			popupScript.mapIndex = mapIndex;
			yield return new WaitUntil(() => popupScript.clicked || this.skipAll);
			if (this.skipAll)
			{
				yield return new WaitForSeconds(0.01f);
			}
			global::UnityEngine.Object.Destroy(tempPopup);
			tempPopup = null;
		}
		SaveLoadSystem.Instance.Save();
		MapsManager.Instance.UpdateUnlockedMaps();
		CosmeticsManager.Instance.UpdateUnlockable();
		Settings.Instance.mapsUnlocked = (float)MapsManager.Instance.unlockedMaps.Length;
		yield break;
	}

	// Token: 0x06000C91 RID: 3217 RVA: 0x00056194 File Offset: 0x00054394
	private void UIBootup()
	{
		for (int i = 0; i < this.instances.Length; i++)
		{
			Vector3 vector = Vector3.Lerp(this.firstPosition.position, this.lastPosition.position, (float)this.instances[i].xpToUnlock / (float)this.instances[this.instances.Length - 1].xpToUnlock);
			GameObject gameObject = global::UnityEngine.Object.Instantiate<GameObject>(this.verticalGroupPrefab, vector, Quaternion.identity, this.contentArea);
			gameObject.GetComponent<XpContentLayout>().xpToUnlock = this.instances[i].xpToUnlock;
			if (this.instances[i].cosmetic != null)
			{
				global::UnityEngine.Object.Instantiate<GameObject>(this.xpContentPrefab, Vector3.zero, Quaternion.identity, gameObject.transform).GetComponent<XpContentInstance>().index = i;
			}
			else
			{
				for (int j = 0; j < this.instances[i].maps.Length; j++)
				{
					XpContentInstance component = global::UnityEngine.Object.Instantiate<GameObject>(this.xpContentPrefab, Vector3.zero, Quaternion.identity, gameObject.transform).GetComponent<XpContentInstance>();
					component.index = i;
					component.mapIndex = j;
				}
			}
		}
		foreach (XpContentInstance xpContentInstance in this.xpContentInstances)
		{
			xpContentInstance.UpdateUI();
		}
	}

	// Token: 0x06000C92 RID: 3218 RVA: 0x000562F4 File Offset: 0x000544F4
	private void HandleUIUpdate()
	{
		this.xpBarProgressImage.fillAmount = this.xp / (float)this.instances[this.instances.Length - 1].xpToUnlock;
		this.xpText.text = ((int)this.xp).ToString() + "/" + this.instances[this.instances.Length - 1].xpToUnlock.ToString();
	}

	// Token: 0x06000C93 RID: 3219 RVA: 0x0005636C File Offset: 0x0005456C
	public object SaveState()
	{
		return new ProgressManager.SaveData
		{
			xp = this.xp,
			unlocked = this.unlocked
		};
	}

	// Token: 0x06000C94 RID: 3220 RVA: 0x000563A4 File Offset: 0x000545A4
	public void LoadState(JObject state)
	{
		ProgressManager.SaveData saveData = state.ToObject<ProgressManager.SaveData>();
		this.xp = saveData.xp;
		this.unlocked = saveData.unlocked;
		if (this.instances.Length != this.unlocked.Length)
		{
			Debug.LogError(string.Format("ProgressManager: Save data unlocked array length ({0}) does not match progress instances length ({1})", this.unlocked.Length, this.instances.Length));
			bool[] array = new bool[this.instances.Length];
			int num = Mathf.Min(this.instances.Length, this.unlocked.Length);
			for (int i = 0; i < num; i++)
			{
				array[i] = this.unlocked[i];
			}
			this.unlocked = array;
		}
		for (int j = 0; j < this.instances.Length; j++)
		{
			if (!this.instances[j].dlcExlusive || SteamLobby.ownDlc0)
			{
				this.instances[j].unlocked = this.unlocked[j];
			}
		}
	}

	// Token: 0x04000ACC RID: 2764
	private float xpTimer;

	// Token: 0x04000ACD RID: 2765
	public float xp;

	// Token: 0x04000ACE RID: 2766
	[SerializeField]
	private GameObject xpPopupPrefab;

	// Token: 0x04000ACF RID: 2767
	public ProgressInstance[] instances;

	// Token: 0x04000AD0 RID: 2768
	public bool[] unlocked;

	// Token: 0x04000AD1 RID: 2769
	private PauseManager pauseManager;

	// Token: 0x04000AD2 RID: 2770
	[Space]
	[SerializeField]
	private Transform parentObject;

	// Token: 0x04000AD3 RID: 2771
	[SerializeField]
	private Transform contentArea;

	// Token: 0x04000AD4 RID: 2772
	[SerializeField]
	private Transform firstPosition;

	// Token: 0x04000AD5 RID: 2773
	[SerializeField]
	private Transform lastPosition;

	// Token: 0x04000AD6 RID: 2774
	[SerializeField]
	private Image xpBarProgressImage;

	// Token: 0x04000AD7 RID: 2775
	[SerializeField]
	private GameObject xpContentPrefab;

	// Token: 0x04000AD8 RID: 2776
	[SerializeField]
	private GameObject verticalGroupPrefab;

	// Token: 0x04000AD9 RID: 2777
	[SerializeField]
	private TextMeshProUGUI xpText;

	// Token: 0x04000ADA RID: 2778
	[HideInInspector]
	public List<XpContentInstance> xpContentInstances = new List<XpContentInstance>();

	// Token: 0x04000ADB RID: 2779
	public static ProgressManager Instance;

	// Token: 0x04000ADC RID: 2780
	public bool skipAll;

	// Token: 0x020000F2 RID: 242
	[Serializable]
	private struct SaveData
	{
		// Token: 0x04000ADD RID: 2781
		public float xp;

		// Token: 0x04000ADE RID: 2782
		public bool[] unlocked;
	}
}

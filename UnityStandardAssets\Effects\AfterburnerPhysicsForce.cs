﻿using System;
using UnityEngine;

namespace UnityStandardAssets.Effects
{
	// Token: 0x0200018D RID: 397
	[RequireComponent(typeof(SphereCollider))]
	public class AfterburnerPhysicsForce : MonoBehaviour
	{
		// Token: 0x06001014 RID: 4116 RVA: 0x00068B53 File Offset: 0x00066D53
		private void OnEnable()
		{
			this.m_Sphere = base.GetComponent<Collider>() as SphereCollider;
		}

		// Token: 0x06001015 RID: 4117 RVA: 0x00068B68 File Offset: 0x00066D68
		private void FixedUpdate()
		{
			this.m_Cols = Physics.OverlapSphere(base.transform.position + this.m_Sphere.center, this.m_Sphere.radius);
			for (int i = 0; i < this.m_Cols.Length; i++)
			{
				if (this.m_Cols[i].attachedRigidbody != null)
				{
					Vector3 vector = base.transform.InverseTransformPoint(this.m_Cols[i].transform.position);
					vector = Vector3.MoveTowards(vector, new Vector3(0f, 0f, vector.z), this.effectWidth * 0.5f);
					float num = Mathf.Abs(Mathf.Atan2(vector.x, vector.z) * 57.29578f);
					float num2 = Mathf.InverseLerp(this.effectDistance, 0f, vector.magnitude);
					num2 *= Mathf.InverseLerp(this.effectAngle, 0f, num);
					Vector3 vector2 = this.m_Cols[i].transform.position - base.transform.position;
					this.m_Cols[i].attachedRigidbody.AddForceAtPosition(vector2.normalized * this.force * num2, Vector3.Lerp(this.m_Cols[i].transform.position, base.transform.TransformPoint(0f, 0f, vector.z), 0.1f));
				}
			}
		}

		// Token: 0x06001016 RID: 4118 RVA: 0x00068CEC File Offset: 0x00066EEC
		private void OnDrawGizmosSelected()
		{
			if (this.m_Sphere == null)
			{
				this.m_Sphere = base.GetComponent<Collider>() as SphereCollider;
			}
			this.m_Sphere.radius = this.effectDistance * 0.5f;
			this.m_Sphere.center = new Vector3(0f, 0f, this.effectDistance * 0.5f);
			Vector3[] array = new Vector3[]
			{
				Vector3.up,
				-Vector3.up,
				Vector3.right,
				-Vector3.right
			};
			Vector3[] array2 = new Vector3[]
			{
				-Vector3.right,
				Vector3.right,
				Vector3.up,
				-Vector3.up
			};
			Gizmos.color = new Color(0f, 1f, 0f, 0.5f);
			for (int i = 0; i < 4; i++)
			{
				Vector3 vector = base.transform.position + base.transform.rotation * array[i] * this.effectWidth * 0.5f;
				Vector3 vector2 = base.transform.TransformDirection(Quaternion.AngleAxis(this.effectAngle, array2[i]) * Vector3.forward);
				Gizmos.DrawLine(vector, vector + vector2 * this.m_Sphere.radius * 2f);
			}
		}

		// Token: 0x04000EE1 RID: 3809
		public float effectAngle = 15f;

		// Token: 0x04000EE2 RID: 3810
		public float effectWidth = 1f;

		// Token: 0x04000EE3 RID: 3811
		public float effectDistance = 10f;

		// Token: 0x04000EE4 RID: 3812
		public float force = 10f;

		// Token: 0x04000EE5 RID: 3813
		private Collider[] m_Cols;

		// Token: 0x04000EE6 RID: 3814
		private SphereCollider m_Sphere;
	}
}

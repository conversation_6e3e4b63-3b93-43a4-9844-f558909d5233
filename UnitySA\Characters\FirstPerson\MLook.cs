﻿using System;
using UnityEngine;

namespace UnitySA.Characters.FirstPerson
{
	// Token: 0x02000177 RID: 375
	[Serializable]
	public class MLook
	{
		// Token: 0x06000F92 RID: 3986 RVA: 0x0006680B File Offset: 0x00064A0B
		public void Init(Transform character, Transform camera)
		{
			this.m_CharacterTargetRot = character.localRotation;
			this.m_CameraTargetRot = camera.localRotation;
		}

		// Token: 0x06000F93 RID: 3987 RVA: 0x00066828 File Offset: 0x00064A28
		public void LookRotation(Transform character, Transform camera)
		{
			float num = Input.GetAxis("Mouse X") * this.XSensitivity;
			float num2 = Input.GetAxis("Mouse Y") * this.YSensitivity;
			this.m_CharacterTargetRot *= Quaternion.Euler(0f, num, 0f);
			this.m_CameraTargetRot *= Quaternion.Euler(-num2, 0f, 0f);
			if (this.clampVerticalRotation)
			{
				this.m_CameraTargetRot = this.ClampRotationAroundXAxis(this.m_CameraTargetRot);
			}
			if (this.smooth)
			{
				character.localRotation = Quaternion.Slerp(character.localRotation, this.m_CharacterTargetRot, this.smoothTime * Time.deltaTime);
				camera.localRotation = Quaternion.Slerp(camera.localRotation, this.m_CameraTargetRot, this.smoothTime * Time.deltaTime);
			}
			else
			{
				character.localRotation = this.m_CharacterTargetRot;
				camera.localRotation = this.m_CameraTargetRot;
			}
			this.UpdateCursorLock();
		}

		// Token: 0x06000F94 RID: 3988 RVA: 0x00066924 File Offset: 0x00064B24
		public void SetCursorLock(bool value)
		{
			this.lockCursor = value;
			if (!this.lockCursor)
			{
				Cursor.lockState = CursorLockMode.None;
				Cursor.visible = true;
			}
		}

		// Token: 0x06000F95 RID: 3989 RVA: 0x00066941 File Offset: 0x00064B41
		public void UpdateCursorLock()
		{
			if (this.lockCursor)
			{
				this.InternalLockUpdate();
			}
		}

		// Token: 0x06000F96 RID: 3990 RVA: 0x00066954 File Offset: 0x00064B54
		private void InternalLockUpdate()
		{
			if (Input.GetKeyUp(KeyCode.Escape))
			{
				this.m_cursorIsLocked = false;
			}
			else if (Input.GetMouseButtonUp(0))
			{
				this.m_cursorIsLocked = true;
			}
			if (this.m_cursorIsLocked)
			{
				Cursor.lockState = CursorLockMode.Locked;
				Cursor.visible = false;
				return;
			}
			if (!this.m_cursorIsLocked)
			{
				Cursor.lockState = CursorLockMode.None;
				Cursor.visible = true;
			}
		}

		// Token: 0x06000F97 RID: 3991 RVA: 0x000669AC File Offset: 0x00064BAC
		private Quaternion ClampRotationAroundXAxis(Quaternion q)
		{
			q.x /= q.w;
			q.y /= q.w;
			q.z /= q.w;
			q.w = 1f;
			float num = 114.59156f * Mathf.Atan(q.x);
			num = Mathf.Clamp(num, this.MinimumX, this.MaximumX);
			q.x = Mathf.Tan(0.008726646f * num);
			return q;
		}

		// Token: 0x04000E73 RID: 3699
		public float XSensitivity = 2f;

		// Token: 0x04000E74 RID: 3700
		public float YSensitivity = 2f;

		// Token: 0x04000E75 RID: 3701
		public bool clampVerticalRotation = true;

		// Token: 0x04000E76 RID: 3702
		public float MinimumX = -90f;

		// Token: 0x04000E77 RID: 3703
		public float MaximumX = 90f;

		// Token: 0x04000E78 RID: 3704
		public bool smooth;

		// Token: 0x04000E79 RID: 3705
		public float smoothTime = 5f;

		// Token: 0x04000E7A RID: 3706
		public bool lockCursor = true;

		// Token: 0x04000E7B RID: 3707
		private Quaternion m_CharacterTargetRot;

		// Token: 0x04000E7C RID: 3708
		private Quaternion m_CameraTargetRot;

		// Token: 0x04000E7D RID: 3709
		private bool m_cursorIsLocked = true;
	}
}

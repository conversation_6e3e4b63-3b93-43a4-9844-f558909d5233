﻿using System;
using FishNet;
using FishNet.Connection;
using FishNet.Managing;
using FishNet.Object;
using FishNet.Object.Delegating;
using FishNet.Serializing;
using FishNet.Serializing.Generated;
using FishNet.Transporting;
using UnityEngine;

// Token: 0x02000033 RID: 51
public class PlayerSpawnObject : NetworkBehaviour
{
	// Token: 0x060002C5 RID: 709 RVA: 0x00017E20 File Offset: 0x00016020
	public override void OnStartClient()
	{
		base.OnStartClient();
		if (!base.IsOwner)
		{
			base.gameObject.GetComponent<PlayerSpawnObject>().enabled = false;
		}
	}

	// Token: 0x060002C6 RID: 710 RVA: 0x00017E44 File Offset: 0x00016044
	private void Update()
	{
		if (this.spawnedObject == null && Input.GetKeyDown(KeyCode.Alpha1))
		{
			this.SpawnObject(this.objToSpawn, base.transform, this);
		}
		if (this.spawnedObject != null && Input.GetKeyDown(KeyCode.Alpha2))
		{
			this.DespawnObject(this.spawnedObject);
		}
	}

	// Token: 0x060002C7 RID: 711 RVA: 0x00017EA0 File Offset: 0x000160A0
	[ServerRpc]
	public void SpawnObject(GameObject obj, Transform player, PlayerSpawnObject script)
	{
		this.RpcWriter___Server_SpawnObject_1585589339(obj, player, script);
	}

	// Token: 0x060002C8 RID: 712 RVA: 0x00017EBF File Offset: 0x000160BF
	[ObserversRpc]
	public void SetSpawnedObject(GameObject spawned, PlayerSpawnObject script)
	{
		this.RpcWriter___Observers_SetSpawnedObject_1450126728(spawned, script);
	}

	// Token: 0x060002C9 RID: 713 RVA: 0x00017ED0 File Offset: 0x000160D0
	[ServerRpc(RequireOwnership = false)]
	public void DespawnObject(GameObject obj)
	{
		this.RpcWriter___Server_DespawnObject_1934289915(obj);
	}

	// Token: 0x060002CB RID: 715 RVA: 0x00017EE8 File Offset: 0x000160E8
	public virtual void NetworkInitialize___Early()
	{
		if (this.NetworkInitializeEarly_PlayerSpawnObject_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeEarly_PlayerSpawnObject_Assembly-CSharp.dll = true;
		base.RegisterServerRpc(0U, new ServerRpcDelegate(this.RpcReader___Server_SpawnObject_1585589339));
		base.RegisterObserversRpc(1U, new ClientRpcDelegate(this.RpcReader___Observers_SetSpawnedObject_1450126728));
		base.RegisterServerRpc(2U, new ServerRpcDelegate(this.RpcReader___Server_DespawnObject_1934289915));
	}

	// Token: 0x060002CC RID: 716 RVA: 0x00017F4B File Offset: 0x0001614B
	public virtual void NetworkInitialize__Late()
	{
		if (this.NetworkInitializeLate_PlayerSpawnObject_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeLate_PlayerSpawnObject_Assembly-CSharp.dll = true;
	}

	// Token: 0x060002CD RID: 717 RVA: 0x00017F5E File Offset: 0x0001615E
	public override void NetworkInitializeIfDisabled()
	{
		this.NetworkInitialize___Early();
		this.NetworkInitialize__Late();
	}

	// Token: 0x060002CE RID: 718 RVA: 0x00017F6C File Offset: 0x0001616C
	private void RpcWriter___Server_SpawnObject_1585589339(GameObject obj, Transform player, PlayerSpawnObject script)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteGameObject(obj);
		writer.WriteTransform(player);
		writer.Write___PlayerSpawnObjectFishNet.Serializing.Generated(script);
		base.SendServerRpc(0U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x060002CF RID: 719 RVA: 0x00018088 File Offset: 0x00016288
	public void RpcLogic___SpawnObject_1585589339(GameObject obj, Transform player, PlayerSpawnObject script)
	{
		GameObject gameObject = global::UnityEngine.Object.Instantiate<GameObject>(obj, player.position + player.forward, Quaternion.identity);
		base.ServerManager.Spawn(gameObject, null);
		this.SetSpawnedObject(gameObject, script);
	}

	// Token: 0x060002D0 RID: 720 RVA: 0x000180C8 File Offset: 0x000162C8
	private void RpcReader___Server_SpawnObject_1585589339(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		GameObject gameObject = PooledReader0.ReadGameObject();
		Transform transform = PooledReader0.ReadTransform();
		PlayerSpawnObject playerSpawnObject = global::FishNet.Serializing.Generated.GeneratedReaders___Internal.Read___PlayerSpawnObjectFishNet.Serializing.Generateds(PooledReader0);
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		this.RpcLogic___SpawnObject_1585589339(gameObject, transform, playerSpawnObject);
	}

	// Token: 0x060002D1 RID: 721 RVA: 0x0001812C File Offset: 0x0001632C
	private void RpcWriter___Observers_SetSpawnedObject_1450126728(GameObject spawned, PlayerSpawnObject script)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteGameObject(spawned);
		writer.Write___PlayerSpawnObjectFishNet.Serializing.Generated(script);
		base.SendObserversRpc(1U, writer, channel, DataOrderType.Default, false, false, false);
		writer.Store();
	}

	// Token: 0x060002D2 RID: 722 RVA: 0x000181EF File Offset: 0x000163EF
	public void RpcLogic___SetSpawnedObject_1450126728(GameObject spawned, PlayerSpawnObject script)
	{
		script.spawnedObject = spawned;
	}

	// Token: 0x060002D3 RID: 723 RVA: 0x000181F8 File Offset: 0x000163F8
	private void RpcReader___Observers_SetSpawnedObject_1450126728(PooledReader PooledReader0, Channel channel)
	{
		GameObject gameObject = PooledReader0.ReadGameObject();
		PlayerSpawnObject playerSpawnObject = global::FishNet.Serializing.Generated.GeneratedReaders___Internal.Read___PlayerSpawnObjectFishNet.Serializing.Generateds(PooledReader0);
		if (!base.IsClient)
		{
			return;
		}
		this.RpcLogic___SetSpawnedObject_1450126728(gameObject, playerSpawnObject);
	}

	// Token: 0x060002D4 RID: 724 RVA: 0x0001823C File Offset: 0x0001643C
	private void RpcWriter___Server_DespawnObject_1934289915(GameObject obj)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteGameObject(obj);
		base.SendServerRpc(2U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x060002D5 RID: 725 RVA: 0x000182E4 File Offset: 0x000164E4
	public void RpcLogic___DespawnObject_1934289915(GameObject obj)
	{
		base.ServerManager.Despawn(obj, null);
	}

	// Token: 0x060002D6 RID: 726 RVA: 0x00018308 File Offset: 0x00016508
	private void RpcReader___Server_DespawnObject_1934289915(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		GameObject gameObject = PooledReader0.ReadGameObject();
		if (!base.IsServer)
		{
			return;
		}
		this.RpcLogic___DespawnObject_1934289915(gameObject);
	}

	// Token: 0x060002D7 RID: 727 RVA: 0x00017F5E File Offset: 0x0001615E
	public virtual void Awake()
	{
		this.NetworkInitialize___Early();
		this.NetworkInitialize__Late();
	}

	// Token: 0x060002D8 RID: 728 RVA: 0x000023D6 File Offset: 0x000005D6
	public virtual void Awake___UserLogic()
	{
	}

	// Token: 0x04000322 RID: 802
	public GameObject objToSpawn;

	// Token: 0x04000323 RID: 803
	public GameObject spawnedObject;

	// Token: 0x04000324 RID: 804
	private bool NetworkInitializeEarly_PlayerSpawnObject_Assembly-CSharp.dll;

	// Token: 0x04000325 RID: 805
	private bool NetworkInitializeLate_PlayerSpawnObject_Assembly-CSharp.dll;
}

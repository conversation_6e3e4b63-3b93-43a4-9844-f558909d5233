﻿using System;
using UnityEngine;

// Token: 0x0200007E RID: 126
public class LineRendererEffect : MonoBehaviour
{
	// Token: 0x06000565 RID: 1381 RVA: 0x000232EB File Offset: 0x000214EB
	private void Awake()
	{
		this.line = base.GetComponent<LineRenderer>();
	}

	// Token: 0x06000566 RID: 1382 RVA: 0x000232F9 File Offset: 0x000214F9
	private void Update()
	{
		this.timer -= Time.deltaTime;
		if (this.timer < 0f)
		{
			this.line.startColor == this.colorOne;
		}
	}

	// Token: 0x04000569 RID: 1385
	private LineRenderer line;

	// Token: 0x0400056A RID: 1386
	[SerializeField]
	private Color colorOne;

	// Token: 0x0400056B RID: 1387
	[SerializeField]
	private Color colorTwo;

	// Token: 0x0400056C RID: 1388
	private float timer;
}

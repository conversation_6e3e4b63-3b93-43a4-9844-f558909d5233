﻿using System;
using TMPro;
using UnityEngine;

// Token: 0x02000139 RID: 313
public class TurningGlow : MonoBehaviour
{
	// Token: 0x06000E86 RID: 3718 RVA: 0x0005FD2F File Offset: 0x0005DF2F
	private void Awake()
	{
		this.c = this.textLabel.color;
	}

	// Token: 0x06000E87 RID: 3719 RVA: 0x0005FD44 File Offset: 0x0005DF44
	private void Update()
	{
		this.glowTimer += Time.deltaTime * this.glowSpeed;
		this.textLabel.fontMaterial.SetFloat(ShaderUtilities.ID_LightAngle, 3f + Mathf.Sin(this.glowTimer) * this.glowAmount);
	}

	// Token: 0x04000D12 RID: 3346
	[SerializeField]
	private TMP_Text textLabel;

	// Token: 0x04000D13 RID: 3347
	private float glowTimer;

	// Token: 0x04000D14 RID: 3348
	public float glowSpeed;

	// Token: 0x04000D15 RID: 3349
	public float glowAmount;

	// Token: 0x04000D16 RID: 3350
	private Color c;
}

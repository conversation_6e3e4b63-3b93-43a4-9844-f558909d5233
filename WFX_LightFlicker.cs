﻿using System;
using System.Collections;
using UnityEngine;

// Token: 0x0200016E RID: 366
[RequireComponent(typeof(Light))]
public class WFX_LightFlicker : MonoBehaviour
{
	// Token: 0x06000F60 RID: 3936 RVA: 0x000652F8 File Offset: 0x000634F8
	private void Start()
	{
		this.timer = this.time;
		base.StartCoroutine("Flicker");
	}

	// Token: 0x06000F61 RID: 3937 RVA: 0x00065312 File Offset: 0x00063512
	private IEnumerator Flicker()
	{
		for (;;)
		{
			base.GetComponent<Light>().enabled = !base.GetComponent<Light>().enabled;
			do
			{
				this.timer -= Time.deltaTime;
				yield return null;
			}
			while (this.timer > 0f);
			this.timer = this.time;
		}
		yield break;
	}

	// Token: 0x04000E2E RID: 3630
	public float time = 0.05f;

	// Token: 0x04000E2F RID: 3631
	private float timer;
}

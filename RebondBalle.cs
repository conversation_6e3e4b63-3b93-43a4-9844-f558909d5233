﻿using System;
using FishNet;
using UnityEngine;

// Token: 0x020000A6 RID: 166
public class RebondBalle : MonoBehaviour
{
	// Token: 0x06000927 RID: 2343 RVA: 0x00042C86 File Offset: 0x00040E86
	private void Awake()
	{
		this.audio = base.GetComponent<AudioSource>();
		this._gravity = this.gravityStart;
	}

	// Token: 0x06000928 RID: 2344 RVA: 0x00042CA0 File Offset: 0x00040EA0
	public void Initialize(Vector3 direction, float force, float passedTime, GameObject rootObject, GameObject gun)
	{
		this._direction = direction;
		this._passedTime = passedTime;
		this._rootObject = rootObject;
		this._force = force;
		this._gun = gun;
		base.transform.rotation = Quaternion.LookRotation(-direction);
	}

	// Token: 0x06000929 RID: 2345 RVA: 0x00042CDD File Offset: 0x00040EDD
	private void Update()
	{
		this.currentPosition = base.transform.position;
		this.safeTimer -= Time.deltaTime;
		this.Move();
		this.HandleCollision();
		this.lastPosition = this.currentPosition;
	}

	// Token: 0x0600092A RID: 2346 RVA: 0x00042D1C File Offset: 0x00040F1C
	private void Move()
	{
		this.velocity = this.lastPosition - this.currentPosition;
		base.transform.rotation = Quaternion.LookRotation(-this.velocity);
		float deltaTime = Time.deltaTime;
		float num = 0f;
		if (this._passedTime > 0f)
		{
			float num2 = this._passedTime * 0.08f;
			this._passedTime -= num2;
			if (this._passedTime <= deltaTime / 2f)
			{
				num2 += this._passedTime;
				this._passedTime = 0f;
			}
			num = num2;
		}
		if (this.useGravity)
		{
			this._gravity += this.gravity * Time.deltaTime;
		}
		if (this.usePhysics)
		{
			this._force -= this.friction * Time.deltaTime;
		}
		base.transform.position += this._direction * ((this.usePhysics ? this._force : this.MOVE_RATE) * (deltaTime + num));
		if (this.useGravity)
		{
			base.transform.position -= Vector3.up * (this._gravity * (deltaTime + num));
		}
	}

	// Token: 0x0600092B RID: 2347 RVA: 0x00042E64 File Offset: 0x00041064
	private void HandleCollision()
	{
		RaycastHit raycastHit;
		this.backupRaycast = Physics.Raycast(base.transform.position - base.transform.forward * this.backupRayDistance, base.transform.forward, out raycastHit, this.backupRayLength, this.playerLayer);
		Collider[] array = Physics.OverlapSphere(base.transform.position, this.radius, this.playerLayer);
		if (array.Length != 0 && this.safeTimer < 0f && raycastHit.normal != this.firstNormal)
		{
			bool flag = false;
			for (int i = 0; i < array.Length; i++)
			{
				flag = array[i].gameObject == this._gun;
			}
			bool flag2 = false;
			for (int j = 0; j < array.Length; j++)
			{
				flag2 = array[j].gameObject == this._rootObject;
			}
			if (InstanceFinder.IsClient && !flag && !flag2)
			{
				this.audio.PlayOneShot(this.hitClip);
				global::UnityEngine.Object.Instantiate<GameObject>(this.hitVfx, base.transform.position, Quaternion.identity);
			}
			if (!flag && !flag2)
			{
				PlayerHealth componentInParent = array[0].GetComponentInParent<PlayerHealth>();
				if (componentInParent != null)
				{
					if (componentInParent.transform.gameObject == this._rootObject)
					{
						return;
					}
					if (componentInParent.SyncAccessor_health - this.damage <= 0f)
					{
						componentInParent.Explode(false, true, componentInParent.gameObject.name, componentInParent.transform.position - base.transform.position, this.ragdollEjectForce, base.transform.position);
						componentInParent.sync___set_value_isKilled(true, true);
					}
					PlayerHealth playerHealth = componentInParent;
					playerHealth.sync___set_value_health(playerHealth.SyncAccessor_health - this.damage, true);
					componentInParent.sync___set_value_killer(this._rootObject.transform, true);
					if (this.vfx != null)
					{
						global::UnityEngine.Object.Instantiate<GameObject>(this.vfx, base.transform.position, Quaternion.identity);
					}
					global::UnityEngine.Object.Destroy(base.gameObject);
				}
			}
			if (!flag && !flag2)
			{
				this.firstNormal = raycastHit.normal;
				this.secondNormal = this.firstNormal;
				this._direction += raycastHit.normal;
				this._direction = this._direction.normalized;
				this.rebonds -= 1f;
				if (this.rebonds <= 0f)
				{
					global::UnityEngine.Object.Destroy(base.gameObject);
				}
			}
		}
	}

	// Token: 0x04000866 RID: 2150
	private Vector3 _direction;

	// Token: 0x04000867 RID: 2151
	private float _passedTime;

	// Token: 0x04000868 RID: 2152
	[SerializeField]
	private float MOVE_RATE = 5f;

	// Token: 0x04000869 RID: 2153
	[SerializeField]
	private float radius = 0.2f;

	// Token: 0x0400086A RID: 2154
	[SerializeField]
	private float damage = 1f;

	// Token: 0x0400086B RID: 2155
	[SerializeField]
	private float rebonds = 2f;

	// Token: 0x0400086C RID: 2156
	[SerializeField]
	private GameObject vfx;

	// Token: 0x0400086D RID: 2157
	[SerializeField]
	private LayerMask playerLayer;

	// Token: 0x0400086E RID: 2158
	[SerializeField]
	private AudioClip hitClip;

	// Token: 0x0400086F RID: 2159
	[SerializeField]
	private GameObject hitVfx;

	// Token: 0x04000870 RID: 2160
	private GameObject _rootObject;

	// Token: 0x04000871 RID: 2161
	private AudioSource audio;

	// Token: 0x04000872 RID: 2162
	[SerializeField]
	private bool useGravity;

	// Token: 0x04000873 RID: 2163
	[SerializeField]
	private float gravityStart;

	// Token: 0x04000874 RID: 2164
	[SerializeField]
	private float gravity;

	// Token: 0x04000875 RID: 2165
	private float _gravity;

	// Token: 0x04000876 RID: 2166
	[SerializeField]
	private bool usePhysics;

	// Token: 0x04000877 RID: 2167
	[SerializeField]
	private float friction;

	// Token: 0x04000878 RID: 2168
	[SerializeField]
	private float ragdollEjectForce = 2f;

	// Token: 0x04000879 RID: 2169
	private float _force;

	// Token: 0x0400087A RID: 2170
	private bool backupRaycast;

	// Token: 0x0400087B RID: 2171
	private bool forwardCast;

	// Token: 0x0400087C RID: 2172
	private float safeTimer;

	// Token: 0x0400087D RID: 2173
	private GameObject _gun;

	// Token: 0x0400087E RID: 2174
	[HideInInspector]
	public Weapon weapon;

	// Token: 0x0400087F RID: 2175
	private Vector3 currentPosition;

	// Token: 0x04000880 RID: 2176
	private Vector3 lastPosition;

	// Token: 0x04000881 RID: 2177
	private Vector3 velocity;

	// Token: 0x04000882 RID: 2178
	[SerializeField]
	private float backupRayLength = 1.5f;

	// Token: 0x04000883 RID: 2179
	[SerializeField]
	private float backupRayDistance = 1f;

	// Token: 0x04000884 RID: 2180
	[SerializeField]
	private float forwardCastLength = 1.5f;

	// Token: 0x04000885 RID: 2181
	private Vector3 firstNormal;

	// Token: 0x04000886 RID: 2182
	private Vector3 secondNormal;
}

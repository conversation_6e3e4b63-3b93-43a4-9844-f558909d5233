﻿using System;
using System.Collections;
using UnityEngine;

namespace UnitySA.Utility
{
	// Token: 0x02000179 RID: 377
	[Serializable]
	public class FOVZoom
	{
		// Token: 0x06000F9C RID: 3996 RVA: 0x00066C73 File Offset: 0x00064E73
		public void Setup(Camera camera)
		{
			this.CheckStatus(camera);
			this.Camera = camera;
			this.originalFov = camera.fieldOfView;
		}

		// Token: 0x06000F9D RID: 3997 RVA: 0x00066C8F File Offset: 0x00064E8F
		private void CheckStatus(Camera camera)
		{
			if (camera == null)
			{
				throw new Exception("FOVKick camera is null, please supply the camera to the constructor");
			}
			if (this.IncreaseCurve == null)
			{
				throw new Exception("FOVKick Increase curve is null, please define the curve for the field of view kicks");
			}
		}

		// Token: 0x06000F9E RID: 3998 RVA: 0x00066CB8 File Offset: 0x00064EB8
		public void ChangeCamera(Camera camera)
		{
			this.Camera = camera;
		}

		// Token: 0x06000F9F RID: 3999 RVA: 0x00066CC1 File Offset: 0x00064EC1
		public IEnumerator FOVKickUp()
		{
			float t = Mathf.Abs((this.Camera.fieldOfView - this.originalFov) / this.FOVIncrease);
			while (t < this.TimeToIncrease)
			{
				this.Camera.fieldOfView = this.originalFov + this.IncreaseCurve.Evaluate(t / this.TimeToIncrease) * this.FOVIncrease;
				t += Time.deltaTime;
				yield return new WaitForEndOfFrame();
			}
			yield break;
		}

		// Token: 0x06000FA0 RID: 4000 RVA: 0x00066CD0 File Offset: 0x00064ED0
		public IEnumerator FOVKickDown()
		{
			float t = Mathf.Abs((this.Camera.fieldOfView - this.originalFov) / this.FOVIncrease);
			while (t > 0f)
			{
				this.Camera.fieldOfView = this.originalFov + this.IncreaseCurve.Evaluate(t / this.TimeToDecrease) * this.FOVIncrease;
				t -= Time.deltaTime;
				yield return new WaitForEndOfFrame();
			}
			this.Camera.fieldOfView = this.originalFov;
			yield break;
		}

		// Token: 0x04000E87 RID: 3719
		public Camera Camera;

		// Token: 0x04000E88 RID: 3720
		[HideInInspector]
		public float originalFov;

		// Token: 0x04000E89 RID: 3721
		public float FOVIncrease = 3f;

		// Token: 0x04000E8A RID: 3722
		public float TimeToIncrease = 1f;

		// Token: 0x04000E8B RID: 3723
		public float TimeToDecrease = 1f;

		// Token: 0x04000E8C RID: 3724
		public AnimationCurve IncreaseCurve;
	}
}

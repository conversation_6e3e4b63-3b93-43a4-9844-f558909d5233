﻿using System;
using UnityEngine;

// Token: 0x0200015F RID: 351
public class CFX_Demo_RandomDir : MonoBehaviour
{
	// Token: 0x06000F0F RID: 3855 RVA: 0x00063360 File Offset: 0x00061560
	private void Awake()
	{
		base.transform.eulerAngles = new Vector3(global::UnityEngine.Random.Range(this.min.x, this.max.x), global::UnityEngine.Random.Range(this.min.y, this.max.y), global::UnityEngine.Random.Range(this.min.z, this.max.z));
	}

	// Token: 0x04000DC8 RID: 3528
	public Vector3 min = new Vector3(0f, 0f, 0f);

	// Token: 0x04000DC9 RID: 3529
	public Vector3 max = new Vector3(0f, 360f, 0f);
}

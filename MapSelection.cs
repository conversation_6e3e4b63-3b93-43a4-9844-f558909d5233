﻿using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

// Token: 0x020000D6 RID: 214
public class MapSelection : MonoBehaviour
{
	// Token: 0x06000BA7 RID: 2983 RVA: 0x00051C46 File Offset: 0x0004FE46
	private void Awake()
	{
		MapSelection.Instance = this;
	}

	// Token: 0x06000BA8 RID: 2984 RVA: 0x00051C4E File Offset: 0x0004FE4E
	private void Start()
	{
		this.PopulateMaps();
	}

	// Token: 0x06000BA9 RID: 2985 RVA: 0x00051C56 File Offset: 0x0004FE56
	private void LateUpdate()
	{
		if (this.sceneInstances.Length != MapsManager.Instance.unlockedMaps.Length)
		{
			this.PopulateMaps();
		}
	}

	// Token: 0x06000BAA RID: 2986 RVA: 0x00051C74 File Offset: 0x0004FE74
	public void PopulateMaps()
	{
		for (int i = 0; i < base.transform.childCount; i++)
		{
			global::UnityEngine.Object.Destroy(base.transform.GetChild(i).gameObject);
		}
		this.sceneNames = new string[MapsManager.Instance.unlockedMaps.Length];
		for (int j = 0; j < MapsManager.Instance.unlockedMaps.Length; j++)
		{
			int num = MapsManager.Instance.unlockedMaps[j];
			this.sceneNames[j] = MapsManager.Instance.allMaps[num].mapName;
		}
		Array.Sort<string>(this.sceneNames);
		this.sceneInstances = new SelectSceneInstance[this.sceneNames.Length];
		for (int k = 0; k < this.sceneNames.Length; k++)
		{
			GameObject gameObject = global::UnityEngine.Object.Instantiate<GameObject>(this.sceneInstance, base.transform.position, Quaternion.identity, base.transform);
			this.sceneInstances[k] = gameObject.GetComponent<SelectSceneInstance>();
			this.sceneInstances[k].sceneName = this.sceneNames[k];
			this.sceneInstances[k].UpdateUI();
			this.sceneInstances[k].mapSelectionScript = this;
		}
	}

	// Token: 0x06000BAB RID: 2987 RVA: 0x00051D94 File Offset: 0x0004FF94
	public void InitiateMaps()
	{
		this.SelectAll();
		MapsManager.Instance.LoadActivePlaylist();
	}

	// Token: 0x06000BAC RID: 2988 RVA: 0x00051DA8 File Offset: 0x0004FFA8
	public void LoadScenes(string[] tempscenes)
	{
		this.loadPlaylistPanel.SetActive(false);
		SelectSceneInstance[] array = this.sceneInstances;
		for (int i = 0; i < array.Length; i++)
		{
			SelectSceneInstance selectSceneInstance = array[i];
			bool flag = tempscenes.Any((string sceneName) => selectSceneInstance.sceneName == sceneName);
			selectSceneInstance.SetState(flag);
		}
		this.UpdateScenes();
	}

	// Token: 0x06000BAD RID: 2989 RVA: 0x00051E0C File Offset: 0x0005000C
	public void UpdateScenes()
	{
		this.sceneMotor = SceneMotor.Instance;
		this.sceneMotor.scenes.Clear();
		this.sceneMotor.remainingMaps.Clear();
		this.sceneMotor.remainingMaps = this.sceneMotor.scenes.ToArray().ToList<string>();
		this.sceneMotor.Shuffle(this.sceneMotor.remainingMaps);
		for (int i = 0; i < this.sceneInstances.Length; i++)
		{
			if (this.sceneInstances[i].selected)
			{
				this.sceneMotor.scenes.Add(this.sceneInstances[i].sceneName);
			}
		}
		this.sceneMotor.RoundsAlgorithm();
	}

	// Token: 0x06000BAE RID: 2990 RVA: 0x00051EC4 File Offset: 0x000500C4
	public void SelectAll()
	{
		SelectSceneInstance[] array = this.sceneInstances;
		for (int i = 0; i < array.Length; i++)
		{
			array[i].SetState(true);
		}
		this.UpdateScenes();
	}

	// Token: 0x06000BAF RID: 2991 RVA: 0x00051EF8 File Offset: 0x000500F8
	public void DeselectAll()
	{
		SelectSceneInstance[] array = this.sceneInstances;
		for (int i = 0; i < array.Length; i++)
		{
			array[i].SetState(false);
		}
		this.UpdateScenes();
	}

	// Token: 0x06000BB0 RID: 2992 RVA: 0x00051F2C File Offset: 0x0005012C
	public void AddPlaylistFromSelection()
	{
		List<SelectSceneInstance> list = new List<SelectSceneInstance>();
		foreach (SelectSceneInstance selectSceneInstance in this.sceneInstances)
		{
			if (selectSceneInstance.selected)
			{
				list.Add(selectSceneInstance);
			}
		}
		string[] array2 = new string[list.Count];
		for (int j = 0; j < array2.Length; j++)
		{
			array2[j] = list[j].sceneName;
		}
		MatchLogs.Instance.WriteLocalLog(string.Format("Created Playlist n°{0} from selection", MapsManager.Instance.Playlists.Count));
		MapsManager.Instance.AddPlaylist(array2, string.Format("Playlist n°{0}", MapsManager.Instance.Playlists.Count));
	}

	// Token: 0x040009CC RID: 2508
	public static MapSelection Instance;

	// Token: 0x040009CD RID: 2509
	public SceneMotor sceneMotor;

	// Token: 0x040009CE RID: 2510
	private SelectSceneInstance[] sceneInstances;

	// Token: 0x040009CF RID: 2511
	[SerializeField]
	private string[] sceneNames;

	// Token: 0x040009D0 RID: 2512
	[SerializeField]
	private GameObject sceneInstance;

	// Token: 0x040009D1 RID: 2513
	[Space]
	[SerializeField]
	private GameObject loadPlaylistPanel;

	// Token: 0x040009D2 RID: 2514
	private bool haveInitiated;
}

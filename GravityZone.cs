﻿using System;
using UnityEngine;

// Token: 0x02000055 RID: 85
[RequireComponent(typeof(Collider))]
public class GravityZone : MonoBehaviour
{
	// Token: 0x060003D7 RID: 983 RVA: 0x0001BDD0 File Offset: 0x00019FD0
	private void Awake()
	{
		this.Collider = base.GetComponent<Collider>();
		MeshCollider meshCollider = this.Collider as MeshCollider;
		if (meshCollider != null)
		{
			meshCollider.convex = true;
		}
		this.Collider.isTrigger = true;
		if (this.gravityMultiplier == 0f)
		{
			Debug.LogWarning("Gravity multiplier is zero, setting to 0.01 to avoid division by zero errors.");
			this.gravityMultiplier = 0.01f;
		}
	}

	// Token: 0x060003D8 RID: 984 RVA: 0x0001BE30 File Offset: 0x0001A030
	private void OnTriggerEnter(Collider other)
	{
		FirstPersonController component = other.GetComponent<FirstPersonController>();
		if (component != null)
		{
			component.gravityMultiplier *= this.gravityMultiplier;
		}
	}

	// Token: 0x060003D9 RID: 985 RVA: 0x0001BE60 File Offset: 0x0001A060
	private void OnTriggerExit(Collider other)
	{
		FirstPersonController component = other.GetComponent<FirstPersonController>();
		if (component != null)
		{
			component.gravityMultiplier /= this.gravityMultiplier;
		}
	}

	// Token: 0x04000428 RID: 1064
	[SerializeField]
	private float gravityMultiplier = 1f;

	// Token: 0x04000429 RID: 1065
	private Collider Collider;
}

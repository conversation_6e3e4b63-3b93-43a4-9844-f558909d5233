﻿using System;
using UnityEngine;

// Token: 0x02000168 RID: 360
public class WFX_Demo_Wall : MonoBehaviour
{
	// Token: 0x06000F46 RID: 3910 RVA: 0x00064DD8 File Offset: 0x00062FD8
	private void OnMouseDown()
	{
		RaycastHit raycastHit = default(RaycastHit);
		if (base.GetComponent<Collider>().Raycast(Camera.main.ScreenPointToRay(Input.mousePosition), out raycastHit, 9999f))
		{
			GameObject gameObject = this.demo.spawnParticle();
			gameObject.transform.position = raycastHit.point;
			gameObject.transform.rotation = Quaternion.FromToRotation(Vector3.forward, raycastHit.normal);
		}
	}

	// Token: 0x04000E15 RID: 3605
	public WFX_Demo_New demo;
}

﻿using System;
using UnityEngine;

// Token: 0x0200007F RID: 127
public class MeleeChildCollision : MonoBehaviour
{
	// Token: 0x06000568 RID: 1384 RVA: 0x00023331 File Offset: 0x00021531
	private void Update()
	{
		if (base.gameObject.layer != 21)
		{
			base.gameObject.layer = 21;
		}
		if (this.trailObject != null)
		{
			this.trailObject.SetActive(this.canHit);
		}
	}

	// Token: 0x06000569 RID: 1385 RVA: 0x00023370 File Offset: 0x00021570
	private void OnCollisionEnter(Collision collision)
	{
		if (this.weaponScript.gameObject.layer == 7)
		{
			return;
		}
		if (this.behaviour.rootObject != null && collision.transform.root == this.behaviour.rootObject.transform)
		{
			return;
		}
		if (collision.transform.tag == "ShatterableGlass" && this.canHitEnvi)
		{
			this.weaponScript.BounceHolder();
			this.weaponScript.BreakGlassServer(collision.contacts[0].point, -collision.contacts[0].normal, collision.transform.gameObject);
			global::UnityEngine.Object.Instantiate<GameObject>(this.hitVFX, collision.contacts[0].point, Quaternion.LookRotation(collision.contacts[0].normal));
			base.GetComponentInParent<AudioSource>().PlayOneShot(this.hitSFX);
		}
		this.hitsCounter++;
		if (this.canHit)
		{
			if (collision.transform.GetComponentInParent<PlayerHealth>() != null)
			{
				this.weaponScript.HitServer(collision.transform.GetComponentInParent<PlayerHealth>(), collision.contacts[0].point, collision.contacts[0].normal, collision.transform.gameObject.name);
				if (this.hitsCounter >= this.weaponScript.hitsAmount)
				{
					this.canHit = false;
				}
			}
			if (collision.transform.gameObject.layer == LayerMask.NameToLayer("Ragdoll"))
			{
				Rigidbody[] componentsInChildren = collision.transform.root.GetComponentsInChildren<Rigidbody>();
				global::UnityEngine.Object.Instantiate<GameObject>(this.weaponScript.bodyImpact, collision.contacts[0].point, Quaternion.LookRotation(collision.contacts[0].normal));
				global::UnityEngine.Object.Instantiate<GameObject>(this.weaponScript.bloodSplatter, collision.contacts[0].point, Quaternion.LookRotation(collision.contacts[0].normal));
				Rigidbody[] array = componentsInChildren;
				for (int i = 0; i < array.Length; i++)
				{
					array[i].AddExplosionForce(this.weaponScript.ragdollEjectForce, collision.contacts[0].point - this.weaponScript.rootObject.transform.forward, 100f, 1f, ForceMode.Impulse);
				}
			}
		}
		if ((collision.transform.gameObject.layer == LayerMask.NameToLayer("Default") || collision.transform.gameObject.layer == LayerMask.NameToLayer("ShootThrough") || collision.transform.gameObject.layer == LayerMask.NameToLayer("InteractEnvironment")) && this.canHitEnvi)
		{
			this.weaponScript.BounceHolder();
			this.canHitEnvi = false;
			global::UnityEngine.Object.Instantiate<GameObject>(this.hitVFX, collision.contacts[0].point, Quaternion.LookRotation(collision.contacts[0].normal));
			base.GetComponentInParent<AudioSource>().PlayOneShot(this.hitSFX);
		}
	}

	// Token: 0x0400056D RID: 1389
	[SerializeField]
	private MeleeWeapon weaponScript;

	// Token: 0x0400056E RID: 1390
	[SerializeField]
	private ItemBehaviour behaviour;

	// Token: 0x0400056F RID: 1391
	[SerializeField]
	private GameObject graphicalObject;

	// Token: 0x04000570 RID: 1392
	[SerializeField]
	private GameObject hitVFX;

	// Token: 0x04000571 RID: 1393
	[SerializeField]
	private GameObject trailObject;

	// Token: 0x04000572 RID: 1394
	[SerializeField]
	private AudioClip hitSFX;

	// Token: 0x04000573 RID: 1395
	public bool canHit;

	// Token: 0x04000574 RID: 1396
	public bool canHitEnvi;

	// Token: 0x04000575 RID: 1397
	private int hitsCounter;
}

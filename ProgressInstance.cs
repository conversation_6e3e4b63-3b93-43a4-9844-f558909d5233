﻿using System;
using UnityEngine;

// Token: 0x020000F0 RID: 240
[Serializable]
public class ProgressInstance
{
	// Token: 0x04000AC6 RID: 2758
	public int xpToUnlock;

	// Token: 0x04000AC7 RID: 2759
	public CosmeticInstance cosmetic;

	// Token: 0x04000AC8 RID: 2760
	public string[] maps;

	// Token: 0x04000AC9 RID: 2761
	public bool unlocked;

	// Token: 0x04000ACA RID: 2762
	public bool dlcExlusive;

	// Token: 0x04000ACB RID: 2763
	[HideInInspector]
	public int index;
}

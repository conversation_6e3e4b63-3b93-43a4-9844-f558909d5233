﻿using System;
using FishNet;
using FishNet.Connection;
using FishNet.Managing;
using FishNet.Object;
using FishNet.Object.Delegating;
using FishNet.Serializing;
using FishNet.Serializing.Generated;
using FishNet.Transporting;
using UnityEngine;

// Token: 0x020000AF RID: 175
public class Taser : Weapon
{
	// Token: 0x060009EC RID: 2540 RVA: 0x00049304 File Offset: 0x00047504
	private void Update()
	{
		base.WeaponUpdate();
		if (this.fireTimer > 0f)
		{
			this.fireTimer -= Time.deltaTime;
		}
		if (base.gameObject.layer == 7)
		{
			return;
		}
		if ((this.invertFire ? this.fire2 : this.fire1).ReadValue<float>() > 0.1f && this.inRightHand && base.SyncAccessor_currentAmmo > 0)
		{
			this.Fire();
		}
		if ((this.invertFire ? this.fire1 : this.fire2).ReadValue<float>() > 0.1f && this.inLeftHand && base.SyncAccessor_currentAmmo > 0)
		{
			this.Fire();
		}
		if (this.fireTimer < 0f && this.fireTimer > -1f)
		{
			this.fireTimer = -2f;
			this.chargingAudio.Stop();
			Material[] array = new Material[] { this.readyMat };
			this.light.materials = array;
			this.SetVfxActive(true);
		}
	}

	// Token: 0x060009ED RID: 2541 RVA: 0x0004940A File Offset: 0x0004760A
	[ServerRpc(RunLocally = true)]
	private void SetVfxActive(bool active)
	{
		this.RpcWriter___Server_SetVfxActive_1140765316(active);
		this.RpcLogic___SetVfxActive_1140765316(active);
	}

	// Token: 0x060009EE RID: 2542 RVA: 0x00049420 File Offset: 0x00047620
	[ObserversRpc(RunLocally = true, ExcludeOwner = true)]
	private void SetVfxActiveObservers(bool active)
	{
		this.RpcWriter___Observers_SetVfxActiveObservers_1140765316(active);
		this.RpcLogic___SetVfxActiveObservers_1140765316(active);
	}

	// Token: 0x060009EF RID: 2543 RVA: 0x00049438 File Offset: 0x00047638
	private void Fire()
	{
		if (PauseManager.Instance.pause || !this.playerController.SyncAccessor_canMove)
		{
			return;
		}
		if (!this.playerController.IsOwner)
		{
			return;
		}
		if (this.fireTimer > 0f)
		{
			return;
		}
		if (!this.reloadWeapon && base.SyncAccessor_currentAmmo <= 0)
		{
			this.audio.PlayOneShot(this.nobulletClip);
			this.noAmmoClicks++;
		}
		this.chargingAudio.Play();
		Material[] array = new Material[] { this.chargingMat };
		this.light.materials = array;
		this.SetVfxActive(false);
		this.ShootServer(this.damage, this.cam.transform.position, this.cam.transform.forward);
		this.fireTimer = this.timeBetweenFire;
	}

	// Token: 0x060009F0 RID: 2544 RVA: 0x00049514 File Offset: 0x00047714
	private void ShootServer(float damageToGive, Vector3 position, Vector3 direction)
	{
		this.enemyHealth = null;
		this.ShootServerEffect();
		foreach (Collider collider in Physics.OverlapBox(position + direction * (this.boxdimensions.z / 2f), this.boxdimensions, Quaternion.LookRotation(direction), this.playerLayer))
		{
			if (collider.GetComponentInParent<PlayerHealth>() != null)
			{
				this.enemyHealth = collider.GetComponentInParent<PlayerHealth>();
				Transform transform = collider.transform;
				break;
			}
		}
		if (this.enemyHealth != null)
		{
			Settings.Instance.taserShots += 1f;
			if (this.enemyHealth.gameObject == base.transform.root.gameObject)
			{
				return;
			}
			this.TaserEnemy(this.enemyHealth);
		}
	}

	// Token: 0x060009F1 RID: 2545 RVA: 0x000495F0 File Offset: 0x000477F0
	[ServerRpc]
	private void TaserEnemy(PlayerHealth enemyHealth)
	{
		this.RpcWriter___Server_TaserEnemy_1722911636(enemyHealth);
	}

	// Token: 0x060009F2 RID: 2546 RVA: 0x000495FC File Offset: 0x000477FC
	[TargetRpc]
	private void TaserEnemyTarget(NetworkConnection conn, PlayerHealth enemyHealth)
	{
		this.RpcWriter___Target_TaserEnemyTarget_4033860311(conn, enemyHealth);
	}

	// Token: 0x060009F3 RID: 2547 RVA: 0x0004960C File Offset: 0x0004780C
	[ServerRpc(RunLocally = true)]
	private void ShootServerEffect()
	{
		this.RpcWriter___Server_ShootServerEffect_2166136261();
		this.RpcLogic___ShootServerEffect_2166136261();
	}

	// Token: 0x060009F4 RID: 2548 RVA: 0x0004961C File Offset: 0x0004781C
	[ObserversRpc(RunLocally = true, ExcludeOwner = true)]
	private void ShootObserversEffect()
	{
		this.RpcWriter___Observers_ShootObserversEffect_2166136261();
		this.RpcLogic___ShootObserversEffect_2166136261();
	}

	// Token: 0x060009F5 RID: 2549 RVA: 0x00049635 File Offset: 0x00047835
	[ServerRpc(RunLocally = true)]
	private void SpawnBulletTrailServer(Vector3 hitPoint)
	{
		this.RpcWriter___Server_SpawnBulletTrailServer_4276783012(hitPoint);
		this.RpcLogic___SpawnBulletTrailServer_4276783012(hitPoint);
	}

	// Token: 0x060009F6 RID: 2550 RVA: 0x0004964C File Offset: 0x0004784C
	[ObserversRpc(RunLocally = true, ExcludeOwner = true)]
	private void SpawnBulletTrail(Vector3 hitPoint)
	{
		this.RpcWriter___Observers_SpawnBulletTrail_4276783012(hitPoint);
		this.RpcLogic___SpawnBulletTrail_4276783012(hitPoint);
	}

	// Token: 0x060009F7 RID: 2551 RVA: 0x0004966D File Offset: 0x0004786D
	[ServerRpc(RunLocally = true)]
	private void SpawnVFXServer(int index, Vector3 hitPoint, Vector3 hitNormal)
	{
		this.RpcWriter___Server_SpawnVFXServer_2552092910(index, hitPoint, hitNormal);
		this.RpcLogic___SpawnVFXServer_2552092910(index, hitPoint, hitNormal);
	}

	// Token: 0x060009F8 RID: 2552 RVA: 0x00049694 File Offset: 0x00047894
	[ObserversRpc(RunLocally = true, ExcludeOwner = true)]
	private void SpawnVFX(int index, Vector3 hitPoint, Vector3 hitNormal)
	{
		this.RpcWriter___Observers_SpawnVFX_2552092910(index, hitPoint, hitNormal);
		this.RpcLogic___SpawnVFX_2552092910(index, hitPoint, hitNormal);
	}

	// Token: 0x060009FA RID: 2554 RVA: 0x000496C8 File Offset: 0x000478C8
	public override void NetworkInitialize___Early()
	{
		if (this.NetworkInitializeEarly_Taser_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeEarly_Taser_Assembly-CSharp.dll = true;
		base.NetworkInitialize___Early();
		base.RegisterServerRpc(15U, new ServerRpcDelegate(this.RpcReader___Server_SetVfxActive_1140765316));
		base.RegisterObserversRpc(16U, new ClientRpcDelegate(this.RpcReader___Observers_SetVfxActiveObservers_1140765316));
		base.RegisterServerRpc(17U, new ServerRpcDelegate(this.RpcReader___Server_TaserEnemy_1722911636));
		base.RegisterTargetRpc(18U, new ClientRpcDelegate(this.RpcReader___Target_TaserEnemyTarget_4033860311));
		base.RegisterServerRpc(19U, new ServerRpcDelegate(this.RpcReader___Server_ShootServerEffect_2166136261));
		base.RegisterObserversRpc(20U, new ClientRpcDelegate(this.RpcReader___Observers_ShootObserversEffect_2166136261));
		base.RegisterServerRpc(21U, new ServerRpcDelegate(this.RpcReader___Server_SpawnBulletTrailServer_4276783012));
		base.RegisterObserversRpc(22U, new ClientRpcDelegate(this.RpcReader___Observers_SpawnBulletTrail_4276783012));
		base.RegisterServerRpc(23U, new ServerRpcDelegate(this.RpcReader___Server_SpawnVFXServer_2552092910));
		base.RegisterObserversRpc(24U, new ClientRpcDelegate(this.RpcReader___Observers_SpawnVFX_2552092910));
	}

	// Token: 0x060009FB RID: 2555 RVA: 0x000497D2 File Offset: 0x000479D2
	public override void NetworkInitialize__Late()
	{
		if (this.NetworkInitializeLate_Taser_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeLate_Taser_Assembly-CSharp.dll = true;
		base.NetworkInitialize__Late();
	}

	// Token: 0x060009FC RID: 2556 RVA: 0x000497EB File Offset: 0x000479EB
	public override void NetworkInitializeIfDisabled()
	{
		this.NetworkInitialize___Early();
		this.NetworkInitialize__Late();
	}

	// Token: 0x060009FD RID: 2557 RVA: 0x000497FC File Offset: 0x000479FC
	private void RpcWriter___Server_SetVfxActive_1140765316(bool active)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteBoolean(active);
		base.SendServerRpc(15U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x060009FE RID: 2558 RVA: 0x000498FD File Offset: 0x00047AFD
	private void RpcLogic___SetVfxActive_1140765316(bool active)
	{
		this.SetVfxActiveObservers(active);
	}

	// Token: 0x060009FF RID: 2559 RVA: 0x00049908 File Offset: 0x00047B08
	private void RpcReader___Server_SetVfxActive_1140765316(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		bool flag = PooledReader0.ReadBoolean();
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___SetVfxActive_1140765316(flag);
	}

	// Token: 0x06000A00 RID: 2560 RVA: 0x00049958 File Offset: 0x00047B58
	private void RpcWriter___Observers_SetVfxActiveObservers_1140765316(bool active)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteBoolean(active);
		base.SendObserversRpc(16U, writer, channel, DataOrderType.Default, false, false, true);
		writer.Store();
	}

	// Token: 0x06000A01 RID: 2561 RVA: 0x00049A0E File Offset: 0x00047C0E
	private void RpcLogic___SetVfxActiveObservers_1140765316(bool active)
	{
		this.readyVfx.SetActive(active);
	}

	// Token: 0x06000A02 RID: 2562 RVA: 0x00049A1C File Offset: 0x00047C1C
	private void RpcReader___Observers_SetVfxActiveObservers_1140765316(PooledReader PooledReader0, Channel channel)
	{
		bool flag = PooledReader0.ReadBoolean();
		if (!base.IsClient)
		{
			return;
		}
		if (base.IsHost)
		{
			return;
		}
		this.RpcLogic___SetVfxActiveObservers_1140765316(flag);
	}

	// Token: 0x06000A03 RID: 2563 RVA: 0x00049A58 File Offset: 0x00047C58
	private void RpcWriter___Server_TaserEnemy_1722911636(PlayerHealth enemyHealth)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.Write___PlayerHealthFishNet.Serializing.Generated(enemyHealth);
		base.SendServerRpc(17U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000A04 RID: 2564 RVA: 0x00049B59 File Offset: 0x00047D59
	private void RpcLogic___TaserEnemy_1722911636(PlayerHealth enemyHealth)
	{
		enemyHealth.controller.sync___set_value_canMove(false, true);
		this.TaserEnemyTarget(enemyHealth.SyncAccessor_playerValues.SyncAccessor_playerClient.transform.GetComponent<NetworkObject>().Owner, enemyHealth);
	}

	// Token: 0x06000A05 RID: 2565 RVA: 0x00049B8C File Offset: 0x00047D8C
	private void RpcReader___Server_TaserEnemy_1722911636(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		PlayerHealth playerHealth = global::FishNet.Serializing.Generated.GeneratedReaders___Internal.Read___PlayerHealthFishNet.Serializing.Generateds(PooledReader0);
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		this.RpcLogic___TaserEnemy_1722911636(playerHealth);
	}

	// Token: 0x06000A06 RID: 2566 RVA: 0x00049BD0 File Offset: 0x00047DD0
	private void RpcWriter___Target_TaserEnemyTarget_4033860311(NetworkConnection conn, PlayerHealth enemyHealth)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.Write___PlayerHealthFishNet.Serializing.Generated(enemyHealth);
		base.SendTargetRpc(18U, writer, channel, DataOrderType.Default, conn, false, true);
		writer.Store();
	}

	// Token: 0x06000A07 RID: 2567 RVA: 0x00049C85 File Offset: 0x00047E85
	private void RpcLogic___TaserEnemyTarget_4033860311(NetworkConnection conn, PlayerHealth enemyHealth)
	{
		enemyHealth.StartCoroutine(enemyHealth.UnfreezePlayer(this.stunTime));
	}

	// Token: 0x06000A08 RID: 2568 RVA: 0x00049C9C File Offset: 0x00047E9C
	private void RpcReader___Target_TaserEnemyTarget_4033860311(PooledReader PooledReader0, Channel channel)
	{
		PlayerHealth playerHealth = global::FishNet.Serializing.Generated.GeneratedReaders___Internal.Read___PlayerHealthFishNet.Serializing.Generateds(PooledReader0);
		if (!base.IsClient)
		{
			return;
		}
		this.RpcLogic___TaserEnemyTarget_4033860311(base.LocalConnection, playerHealth);
	}

	// Token: 0x06000A09 RID: 2569 RVA: 0x00049CD4 File Offset: 0x00047ED4
	private void RpcWriter___Server_ShootServerEffect_2166136261()
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendServerRpc(19U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000A0A RID: 2570 RVA: 0x00049DC8 File Offset: 0x00047FC8
	private void RpcLogic___ShootServerEffect_2166136261()
	{
		this.ShootObserversEffect();
	}

	// Token: 0x06000A0B RID: 2571 RVA: 0x00049DD0 File Offset: 0x00047FD0
	private void RpcReader___Server_ShootServerEffect_2166136261(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___ShootServerEffect_2166136261();
	}

	// Token: 0x06000A0C RID: 2572 RVA: 0x00049E10 File Offset: 0x00048010
	private void RpcWriter___Observers_ShootObserversEffect_2166136261()
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendObserversRpc(20U, writer, channel, DataOrderType.Default, false, false, true);
		writer.Store();
	}

	// Token: 0x06000A0D RID: 2573 RVA: 0x00049EBC File Offset: 0x000480BC
	private void RpcLogic___ShootObserversEffect_2166136261()
	{
		if (Settings.Instance.reduceVFX)
		{
			return;
		}
		this.audio.PlayOneShot(this.fireClip);
		global::UnityEngine.Object.Instantiate<GameObject>(this.muzzleFlash, this.muzzleFlashPoint.position, this.shootPoint.rotation, this.behaviour.vfxAttachedOnGun ? base.transform : null).GetComponent<ParticleSystem>().Play();
	}

	// Token: 0x06000A0E RID: 2574 RVA: 0x00049F28 File Offset: 0x00048128
	private void RpcReader___Observers_ShootObserversEffect_2166136261(PooledReader PooledReader0, Channel channel)
	{
		if (!base.IsClient)
		{
			return;
		}
		if (base.IsHost)
		{
			return;
		}
		this.RpcLogic___ShootObserversEffect_2166136261();
	}

	// Token: 0x06000A0F RID: 2575 RVA: 0x00049F54 File Offset: 0x00048154
	private void RpcWriter___Server_SpawnBulletTrailServer_4276783012(Vector3 hitPoint)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteVector3(hitPoint);
		base.SendServerRpc(21U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000A10 RID: 2576 RVA: 0x0004A055 File Offset: 0x00048255
	private void RpcLogic___SpawnBulletTrailServer_4276783012(Vector3 hitPoint)
	{
		this.SpawnBulletTrail(hitPoint);
	}

	// Token: 0x06000A11 RID: 2577 RVA: 0x0004A060 File Offset: 0x00048260
	private void RpcReader___Server_SpawnBulletTrailServer_4276783012(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		Vector3 vector = PooledReader0.ReadVector3();
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___SpawnBulletTrailServer_4276783012(vector);
	}

	// Token: 0x06000A12 RID: 2578 RVA: 0x0004A0B0 File Offset: 0x000482B0
	private void RpcWriter___Observers_SpawnBulletTrail_4276783012(Vector3 hitPoint)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteVector3(hitPoint);
		base.SendObserversRpc(22U, writer, channel, DataOrderType.Default, false, false, true);
		writer.Store();
	}

	// Token: 0x06000A13 RID: 2579 RVA: 0x0004A168 File Offset: 0x00048368
	private void RpcLogic___SpawnBulletTrail_4276783012(Vector3 hitPoint)
	{
		GameObject gameObject = global::UnityEngine.Object.Instantiate<GameObject>(this.bulletTrailLocal.gameObject, this.shootPoint.position, Quaternion.identity);
		LineRenderer component = gameObject.GetComponent<LineRenderer>();
		component.SetPosition(0, this.shootPoint.position);
		component.SetPosition(1, hitPoint);
		global::UnityEngine.Object.Destroy(gameObject, 0.4f);
	}

	// Token: 0x06000A14 RID: 2580 RVA: 0x0004A1C0 File Offset: 0x000483C0
	private void RpcReader___Observers_SpawnBulletTrail_4276783012(PooledReader PooledReader0, Channel channel)
	{
		Vector3 vector = PooledReader0.ReadVector3();
		if (!base.IsClient)
		{
			return;
		}
		if (base.IsHost)
		{
			return;
		}
		this.RpcLogic___SpawnBulletTrail_4276783012(vector);
	}

	// Token: 0x06000A15 RID: 2581 RVA: 0x0004A1FC File Offset: 0x000483FC
	private void RpcWriter___Server_SpawnVFXServer_2552092910(int index, Vector3 hitPoint, Vector3 hitNormal)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteInt32(index, AutoPackType.Packed);
		writer.WriteVector3(hitPoint);
		writer.WriteVector3(hitNormal);
		base.SendServerRpc(23U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000A16 RID: 2582 RVA: 0x0004A31C File Offset: 0x0004851C
	private void RpcLogic___SpawnVFXServer_2552092910(int index, Vector3 hitPoint, Vector3 hitNormal)
	{
		this.SpawnVFX(index, hitPoint, hitNormal);
	}

	// Token: 0x06000A17 RID: 2583 RVA: 0x0004A328 File Offset: 0x00048528
	private void RpcReader___Server_SpawnVFXServer_2552092910(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		int num = PooledReader0.ReadInt32(AutoPackType.Packed);
		Vector3 vector = PooledReader0.ReadVector3();
		Vector3 vector2 = PooledReader0.ReadVector3();
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___SpawnVFXServer_2552092910(num, vector, vector2);
	}

	// Token: 0x06000A18 RID: 2584 RVA: 0x0004A3A0 File Offset: 0x000485A0
	private void RpcWriter___Observers_SpawnVFX_2552092910(int index, Vector3 hitPoint, Vector3 hitNormal)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteInt32(index, AutoPackType.Packed);
		writer.WriteVector3(hitPoint);
		writer.WriteVector3(hitNormal);
		base.SendObserversRpc(24U, writer, channel, DataOrderType.Default, false, false, true);
		writer.Store();
	}

	// Token: 0x06000A19 RID: 2585 RVA: 0x0004A478 File Offset: 0x00048678
	private void RpcLogic___SpawnVFX_2552092910(int index, Vector3 hitPoint, Vector3 hitNormal)
	{
		if (Settings.Instance.reduceVFX)
		{
			return;
		}
		if (index == 0)
		{
			global::UnityEngine.Object.Instantiate<GameObject>(this.genericImpact, hitPoint, Quaternion.LookRotation(hitNormal));
		}
		if (index == 1)
		{
			global::UnityEngine.Object.Instantiate<GameObject>(this.bulletHole, hitPoint, Quaternion.LookRotation(hitNormal));
		}
		if (index == 2)
		{
			global::UnityEngine.Object.Instantiate<GameObject>(this.bodyImpact, hitPoint, Quaternion.identity);
		}
	}

	// Token: 0x06000A1A RID: 2586 RVA: 0x0004A4D8 File Offset: 0x000486D8
	private void RpcReader___Observers_SpawnVFX_2552092910(PooledReader PooledReader0, Channel channel)
	{
		int num = PooledReader0.ReadInt32(AutoPackType.Packed);
		Vector3 vector = PooledReader0.ReadVector3();
		Vector3 vector2 = PooledReader0.ReadVector3();
		if (!base.IsClient)
		{
			return;
		}
		if (base.IsHost)
		{
			return;
		}
		this.RpcLogic___SpawnVFX_2552092910(num, vector, vector2);
	}

	// Token: 0x06000A1B RID: 2587 RVA: 0x0004A53A File Offset: 0x0004873A
	public override void Awake()
	{
		this.NetworkInitialize___Early();
		base.Awake();
		this.NetworkInitialize__Late();
	}

	// Token: 0x06000A1C RID: 2588 RVA: 0x000023D6 File Offset: 0x000005D6
	public override void Awake___UserLogic()
	{
	}

	// Token: 0x040008E6 RID: 2278
	[Header("Weapon Specials")]
	[SerializeField]
	private float chargeTime;

	// Token: 0x040008E7 RID: 2279
	[SerializeField]
	private AudioClip reloadClip;

	// Token: 0x040008E8 RID: 2280
	[SerializeField]
	private float stunTime;

	// Token: 0x040008E9 RID: 2281
	[SerializeField]
	private Vector3 boxdimensions;

	// Token: 0x040008EA RID: 2282
	[Space]
	[SerializeField]
	private MeshRenderer light;

	// Token: 0x040008EB RID: 2283
	[SerializeField]
	private Material chargingMat;

	// Token: 0x040008EC RID: 2284
	[SerializeField]
	private Material readyMat;

	// Token: 0x040008ED RID: 2285
	[SerializeField]
	private AudioSource chargingAudio;

	// Token: 0x040008EE RID: 2286
	[SerializeField]
	private GameObject readyVfx;

	// Token: 0x040008EF RID: 2287
	private float fireTimer;

	// Token: 0x040008F0 RID: 2288
	private PlayerHealth enemyHealth;

	// Token: 0x040008F1 RID: 2289
	private bool NetworkInitializeEarly_Taser_Assembly-CSharp.dll;

	// Token: 0x040008F2 RID: 2290
	private bool NetworkInitializeLate_Taser_Assembly-CSharp.dll;
}

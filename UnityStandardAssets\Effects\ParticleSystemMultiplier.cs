﻿using System;
using UnityEngine;

namespace UnityStandardAssets.Effects
{
	// Token: 0x02000195 RID: 405
	public class ParticleSystemMultiplier : MonoBehaviour
	{
		// Token: 0x06001032 RID: 4146 RVA: 0x000694F0 File Offset: 0x000676F0
		private void Start()
		{
			foreach (ParticleSystem particleSystem in base.GetComponentsInChildren<ParticleSystem>())
			{
				ParticleSystem.MainModule main = particleSystem.main;
				main.startSizeMultiplier *= this.multiplier;
				main.startSpeedMultiplier *= this.multiplier;
				main.startLifetimeMultiplier *= Mathf.Lerp(this.multiplier, 1f, 0.5f);
				particleSystem.Clear();
				particleSystem.Play();
			}
		}

		// Token: 0x04000EFE RID: 3838
		public float multiplier = 1f;
	}
}

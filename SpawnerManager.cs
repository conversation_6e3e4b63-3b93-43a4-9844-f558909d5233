﻿using System;
using System.Collections.Generic;
using FishNet.Object;
using FishNet.Object.Delegating;
using FishNet.Object.Synchronizing;
using FishNet.Serializing;
using FishNet.Transporting;
using UnityEngine;

// Token: 0x02000113 RID: 275
public class SpawnerManager : NetworkBehaviour
{
	// Token: 0x06000D9E RID: 3486 RVA: 0x0005C3B0 File Offset: 0x0005A5B0
	public static void PopulateAllWeapons()
	{
		SpawnerManager.AllWeapons = Resources.LoadAll<GameObject>(SpawnerManager.WeaponsPath);
		SpawnerManager.NameToWeaponDict = new Dictionary<string, GameObject>();
		foreach (GameObject gameObject in SpawnerManager.AllWeapons)
		{
			SpawnerManager.NameToWeaponDict[gameObject.name] = gameObject;
		}
	}

	// Token: 0x06000D9F RID: 3487 RVA: 0x0005C3FF File Offset: 0x0005A5FF
	public virtual void Awake()
	{
		this.NetworkInitialize___Early();
		this.Awake___UserLogic();
		this.NetworkInitialize__Late();
	}

	// Token: 0x06000DA0 RID: 3488 RVA: 0x0005C414 File Offset: 0x0005A614
	public void SyncRandomSettings(IEnumerable<WeaponData> weaponData, uint totalWeight)
	{
		this.spawnChances.Clear();
		foreach (WeaponData weaponData2 in weaponData)
		{
			if (weaponData2.IsSpawnable)
			{
				this.spawnChances.Add(weaponData2);
			}
		}
		this.sync___set_value_totalSpawnWeight(totalWeight, true);
	}

	// Token: 0x06000DA1 RID: 3489 RVA: 0x0005C47C File Offset: 0x0005A67C
	public GameObject GetRandomSpawnableWeapon()
	{
		if (this.spawnChances == null || this.spawnChances.Count == 0)
		{
			Spawner.UpdateSpawnableWeapons();
		}
		if (this.spawnChances.Count == 0)
		{
			Debug.LogWarning("No spawnable weapons available.");
			return null;
		}
		uint num = (uint)global::UnityEngine.Random.Range(0f, this.SyncAccessor_totalSpawnWeight);
		uint num2 = 0U;
		foreach (WeaponData weaponData in this.spawnChances)
		{
			num2 += weaponData.SpawnChance;
			if (num < num2)
			{
				return SpawnerManager.NameToWeaponDict[weaponData.WeaponName];
			}
		}
		Debug.LogError("Failed to find a weapon for the random value: " + num.ToString());
		return null;
	}

	// Token: 0x06000DA4 RID: 3492 RVA: 0x0005C56C File Offset: 0x0005A76C
	public virtual void NetworkInitialize___Early()
	{
		if (this.NetworkInitializeEarly_SpawnerManager_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeEarly_SpawnerManager_Assembly-CSharp.dll = true;
		this.syncVar___totalSpawnWeight = new SyncVar<uint>(this, 2U, WritePermission.ServerOnly, ReadPermission.Observers, -1f, Channel.Reliable, this.totalSpawnWeight);
		this.spawnChances.InitializeInstance(this, 1U, WritePermission.ServerOnly, ReadPermission.Observers, -1f, Channel.Reliable, true);
		this.syncVar___randomiseWeapons = new SyncVar<bool>(this, 0U, WritePermission.ServerOnly, ReadPermission.Observers, -1f, Channel.Reliable, this.randomiseWeapons);
		base.RegisterSyncVarRead(new SyncVarReadDelegate(this.ReadSyncVar___SpawnerManager));
	}

	// Token: 0x06000DA5 RID: 3493 RVA: 0x0005C618 File Offset: 0x0005A818
	public virtual void NetworkInitialize__Late()
	{
		if (this.NetworkInitializeLate_SpawnerManager_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeLate_SpawnerManager_Assembly-CSharp.dll = true;
		this.syncVar___totalSpawnWeight.SetRegistered();
		this.syncVar___randomiseWeapons.SetRegistered();
		this.spawnChances.SetRegistered();
	}

	// Token: 0x06000DA6 RID: 3494 RVA: 0x0005C64C File Offset: 0x0005A84C
	public override void NetworkInitializeIfDisabled()
	{
		this.NetworkInitialize___Early();
		this.NetworkInitialize__Late();
	}

	// Token: 0x170000CB RID: 203
	// (get) Token: 0x06000DA7 RID: 3495 RVA: 0x0005C65A File Offset: 0x0005A85A
	// (set) Token: 0x06000DA8 RID: 3496 RVA: 0x0005C662 File Offset: 0x0005A862
	public bool SyncAccessor_randomiseWeapons
	{
		get
		{
			return this.randomiseWeapons;
		}
		set
		{
			if (value || !base.IsServer)
			{
				this.randomiseWeapons = value;
			}
			this.syncVar___randomiseWeapons.SetValue(value, value);
		}
	}

	// Token: 0x06000DA9 RID: 3497 RVA: 0x0005C698 File Offset: 0x0005A898
	public virtual bool ReadSyncVar___SpawnerManager(PooledReader PooledReader0, uint UInt321, bool Boolean2)
	{
		if (UInt321 == 2U)
		{
			if (PooledReader0 == null)
			{
				this.sync___set_value_totalSpawnWeight(this.syncVar___totalSpawnWeight.GetValue(true), true);
				return true;
			}
			uint num = PooledReader0.ReadUInt32(AutoPackType.Packed);
			this.sync___set_value_totalSpawnWeight(num, Boolean2);
			return true;
		}
		else
		{
			if (UInt321 != 0U)
			{
				return false;
			}
			if (PooledReader0 == null)
			{
				this.sync___set_value_randomiseWeapons(this.syncVar___randomiseWeapons.GetValue(true), true);
				return true;
			}
			bool flag = PooledReader0.ReadBoolean();
			this.sync___set_value_randomiseWeapons(flag, Boolean2);
			return true;
		}
	}

	// Token: 0x170000CC RID: 204
	// (get) Token: 0x06000DAA RID: 3498 RVA: 0x0005C733 File Offset: 0x0005A933
	// (set) Token: 0x06000DAB RID: 3499 RVA: 0x0005C73B File Offset: 0x0005A93B
	public uint SyncAccessor_totalSpawnWeight
	{
		get
		{
			return this.totalSpawnWeight;
		}
		set
		{
			if (value || !base.IsServer)
			{
				this.totalSpawnWeight = value;
			}
			this.syncVar___totalSpawnWeight.SetValue(value, value);
		}
	}

	// Token: 0x06000DAC RID: 3500 RVA: 0x0005C770 File Offset: 0x0005A970
	public virtual void Awake___UserLogic()
	{
		if (SpawnerManager.Instance == null)
		{
			SpawnerManager.Instance = this;
		}
	}

	// Token: 0x04000C0E RID: 3086
	public static GameObject[] AllWeapons;

	// Token: 0x04000C0F RID: 3087
	public static Dictionary<string, GameObject> NameToWeaponDict;

	// Token: 0x04000C10 RID: 3088
	[SyncVar]
	public bool randomiseWeapons;

	// Token: 0x04000C11 RID: 3089
	[SyncObject]
	public readonly SyncList<WeaponData> spawnChances = new SyncList<WeaponData>();

	// Token: 0x04000C12 RID: 3090
	[SyncVar]
	public uint totalSpawnWeight;

	// Token: 0x04000C13 RID: 3091
	private static readonly string WeaponsPath = "RandomWeapons";

	// Token: 0x04000C14 RID: 3092
	public static SpawnerManager Instance;

	// Token: 0x04000C15 RID: 3093
	public SyncVar<bool> syncVar___randomiseWeapons;

	// Token: 0x04000C16 RID: 3094
	public SyncVar<uint> syncVar___totalSpawnWeight;

	// Token: 0x04000C17 RID: 3095
	private bool NetworkInitializeEarly_SpawnerManager_Assembly-CSharp.dll;

	// Token: 0x04000C18 RID: 3096
	private bool NetworkInitializeLate_SpawnerManager_Assembly-CSharp.dll;
}

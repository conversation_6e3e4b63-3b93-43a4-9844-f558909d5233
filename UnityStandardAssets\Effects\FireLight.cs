﻿using System;
using UnityEngine;

namespace UnityStandardAssets.Effects
{
	// Token: 0x02000193 RID: 403
	public class FireLight : MonoBehaviour
	{
		// Token: 0x0600102C RID: 4140 RVA: 0x0006929A File Offset: 0x0006749A
		private void Start()
		{
			this.m_Rnd = global::UnityEngine.Random.value * 100f;
			this.m_Light = base.GetComponent<Light>();
		}

		// Token: 0x0600102D RID: 4141 RVA: 0x000692BC File Offset: 0x000674BC
		private void Update()
		{
			if (this.m_Burning)
			{
				this.m_Light.intensity = 2f * Mathf.PerlinNoise(this.m_Rnd + Time.time, this.m_Rnd + 1f + Time.time * 1f);
				float num = Mathf.PerlinNoise(this.m_Rnd + 0f + Time.time * 2f, this.m_Rnd + 1f + Time.time * 2f) - 0.5f;
				float num2 = Mathf.<PERSON>lin<PERSON>oise(this.m_Rnd + 2f + Time.time * 2f, this.m_Rnd + 3f + Time.time * 2f) - 0.5f;
				float num3 = Mathf.PerlinNoise(this.m_Rnd + 4f + Time.time * 2f, this.m_Rnd + 5f + Time.time * 2f) - 0.5f;
				base.transform.localPosition = Vector3.up + new Vector3(num, num2, num3) * 1f;
			}
		}

		// Token: 0x0600102E RID: 4142 RVA: 0x000693E9 File Offset: 0x000675E9
		public void Extinguish()
		{
			this.m_Burning = false;
			this.m_Light.enabled = false;
		}

		// Token: 0x04000EF5 RID: 3829
		private float m_Rnd;

		// Token: 0x04000EF6 RID: 3830
		private bool m_Burning = true;

		// Token: 0x04000EF7 RID: 3831
		private Light m_Light;
	}
}

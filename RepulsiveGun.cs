﻿using System;
using System.Collections;
using FishNet;
using FishNet.Connection;
using FishNet.Managing;
using FishNet.Object;
using FishNet.Object.Delegating;
using FishNet.Serializing;
using FishNet.Serializing.Generated;
using FishNet.Transporting;
using UnityEngine;

// Token: 0x020000A7 RID: 167
public class RepulsiveGun : Weapon
{
	// Token: 0x0600092D RID: 2349 RVA: 0x00043183 File Offset: 0x00041383
	private void Start()
	{
		this.chargedBullets = (float)this.ammoCharge;
		base.sync___set_value_currentAmmo(base.SyncAccessor_currentAmmo - this.ammoCharge, true);
	}

	// Token: 0x0600092E RID: 2350 RVA: 0x000431A8 File Offset: 0x000413A8
	private void Update()
	{
		base.WeaponUpdate();
		if (this.fireTimer > 0f)
		{
			this.fireTimer -= Time.deltaTime;
		}
		if (base.gameObject.layer == 7)
		{
			return;
		}
		if (!this.onePressShoot)
		{
			if ((this.invertFire ? this.fire2 : this.fire1).ReadValue<float>() > 0.1f && this.inRightHand && (this.reloadWeapon || base.SyncAccessor_currentAmmo > 0))
			{
				this.Fire();
			}
			if ((this.invertFire ? this.fire1 : this.fire2).ReadValue<float>() > 0.1f && this.inLeftHand && (this.reloadWeapon || base.SyncAccessor_currentAmmo > 0))
			{
				this.Fire();
			}
			if ((this.invertFire ? this.fire2 : this.fire1).ReadValue<float>() > 0.1f && this.inRightHand && this.akAnim && base.SyncAccessor_currentAmmo > 0)
			{
				this.camAnimScript.rotateBack = false;
			}
			else if ((this.invertFire ? this.fire1 : this.fire2).ReadValue<float>() > 0.1f && this.inLeftHand && this.akAnim && base.SyncAccessor_currentAmmo > 0)
			{
				this.camAnimScript.rotateBack = false;
			}
			else
			{
				this.camAnimScript.rotateBack = true;
			}
			if (!this.reloadWeapon && base.SyncAccessor_currentAmmo <= 0)
			{
				if ((this.invertFire ? this.fire2 : this.fire1).ReadValue<float>() < 0.1f && this.inRightHand)
				{
					this.isClicked = true;
				}
				if ((this.invertFire ? this.fire1 : this.fire2).ReadValue<float>() < 0.1f && this.inLeftHand)
				{
					this.isClicked = true;
				}
			}
		}
		else
		{
			if ((this.invertFire ? this.fire2 : this.fire1).ReadValue<float>() < 0.1f && this.inRightHand)
			{
				this.isClicked = true;
			}
			if ((this.invertFire ? this.fire1 : this.fire2).ReadValue<float>() < 0.1f && this.inLeftHand)
			{
				this.isClicked = true;
			}
			this.camAnimScript.rotateBack = true;
		}
		if (this.isClicked)
		{
			if ((this.invertFire ? this.fire2 : this.fire1).ReadValue<float>() > 0.1f && this.inRightHand)
			{
				this.Fire();
				this.isClicked = false;
			}
			if ((this.invertFire ? this.fire1 : this.fire2).ReadValue<float>() > 0.1f && this.inLeftHand)
			{
				this.Fire();
				this.isClicked = false;
			}
		}
		if (this.shot && this.timeBetweenFire < 0.2f && this.onePressShoot)
		{
			this.Fire();
		}
	}

	// Token: 0x0600092F RID: 2351 RVA: 0x000434A0 File Offset: 0x000416A0
	private void Fire()
	{
		if (PauseManager.Instance.pause || !this.playerController.SyncAccessor_canMove)
		{
			return;
		}
		if (!this.playerController.IsOwner)
		{
			return;
		}
		if (this.fireTimer > 0f)
		{
			this.shot = true;
			return;
		}
		this.shot = false;
		this.fireTimer = this.timeBetweenFire;
		if (!this.reloadWeapon && base.SyncAccessor_currentAmmo <= 0)
		{
			this.audio.PlayOneShot(this.nobulletClip);
			this.noAmmoClicks++;
		}
		if (!this.reloadWeapon && base.SyncAccessor_currentAmmo <= 0)
		{
			return;
		}
		if (this.reloadWeapon && this.isReloading)
		{
			return;
		}
		if (this.reloadWeapon && this.chargedBullets <= 0f && base.SyncAccessor_currentAmmo > 0)
		{
			base.StartCoroutine(this.Reload());
		}
		if (this.reloadWeapon && this.chargedBullets <= 0f && base.SyncAccessor_currentAmmo <= 0)
		{
			this.audio.PlayOneShot(this.nobulletClip);
		}
		if (this.reloadWeapon && this.chargedBullets <= 0f)
		{
			return;
		}
		this.ShootServer(this.damage, this.cam.transform.position, this.cam.transform.forward);
		if (this.revolverShake)
		{
			base.CameraRevolverAnimation();
		}
		else
		{
			base.CameraAnimation();
		}
		base.WeaponAnimation();
		if (this.playerKnockback != 0f)
		{
			this.playerController.AddForce(-this.cam.transform.forward, this.playerKnockback);
		}
	}

	// Token: 0x06000930 RID: 2352 RVA: 0x0004363C File Offset: 0x0004183C
	private void ShootServer(float damageToGive, Vector3 position, Vector3 direction)
	{
		this.enemyHealth = null;
		this.ShootServerEffect();
		foreach (Collider collider in Physics.OverlapBox(position + direction * (this.boxdimensions.z / 2f), this.boxdimensions, Quaternion.LookRotation(direction), this.playerLayer))
		{
			if (collider.GetComponentInParent<PlayerHealth>() != null)
			{
				this.enemyHealth = collider.GetComponentInParent<PlayerHealth>();
				Transform transform = collider.transform;
				break;
			}
		}
		if (this.enemyHealth != null)
		{
			if (this.enemyHealth.gameObject == base.transform.root.gameObject)
			{
				return;
			}
			this.BumpPlayerServer(direction + Vector3.up * 2f, this.repulseForce, this.enemyHealth);
			this.hitOK = true;
		}
	}

	// Token: 0x06000931 RID: 2353 RVA: 0x00043724 File Offset: 0x00041924
	[ServerRpc]
	private void BumpPlayerServer(Vector3 direction, float force, PlayerHealth ph)
	{
		this.RpcWriter___Server_BumpPlayerServer_1076951378(direction, force, ph);
	}

	// Token: 0x06000932 RID: 2354 RVA: 0x00043738 File Offset: 0x00041938
	[TargetRpc]
	private void BumpPlayer(NetworkConnection conn, PlayerHealth enemyHealth, float force, Vector3 direction)
	{
		this.RpcWriter___Target_BumpPlayer_2429708885(conn, enemyHealth, force, direction);
	}

	// Token: 0x06000933 RID: 2355 RVA: 0x00043750 File Offset: 0x00041950
	[ServerRpc(RunLocally = true)]
	private void RemoveAmmo()
	{
		this.RpcWriter___Server_RemoveAmmo_2166136261();
		this.RpcLogic___RemoveAmmo_2166136261();
	}

	// Token: 0x06000934 RID: 2356 RVA: 0x0004375E File Offset: 0x0004195E
	[ServerRpc(RunLocally = true)]
	private void GiveDamage(float damageToGive, PlayerHealth enemyHealth, string name)
	{
		this.RpcWriter___Server_GiveDamage_324487999(damageToGive, enemyHealth, name);
		this.RpcLogic___GiveDamage_324487999(damageToGive, enemyHealth, name);
	}

	// Token: 0x06000935 RID: 2357 RVA: 0x00043784 File Offset: 0x00041984
	[ServerRpc]
	private void KillServer(PlayerHealth enemyHealth)
	{
		this.RpcWriter___Server_KillServer_1722911636(enemyHealth);
	}

	// Token: 0x06000936 RID: 2358 RVA: 0x0004379B File Offset: 0x0004199B
	[TargetRpc]
	private void KillObserver(NetworkConnection conn, ClientInstance client, PlayerHealth enemyHealth)
	{
		this.RpcWriter___Target_KillObserver_123853379(conn, client, enemyHealth);
	}

	// Token: 0x06000937 RID: 2359 RVA: 0x000437AF File Offset: 0x000419AF
	[ObserversRpc]
	private void HitFeeback(PlayerHealth enemyHealth)
	{
		this.RpcWriter___Observers_HitFeeback_1722911636(enemyHealth);
	}

	// Token: 0x06000938 RID: 2360 RVA: 0x000437BB File Offset: 0x000419BB
	[ServerRpc(RunLocally = true)]
	private void ShootServerEffect()
	{
		this.RpcWriter___Server_ShootServerEffect_2166136261();
		this.RpcLogic___ShootServerEffect_2166136261();
	}

	// Token: 0x06000939 RID: 2361 RVA: 0x000437CC File Offset: 0x000419CC
	[ObserversRpc(RunLocally = true, ExcludeOwner = true)]
	private void ShootObserversEffect()
	{
		this.RpcWriter___Observers_ShootObserversEffect_2166136261();
		this.RpcLogic___ShootObserversEffect_2166136261();
	}

	// Token: 0x0600093A RID: 2362 RVA: 0x000437E5 File Offset: 0x000419E5
	[ServerRpc(RunLocally = true)]
	private void SpawnBulletTrailServer(Vector3 hitPoint)
	{
		this.RpcWriter___Server_SpawnBulletTrailServer_4276783012(hitPoint);
		this.RpcLogic___SpawnBulletTrailServer_4276783012(hitPoint);
	}

	// Token: 0x0600093B RID: 2363 RVA: 0x000437FC File Offset: 0x000419FC
	[ObserversRpc(RunLocally = true, ExcludeOwner = true)]
	private void SpawnBulletTrail(Vector3 hitPoint)
	{
		this.RpcWriter___Observers_SpawnBulletTrail_4276783012(hitPoint);
		this.RpcLogic___SpawnBulletTrail_4276783012(hitPoint);
	}

	// Token: 0x0600093C RID: 2364 RVA: 0x0004381D File Offset: 0x00041A1D
	[ServerRpc(RunLocally = true)]
	private void SpawnVFXServer(int index, Vector3 hitPoint, Vector3 hitNormal, string surface, Transform parent)
	{
		this.RpcWriter___Server_SpawnVFXServer_606331033(index, hitPoint, hitNormal, surface, parent);
		this.RpcLogic___SpawnVFXServer_606331033(index, hitPoint, hitNormal, surface, parent);
	}

	// Token: 0x0600093D RID: 2365 RVA: 0x00043854 File Offset: 0x00041A54
	[ObserversRpc(RunLocally = true, ExcludeOwner = true)]
	private void SpawnVFX(int index, Vector3 hitPoint, Vector3 hitNormal, string surface, Transform parent)
	{
		this.RpcWriter___Observers_SpawnVFX_606331033(index, hitPoint, hitNormal, surface, parent);
		this.RpcLogic___SpawnVFX_606331033(index, hitPoint, hitNormal, surface, parent);
	}

	// Token: 0x0600093E RID: 2366 RVA: 0x0002B771 File Offset: 0x00029971
	private void LocalSound(int index)
	{
		if (index == 0)
		{
			this.audio.PlayOneShot(this.headHitClip);
		}
		if (index == 1)
		{
			this.audio.PlayOneShot(this.bodyHitClip);
		}
		if (index == 2)
		{
			this.audio.PlayOneShot(this.deathClip);
		}
	}

	// Token: 0x0600093F RID: 2367 RVA: 0x00043895 File Offset: 0x00041A95
	private IEnumerator Reload()
	{
		this.isReloading = true;
		this.audio.PlayOneShot(this.reloadClip);
		if (this.animator != null)
		{
			this.animator.SetTrigger("Reload");
		}
		yield return new WaitForSeconds(this.reloadTime);
		if (base.SyncAccessor_currentAmmo - this.ammoCharge >= 0)
		{
			base.sync___set_value_currentAmmo(base.SyncAccessor_currentAmmo - this.ammoCharge, true);
			this.chargedBullets = (float)this.ammoCharge;
		}
		else
		{
			this.chargedBullets = (float)base.SyncAccessor_currentAmmo;
			base.sync___set_value_currentAmmo(0, true);
		}
		this.isReloading = false;
		yield break;
	}

	// Token: 0x06000940 RID: 2368 RVA: 0x000438A4 File Offset: 0x00041AA4
	private IEnumerator BurstFire()
	{
		int num2;
		for (int i = 0; i < this.bulletsAmount; i = num2 + 1)
		{
			float num = ((this.playerController.isSprinting || !this.playerController.isGrounded || !this.playerController.safeGrounded) ? Mathf.Lerp(this.maxSpread, this.minSpread, this.sprintAccuracy) : (this.playerController.isCrouching ? Mathf.Lerp(this.maxSpread, this.minSpread, this.standingAccuracy) : (this.playerController.isWalking ? Mathf.Lerp(this.maxSpread, this.minSpread, this.walkAccuracy) : Mathf.Lerp(this.maxSpread, this.minSpread, this.standingAccuracy))));
			this.spread = this.cam.transform.right * global::UnityEngine.Random.Range(num, -num) + this.cam.transform.up * global::UnityEngine.Random.Range(num, -num);
			this.ShootServer(this.damage, this.cam.transform.position, this.cam.transform.forward + this.spread);
			if (this.revolverShake)
			{
				base.CameraRevolverAnimation();
			}
			else
			{
				base.CameraAnimation();
			}
			base.WeaponAnimation();
			yield return new WaitForSeconds(this.timeBetweenBullets);
			num2 = i;
		}
		yield break;
	}

	// Token: 0x06000941 RID: 2369 RVA: 0x000438B3 File Offset: 0x00041AB3
	[ServerRpc]
	private void SupressionServer(Transform supp)
	{
		this.RpcWriter___Server_SupressionServer_3068987916(supp);
	}

	// Token: 0x06000942 RID: 2370 RVA: 0x000438BF File Offset: 0x00041ABF
	[ObserversRpc]
	private void SuppressionTarget(Transform supp)
	{
		this.RpcWriter___Observers_SuppressionTarget_3068987916(supp);
	}

	// Token: 0x06000944 RID: 2372 RVA: 0x000438E0 File Offset: 0x00041AE0
	public override void NetworkInitialize___Early()
	{
		if (this.NetworkInitializeEarly_RepulsiveGun_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeEarly_RepulsiveGun_Assembly-CSharp.dll = true;
		base.NetworkInitialize___Early();
		base.RegisterServerRpc(15U, new ServerRpcDelegate(this.RpcReader___Server_BumpPlayerServer_1076951378));
		base.RegisterTargetRpc(16U, new ClientRpcDelegate(this.RpcReader___Target_BumpPlayer_2429708885));
		base.RegisterServerRpc(17U, new ServerRpcDelegate(this.RpcReader___Server_RemoveAmmo_2166136261));
		base.RegisterServerRpc(18U, new ServerRpcDelegate(this.RpcReader___Server_GiveDamage_324487999));
		base.RegisterServerRpc(19U, new ServerRpcDelegate(this.RpcReader___Server_KillServer_1722911636));
		base.RegisterTargetRpc(20U, new ClientRpcDelegate(this.RpcReader___Target_KillObserver_123853379));
		base.RegisterObserversRpc(21U, new ClientRpcDelegate(this.RpcReader___Observers_HitFeeback_1722911636));
		base.RegisterServerRpc(22U, new ServerRpcDelegate(this.RpcReader___Server_ShootServerEffect_2166136261));
		base.RegisterObserversRpc(23U, new ClientRpcDelegate(this.RpcReader___Observers_ShootObserversEffect_2166136261));
		base.RegisterServerRpc(24U, new ServerRpcDelegate(this.RpcReader___Server_SpawnBulletTrailServer_4276783012));
		base.RegisterObserversRpc(25U, new ClientRpcDelegate(this.RpcReader___Observers_SpawnBulletTrail_4276783012));
		base.RegisterServerRpc(26U, new ServerRpcDelegate(this.RpcReader___Server_SpawnVFXServer_606331033));
		base.RegisterObserversRpc(27U, new ClientRpcDelegate(this.RpcReader___Observers_SpawnVFX_606331033));
		base.RegisterServerRpc(28U, new ServerRpcDelegate(this.RpcReader___Server_SupressionServer_3068987916));
		base.RegisterObserversRpc(29U, new ClientRpcDelegate(this.RpcReader___Observers_SuppressionTarget_3068987916));
	}

	// Token: 0x06000945 RID: 2373 RVA: 0x00043A5D File Offset: 0x00041C5D
	public override void NetworkInitialize__Late()
	{
		if (this.NetworkInitializeLate_RepulsiveGun_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeLate_RepulsiveGun_Assembly-CSharp.dll = true;
		base.NetworkInitialize__Late();
	}

	// Token: 0x06000946 RID: 2374 RVA: 0x00043A76 File Offset: 0x00041C76
	public override void NetworkInitializeIfDisabled()
	{
		this.NetworkInitialize___Early();
		this.NetworkInitialize__Late();
	}

	// Token: 0x06000947 RID: 2375 RVA: 0x00043A84 File Offset: 0x00041C84
	private void RpcWriter___Server_BumpPlayerServer_1076951378(Vector3 direction, float force, PlayerHealth ph)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteVector3(direction);
		writer.WriteSingle(force, AutoPackType.Unpacked);
		writer.Write___PlayerHealthFishNet.Serializing.Generated(ph);
		base.SendServerRpc(15U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000948 RID: 2376 RVA: 0x00043BA4 File Offset: 0x00041DA4
	private void RpcLogic___BumpPlayerServer_1076951378(Vector3 direction, float force, PlayerHealth ph)
	{
		this.BumpPlayer(ph.SyncAccessor_playerValues.SyncAccessor_playerClient.transform.GetComponent<NetworkObject>().Owner, ph, force, direction);
	}

	// Token: 0x06000949 RID: 2377 RVA: 0x00043BCC File Offset: 0x00041DCC
	private void RpcReader___Server_BumpPlayerServer_1076951378(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		Vector3 vector = PooledReader0.ReadVector3();
		float num = PooledReader0.ReadSingle(AutoPackType.Unpacked);
		PlayerHealth playerHealth = global::FishNet.Serializing.Generated.GeneratedReaders___Internal.Read___PlayerHealthFishNet.Serializing.Generateds(PooledReader0);
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		this.RpcLogic___BumpPlayerServer_1076951378(vector, num, playerHealth);
	}

	// Token: 0x0600094A RID: 2378 RVA: 0x00043C38 File Offset: 0x00041E38
	private void RpcWriter___Target_BumpPlayer_2429708885(NetworkConnection conn, PlayerHealth enemyHealth, float force, Vector3 direction)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.Write___PlayerHealthFishNet.Serializing.Generated(enemyHealth);
		writer.WriteSingle(force, AutoPackType.Unpacked);
		writer.WriteVector3(direction);
		base.SendTargetRpc(16U, writer, channel, DataOrderType.Default, conn, false, true);
		writer.Store();
	}

	// Token: 0x0600094B RID: 2379 RVA: 0x0003A61C File Offset: 0x0003881C
	private void RpcLogic___BumpPlayer_2429708885(NetworkConnection conn, PlayerHealth enemyHealth, float force, Vector3 direction)
	{
		enemyHealth.bounceDirection = direction;
		enemyHealth.bounceForce = force;
		enemyHealth.shouldBounce = true;
	}

	// Token: 0x0600094C RID: 2380 RVA: 0x00043D0C File Offset: 0x00041F0C
	private void RpcReader___Target_BumpPlayer_2429708885(PooledReader PooledReader0, Channel channel)
	{
		PlayerHealth playerHealth = global::FishNet.Serializing.Generated.GeneratedReaders___Internal.Read___PlayerHealthFishNet.Serializing.Generateds(PooledReader0);
		float num = PooledReader0.ReadSingle(AutoPackType.Unpacked);
		Vector3 vector = PooledReader0.ReadVector3();
		if (!base.IsClient)
		{
			return;
		}
		this.RpcLogic___BumpPlayer_2429708885(base.LocalConnection, playerHealth, num, vector);
	}

	// Token: 0x0600094D RID: 2381 RVA: 0x00043D6C File Offset: 0x00041F6C
	private void RpcWriter___Server_RemoveAmmo_2166136261()
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendServerRpc(17U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x0600094E RID: 2382 RVA: 0x0002BFDC File Offset: 0x0002A1DC
	private void RpcLogic___RemoveAmmo_2166136261()
	{
		if (this.reloadWeapon)
		{
			this.chargedBullets -= 1f;
			return;
		}
		base.sync___set_value_currentAmmo(base.SyncAccessor_currentAmmo - 1, true);
	}

	// Token: 0x0600094F RID: 2383 RVA: 0x00043E60 File Offset: 0x00042060
	private void RpcReader___Server_RemoveAmmo_2166136261(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___RemoveAmmo_2166136261();
	}

	// Token: 0x06000950 RID: 2384 RVA: 0x00043EA0 File Offset: 0x000420A0
	private void RpcWriter___Server_GiveDamage_324487999(float damageToGive, PlayerHealth enemyHealth, string name)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteSingle(damageToGive, AutoPackType.Unpacked);
		writer.Write___PlayerHealthFishNet.Serializing.Generated(enemyHealth);
		writer.WriteString(name);
		base.SendServerRpc(18U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000951 RID: 2385 RVA: 0x00043FC0 File Offset: 0x000421C0
	private void RpcLogic___GiveDamage_324487999(float damageToGive, PlayerHealth enemyHealth, string name)
	{
		enemyHealth.sync___set_value_killer(this.rootObject.transform, true);
		enemyHealth.sync___set_value_health(enemyHealth.SyncAccessor_health - damageToGive, true);
		enemyHealth.KillCam();
		this.HitFeeback(enemyHealth);
		enemyHealth.Dismemberment(name);
	}

	// Token: 0x06000952 RID: 2386 RVA: 0x00043FF8 File Offset: 0x000421F8
	private void RpcReader___Server_GiveDamage_324487999(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		float num = PooledReader0.ReadSingle(AutoPackType.Unpacked);
		PlayerHealth playerHealth = global::FishNet.Serializing.Generated.GeneratedReaders___Internal.Read___PlayerHealthFishNet.Serializing.Generateds(PooledReader0);
		string text = PooledReader0.ReadString();
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___GiveDamage_324487999(num, playerHealth, text);
	}

	// Token: 0x06000953 RID: 2387 RVA: 0x00044070 File Offset: 0x00042270
	private void RpcWriter___Server_KillServer_1722911636(PlayerHealth enemyHealth)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.Write___PlayerHealthFishNet.Serializing.Generated(enemyHealth);
		base.SendServerRpc(19U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000954 RID: 2388 RVA: 0x00044174 File Offset: 0x00042374
	private void RpcLogic___KillServer_1722911636(PlayerHealth enemyHealth)
	{
		enemyHealth.sync___set_value_isShot(true, true);
		enemyHealth.sync___set_value_health(-8f, true);
		GameManager.Instance.PlayerDied(enemyHealth.SyncAccessor_playerValues.SyncAccessor_playerClient.PlayerId);
		if (this.rootObject != null)
		{
			enemyHealth.sync___set_value_killer(this.rootObject.transform, true);
		}
		this.KillObserver(enemyHealth.SyncAccessor_playerValues.SyncAccessor_playerClient.transform.GetComponent<NetworkObject>().Owner, enemyHealth.SyncAccessor_playerValues.SyncAccessor_playerClient, enemyHealth);
	}

	// Token: 0x06000955 RID: 2389 RVA: 0x000441FC File Offset: 0x000423FC
	private void RpcReader___Server_KillServer_1722911636(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		PlayerHealth playerHealth = global::FishNet.Serializing.Generated.GeneratedReaders___Internal.Read___PlayerHealthFishNet.Serializing.Generateds(PooledReader0);
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		this.RpcLogic___KillServer_1722911636(playerHealth);
	}

	// Token: 0x06000956 RID: 2390 RVA: 0x00044240 File Offset: 0x00042440
	private void RpcWriter___Target_KillObserver_123853379(NetworkConnection conn, ClientInstance client, PlayerHealth enemyHealth)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.Write___ClientInstanceFishNet.Serializing.Generated(client);
		writer.Write___PlayerHealthFishNet.Serializing.Generated(enemyHealth);
		base.SendTargetRpc(20U, writer, channel, DataOrderType.Default, conn, false, true);
		writer.Store();
	}

	// Token: 0x06000957 RID: 2391 RVA: 0x000275DA File Offset: 0x000257DA
	private void RpcLogic___KillObserver_123853379(NetworkConnection conn, ClientInstance client, PlayerHealth enemyHealth)
	{
		enemyHealth.shouldDropWeapon = true;
		enemyHealth.isDeadFromTargetRpc = true;
		if (this.rootObject != null)
		{
			GameObject.Find("Main Camera").GetComponent<KillCam>().enemy = this.rootObject.transform;
		}
	}

	// Token: 0x06000958 RID: 2392 RVA: 0x00044304 File Offset: 0x00042504
	private void RpcReader___Target_KillObserver_123853379(PooledReader PooledReader0, Channel channel)
	{
		ClientInstance clientInstance = global::FishNet.Serializing.Generated.GeneratedReaders___Internal.Read___ClientInstanceFishNet.Serializing.Generateds(PooledReader0);
		PlayerHealth playerHealth = global::FishNet.Serializing.Generated.GeneratedReaders___Internal.Read___PlayerHealthFishNet.Serializing.Generateds(PooledReader0);
		if (!base.IsClient)
		{
			return;
		}
		this.RpcLogic___KillObserver_123853379(base.LocalConnection, clientInstance, playerHealth);
	}

	// Token: 0x06000959 RID: 2393 RVA: 0x0004434C File Offset: 0x0004254C
	private void RpcWriter___Observers_HitFeeback_1722911636(PlayerHealth enemyHealth)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.Write___PlayerHealthFishNet.Serializing.Generated(enemyHealth);
		base.SendObserversRpc(21U, writer, channel, DataOrderType.Default, false, false, false);
		writer.Store();
	}

	// Token: 0x0600095A RID: 2394 RVA: 0x00027716 File Offset: 0x00025916
	private void RpcLogic___HitFeeback_1722911636(PlayerHealth enemyHealth)
	{
		enemyHealth.HitFeeback();
	}

	// Token: 0x0600095B RID: 2395 RVA: 0x00044404 File Offset: 0x00042604
	private void RpcReader___Observers_HitFeeback_1722911636(PooledReader PooledReader0, Channel channel)
	{
		PlayerHealth playerHealth = global::FishNet.Serializing.Generated.GeneratedReaders___Internal.Read___PlayerHealthFishNet.Serializing.Generateds(PooledReader0);
		if (!base.IsClient)
		{
			return;
		}
		this.RpcLogic___HitFeeback_1722911636(playerHealth);
	}

	// Token: 0x0600095C RID: 2396 RVA: 0x00044438 File Offset: 0x00042638
	private void RpcWriter___Server_ShootServerEffect_2166136261()
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendServerRpc(22U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x0600095D RID: 2397 RVA: 0x0004452C File Offset: 0x0004272C
	private void RpcLogic___ShootServerEffect_2166136261()
	{
		this.ShootObserversEffect();
	}

	// Token: 0x0600095E RID: 2398 RVA: 0x00044534 File Offset: 0x00042734
	private void RpcReader___Server_ShootServerEffect_2166136261(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___ShootServerEffect_2166136261();
	}

	// Token: 0x0600095F RID: 2399 RVA: 0x00044574 File Offset: 0x00042774
	private void RpcWriter___Observers_ShootObserversEffect_2166136261()
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendObserversRpc(23U, writer, channel, DataOrderType.Default, false, false, true);
		writer.Store();
	}

	// Token: 0x06000960 RID: 2400 RVA: 0x00044620 File Offset: 0x00042820
	private void RpcLogic___ShootObserversEffect_2166136261()
	{
		if (Settings.Instance.reduceVFX)
		{
			return;
		}
		this.audio.PlayOneShot(this.fireClip);
		GameObject gameObject = global::UnityEngine.Object.Instantiate<GameObject>(this.muzzleFlash, this.muzzleFlashPoint.position, this.shootPoint.rotation);
		foreach (Transform transform in gameObject.transform.GetComponentsInChildren<Transform>(true))
		{
			if (transform.GetComponent<Light>() == null && transform.tag != "vfx")
			{
				transform.gameObject.layer = 8;
			}
			if (transform.GetComponent<Light>() != null)
			{
				transform.GetComponent<Light>().intensity = this.lightIntensity;
			}
		}
		ParticleSystem[] componentsInChildren2 = gameObject.GetComponentsInChildren<ParticleSystem>();
		for (int i = 0; i < componentsInChildren2.Length; i++)
		{
			componentsInChildren2[i].Play();
		}
	}

	// Token: 0x06000961 RID: 2401 RVA: 0x000446F8 File Offset: 0x000428F8
	private void RpcReader___Observers_ShootObserversEffect_2166136261(PooledReader PooledReader0, Channel channel)
	{
		if (!base.IsClient)
		{
			return;
		}
		if (base.IsHost)
		{
			return;
		}
		this.RpcLogic___ShootObserversEffect_2166136261();
	}

	// Token: 0x06000962 RID: 2402 RVA: 0x00044724 File Offset: 0x00042924
	private void RpcWriter___Server_SpawnBulletTrailServer_4276783012(Vector3 hitPoint)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteVector3(hitPoint);
		base.SendServerRpc(24U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000963 RID: 2403 RVA: 0x00044825 File Offset: 0x00042A25
	private void RpcLogic___SpawnBulletTrailServer_4276783012(Vector3 hitPoint)
	{
		this.SpawnBulletTrail(hitPoint);
	}

	// Token: 0x06000964 RID: 2404 RVA: 0x00044830 File Offset: 0x00042A30
	private void RpcReader___Server_SpawnBulletTrailServer_4276783012(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		Vector3 vector = PooledReader0.ReadVector3();
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___SpawnBulletTrailServer_4276783012(vector);
	}

	// Token: 0x06000965 RID: 2405 RVA: 0x00044880 File Offset: 0x00042A80
	private void RpcWriter___Observers_SpawnBulletTrail_4276783012(Vector3 hitPoint)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteVector3(hitPoint);
		base.SendObserversRpc(25U, writer, channel, DataOrderType.Default, false, false, true);
		writer.Store();
	}

	// Token: 0x06000966 RID: 2406 RVA: 0x00044938 File Offset: 0x00042B38
	private void RpcLogic___SpawnBulletTrail_4276783012(Vector3 hitPoint)
	{
		GameObject gameObject = global::UnityEngine.Object.Instantiate<GameObject>(this.bulletTrailLocal.gameObject, this.shootPoint.position, Quaternion.identity);
		LineRenderer component = gameObject.GetComponent<LineRenderer>();
		component.SetPosition(0, this.shootPoint.position);
		component.SetPosition(1, hitPoint);
		global::UnityEngine.Object.Destroy(gameObject, 0.4f);
	}

	// Token: 0x06000967 RID: 2407 RVA: 0x00044990 File Offset: 0x00042B90
	private void RpcReader___Observers_SpawnBulletTrail_4276783012(PooledReader PooledReader0, Channel channel)
	{
		Vector3 vector = PooledReader0.ReadVector3();
		if (!base.IsClient)
		{
			return;
		}
		if (base.IsHost)
		{
			return;
		}
		this.RpcLogic___SpawnBulletTrail_4276783012(vector);
	}

	// Token: 0x06000968 RID: 2408 RVA: 0x000449CC File Offset: 0x00042BCC
	private void RpcWriter___Server_SpawnVFXServer_606331033(int index, Vector3 hitPoint, Vector3 hitNormal, string surface, Transform parent)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteInt32(index, AutoPackType.Packed);
		writer.WriteVector3(hitPoint);
		writer.WriteVector3(hitNormal);
		writer.WriteString(surface);
		writer.WriteTransform(parent);
		base.SendServerRpc(26U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000969 RID: 2409 RVA: 0x00044B06 File Offset: 0x00042D06
	private void RpcLogic___SpawnVFXServer_606331033(int index, Vector3 hitPoint, Vector3 hitNormal, string surface, Transform parent)
	{
		this.SpawnVFX(index, hitPoint, hitNormal, surface, parent);
	}

	// Token: 0x0600096A RID: 2410 RVA: 0x00044B18 File Offset: 0x00042D18
	private void RpcReader___Server_SpawnVFXServer_606331033(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		int num = PooledReader0.ReadInt32(AutoPackType.Packed);
		Vector3 vector = PooledReader0.ReadVector3();
		Vector3 vector2 = PooledReader0.ReadVector3();
		string text = PooledReader0.ReadString();
		Transform transform = PooledReader0.ReadTransform();
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___SpawnVFXServer_606331033(num, vector, vector2, text, transform);
	}

	// Token: 0x0600096B RID: 2411 RVA: 0x00044BB0 File Offset: 0x00042DB0
	private void RpcWriter___Observers_SpawnVFX_606331033(int index, Vector3 hitPoint, Vector3 hitNormal, string surface, Transform parent)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteInt32(index, AutoPackType.Packed);
		writer.WriteVector3(hitPoint);
		writer.WriteVector3(hitNormal);
		writer.WriteString(surface);
		writer.WriteTransform(parent);
		base.SendObserversRpc(27U, writer, channel, DataOrderType.Default, false, false, true);
		writer.Store();
	}

	// Token: 0x0600096C RID: 2412 RVA: 0x00044CA0 File Offset: 0x00042EA0
	private void RpcLogic___SpawnVFX_606331033(int index, Vector3 hitPoint, Vector3 hitNormal, string surface, Transform parent)
	{
		if (Settings.Instance.reduceVFX)
		{
			return;
		}
		uint num = <PrivateImplementationDetails>.ComputeStringHash(surface);
		if (num <= 1825421690U)
		{
			if (num <= 1378315797U)
			{
				if (num <= 464173256U)
				{
					if (num != 456440475U)
					{
						if (num == 464173256U)
						{
							if (surface == "Footsteps/Concrete/Dalles")
							{
								if (index == 0)
								{
									global::UnityEngine.Object.Instantiate<GameObject>(this.genericImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
								}
								if (index == 1)
								{
									global::UnityEngine.Object.Instantiate<GameObject>(this.bulletHole, hitPoint, Quaternion.LookRotation(hitNormal), parent);
									goto IL_06C1;
								}
								goto IL_06C1;
							}
						}
					}
					else if (surface == "Footsteps/Sand")
					{
						if (index == 0)
						{
							global::UnityEngine.Object.Instantiate<GameObject>(this.genericImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
						}
						if (index == 1)
						{
							global::UnityEngine.Object.Instantiate<GameObject>(this.sandHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
							goto IL_06C1;
						}
						goto IL_06C1;
					}
				}
				else if (num != 913360285U)
				{
					if (num != 977466297U)
					{
						if (num == 1378315797U)
						{
							if (surface == "Footsteps/Moquette")
							{
								if (index == 0)
								{
									global::UnityEngine.Object.Instantiate<GameObject>(this.genericImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
								}
								if (index == 1)
								{
									global::UnityEngine.Object.Instantiate<GameObject>(this.softbodyHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
									goto IL_06C1;
								}
								goto IL_06C1;
							}
						}
					}
					else if (surface == "Footsteps/Concrete/Solide")
					{
						if (index == 0)
						{
							global::UnityEngine.Object.Instantiate<GameObject>(this.genericImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
						}
						if (index == 1)
						{
							global::UnityEngine.Object.Instantiate<GameObject>(this.bulletHole, hitPoint, Quaternion.LookRotation(hitNormal), parent);
							goto IL_06C1;
						}
						goto IL_06C1;
					}
				}
				else if (surface == "Grenade")
				{
					goto IL_06C1;
				}
			}
			else if (num <= 1430892386U)
			{
				if (num != 1429664136U)
				{
					if (num == 1430892386U)
					{
						if (surface == "Hat")
						{
							goto IL_06C1;
						}
					}
				}
				else if (surface == "Footsteps/Water")
				{
					if (index == 0)
					{
						global::UnityEngine.Object.Instantiate<GameObject>(this.genericImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
					}
					if (index == 1)
					{
						global::UnityEngine.Object.Instantiate<GameObject>(this.waterHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
						goto IL_06C1;
					}
					goto IL_06C1;
				}
			}
			else if (num != 1610969363U)
			{
				if (num != 1624496828U)
				{
					if (num == 1825421690U)
					{
						if (surface == "Footsteps/Wood/Creux")
						{
							if (index == 0)
							{
								global::UnityEngine.Object.Instantiate<GameObject>(this.genericImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
							}
							if (index == 1)
							{
								global::UnityEngine.Object.Instantiate<GameObject>(this.woodHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
								goto IL_06C1;
							}
							goto IL_06C1;
						}
					}
				}
				else if (surface == "Footsteps/Concrete/Default")
				{
					if (index == 0)
					{
						global::UnityEngine.Object.Instantiate<GameObject>(this.genericImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
					}
					if (index == 1)
					{
						global::UnityEngine.Object.Instantiate<GameObject>(this.bulletHole, hitPoint, Quaternion.LookRotation(hitNormal), parent);
						goto IL_06C1;
					}
					goto IL_06C1;
				}
			}
			else if (surface == "Footsteps/Concrete/PleinAir")
			{
				if (index == 0)
				{
					global::UnityEngine.Object.Instantiate<GameObject>(this.genericImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
				}
				if (index == 1)
				{
					global::UnityEngine.Object.Instantiate<GameObject>(this.bulletHole, hitPoint, Quaternion.LookRotation(hitNormal), parent);
					goto IL_06C1;
				}
				goto IL_06C1;
			}
		}
		else if (num <= 2833365437U)
		{
			if (num <= 2180110808U)
			{
				if (num != 1954265137U)
				{
					if (num == 2180110808U)
					{
						if (surface == "Footsteps/Dirt")
						{
							if (index == 0)
							{
								global::UnityEngine.Object.Instantiate<GameObject>(this.genericImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
							}
							if (index == 1)
							{
								global::UnityEngine.Object.Instantiate<GameObject>(this.dirtHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
								goto IL_06C1;
							}
							goto IL_06C1;
						}
					}
				}
				else if (surface == "NoSound")
				{
					goto IL_06C1;
				}
			}
			else if (num != 2251139421U)
			{
				if (num != 2400559108U)
				{
					if (num == 2833365437U)
					{
						if (surface == "Footsteps/Metal/Pipe2")
						{
							if (index == 0)
							{
								global::UnityEngine.Object.Instantiate<GameObject>(this.genericImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
							}
							if (index == 1)
							{
								global::UnityEngine.Object.Instantiate<GameObject>(this.metalHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
								goto IL_06C1;
							}
							goto IL_06C1;
						}
					}
				}
				else if (surface == "Footsteps/Matelas")
				{
					if (index == 0)
					{
						global::UnityEngine.Object.Instantiate<GameObject>(this.genericImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
					}
					if (index == 1)
					{
						global::UnityEngine.Object.Instantiate<GameObject>(this.softbodyHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
						goto IL_06C1;
					}
					goto IL_06C1;
				}
			}
			else if (surface == "Footsteps/Metal/Pipe")
			{
				if (index == 0)
				{
					global::UnityEngine.Object.Instantiate<GameObject>(this.genericImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
				}
				if (index == 1)
				{
					global::UnityEngine.Object.Instantiate<GameObject>(this.tauleHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
					goto IL_06C1;
				}
				goto IL_06C1;
			}
		}
		else if (num <= 3455850406U)
		{
			if (num != 3075599786U)
			{
				if (num == 3455850406U)
				{
					if (surface == "Footsteps/Graviers")
					{
						if (index == 0)
						{
							global::UnityEngine.Object.Instantiate<GameObject>(this.genericImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
						}
						if (index == 1)
						{
							global::UnityEngine.Object.Instantiate<GameObject>(this.bulletHole, hitPoint, Quaternion.LookRotation(hitNormal), parent);
							goto IL_06C1;
						}
						goto IL_06C1;
					}
				}
			}
			else if (surface == "Footsteps/Wood/Sec")
			{
				if (index == 0)
				{
					global::UnityEngine.Object.Instantiate<GameObject>(this.genericImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
				}
				if (index == 1)
				{
					global::UnityEngine.Object.Instantiate<GameObject>(this.woodHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
					goto IL_06C1;
				}
				goto IL_06C1;
			}
		}
		else if (num != 3492154005U)
		{
			if (num != 3807801418U)
			{
				if (num == 3848897750U)
				{
					if (surface == "Mine")
					{
						goto IL_06C1;
					}
				}
			}
			else if (surface == "Footsteps/Metal/Grille")
			{
				if (index == 0)
				{
					global::UnityEngine.Object.Instantiate<GameObject>(this.genericImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
				}
				if (index == 1)
				{
					global::UnityEngine.Object.Instantiate<GameObject>(this.tauleHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
					goto IL_06C1;
				}
				goto IL_06C1;
			}
		}
		else if (surface == "Footsteps/Grass")
		{
			if (index == 0)
			{
				global::UnityEngine.Object.Instantiate<GameObject>(this.genericImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
			}
			if (index == 1)
			{
				global::UnityEngine.Object.Instantiate<GameObject>(this.dirtHitFx, hitPoint, Quaternion.LookRotation(hitNormal), parent);
				goto IL_06C1;
			}
			goto IL_06C1;
		}
		if (index == 0)
		{
			global::UnityEngine.Object.Instantiate<GameObject>(this.genericImpact, hitPoint, Quaternion.LookRotation(hitNormal), parent);
		}
		if (index == 1)
		{
			global::UnityEngine.Object.Instantiate<GameObject>(this.bulletHole, hitPoint, Quaternion.LookRotation(hitNormal), parent);
		}
		IL_06C1:
		if (index == 2)
		{
			global::UnityEngine.Object.Instantiate<GameObject>(this.bodyImpact, hitPoint, Quaternion.identity, parent);
		}
	}

	// Token: 0x0600096D RID: 2413 RVA: 0x00045388 File Offset: 0x00043588
	private void RpcReader___Observers_SpawnVFX_606331033(PooledReader PooledReader0, Channel channel)
	{
		int num = PooledReader0.ReadInt32(AutoPackType.Packed);
		Vector3 vector = PooledReader0.ReadVector3();
		Vector3 vector2 = PooledReader0.ReadVector3();
		string text = PooledReader0.ReadString();
		Transform transform = PooledReader0.ReadTransform();
		if (!base.IsClient)
		{
			return;
		}
		if (base.IsHost)
		{
			return;
		}
		this.RpcLogic___SpawnVFX_606331033(num, vector, vector2, text, transform);
	}

	// Token: 0x0600096E RID: 2414 RVA: 0x0004540C File Offset: 0x0004360C
	private void RpcWriter___Server_SupressionServer_3068987916(Transform supp)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteTransform(supp);
		base.SendServerRpc(28U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x0600096F RID: 2415 RVA: 0x0004550D File Offset: 0x0004370D
	private void RpcLogic___SupressionServer_3068987916(Transform supp)
	{
		this.SuppressionTarget(supp);
	}

	// Token: 0x06000970 RID: 2416 RVA: 0x00045518 File Offset: 0x00043718
	private void RpcReader___Server_SupressionServer_3068987916(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		Transform transform = PooledReader0.ReadTransform();
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		this.RpcLogic___SupressionServer_3068987916(transform);
	}

	// Token: 0x06000971 RID: 2417 RVA: 0x0004555C File Offset: 0x0004375C
	private void RpcWriter___Observers_SuppressionTarget_3068987916(Transform supp)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteTransform(supp);
		base.SendObserversRpc(29U, writer, channel, DataOrderType.Default, false, false, false);
		writer.Store();
	}

	// Token: 0x06000972 RID: 2418 RVA: 0x00027B2A File Offset: 0x00025D2A
	private void RpcLogic___SuppressionTarget_3068987916(Transform supp)
	{
		supp.GetComponent<Suppression>().SuppressionTrigger();
	}

	// Token: 0x06000973 RID: 2419 RVA: 0x00045614 File Offset: 0x00043814
	private void RpcReader___Observers_SuppressionTarget_3068987916(PooledReader PooledReader0, Channel channel)
	{
		Transform transform = PooledReader0.ReadTransform();
		if (!base.IsClient)
		{
			return;
		}
		this.RpcLogic___SuppressionTarget_3068987916(transform);
	}

	// Token: 0x06000974 RID: 2420 RVA: 0x00045645 File Offset: 0x00043845
	public override void Awake()
	{
		this.NetworkInitialize___Early();
		base.Awake();
		this.NetworkInitialize__Late();
	}

	// Token: 0x06000975 RID: 2421 RVA: 0x000023D6 File Offset: 0x000005D6
	public override void Awake___UserLogic()
	{
	}

	// Token: 0x04000887 RID: 2183
	[Header("Weapon Specials")]
	[SerializeField]
	private float reloadTime;

	// Token: 0x04000888 RID: 2184
	[SerializeField]
	private AudioClip reloadClip;

	// Token: 0x04000889 RID: 2185
	[SerializeField]
	private float repulseForce = 3f;

	// Token: 0x0400088A RID: 2186
	[SerializeField]
	private float playerKnockback;

	// Token: 0x0400088B RID: 2187
	[SerializeField]
	private Vector3 boxdimensions;

	// Token: 0x0400088C RID: 2188
	private float fireTimer;

	// Token: 0x0400088D RID: 2189
	private bool touched;

	// Token: 0x0400088E RID: 2190
	private Vector3 spread;

	// Token: 0x0400088F RID: 2191
	private bool hitOK;

	// Token: 0x04000890 RID: 2192
	private PlayerHealth enemyHealth;

	// Token: 0x04000891 RID: 2193
	private bool NetworkInitializeEarly_RepulsiveGun_Assembly-CSharp.dll;

	// Token: 0x04000892 RID: 2194
	private bool NetworkInitializeLate_RepulsiveGun_Assembly-CSharp.dll;
}

﻿using System;
using System.Collections;
using DG.Tweening;
using FishNet;
using FishNet.Component.Utility;
using TMPro;
using UnityEngine;

// Token: 0x02000128 RID: 296
public class HUD : MonoBehaviour
{
	// Token: 0x06000E3F RID: 3647 RVA: 0x0005EF95 File Offset: 0x0005D195
	private IEnumerator Start()
	{
		if (!this.fpsDisplay)
		{
			yield break;
		}
		for (;;)
		{
			this.count = 1f / Time.unscaledDeltaTime;
			yield return new WaitForSeconds(0.1f);
		}
		yield break;
	}

	// Token: 0x06000E40 RID: 3648 RVA: 0x0005EFA4 File Offset: 0x0005D1A4
	private void Awake()
	{
		this.pauseManager = PauseManager.Instance;
		this.text = base.GetComponent<TMP_Text>();
		this.PingDisplay = InstanceFinder.NetworkManager.GetComponent<PingDisplay>();
	}

	// Token: 0x06000E41 RID: 3649 RVA: 0x0005EFD0 File Offset: 0x0005D1D0
	private void Update()
	{
		if (this.ammoRight)
		{
			if (this.text.text != this.pauseManager.rightGunAmmo.text)
			{
				base.transform.DOKill(false);
				base.transform.parent.localScale = Vector3.one;
				base.transform.parent.DOPunchScale(new Vector3(1.15f, 1.15f, 1.15f), 0.1f, 10, 1f);
			}
			else
			{
				base.transform.parent.DOScale(Vector3.one, 0.3f);
			}
			this.text.text = this.pauseManager.rightGunAmmo.text;
		}
		if (this.ammoLeft)
		{
			if (this.text.text != this.pauseManager.leftGunAmmo.text)
			{
				base.transform.DOKill(false);
				base.transform.parent.localScale = Vector3.one;
				base.transform.parent.DOPunchScale(new Vector3(1.15f, 1.15f, 1.15f), 0.1f, 10, 1f);
			}
			else
			{
				base.transform.parent.DOScale(Vector3.one, 0.3f);
			}
			this.text.text = this.pauseManager.leftGunAmmo.text;
		}
		if (this.ammoReloadRight)
		{
			this.text.text = this.pauseManager.rightGunAmmoReload.text;
		}
		if (this.ammoReloadLeft)
		{
			this.text.text = this.pauseManager.leftGunAmmoReload.text;
		}
		if (this.pingDisplay)
		{
			this.text.text = this.PingDisplay.ping.ToString();
		}
		if (this.fpsDisplay)
		{
			this.text.text = Mathf.Round(this.count).ToString();
		}
	}

	// Token: 0x04000CCC RID: 3276
	private PauseManager pauseManager;

	// Token: 0x04000CCD RID: 3277
	private TMP_Text text;

	// Token: 0x04000CCE RID: 3278
	[SerializeField]
	private bool ammoRight;

	// Token: 0x04000CCF RID: 3279
	[SerializeField]
	private bool ammoReloadRight;

	// Token: 0x04000CD0 RID: 3280
	[SerializeField]
	private bool ammoLeft;

	// Token: 0x04000CD1 RID: 3281
	[SerializeField]
	private bool ammoReloadLeft;

	// Token: 0x04000CD2 RID: 3282
	[SerializeField]
	private bool pingDisplay;

	// Token: 0x04000CD3 RID: 3283
	[SerializeField]
	private bool fpsDisplay;

	// Token: 0x04000CD4 RID: 3284
	private PingDisplay PingDisplay;

	// Token: 0x04000CD5 RID: 3285
	private float count;

	// Token: 0x04000CD6 RID: 3286
	public bool display;
}

﻿using System;
using TMPro;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.UI;

// Token: 0x02000148 RID: 328
public class WeaponRow : MonoBehaviour
{
	// Token: 0x06000EC4 RID: 3780 RVA: 0x00061226 File Offset: 0x0005F426
	private void Awake()
	{
		this.slider.minValue = 0f;
		this.slider.maxValue = 100f;
	}

	// Token: 0x06000EC5 RID: 3781 RVA: 0x00061248 File Offset: 0x0005F448
	private void Start()
	{
		this.inputField.text = "100";
		this.slider.value = 100f;
		this.inputField.onValueChanged.AddListener(new UnityAction<string>(this.OnInputFieldChanged));
		this.slider.onValueChanged.AddListener(new UnityAction<float>(this.OnSliderChanged));
		this.toggle.onValueChanged.AddListener(new UnityAction<bool>(this.OnToggleChanged));
	}

	// Token: 0x06000EC6 RID: 3782 RVA: 0x000612C9 File Offset: 0x0005F4C9
	public void Init(string weaponName, string displayName)
	{
		this.WeaponName = weaponName;
		this.DisplayName = displayName;
	}

	// Token: 0x06000EC7 RID: 3783 RVA: 0x000612DC File Offset: 0x0005F4DC
	private void OnInputFieldChanged(string value)
	{
		int num;
		if (int.TryParse(value, out num))
		{
			num = Mathf.Clamp(num, 0, 100);
			this.inputField.text = num.ToString();
			this.slider.value = (float)num;
		}
		else if (value == "")
		{
			this.slider.value = 0f;
		}
		else
		{
			this.inputField.text = this.slider.value.ToString("0");
		}
		WeaponData weaponData = Spawner.weaponInfo[this.WeaponName];
		weaponData.SpawnChance = (uint)num;
		Spawner.weaponInfo[this.WeaponName] = weaponData;
		if (!this.suppressEvents)
		{
			WeaponRandomizationMenu.UpdateAllSpawnProbabilities();
		}
	}

	// Token: 0x06000EC8 RID: 3784 RVA: 0x00061398 File Offset: 0x0005F598
	private void OnSliderChanged(float value)
	{
		this.inputField.text = ((int)value).ToString();
	}

	// Token: 0x06000EC9 RID: 3785 RVA: 0x000613BC File Offset: 0x0005F5BC
	private void OnToggleChanged(bool value)
	{
		this.inputField.interactable = value;
		this.slider.interactable = value;
		WeaponData weaponData = Spawner.weaponInfo[this.WeaponName];
		weaponData.IsSpawnable = value;
		Spawner.weaponInfo[this.WeaponName] = weaponData;
		if (!this.suppressEvents)
		{
			WeaponRandomizationMenu.UpdateAllSpawnProbabilities();
		}
	}

	// Token: 0x06000ECA RID: 3786 RVA: 0x00061418 File Offset: 0x0005F618
	public float GetSpawnProbabilityPercentage()
	{
		uint cachedTotalSpawnableWeight = WeaponRandomizationMenu.CachedTotalSpawnableWeight;
		if (cachedTotalSpawnableWeight == 0U)
		{
			return 0f;
		}
		WeaponData weaponData;
		if (!Spawner.weaponInfo.TryGetValue(this.WeaponName, out weaponData) || !weaponData.IsSpawnable)
		{
			return 0f;
		}
		return weaponData.SpawnChance / cachedTotalSpawnableWeight * 100f;
	}

	// Token: 0x06000ECB RID: 3787 RVA: 0x00061468 File Offset: 0x0005F668
	public void UpdateRowDisplay()
	{
		this.suppressEvents = true;
		float spawnProbabilityPercentage = this.GetSpawnProbabilityPercentage();
		this.weightedSpawnChance.text = spawnProbabilityPercentage.ToString("F1") + "%";
		this.inputField.text = Spawner.weaponInfo[this.WeaponName].SpawnChance.ToString();
		this.toggle.isOn = Spawner.weaponInfo[this.WeaponName].IsSpawnable;
		this.suppressEvents = false;
	}

	// Token: 0x04000D57 RID: 3415
	public string WeaponName;

	// Token: 0x04000D58 RID: 3416
	public string DisplayName;

	// Token: 0x04000D59 RID: 3417
	public TMP_InputField inputField;

	// Token: 0x04000D5A RID: 3418
	public Slider slider;

	// Token: 0x04000D5B RID: 3419
	public Toggle toggle;

	// Token: 0x04000D5C RID: 3420
	public TMP_Text weightedSpawnChance;

	// Token: 0x04000D5D RID: 3421
	public bool suppressEvents;
}

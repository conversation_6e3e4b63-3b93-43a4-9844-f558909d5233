﻿using System;
using UnityEngine;

namespace ch.sycoforge.Decal.Demo
{
	// Token: 0x02000181 RID: 385
	public class Footprints : MonoBehaviour
	{
		// Token: 0x06000FC0 RID: 4032 RVA: 0x0006744A File Offset: 0x0006564A
		public void Start()
		{
			if (this.DecalPrefab == null)
			{
				Debug.LogError("The DynamicDemo script has no decal prefab attached.");
			}
		}

		// Token: 0x06000FC1 RID: 4033 RVA: 0x00067464 File Offset: 0x00065664
		public void Update()
		{
			if (this.DecalPrefab == null)
			{
				return;
			}
			if (this.distance >= this.DistanceThreshold)
			{
				Vector3 vector = ((this.index == 0) ? Vector3.right : Vector3.left) * this.FootDistance * 0.5f;
				Vector3 vector2 = base.transform.TransformPoint(vector);
				RaycastHit raycastHit;
				if (Physics.Raycast(new Ray(vector2 + Vector3.up * 0.1f, Vector3.down), out raycastHit, 200f))
				{
					EasyDecal easyDecal = EasyDecal.ProjectAt(this.DecalPrefab.gameObject, raycastHit.collider.gameObject, raycastHit.point, raycastHit.normal, true);
					easyDecal.AtlasRegionIndex = this.index;
					easyDecal.transform.rotation = Quaternion.Euler(Vector3.up * base.transform.rotation.eulerAngles.y);
				}
				int num = this.index + 1;
				this.index = num;
				this.index = num % 2;
				this.distance = 0f;
			}
			this.distance += Vector3.Distance(this.lastPosition, base.transform.position);
			this.lastPosition = base.transform.position;
		}

		// Token: 0x04000EA1 RID: 3745
		public EasyDecal DecalPrefab;

		// Token: 0x04000EA2 RID: 3746
		public float DistanceThreshold = 0.8f;

		// Token: 0x04000EA3 RID: 3747
		public float FootDistance = 0.5f;

		// Token: 0x04000EA4 RID: 3748
		private float distance;

		// Token: 0x04000EA5 RID: 3749
		private int index;

		// Token: 0x04000EA6 RID: 3750
		private Vector3 lastPosition;
	}
}

﻿using System;
using UnityEngine;

// Token: 0x0200015E RID: 350
[RequireComponent(typeof(ParticleSystem))]
public class CFX_AutoStopLoopedEffect : MonoBehaviour
{
	// Token: 0x06000F0C RID: 3852 RVA: 0x000632DC File Offset: 0x000614DC
	private void OnEnable()
	{
		this.d = this.effectDuration;
	}

	// Token: 0x06000F0D RID: 3853 RVA: 0x000632EC File Offset: 0x000614EC
	private void Update()
	{
		if (this.d > 0f)
		{
			this.d -= Time.deltaTime;
			if (this.d <= 0f)
			{
				base.GetComponent<ParticleSystem>().Stop(true);
				CFX_Demo_Translate component = base.gameObject.GetComponent<CFX_Demo_Translate>();
				if (component != null)
				{
					component.enabled = false;
				}
			}
		}
	}

	// Token: 0x04000DC6 RID: 3526
	public float effectDuration = 2.5f;

	// Token: 0x04000DC7 RID: 3527
	private float d;
}

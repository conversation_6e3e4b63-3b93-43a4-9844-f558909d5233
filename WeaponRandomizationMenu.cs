﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using TMPro;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.UI;

// Token: 0x02000145 RID: 325
public class WeaponRandomizationMenu : MonoBehaviour
{
	// Token: 0x06000EB1 RID: 3761 RVA: 0x00060C86 File Offset: 0x0005EE86
	private void Awake()
	{
		SpawnerManager.PopulateAllWeapons();
		GameObject gameObject = this.menuButton;
		if (gameObject != null)
		{
			gameObject.SetActive(SteamLobby.ownDlc0);
		}
		this.menuCanvas.enabled = false;
	}

	// Token: 0x06000EB2 RID: 3762 RVA: 0x00060CAF File Offset: 0x0005EEAF
	private void Start()
	{
		this.PopulateMenu();
		this.weaponNameInputField.onValueChanged.AddListener(new UnityAction<string>(this.FilterListByWeaponName));
	}

	// Token: 0x06000EB3 RID: 3763 RVA: 0x00060CD4 File Offset: 0x0005EED4
	private static void UpdateCachedTotalWeight()
	{
		WeaponRandomizationMenu.CachedTotalSpawnableWeight = (uint)Spawner.weaponInfo.Values.Where((WeaponData data) => data.IsSpawnable).Sum((WeaponData data) => (long)((ulong)data.SpawnChance));
	}

	// Token: 0x06000EB4 RID: 3764 RVA: 0x00060D39 File Offset: 0x0005EF39
	public void ToggleMenu()
	{
		this.menuCanvas.enabled = !this.menuCanvas.enabled;
	}

	// Token: 0x06000EB5 RID: 3765 RVA: 0x00060D54 File Offset: 0x0005EF54
	private void PopulateMenu()
	{
		foreach (GameObject gameObject in SpawnerManager.AllWeapons)
		{
			ItemBehaviour component = gameObject.GetComponent<ItemBehaviour>();
			GameObject gameObject2 = global::UnityEngine.Object.Instantiate<GameObject>(this.weaponRowPrefab, this.weaponList.transform);
			WeaponRow component2 = gameObject2.GetComponent<WeaponRow>();
			string text = component.weaponName.ToUpper(new CultureInfo("en-US", false));
			component2.Init(gameObject.name, text);
			this.RegisterRow(component2);
			TextMeshProUGUI component3 = gameObject2.transform.Find("NameLabel").GetComponent<TextMeshProUGUI>();
			Toggle component4 = gameObject2.transform.Find("EnableToggle").GetComponent<Toggle>();
			Slider component5 = gameObject2.transform.Find("SpawnChanceSlider").GetComponent<Slider>();
			gameObject2.transform.Find("PercentInput").GetComponent<TMP_InputField>();
			component3.text = text;
			component4.isOn = true;
			component5.value = 100f;
			WeaponData weaponData = new WeaponData(gameObject.name, (uint)component5.value, component4.isOn);
			Spawner.weaponInfo.Add(gameObject.name, weaponData);
		}
		WeaponRandomizationMenu.UpdateAllSpawnProbabilities();
	}

	// Token: 0x06000EB6 RID: 3766 RVA: 0x00060E7D File Offset: 0x0005F07D
	private void RegisterRow(WeaponRow row)
	{
		if (!WeaponRandomizationMenu.weaponRows.Contains(row))
		{
			WeaponRandomizationMenu.weaponRows.Add(row);
		}
	}

	// Token: 0x06000EB7 RID: 3767 RVA: 0x00060E98 File Offset: 0x0005F098
	public static void UpdateAllSpawnProbabilities()
	{
		WeaponRandomizationMenu.UpdateCachedTotalWeight();
		foreach (WeaponRow weaponRow in WeaponRandomizationMenu.weaponRows)
		{
			weaponRow.UpdateRowDisplay();
		}
		Spawner.UpdateSpawnableWeapons();
	}

	// Token: 0x06000EB8 RID: 3768 RVA: 0x00060EF4 File Offset: 0x0005F0F4
	public void ToggleAllWeapons()
	{
		foreach (WeaponRow weaponRow in WeaponRandomizationMenu.weaponRows)
		{
			if (weaponRow != null)
			{
				weaponRow.suppressEvents = true;
				weaponRow.toggle.isOn = WeaponRandomizationMenu.toggleState;
				weaponRow.suppressEvents = false;
			}
		}
		WeaponRandomizationMenu.toggleState = !WeaponRandomizationMenu.toggleState;
		WeaponRandomizationMenu.UpdateAllSpawnProbabilities();
	}

	// Token: 0x06000EB9 RID: 3769 RVA: 0x00060F78 File Offset: 0x0005F178
	public void SetAllSlidersToMinOrMax()
	{
		foreach (WeaponRow weaponRow in WeaponRandomizationMenu.weaponRows)
		{
			if (weaponRow != null)
			{
				weaponRow.suppressEvents = true;
				weaponRow.slider.value = (float)(WeaponRandomizationMenu.sliderMaxToggle ? 100 : 0);
				weaponRow.suppressEvents = false;
			}
		}
		WeaponRandomizationMenu.sliderMaxToggle = !WeaponRandomizationMenu.sliderMaxToggle;
		WeaponRandomizationMenu.UpdateAllSpawnProbabilities();
	}

	// Token: 0x06000EBA RID: 3770 RVA: 0x00061004 File Offset: 0x0005F204
	public static void RandomizeSettings()
	{
		foreach (KeyValuePair<string, WeaponData> keyValuePair in Spawner.weaponInfo.ToList<KeyValuePair<string, WeaponData>>())
		{
			Spawner.weaponInfo[keyValuePair.Key] = new WeaponData
			{
				WeaponName = keyValuePair.Key,
				SpawnChance = (uint)global::UnityEngine.Random.Range(0, 101),
				IsSpawnable = (global::UnityEngine.Random.Range(0, 2) == 0)
			};
		}
		WeaponRandomizationMenu.UpdateAllSpawnProbabilities();
	}

	// Token: 0x06000EBB RID: 3771 RVA: 0x000610A4 File Offset: 0x0005F2A4
	public void FilterListByWeaponName(string weaponName)
	{
		if (this.weaponNameInputField == null)
		{
			return;
		}
		if (string.IsNullOrEmpty(weaponName))
		{
			foreach (WeaponRow weaponRow in WeaponRandomizationMenu.weaponRows)
			{
				if (weaponRow != null && !weaponRow.isActiveAndEnabled)
				{
					weaponRow.gameObject.SetActive(true);
				}
			}
			WeaponRandomizationMenu.UpdateAllSpawnProbabilities();
			return;
		}
		HashSet<WeaponRow> hashSet = new HashSet<WeaponRow>(WeaponRandomizationMenu.weaponRows.Where((WeaponRow i) => i != null && i.DisplayName.Contains(weaponName, StringComparison.CurrentCultureIgnoreCase)));
		foreach (WeaponRow weaponRow2 in WeaponRandomizationMenu.weaponRows)
		{
			if (!(weaponRow2 == null))
			{
				bool flag = hashSet.Contains(weaponRow2);
				if (weaponRow2.gameObject.activeSelf != flag)
				{
					weaponRow2.gameObject.SetActive(flag);
				}
			}
		}
		WeaponRandomizationMenu.UpdateAllSpawnProbabilities();
	}

	// Token: 0x04000D49 RID: 3401
	public static uint CachedTotalSpawnableWeight = 0U;

	// Token: 0x04000D4A RID: 3402
	[SerializeField]
	private VerticalLayoutGroup weaponList;

	// Token: 0x04000D4B RID: 3403
	[SerializeField]
	private GameObject weaponRowPrefab;

	// Token: 0x04000D4C RID: 3404
	[SerializeField]
	private GameObject menuButton;

	// Token: 0x04000D4D RID: 3405
	[SerializeField]
	private Canvas menuCanvas;

	// Token: 0x04000D4E RID: 3406
	[SerializeField]
	private TMP_Text presetName;

	// Token: 0x04000D4F RID: 3407
	[SerializeField]
	private TMP_InputField weaponNameInputField;

	// Token: 0x04000D50 RID: 3408
	private static bool toggleState = false;

	// Token: 0x04000D51 RID: 3409
	private static bool sliderMaxToggle = true;

	// Token: 0x04000D52 RID: 3410
	private static List<WeaponRow> weaponRows = new List<WeaponRow>();
}

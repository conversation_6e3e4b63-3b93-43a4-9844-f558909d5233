﻿using System;
using UnityEngine;

// Token: 0x0200011F RID: 287
public class AboubiCameraControl : MonoBehaviour
{
	// Token: 0x06000E17 RID: 3607 RVA: 0x0005E560 File Offset: 0x0005C760
	private void Start()
	{
		this.pauseManager = PauseManager.Instance;
	}

	// Token: 0x06000E18 RID: 3608 RVA: 0x0005E570 File Offset: 0x0005C770
	private void Update()
	{
		if (this.cam.enabled != (this.pauseManager.inMainMenu || this.pauseManager.tabScreen.activeSelf))
		{
			this.cam.enabled = this.pauseManager.inMainMenu || this.pauseManager.tabScreen.activeSelf;
		}
	}

	// Token: 0x04000C88 RID: 3208
	[SerializeField]
	private Camera cam;

	// Token: 0x04000C89 RID: 3209
	private PauseManager pauseManager;
}

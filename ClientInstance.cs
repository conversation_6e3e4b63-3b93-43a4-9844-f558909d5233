﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using FishNet;
using FishNet.Connection;
using FishNet.Managing;
using FishNet.Managing.Transporting;
using FishNet.Object;
using FishNet.Object.Delegating;
using FishNet.Serializing;
using FishNet.Transporting;
using FishySteamworks;
using HeathenEngineering.SteamworksIntegration;
using Steamworks;
using UnityEngine;
using UnityEngine.InputSystem;
using UnityEngine.SceneManagement;
using UnityEngine.Serialization;

// Token: 0x020000C2 RID: 194
public class ClientInstance : NetworkBehaviour
{
	// Token: 0x06000AC6 RID: 2758 RVA: 0x0004D124 File Offset: 0x0004B324
	public override void OnStartClient()
	{
		base.OnStartClient();
		bool flag = this.transportManager.Transport == this.networkManager.gameObject.GetComponent<global::FishySteamworks.FishySteamworks>();
		if (base.IsOwner)
		{
			this.playerControls = InputManager.inputActions;
			this.record = this.playerControls.Player.VoiceChat;
			this.record.Enable();
			ClientInstance.Instance = this;
			this.PlayerSpawner = base.GetComponent<PlayerManager>();
			this.pauseManager = PauseManager.Instance;
			if (!flag)
			{
				this.PlayerSpawner.PopulateSpawnPoints();
				this.PlayerSpawner.TryRespawn();
				return;
			}
			MatchChat.Instance.localPlayer = this;
			LobbyController.Instance.LocalPlayerObject = base.gameObject;
			LobbyController.Instance.LocalPlayerController = this;
			string personaName = SteamFriends.GetPersonaName();
			this.SetSyncValues(base.GetComponent<NetworkObject>(), personaName, null);
			if (base.IsServer)
			{
				GameManager.Instance.sync___set_value_playingTeams(SteamLobby.Instance.playingTeams, true);
			}
		}
		else if (flag)
		{
			SoundManager.Instance.PlaySound(this.joinSfx);
			if (SteamLobby.Instance.isInExplorationMap && InstanceFinder.NetworkManager.IsServer && global::UnityEngine.SceneManagement.SceneManager.GetActiveScene().name != "MainMenu")
			{
				SceneMotor.Instance.LeaveMatchForAll();
				MenuController.Instance.OpenGame();
				SceneMotor.Instance.testMap = false;
			}
			base.StartCoroutine(this.AddPlayerToHistory(this));
		}
		this.alreadySpawned = true;
	}

	// Token: 0x06000AC7 RID: 2759 RVA: 0x0004D29F File Offset: 0x0004B49F
	private IEnumerator AddPlayerToHistory(ClientInstance self)
	{
		yield return new WaitForSeconds(5f);
		if (SteamLobby.Instance.players.Count >= 2)
		{
			Settings.Instance.AddPlayerToHistory(self);
		}
		yield break;
	}

	// Token: 0x06000AC8 RID: 2760 RVA: 0x0004D2AE File Offset: 0x0004B4AE
	private IEnumerator StopPlayerRunning(int id)
	{
		yield return new WaitForSeconds(1f);
		this.MenuAnimationServer(0, id);
		yield break;
	}

	// Token: 0x06000AC9 RID: 2761 RVA: 0x0004D2C4 File Offset: 0x0004B4C4
	[ServerRpc]
	private void SetSyncValues(NetworkObject newPlayer, string name, NetworkConnection conn = null)
	{
		this.RpcWriter___Server_SetSyncValues_53007229(newPlayer, name, conn);
	}

	// Token: 0x06000ACA RID: 2762 RVA: 0x0004D2E3 File Offset: 0x0004B4E3
	[ObserversRpc]
	private void UpdateOnClients(int maxPlayers)
	{
		this.RpcWriter___Observers_UpdateOnClients_3316948804(maxPlayers);
	}

	// Token: 0x06000ACB RID: 2763 RVA: 0x0004D2EF File Offset: 0x0004B4EF
	[ContextMenu("UpdateOnClients")]
	public void UpdateOnClients()
	{
		LobbyController.Instance.UpdateLobbyName();
		LobbyController.Instance.UpdatePlayerList();
	}

	// Token: 0x06000ACC RID: 2764 RVA: 0x0004D308 File Offset: 0x0004B508
	[ObserversRpc(BufferLast = true)]
	private void AddNewPlayer(NetworkConnection owner, ulong steamId, NetworkObject newPlayer, int id, string playerName)
	{
		this.RpcWriter___Observers_AddNewPlayer_1978993739(owner, steamId, newPlayer, id, playerName);
	}

	// Token: 0x06000ACD RID: 2765 RVA: 0x0004D330 File Offset: 0x0004B530
	[ObserversRpc]
	private void RunIntoLobby(int id)
	{
		this.RpcWriter___Observers_RunIntoLobby_3316948804(id);
	}

	// Token: 0x06000ACE RID: 2766 RVA: 0x0004D347 File Offset: 0x0004B547
	[TargetRpc]
	private void InitiateClient(NetworkConnection conn, int id)
	{
		this.RpcWriter___Target_InitiateClient_2681120339(conn, id);
	}

	// Token: 0x06000ACF RID: 2767 RVA: 0x0004D357 File Offset: 0x0004B557
	[ServerRpc(RequireOwnership = false)]
	public void UpdateServerMaxPlayers()
	{
		this.RpcWriter___Server_UpdateServerMaxPlayers_2166136261();
	}

	// Token: 0x06000AD0 RID: 2768 RVA: 0x0004D35F File Offset: 0x0004B55F
	[ObserversRpc]
	private void UpdateObserversMaxPlayers(int maxPlayers)
	{
		this.RpcWriter___Observers_UpdateObserversMaxPlayers_3316948804(maxPlayers);
	}

	// Token: 0x06000AD1 RID: 2769 RVA: 0x0004D36B File Offset: 0x0004B56B
	public void ChangeReady()
	{
		this.ServerSetPlayerReady();
	}

	// Token: 0x06000AD2 RID: 2770 RVA: 0x0004D373 File Offset: 0x0004B573
	[ServerRpc(RequireOwnership = false)]
	private void ServerSetPlayerReady()
	{
		this.RpcWriter___Server_ServerSetPlayerReady_2166136261();
	}

	// Token: 0x06000AD3 RID: 2771 RVA: 0x0004D37B File Offset: 0x0004B57B
	[ServerRpc(RequireOwnership = false)]
	public void ServerSetPlayerReadyState(bool state)
	{
		this.RpcWriter___Server_ServerSetPlayerReadyState_1140765316(state);
	}

	// Token: 0x06000AD4 RID: 2772 RVA: 0x0004D387 File Offset: 0x0004B587
	[ObserversRpc(BufferLast = true)]
	private void PlayerReadyUpdate(bool newValue)
	{
		this.RpcWriter___Observers_PlayerReadyUpdate_1140765316(newValue);
	}

	// Token: 0x06000AD5 RID: 2773 RVA: 0x0004D393 File Offset: 0x0004B593
	[ObserversRpc]
	private void PlayerReadyUpdated()
	{
		this.RpcWriter___Observers_PlayerReadyUpdated_2166136261();
	}

	// Token: 0x06000AD6 RID: 2774 RVA: 0x0004D39C File Offset: 0x0004B59C
	private void OnDisable()
	{
		if (!PauseManager.Instance.inMainMenu || !PauseManager.Instance.inVictoryMenu)
		{
			GameManager.Instance.PlayerDied(this.PlayerId);
		}
		SteamLobby.Instance.players.Remove(base.GetComponent<NetworkObject>());
		LobbyController.Instance.UpdatePlayerList();
		if (this != LobbyController.Instance.LocalPlayerController && this.PlayerId == 0)
		{
			PauseManager.Instance.WriteOfflineLog(this.PlayerName + " left the lobby");
			SoundManager.Instance.PlaySound(this.leaveSfx);
		}
		ClientInstance.playerInstances.Remove(this.PlayerId);
		if (this != LobbyController.Instance.LocalPlayerController)
		{
			return;
		}
		SteamLobby.Instance.LeaveSteamLobby(true);
		ClientInstance.playerInstances.Clear();
		this.record.Disable();
	}

	// Token: 0x06000AD7 RID: 2775 RVA: 0x0004D47C File Offset: 0x0004B67C
	public virtual void Awake()
	{
		this.NetworkInitialize___Early();
		this.Awake___UserLogic();
		this.NetworkInitialize__Late();
	}

	// Token: 0x06000AD8 RID: 2776 RVA: 0x0004D49C File Offset: 0x0004B69C
	private void Update()
	{
		if (!base.IsOwner)
		{
			return;
		}
		this.VoiceChat();
		this.MenuAnimation();
		if (this.nonSteamworksTransport ? Input.GetKeyDown(KeyCode.G) : (Input.GetKey(KeyCode.LeftControl) && Input.GetKeyDown(KeyCode.Alpha9) && Application.isEditor))
		{
			this.PlayerSpawner.TryRespawn();
		}
	}

	// Token: 0x06000AD9 RID: 2777 RVA: 0x0004D4F9 File Offset: 0x0004B6F9
	[ServerRpc]
	public void DressAboubi(GameObject hat, int matIndex, int cigIndex, int id)
	{
		this.RpcWriter___Server_DressAboubi_2497120398(hat, matIndex, cigIndex, id);
	}

	// Token: 0x06000ADA RID: 2778 RVA: 0x0004D514 File Offset: 0x0004B714
	[ObserversRpc(BufferLast = true)]
	public void DressAboubiObservers(GameObject hat, int matIndex, int cigIndex, int id)
	{
		this.RpcWriter___Observers_DressAboubiObservers_2497120398(hat, matIndex, cigIndex, id);
	}

	// Token: 0x06000ADB RID: 2779 RVA: 0x0004D537 File Offset: 0x0004B737
	[ServerRpc]
	public void PlayVoiceChat(byte[] data)
	{
		this.RpcWriter___Server_PlayVoiceChat_3345084894(data);
	}

	// Token: 0x06000ADC RID: 2780 RVA: 0x0004D543 File Offset: 0x0004B743
	[ObserversRpc]
	public void PlayVoiceChatObservers(byte[] data)
	{
		this.RpcWriter___Observers_PlayVoiceChatObservers_3345084894(data);
	}

	// Token: 0x06000ADD RID: 2781 RVA: 0x0004D550 File Offset: 0x0004B750
	private void VoiceChat()
	{
		if ((this.record.ReadValue<float>() > 0.1f || !Settings.Instance.pushToTalk) && !this.pauseManager.chatting)
		{
			if (!this.pauseManager.inMainMenu && !(Camera.main != null))
			{
				this.vstreamRecorder.IsRecording = false;
				this.pauseManager.isRecording = false;
				return;
			}
			if (Settings.Instance.enableVoiceChat)
			{
				this.vstreamRecorder.IsRecording = true;
				this.pauseManager.isRecording = true;
				return;
			}
		}
		else
		{
			this.vstreamRecorder.IsRecording = false;
			this.pauseManager.isRecording = false;
		}
	}

	// Token: 0x06000ADE RID: 2782 RVA: 0x0004D5FC File Offset: 0x0004B7FC
	private void MenuAnimation()
	{
		for (int i = 0; i < 6; i++)
		{
			if (Input.GetKeyDown(KeyCode.Alpha1 + i))
			{
				this.MenuAnimationServer(i, this.PlayerId);
			}
		}
	}

	// Token: 0x06000ADF RID: 2783 RVA: 0x0004D62D File Offset: 0x0004B82D
	[ServerRpc(RequireOwnership = false)]
	public void MenuAnimationServer(int index, int id)
	{
		this.RpcWriter___Server_MenuAnimationServer_1692629761(index, id);
	}

	// Token: 0x06000AE0 RID: 2784 RVA: 0x0004D640 File Offset: 0x0004B840
	[ObserversRpc]
	public void MenuAnimationObservers(int index, int id)
	{
		this.RpcWriter___Observers_MenuAnimationObservers_1692629761(index, id);
	}

	// Token: 0x06000AE1 RID: 2785 RVA: 0x0004D65B File Offset: 0x0004B85B
	public void KickSelf()
	{
		if (InstanceFinder.NetworkManager.IsServer)
		{
			return;
		}
		PauseManager.Instance.WriteOfflineLog("You got kicked");
		SteamLobby.Instance.LeaveLobby();
	}

	// Token: 0x06000AE4 RID: 2788 RVA: 0x0004D6A0 File Offset: 0x0004B8A0
	public virtual void NetworkInitialize___Early()
	{
		if (this.NetworkInitializeEarly_ClientInstance_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeEarly_ClientInstance_Assembly-CSharp.dll = true;
		base.RegisterServerRpc(0U, new ServerRpcDelegate(this.RpcReader___Server_SetSyncValues_53007229));
		base.RegisterObserversRpc(1U, new ClientRpcDelegate(this.RpcReader___Observers_UpdateOnClients_3316948804));
		base.RegisterObserversRpc(2U, new ClientRpcDelegate(this.RpcReader___Observers_AddNewPlayer_1978993739));
		base.RegisterObserversRpc(3U, new ClientRpcDelegate(this.RpcReader___Observers_RunIntoLobby_3316948804));
		base.RegisterTargetRpc(4U, new ClientRpcDelegate(this.RpcReader___Target_InitiateClient_2681120339));
		base.RegisterServerRpc(5U, new ServerRpcDelegate(this.RpcReader___Server_UpdateServerMaxPlayers_2166136261));
		base.RegisterObserversRpc(6U, new ClientRpcDelegate(this.RpcReader___Observers_UpdateObserversMaxPlayers_3316948804));
		base.RegisterServerRpc(7U, new ServerRpcDelegate(this.RpcReader___Server_ServerSetPlayerReady_2166136261));
		base.RegisterServerRpc(8U, new ServerRpcDelegate(this.RpcReader___Server_ServerSetPlayerReadyState_1140765316));
		base.RegisterObserversRpc(9U, new ClientRpcDelegate(this.RpcReader___Observers_PlayerReadyUpdate_1140765316));
		base.RegisterObserversRpc(10U, new ClientRpcDelegate(this.RpcReader___Observers_PlayerReadyUpdated_2166136261));
		base.RegisterServerRpc(11U, new ServerRpcDelegate(this.RpcReader___Server_DressAboubi_2497120398));
		base.RegisterObserversRpc(12U, new ClientRpcDelegate(this.RpcReader___Observers_DressAboubiObservers_2497120398));
		base.RegisterServerRpc(13U, new ServerRpcDelegate(this.RpcReader___Server_PlayVoiceChat_3345084894));
		base.RegisterObserversRpc(14U, new ClientRpcDelegate(this.RpcReader___Observers_PlayVoiceChatObservers_3345084894));
		base.RegisterServerRpc(15U, new ServerRpcDelegate(this.RpcReader___Server_MenuAnimationServer_1692629761));
		base.RegisterObserversRpc(16U, new ClientRpcDelegate(this.RpcReader___Observers_MenuAnimationObservers_1692629761));
	}

	// Token: 0x06000AE5 RID: 2789 RVA: 0x0004D845 File Offset: 0x0004BA45
	public virtual void NetworkInitialize__Late()
	{
		if (this.NetworkInitializeLate_ClientInstance_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeLate_ClientInstance_Assembly-CSharp.dll = true;
	}

	// Token: 0x06000AE6 RID: 2790 RVA: 0x0004D858 File Offset: 0x0004BA58
	public override void NetworkInitializeIfDisabled()
	{
		this.NetworkInitialize___Early();
		this.NetworkInitialize__Late();
	}

	// Token: 0x06000AE7 RID: 2791 RVA: 0x0004D868 File Offset: 0x0004BA68
	private void RpcWriter___Server_SetSyncValues_53007229(NetworkObject newPlayer, string name, NetworkConnection conn = null)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteNetworkObject(newPlayer);
		writer.WriteString(name);
		base.SendServerRpc(0U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000AE8 RID: 2792 RVA: 0x0004D978 File Offset: 0x0004BB78
	private void RpcLogic___SetSyncValues_53007229(NetworkObject newPlayer, string name, NetworkConnection conn = null)
	{
		ulong num;
		if (!ulong.TryParse(conn.GetAddress(), out num))
		{
			Debug.LogError("Failed to parse SteamID from connection address?!?!?, somthing is wrong, or I made a bad guess about how any of this undocumented shit works.");
			return;
		}
		int num2 = 0;
		int[] array = (from x in global::UnityEngine.Object.FindObjectsOfType<ClientInstance>()
			select x.PlayerId).ToArray<int>();
		int num3 = array.Length;
		for (int i = 0; i < num3; i++)
		{
			if (!array.Contains(i))
			{
				num2 = i;
				break;
			}
		}
		this.AddNewPlayer(conn, num, newPlayer, num2, name);
		this.UpdateOnClients(SteamLobby.Instance.maxPlayers);
		this.RunIntoLobby(num2);
		this.InitiateClient(conn, num2);
	}

	// Token: 0x06000AE9 RID: 2793 RVA: 0x0004DA20 File Offset: 0x0004BC20
	private void RpcReader___Server_SetSyncValues_53007229(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		NetworkObject networkObject = PooledReader0.ReadNetworkObject();
		string text = PooledReader0.ReadString();
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		this.RpcLogic___SetSyncValues_53007229(networkObject, text, conn);
	}

	// Token: 0x06000AEA RID: 2794 RVA: 0x0004DA78 File Offset: 0x0004BC78
	private void RpcWriter___Observers_UpdateOnClients_3316948804(int maxPlayers)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteInt32(maxPlayers, AutoPackType.Packed);
		base.SendObserversRpc(1U, writer, channel, DataOrderType.Default, false, false, false);
		writer.Store();
	}

	// Token: 0x06000AEB RID: 2795 RVA: 0x0004DB33 File Offset: 0x0004BD33
	private void RpcLogic___UpdateOnClients_3316948804(int maxPlayers)
	{
		if (SteamLobby.Instance.players.Count > maxPlayers)
		{
			return;
		}
		SteamLobby.Instance.maxPlayers = maxPlayers;
		LobbyController.Instance.UpdateLobbyName();
		LobbyController.Instance.UpdatePlayerList();
	}

	// Token: 0x06000AEC RID: 2796 RVA: 0x0004DB68 File Offset: 0x0004BD68
	private void RpcReader___Observers_UpdateOnClients_3316948804(PooledReader PooledReader0, Channel channel)
	{
		int num = PooledReader0.ReadInt32(AutoPackType.Packed);
		if (!base.IsClient)
		{
			return;
		}
		this.RpcLogic___UpdateOnClients_3316948804(num);
	}

	// Token: 0x06000AED RID: 2797 RVA: 0x0004DBA0 File Offset: 0x0004BDA0
	private void RpcWriter___Observers_AddNewPlayer_1978993739(NetworkConnection owner, ulong steamId, NetworkObject newPlayer, int id, string playerName)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteNetworkConnection(owner);
		writer.WriteUInt64(steamId, AutoPackType.Packed);
		writer.WriteNetworkObject(newPlayer);
		writer.WriteInt32(id, AutoPackType.Packed);
		writer.WriteString(playerName);
		base.SendObserversRpc(2U, writer, channel, DataOrderType.Default, true, false, false);
		writer.Store();
	}

	// Token: 0x06000AEE RID: 2798 RVA: 0x0004DC94 File Offset: 0x0004BE94
	private void RpcLogic___AddNewPlayer_1978993739(NetworkConnection owner, ulong steamId, NetworkObject newPlayer, int id, string playerName)
	{
		this.PlayerId = id;
		this.ConnectionID = owner.ClientId;
		this.PlayerSteamID = steamId;
		SteamLobby.Instance.players.Add(newPlayer);
		ClientInstance.playerInstances.Add(id, this);
		playerName = Regex.Replace(playerName, "<size=\\d+>", "");
		playerName = Regex.Replace(playerName, "</size>", "");
		this.PlayerName = playerName;
		base.gameObject.name = this.PlayerName;
	}

	// Token: 0x06000AEF RID: 2799 RVA: 0x0004DD18 File Offset: 0x0004BF18
	private void RpcReader___Observers_AddNewPlayer_1978993739(PooledReader PooledReader0, Channel channel)
	{
		NetworkConnection networkConnection = PooledReader0.ReadNetworkConnection();
		ulong num = PooledReader0.ReadUInt64(AutoPackType.Packed);
		NetworkObject networkObject = PooledReader0.ReadNetworkObject();
		int num2 = PooledReader0.ReadInt32(AutoPackType.Packed);
		string text = PooledReader0.ReadString();
		if (!base.IsClient)
		{
			return;
		}
		this.RpcLogic___AddNewPlayer_1978993739(networkConnection, num, networkObject, num2, text);
	}

	// Token: 0x06000AF0 RID: 2800 RVA: 0x0004DD98 File Offset: 0x0004BF98
	private void RpcWriter___Observers_RunIntoLobby_3316948804(int id)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteInt32(id, AutoPackType.Packed);
		base.SendObserversRpc(3U, writer, channel, DataOrderType.Default, false, false, false);
		writer.Store();
	}

	// Token: 0x06000AF1 RID: 2801 RVA: 0x0004DE54 File Offset: 0x0004C054
	private void RpcLogic___RunIntoLobby_3316948804(int id)
	{
		AboubiPreviewLobby aboubiPreviewLobby = LobbyController.Instance.previews[id];
		if (!aboubiPreviewLobby)
		{
			Debug.LogError("Preview not found for player ID: " + id.ToString());
			return;
		}
		base.StartCoroutine(aboubiPreviewLobby.RunIntoLobby());
		base.StartCoroutine(this.StopPlayerRunning(id));
	}

	// Token: 0x06000AF2 RID: 2802 RVA: 0x0004DEA8 File Offset: 0x0004C0A8
	private void RpcReader___Observers_RunIntoLobby_3316948804(PooledReader PooledReader0, Channel channel)
	{
		int num = PooledReader0.ReadInt32(AutoPackType.Packed);
		if (!base.IsClient)
		{
			return;
		}
		this.RpcLogic___RunIntoLobby_3316948804(num);
	}

	// Token: 0x06000AF3 RID: 2803 RVA: 0x0004DEE0 File Offset: 0x0004C0E0
	private void RpcWriter___Target_InitiateClient_2681120339(NetworkConnection conn, int id)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteInt32(id, AutoPackType.Packed);
		base.SendTargetRpc(4U, writer, channel, DataOrderType.Default, conn, false, true);
		writer.Store();
	}

	// Token: 0x06000AF4 RID: 2804 RVA: 0x0004DF9A File Offset: 0x0004C19A
	private void RpcLogic___InitiateClient_2681120339(NetworkConnection conn, int id)
	{
		this.DressAboubi(CosmeticsManager.Instance.currenthat, CosmeticsManager.Instance.currentsuitIndex, CosmeticsManager.Instance.currentcigIndex, id);
		this.MenuAnimationServer(4, id);
	}

	// Token: 0x06000AF5 RID: 2805 RVA: 0x0004DFCC File Offset: 0x0004C1CC
	private void RpcReader___Target_InitiateClient_2681120339(PooledReader PooledReader0, Channel channel)
	{
		int num = PooledReader0.ReadInt32(AutoPackType.Packed);
		if (!base.IsClient)
		{
			return;
		}
		this.RpcLogic___InitiateClient_2681120339(base.LocalConnection, num);
	}

	// Token: 0x06000AF6 RID: 2806 RVA: 0x0004E008 File Offset: 0x0004C208
	private void RpcWriter___Server_UpdateServerMaxPlayers_2166136261()
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendServerRpc(5U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000AF7 RID: 2807 RVA: 0x0004E0A2 File Offset: 0x0004C2A2
	public void RpcLogic___UpdateServerMaxPlayers_2166136261()
	{
		this.UpdateObserversMaxPlayers(SteamLobby.Instance.maxPlayers);
	}

	// Token: 0x06000AF8 RID: 2808 RVA: 0x0004E0B4 File Offset: 0x0004C2B4
	private void RpcReader___Server_UpdateServerMaxPlayers_2166136261(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		if (!base.IsServer)
		{
			return;
		}
		this.RpcLogic___UpdateServerMaxPlayers_2166136261();
	}

	// Token: 0x06000AF9 RID: 2809 RVA: 0x0004E0D4 File Offset: 0x0004C2D4
	private void RpcWriter___Observers_UpdateObserversMaxPlayers_3316948804(int maxPlayers)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteInt32(maxPlayers, AutoPackType.Packed);
		base.SendObserversRpc(6U, writer, channel, DataOrderType.Default, false, false, false);
		writer.Store();
	}

	// Token: 0x06000AFA RID: 2810 RVA: 0x0004E18F File Offset: 0x0004C38F
	private void RpcLogic___UpdateObserversMaxPlayers_3316948804(int maxPlayers)
	{
		SteamLobby.Instance.maxPlayers = maxPlayers;
	}

	// Token: 0x06000AFB RID: 2811 RVA: 0x0004E19C File Offset: 0x0004C39C
	private void RpcReader___Observers_UpdateObserversMaxPlayers_3316948804(PooledReader PooledReader0, Channel channel)
	{
		int num = PooledReader0.ReadInt32(AutoPackType.Packed);
		if (!base.IsClient)
		{
			return;
		}
		this.RpcLogic___UpdateObserversMaxPlayers_3316948804(num);
	}

	// Token: 0x06000AFC RID: 2812 RVA: 0x0004E1D4 File Offset: 0x0004C3D4
	private void RpcWriter___Server_ServerSetPlayerReady_2166136261()
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendServerRpc(7U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000AFD RID: 2813 RVA: 0x0004E26E File Offset: 0x0004C46E
	private void RpcLogic___ServerSetPlayerReady_2166136261()
	{
		this.PlayerReadyUpdate(!this.Ready);
		this.PlayerReadyUpdated();
	}

	// Token: 0x06000AFE RID: 2814 RVA: 0x0004E288 File Offset: 0x0004C488
	private void RpcReader___Server_ServerSetPlayerReady_2166136261(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		if (!base.IsServer)
		{
			return;
		}
		this.RpcLogic___ServerSetPlayerReady_2166136261();
	}

	// Token: 0x06000AFF RID: 2815 RVA: 0x0004E2A8 File Offset: 0x0004C4A8
	private void RpcWriter___Server_ServerSetPlayerReadyState_1140765316(bool state)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteBoolean(state);
		base.SendServerRpc(8U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000B00 RID: 2816 RVA: 0x0004E34F File Offset: 0x0004C54F
	public void RpcLogic___ServerSetPlayerReadyState_1140765316(bool state)
	{
		this.PlayerReadyUpdate(state);
		this.PlayerReadyUpdated();
	}

	// Token: 0x06000B01 RID: 2817 RVA: 0x0004E360 File Offset: 0x0004C560
	private void RpcReader___Server_ServerSetPlayerReadyState_1140765316(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		bool flag = PooledReader0.ReadBoolean();
		if (!base.IsServer)
		{
			return;
		}
		this.RpcLogic___ServerSetPlayerReadyState_1140765316(flag);
	}

	// Token: 0x06000B02 RID: 2818 RVA: 0x0004E394 File Offset: 0x0004C594
	private void RpcWriter___Observers_PlayerReadyUpdate_1140765316(bool newValue)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteBoolean(newValue);
		base.SendObserversRpc(9U, writer, channel, DataOrderType.Default, true, false, false);
		writer.Store();
	}

	// Token: 0x06000B03 RID: 2819 RVA: 0x0004E44A File Offset: 0x0004C64A
	private void RpcLogic___PlayerReadyUpdate_1140765316(bool newValue)
	{
		this.Ready = newValue;
	}

	// Token: 0x06000B04 RID: 2820 RVA: 0x0004E454 File Offset: 0x0004C654
	private void RpcReader___Observers_PlayerReadyUpdate_1140765316(PooledReader PooledReader0, Channel channel)
	{
		bool flag = PooledReader0.ReadBoolean();
		if (!base.IsClient)
		{
			return;
		}
		this.RpcLogic___PlayerReadyUpdate_1140765316(flag);
	}

	// Token: 0x06000B05 RID: 2821 RVA: 0x0004E488 File Offset: 0x0004C688
	private void RpcWriter___Observers_PlayerReadyUpdated_2166136261()
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendObserversRpc(10U, writer, channel, DataOrderType.Default, false, false, false);
		writer.Store();
	}

	// Token: 0x06000B06 RID: 2822 RVA: 0x0004E531 File Offset: 0x0004C731
	private void RpcLogic___PlayerReadyUpdated_2166136261()
	{
		LobbyController.Instance.UpdatePlayerList();
	}

	// Token: 0x06000B07 RID: 2823 RVA: 0x0004E540 File Offset: 0x0004C740
	private void RpcReader___Observers_PlayerReadyUpdated_2166136261(PooledReader PooledReader0, Channel channel)
	{
		if (!base.IsClient)
		{
			return;
		}
		this.RpcLogic___PlayerReadyUpdated_2166136261();
	}

	// Token: 0x06000B08 RID: 2824 RVA: 0x0004E560 File Offset: 0x0004C760
	private void RpcWriter___Server_DressAboubi_2497120398(GameObject hat, int matIndex, int cigIndex, int id)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteGameObject(hat);
		writer.WriteInt32(matIndex, AutoPackType.Packed);
		writer.WriteInt32(cigIndex, AutoPackType.Packed);
		writer.WriteInt32(id, AutoPackType.Packed);
		base.SendServerRpc(11U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000B09 RID: 2825 RVA: 0x0004E697 File Offset: 0x0004C897
	public void RpcLogic___DressAboubi_2497120398(GameObject hat, int matIndex, int cigIndex, int id)
	{
		this.DressAboubiObservers(hat, matIndex, cigIndex, id);
	}

	// Token: 0x06000B0A RID: 2826 RVA: 0x0004E6A4 File Offset: 0x0004C8A4
	private void RpcReader___Server_DressAboubi_2497120398(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		GameObject gameObject = PooledReader0.ReadGameObject();
		int num = PooledReader0.ReadInt32(AutoPackType.Packed);
		int num2 = PooledReader0.ReadInt32(AutoPackType.Packed);
		int num3 = PooledReader0.ReadInt32(AutoPackType.Packed);
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		this.RpcLogic___DressAboubi_2497120398(gameObject, num, num2, num3);
	}

	// Token: 0x06000B0B RID: 2827 RVA: 0x0004E728 File Offset: 0x0004C928
	private void RpcWriter___Observers_DressAboubiObservers_2497120398(GameObject hat, int matIndex, int cigIndex, int id)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteGameObject(hat);
		writer.WriteInt32(matIndex, AutoPackType.Packed);
		writer.WriteInt32(cigIndex, AutoPackType.Packed);
		writer.WriteInt32(id, AutoPackType.Packed);
		base.SendObserversRpc(12U, writer, channel, DataOrderType.Default, true, false, false);
		writer.Store();
	}

	// Token: 0x06000B0C RID: 2828 RVA: 0x0004E814 File Offset: 0x0004CA14
	public void RpcLogic___DressAboubiObservers_2497120398(GameObject hat, int matIndex, int cigIndex, int id)
	{
		AboubiPreviewLobby aboubiPreviewLobby = LobbyController.Instance.previews[id];
		aboubiPreviewLobby.parentObj = base.gameObject;
		aboubiPreviewLobby.previewObject.SetActive(true);
		aboubiPreviewLobby.ChangeDress(hat, CosmeticsManager.Instance.mats[matIndex], CosmeticsManager.Instance.cigs[cigIndex]);
	}

	// Token: 0x06000B0D RID: 2829 RVA: 0x0004E864 File Offset: 0x0004CA64
	private void RpcReader___Observers_DressAboubiObservers_2497120398(PooledReader PooledReader0, Channel channel)
	{
		GameObject gameObject = PooledReader0.ReadGameObject();
		int num = PooledReader0.ReadInt32(AutoPackType.Packed);
		int num2 = PooledReader0.ReadInt32(AutoPackType.Packed);
		int num3 = PooledReader0.ReadInt32(AutoPackType.Packed);
		if (!base.IsClient)
		{
			return;
		}
		this.RpcLogic___DressAboubiObservers_2497120398(gameObject, num, num2, num3);
	}

	// Token: 0x06000B0E RID: 2830 RVA: 0x0004E8D8 File Offset: 0x0004CAD8
	private void RpcWriter___Server_PlayVoiceChat_3345084894(byte[] data)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteBytesAndSize(data);
		base.SendServerRpc(13U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000B0F RID: 2831 RVA: 0x0004E9D9 File Offset: 0x0004CBD9
	public void RpcLogic___PlayVoiceChat_3345084894(byte[] data)
	{
		this.PlayVoiceChatObservers(data);
	}

	// Token: 0x06000B10 RID: 2832 RVA: 0x0004E9E4 File Offset: 0x0004CBE4
	private void RpcReader___Server_PlayVoiceChat_3345084894(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		byte[] array = PooledReader0.ReadBytesAndSizeAllocated();
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		this.RpcLogic___PlayVoiceChat_3345084894(array);
	}

	// Token: 0x06000B11 RID: 2833 RVA: 0x0004EA28 File Offset: 0x0004CC28
	private void RpcWriter___Observers_PlayVoiceChatObservers_3345084894(byte[] data)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteBytesAndSize(data);
		base.SendObserversRpc(14U, writer, channel, DataOrderType.Default, false, false, false);
		writer.Store();
	}

	// Token: 0x06000B12 RID: 2834 RVA: 0x0004EADE File Offset: 0x0004CCDE
	public void RpcLogic___PlayVoiceChatObservers_3345084894(byte[] data)
	{
		if (!base.IsOwner)
		{
			this.vstream.PlayVoiceData(data);
		}
	}

	// Token: 0x06000B13 RID: 2835 RVA: 0x0004EAF4 File Offset: 0x0004CCF4
	private void RpcReader___Observers_PlayVoiceChatObservers_3345084894(PooledReader PooledReader0, Channel channel)
	{
		byte[] array = PooledReader0.ReadBytesAndSizeAllocated();
		if (!base.IsClient)
		{
			return;
		}
		this.RpcLogic___PlayVoiceChatObservers_3345084894(array);
	}

	// Token: 0x06000B14 RID: 2836 RVA: 0x0004EB28 File Offset: 0x0004CD28
	private void RpcWriter___Server_MenuAnimationServer_1692629761(int index, int id)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteInt32(index, AutoPackType.Packed);
		writer.WriteInt32(id, AutoPackType.Packed);
		base.SendServerRpc(15U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000B15 RID: 2837 RVA: 0x0004EBE6 File Offset: 0x0004CDE6
	public void RpcLogic___MenuAnimationServer_1692629761(int index, int id)
	{
		this.MenuAnimationObservers(index, id);
	}

	// Token: 0x06000B16 RID: 2838 RVA: 0x0004EBF0 File Offset: 0x0004CDF0
	private void RpcReader___Server_MenuAnimationServer_1692629761(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		int num = PooledReader0.ReadInt32(AutoPackType.Packed);
		int num2 = PooledReader0.ReadInt32(AutoPackType.Packed);
		if (!base.IsServer)
		{
			return;
		}
		this.RpcLogic___MenuAnimationServer_1692629761(num, num2);
	}

	// Token: 0x06000B17 RID: 2839 RVA: 0x0004EC3C File Offset: 0x0004CE3C
	private void RpcWriter___Observers_MenuAnimationObservers_1692629761(int index, int id)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteInt32(index, AutoPackType.Packed);
		writer.WriteInt32(id, AutoPackType.Packed);
		base.SendObserversRpc(16U, writer, channel, DataOrderType.Default, false, false, false);
		writer.Store();
	}

	// Token: 0x06000B18 RID: 2840 RVA: 0x0004ED0C File Offset: 0x0004CF0C
	public void RpcLogic___MenuAnimationObservers_1692629761(int index, int id)
	{
		Animator componentInChildren = LobbyController.Instance.previews[id].GetComponentInChildren<Animator>(true);
		switch (index)
		{
		case 0:
			componentInChildren.SetBool("Crouch", false);
			componentInChildren.SetBool("Slide", false);
			componentInChildren.SetFloat("MovementSpeed", 0f);
			componentInChildren.SetFloat("crouchMove", 0f);
			return;
		case 1:
			componentInChildren.SetBool("Crouch", true);
			componentInChildren.SetBool("Slide", false);
			componentInChildren.SetFloat("MovementSpeed", 0f);
			componentInChildren.SetFloat("crouchMove", 0f);
			return;
		case 2:
			componentInChildren.SetBool("Crouch", false);
			componentInChildren.SetBool("Slide", true);
			componentInChildren.SetFloat("MovementSpeed", 0f);
			componentInChildren.SetFloat("crouchMove", 0f);
			return;
		case 3:
			componentInChildren.SetBool("Crouch", false);
			componentInChildren.SetBool("Slide", false);
			componentInChildren.SetFloat("MovementSpeed", 0.5f);
			componentInChildren.SetFloat("crouchMove", 0f);
			return;
		case 4:
			componentInChildren.SetBool("Crouch", false);
			componentInChildren.SetBool("Slide", false);
			componentInChildren.SetFloat("MovementSpeed", 1f);
			componentInChildren.SetFloat("crouchMove", 0f);
			return;
		case 5:
			componentInChildren.SetBool("Crouch", true);
			componentInChildren.SetBool("Slide", false);
			componentInChildren.SetFloat("MovementSpeed", 0f);
			componentInChildren.SetFloat("crouchMove", 1f);
			return;
		default:
			return;
		}
	}

	// Token: 0x06000B19 RID: 2841 RVA: 0x0004EEA0 File Offset: 0x0004D0A0
	private void RpcReader___Observers_MenuAnimationObservers_1692629761(PooledReader PooledReader0, Channel channel)
	{
		int num = PooledReader0.ReadInt32(AutoPackType.Packed);
		int num2 = PooledReader0.ReadInt32(AutoPackType.Packed);
		if (!base.IsClient)
		{
			return;
		}
		this.RpcLogic___MenuAnimationObservers_1692629761(num, num2);
	}

	// Token: 0x06000B1A RID: 2842 RVA: 0x0004EEEC File Offset: 0x0004D0EC
	public virtual void Awake___UserLogic()
	{
		this.PlayerSpawner = base.GetComponent<PlayerManager>();
		this.networkManager = InstanceFinder.NetworkManager;
		this.transportManager = InstanceFinder.TransportManager;
		this.nonSteamworksTransport = this.transportManager.Transport != this.networkManager.gameObject.GetComponent<global::FishySteamworks.FishySteamworks>();
	}

	// Token: 0x04000944 RID: 2372
	public static ClientInstance Instance;

	// Token: 0x04000945 RID: 2373
	public static Dictionary<int, ClientInstance> playerInstances = new Dictionary<int, ClientInstance>();

	// Token: 0x04000946 RID: 2374
	public PlayerManager PlayerSpawner;

	// Token: 0x04000947 RID: 2375
	public bool alreadySpawned;

	// Token: 0x04000948 RID: 2376
	public int ConnectionID;

	// Token: 0x04000949 RID: 2377
	[FormerlySerializedAs("PlayerIdNumber")]
	public int PlayerId = -1;

	// Token: 0x0400094A RID: 2378
	public ulong PlayerSteamID;

	// Token: 0x0400094B RID: 2379
	public string PlayerName;

	// Token: 0x0400094C RID: 2380
	public bool Ready;

	// Token: 0x0400094D RID: 2381
	private NetworkManager networkManager;

	// Token: 0x0400094E RID: 2382
	private TransportManager transportManager;

	// Token: 0x0400094F RID: 2383
	private PauseManager pauseManager;

	// Token: 0x04000950 RID: 2384
	[HideInInspector]
	public bool nonSteamworksTransport;

	// Token: 0x04000951 RID: 2385
	private global::UnityEngine.InputSystem.InputAction record;

	// Token: 0x04000952 RID: 2386
	private PlayerControls playerControls;

	// Token: 0x04000953 RID: 2387
	[SerializeField]
	private AudioClip joinSfx;

	// Token: 0x04000954 RID: 2388
	[SerializeField]
	private AudioClip leaveSfx;

	// Token: 0x04000955 RID: 2389
	[SerializeField]
	private VoiceStream vstream;

	// Token: 0x04000956 RID: 2390
	[SerializeField]
	private VoiceRecorder vstreamRecorder;

	// Token: 0x04000957 RID: 2391
	public AudioSource voiceChatSource;

	// Token: 0x04000958 RID: 2392
	private bool NetworkInitializeEarly_ClientInstance_Assembly-CSharp.dll;

	// Token: 0x04000959 RID: 2393
	private bool NetworkInitializeLate_ClientInstance_Assembly-CSharp.dll;
}

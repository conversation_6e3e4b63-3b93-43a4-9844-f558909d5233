﻿using System;
using FishNet.Object;
using UnityEngine;

// Token: 0x0200006A RID: 106
public class Pot : NetworkObject
{
	// Token: 0x060004B4 RID: 1204 RVA: 0x00020343 File Offset: 0x0001E543
	public void Die()
	{
		global::UnityEngine.Object.Destroy(base.gameObject);
		global::UnityEngine.Object.Instantiate<GameObject>(this.vfx, base.transform.position, Quaternion.Euler(-90f, 0f, 0f));
	}

	// Token: 0x040004CD RID: 1229
	[SerializeField]
	private GameObject vfx;
}

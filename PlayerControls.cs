﻿using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.InputSystem;
using UnityEngine.InputSystem.Utilities;

// Token: 0x02000011 RID: 17
public class PlayerControls : IInputActionCollection2, IInputActionCollection, IEnumerable<InputAction>, IEnumerable, IDisposable
{
	// Token: 0x17000007 RID: 7
	// (get) Token: 0x0600006D RID: 109 RVA: 0x000041D6 File Offset: 0x000023D6
	public InputActionAsset asset { get; }

	// Token: 0x0600006E RID: 110 RVA: 0x000041E0 File Offset: 0x000023E0
	public PlayerControls()
	{
		this.asset = InputActionAsset.FromJson("{\r\n    \"name\": \"PlayerControls\",\r\n    \"maps\": [\r\n        {\r\n            \"name\": \"Player\",\r\n            \"id\": \"179457bb-2cfb-4c0d-ad53-96fd7ab76ac4\",\r\n            \"actions\": [\r\n                {\r\n                    \"name\": \"Move\",\r\n                    \"type\": \"Value\",\r\n                    \"id\": \"321658bb-4ef2-4b28-937f-297e62ca5ce6\",\r\n                    \"expectedControlType\": \"Vector2\",\r\n                    \"processors\": \"\",\r\n                    \"interactions\": \"\",\r\n                    \"initialStateCheck\": true\r\n                },\r\n                {\r\n                    \"name\": \"LeftClick\",\r\n                    \"type\": \"Button\",\r\n                    \"id\": \"a7b4cf33-a673-4de2-af0d-6b4794c2bb84\",\r\n                    \"expectedControlType\": \"Button\",\r\n                    \"processors\": \"\",\r\n                    \"interactions\": \"\",\r\n                    \"initialStateCheck\": false\r\n                },\r\n                {\r\n                    \"name\": \"RightClick\",\r\n                    \"type\": \"Button\",\r\n                    \"id\": \"4df23e9c-6428-481f-a5eb-9aad8f0ff574\",\r\n                    \"expectedControlType\": \"Button\",\r\n                    \"processors\": \"\",\r\n                    \"interactions\": \"\",\r\n                    \"initialStateCheck\": false\r\n                },\r\n                {\r\n                    \"name\": \"FireHold\",\r\n                    \"type\": \"Value\",\r\n                    \"id\": \"ef0f886b-d683-4a59-8c59-09cb3caf078a\",\r\n                    \"expectedControlType\": \"Button\",\r\n                    \"processors\": \"\",\r\n                    \"interactions\": \"\",\r\n                    \"initialStateCheck\": true\r\n                },\r\n                {\r\n                    \"name\": \"Jump\",\r\n                    \"type\": \"Button\",\r\n                    \"id\": \"4e601573-0ec5-4a6a-8902-cfadbc65564a\",\r\n                    \"expectedControlType\": \"Button\",\r\n                    \"processors\": \"\",\r\n                    \"interactions\": \"\",\r\n                    \"initialStateCheck\": false\r\n                },\r\n                {\r\n                    \"name\": \"MoveUp\",\r\n                    \"type\": \"Value\",\r\n                    \"id\": \"911cafa0-00bf-422f-9aed-cfd4ad527817\",\r\n                    \"expectedControlType\": \"Button\",\r\n                    \"processors\": \"\",\r\n                    \"interactions\": \"\",\r\n                    \"initialStateCheck\": true\r\n                },\r\n                {\r\n                    \"name\": \"Run\",\r\n                    \"type\": \"Value\",\r\n                    \"id\": \"7936f870-e440-4af5-a61a-705629facdc8\",\r\n                    \"expectedControlType\": \"Axis\",\r\n                    \"processors\": \"\",\r\n                    \"interactions\": \"\",\r\n                    \"initialStateCheck\": true\r\n                },\r\n                {\r\n                    \"name\": \"MouseX\",\r\n                    \"type\": \"PassThrough\",\r\n                    \"id\": \"dad6e2a6-0656-4eb1-8f66-e00c58311fe0\",\r\n                    \"expectedControlType\": \"Axis\",\r\n                    \"processors\": \"\",\r\n                    \"interactions\": \"\",\r\n                    \"initialStateCheck\": false\r\n                },\r\n                {\r\n                    \"name\": \"MouseY\",\r\n                    \"type\": \"PassThrough\",\r\n                    \"id\": \"2a419f02-204c-4d5b-9a8b-d78d0194db58\",\r\n                    \"expectedControlType\": \"Axis\",\r\n                    \"processors\": \"\",\r\n                    \"interactions\": \"\",\r\n                    \"initialStateCheck\": false\r\n                },\r\n                {\r\n                    \"name\": \"Zoom\",\r\n                    \"type\": \"Button\",\r\n                    \"id\": \"29119d52-76a8-4bd5-ab1b-36c3693beb52\",\r\n                    \"expectedControlType\": \"Button\",\r\n                    \"processors\": \"\",\r\n                    \"interactions\": \"\",\r\n                    \"initialStateCheck\": false\r\n                },\r\n                {\r\n                    \"name\": \"Crouch\",\r\n                    \"type\": \"Button\",\r\n                    \"id\": \"a4f9be33-43a5-488d-afa2-4e59a02ecefb\",\r\n                    \"expectedControlType\": \"Button\",\r\n                    \"processors\": \"\",\r\n                    \"interactions\": \"\",\r\n                    \"initialStateCheck\": false\r\n                },\r\n                {\r\n                    \"name\": \"Interact\",\r\n                    \"type\": \"Button\",\r\n                    \"id\": \"e8f328c7-57b0-4806-9b14-3164e2be6f63\",\r\n                    \"expectedControlType\": \"Button\",\r\n                    \"processors\": \"\",\r\n                    \"interactions\": \"\",\r\n                    \"initialStateCheck\": false\r\n                },\r\n                {\r\n                    \"name\": \"Drop\",\r\n                    \"type\": \"Button\",\r\n                    \"id\": \"a5b57028-9d2e-4517-a881-474507172d9b\",\r\n                    \"expectedControlType\": \"Button\",\r\n                    \"processors\": \"\",\r\n                    \"interactions\": \"\",\r\n                    \"initialStateCheck\": false\r\n                },\r\n                {\r\n                    \"name\": \"Reload\",\r\n                    \"type\": \"Button\",\r\n                    \"id\": \"258d79dc-8ff1-4efb-95f7-84352b10815e\",\r\n                    \"expectedControlType\": \"Button\",\r\n                    \"processors\": \"\",\r\n                    \"interactions\": \"\",\r\n                    \"initialStateCheck\": false\r\n                },\r\n                {\r\n                    \"name\": \"LeanRight\",\r\n                    \"type\": \"Value\",\r\n                    \"id\": \"bdd22510-c0c4-4e88-9328-8140796b1048\",\r\n                    \"expectedControlType\": \"Button\",\r\n                    \"processors\": \"\",\r\n                    \"interactions\": \"\",\r\n                    \"initialStateCheck\": true\r\n                },\r\n                {\r\n                    \"name\": \"LeanLeft\",\r\n                    \"type\": \"Value\",\r\n                    \"id\": \"93c45d32-047e-4811-a5c6-09b9f540acf2\",\r\n                    \"expectedControlType\": \"Button\",\r\n                    \"processors\": \"\",\r\n                    \"interactions\": \"\",\r\n                    \"initialStateCheck\": true\r\n                },\r\n                {\r\n                    \"name\": \"ChangeWeapon\",\r\n                    \"type\": \"Value\",\r\n                    \"id\": \"a3f79029-802d-437c-b3c0-3b90fc8ffe35\",\r\n                    \"expectedControlType\": \"Vector2\",\r\n                    \"processors\": \"\",\r\n                    \"interactions\": \"\",\r\n                    \"initialStateCheck\": true\r\n                },\r\n                {\r\n                    \"name\": \"VoiceChat\",\r\n                    \"type\": \"Value\",\r\n                    \"id\": \"7b4185a2-c332-4cea-9334-3753c911dc1b\",\r\n                    \"expectedControlType\": \"Button\",\r\n                    \"processors\": \"\",\r\n                    \"interactions\": \"\",\r\n                    \"initialStateCheck\": true\r\n                },\r\n                {\r\n                    \"name\": \"Any\",\r\n                    \"type\": \"Button\",\r\n                    \"id\": \"75122081-0404-4998-a12d-6d9526f595da\",\r\n                    \"expectedControlType\": \"Button\",\r\n                    \"processors\": \"\",\r\n                    \"interactions\": \"\",\r\n                    \"initialStateCheck\": false\r\n                },\r\n                {\r\n                    \"name\": \"AnyGamepad\",\r\n                    \"type\": \"Button\",\r\n                    \"id\": \"3c2c13ae-2010-4990-a4ec-6f2a497e2a78\",\r\n                    \"expectedControlType\": \"Button\",\r\n                    \"processors\": \"\",\r\n                    \"interactions\": \"\",\r\n                    \"initialStateCheck\": false\r\n                }\r\n            ],\r\n            \"bindings\": [\r\n                {\r\n                    \"name\": \"WASD\",\r\n                    \"id\": \"00ca640b-d935-4593-8157-c05846ea39b3\",\r\n                    \"path\": \"Dpad\",\r\n                    \"interactions\": \"\",\r\n                    \"processors\": \"\",\r\n                    \"groups\": \"\",\r\n                    \"action\": \"Move\",\r\n                    \"isComposite\": true,\r\n                    \"isPartOfComposite\": false\r\n                },\r\n                {\r\n                    \"name\": \"up\",\r\n                    \"id\": \"e2062cb9-1b15-46a2-838c-2f8d72a0bdd9\",\r\n                    \"path\": \"<Keyboard>/w\",\r\n                    \"interactions\": \"\",\r\n                    \"processors\": \"\",\r\n                    \"groups\": \";Keyboard&Mouse\",\r\n                    \"action\": \"Move\",\r\n                    \"isComposite\": false,\r\n                    \"isPartOfComposite\": true\r\n                },\r\n                {\r\n                    \"name\": \"down\",\r\n                    \"id\": \"320bffee-a40b-4347-ac70-c210eb8bc73a\",\r\n                    \"path\": \"<Keyboard>/s\",\r\n                    \"interactions\": \"\",\r\n                    \"processors\": \"\",\r\n                    \"groups\": \";Keyboard&Mouse\",\r\n                    \"action\": \"Move\",\r\n                    \"isComposite\": false,\r\n                    \"isPartOfComposite\": true\r\n                },\r\n                {\r\n                    \"name\": \"left\",\r\n                    \"id\": \"d2581a9b-1d11-4566-b27d-b92aff5fabbc\",\r\n                    \"path\": \"<Keyboard>/a\",\r\n                    \"interactions\": \"\",\r\n                    \"processors\": \"\",\r\n                    \"groups\": \";Keyboard&Mouse\",\r\n                    \"action\": \"Move\",\r\n                    \"isComposite\": false,\r\n                    \"isPartOfComposite\": true\r\n                },\r\n                {\r\n                    \"name\": \"right\",\r\n                    \"id\": \"fcfe95b8-67b9-4526-84b5-5d0bc98d6400\",\r\n                    \"path\": \"<Keyboard>/d\",\r\n                    \"interactions\": \"\",\r\n                    \"processors\": \"\",\r\n                    \"groups\": \";Keyboard&Mouse\",\r\n                    \"action\": \"Move\",\r\n                    \"isComposite\": false,\r\n                    \"isPartOfComposite\": true\r\n                },\r\n                {\r\n                    \"name\": \"Gamepad\",\r\n                    \"id\": \"88951db5-fd39-4195-a698-97decb79f444\",\r\n                    \"path\": \"Dpad\",\r\n                    \"interactions\": \"\",\r\n                    \"processors\": \"\",\r\n                    \"groups\": \"\",\r\n                    \"action\": \"Move\",\r\n                    \"isComposite\": true,\r\n                    \"isPartOfComposite\": false\r\n                },\r\n                {\r\n                    \"name\": \"up\",\r\n                    \"id\": \"dbe829e2-959c-4a84-8324-83b4987605a2\",\r\n                    \"path\": \"<Gamepad>/leftStick/up\",\r\n                    \"interactions\": \"\",\r\n                    \"processors\": \"\",\r\n                    \"groups\": \"Gamepad\",\r\n                    \"action\": \"Move\",\r\n                    \"isComposite\": false,\r\n                    \"isPartOfComposite\": true\r\n                },\r\n                {\r\n                    \"name\": \"down\",\r\n                    \"id\": \"45995e3a-7ed1-4bd2-bd39-cb6c5974206a\",\r\n                    \"path\": \"<Gamepad>/leftStick/down\",\r\n                    \"interactions\": \"\",\r\n                    \"processors\": \"\",\r\n                    \"groups\": \"Gamepad\",\r\n                    \"action\": \"Move\",\r\n                    \"isComposite\": false,\r\n                    \"isPartOfComposite\": true\r\n                },\r\n                {\r\n                    \"name\": \"left\",\r\n                    \"id\": \"4165b590-fbfc-4c04-93ab-3e4f2ae9e3a5\",\r\n                    \"path\": \"<Gamepad>/leftStick/left\",\r\n                    \"interactions\": \"\",\r\n                    \"processors\": \"\",\r\n                    \"groups\": \"Gamepad\",\r\n                    \"action\": \"Move\",\r\n                    \"isComposite\": false,\r\n                    \"isPartOfComposite\": true\r\n                },\r\n                {\r\n                    \"name\": \"right\",\r\n                    \"id\": \"59237484-f267-44ed-854b-a57b8020a1fa\",\r\n                    \"path\": \"<Gamepad>/leftStick/right\",\r\n                    \"interactions\": \"\",\r\n                    \"processors\": \"\",\r\n                    \"groups\": \"Gamepad\",\r\n                    \"action\": \"Move\",\r\n                    \"isComposite\": false,\r\n                    \"isPartOfComposite\": true\r\n                },\r\n                {\r\n                    \"name\": \"\",\r\n                    \"id\": \"143bb1cd-cc10-4eca-a2f0-a3664166fe91\",\r\n                    \"path\": \"<Gamepad>/rightTrigger\",\r\n                    \"interactions\": \"\",\r\n                    \"processors\": \"\",\r\n                    \"groups\": \"\",\r\n                    \"action\": \"LeftClick\",\r\n                    \"isComposite\": false,\r\n                    \"isPartOfComposite\": false\r\n                },\r\n                {\r\n                    \"name\": \"\",\r\n                    \"id\": \"05f6913d-c316-48b2-a6bb-e225f14c7960\",\r\n                    \"path\": \"<Mouse>/leftButton\",\r\n                    \"interactions\": \"\",\r\n                    \"processors\": \"\",\r\n                    \"groups\": \";Keyboard&Mouse\",\r\n                    \"action\": \"LeftClick\",\r\n                    \"isComposite\": false,\r\n                    \"isPartOfComposite\": false\r\n                },\r\n                {\r\n                    \"name\": \"\",\r\n                    \"id\": \"886e731e-7071-4ae4-95c0-e61739dad6fd\",\r\n                    \"path\": \"<Touchscreen>/primaryTouch/tap\",\r\n                    \"interactions\": \"\",\r\n                    \"processors\": \"\",\r\n                    \"groups\": \";Touch\",\r\n                    \"action\": \"LeftClick\",\r\n                    \"isComposite\": false,\r\n                    \"isPartOfComposite\": false\r\n                },\r\n                {\r\n                    \"name\": \"\",\r\n                    \"id\": \"ee3d0cd2-254e-47a7-a8cb-bc94d9658c54\",\r\n                    \"path\": \"<Joystick>/trigger\",\r\n                    \"interactions\": \"\",\r\n                    \"processors\": \"\",\r\n                    \"groups\": \"Joystick\",\r\n                    \"action\": \"LeftClick\",\r\n                    \"isComposite\": false,\r\n                    \"isPartOfComposite\": false\r\n                },\r\n                {\r\n                    \"name\": \"\",\r\n                    \"id\": \"8255d333-5683-4943-a58a-ccb207ff1dce\",\r\n                    \"path\": \"<XRController>/{PrimaryAction}\",\r\n                    \"interactions\": \"\",\r\n                    \"processors\": \"\",\r\n                    \"groups\": \"XR\",\r\n                    \"action\": \"LeftClick\",\r\n                    \"isComposite\": false,\r\n                    \"isPartOfComposite\": false\r\n                },\r\n                {\r\n                    \"name\": \"\",\r\n                    \"id\": \"46d08182-1407-47ba-9c81-fbeec310fad3\",\r\n                    \"path\": \"<Keyboard>/space\",\r\n                    \"interactions\": \"\",\r\n                    \"processors\": \"\",\r\n                    \"groups\": \"Keyboard&Mouse\",\r\n                    \"action\": \"Jump\",\r\n                    \"isComposite\": false,\r\n                    \"isPartOfComposite\": false\r\n                },\r\n                {\r\n                    \"name\": \"\",\r\n                    \"id\": \"ebb90c5a-c512-4c14-a0c6-eaee792a038b\",\r\n                    \"path\": \"<Gamepad>/buttonSouth\",\r\n                    \"interactions\": \"\",\r\n                    \"processors\": \"\",\r\n                    \"groups\": \"Gamepad\",\r\n                    \"action\": \"Jump\",\r\n                    \"isComposite\": false,\r\n                    \"isPartOfComposite\": false\r\n                },\r\n                {\r\n                    \"name\": \"\",\r\n                    \"id\": \"1f3c511f-eed1-4c38-84cf-7d4068f71bb8\",\r\n                    \"path\": \"<Keyboard>/leftShift\",\r\n                    \"interactions\": \"\",\r\n                    \"processors\": \"\",\r\n                    \"groups\": \"\",\r\n                    \"action\": \"Run\",\r\n                    \"isComposite\": false,\r\n                    \"isPartOfComposite\": false\r\n                },\r\n                {\r\n                    \"name\": \"\",\r\n                    \"id\": \"380f2a77-bf6b-4dba-918b-a37714d94ef0\",\r\n                    \"path\": \"<Gamepad>/leftStickPress\",\r\n                    \"interactions\": \"\",\r\n                    \"processors\": \"\",\r\n                    \"groups\": \"Gamepad\",\r\n                    \"action\": \"Run\",\r\n                    \"isComposite\": false,\r\n                    \"isPartOfComposite\": false\r\n                },\r\n                {\r\n                    \"name\": \"\",\r\n                    \"id\": \"1435765a-fd5e-4407-9a21-2724fd0912b0\",\r\n                    \"path\": \"<Mouse>/delta/x\",\r\n                    \"interactions\": \"\",\r\n                    \"processors\": \"\",\r\n                    \"groups\": \"\",\r\n                    \"action\": \"MouseX\",\r\n                    \"isComposite\": false,\r\n                    \"isPartOfComposite\": false\r\n                },\r\n                {\r\n                    \"name\": \"\",\r\n                    \"id\": \"907c24a1-b7e7-4398-995b-23dcb9cccc02\",\r\n                    \"path\": \"<Gamepad>/rightStick/x\",\r\n                    \"interactions\": \"\",\r\n                    \"processors\": \"\",\r\n                    \"groups\": \"Gamepad\",\r\n                    \"action\": \"MouseX\",\r\n                    \"isComposite\": false,\r\n                    \"isPartOfComposite\": false\r\n                },\r\n                {\r\n                    \"name\": \"\",\r\n                    \"id\": \"0acebc2d-53bb-46a1-9125-17b9d1163cb1\",\r\n                    \"path\": \"<Mouse>/delta/y\",\r\n                    \"interactions\": \"\",\r\n                    \"processors\": \"\",\r\n                    \"groups\": \"\",\r\n                    \"action\": \"MouseY\",\r\n                    \"isComposite\": false,\r\n                    \"isPartOfComposite\": false\r\n                },\r\n                {\r\n                    \"name\": \"\",\r\n                    \"id\": \"1761be3e-1659-4499-8874-c3b042c72e55\",\r\n                    \"path\": \"<XInputController>/rightStick/y\",\r\n                    \"interactions\": \"\",\r\n                    \"processors\": \"\",\r\n                    \"groups\": \"Gamepad\",\r\n                    \"action\": \"MouseY\",\r\n                    \"isComposite\": false,\r\n                    \"isPartOfComposite\": false\r\n                },\r\n                {\r\n                    \"name\": \"\",\r\n                    \"id\": \"9e1ed1ad-a6af-4d47-b4cb-d69c1997010a\",\r\n                    \"path\": \"<Mouse>/rightButton\",\r\n                    \"interactions\": \"\",\r\n                    \"processors\": \"\",\r\n                    \"groups\": \"\",\r\n                    \"action\": \"Zoom\",\r\n                    \"isComposite\": false,\r\n                    \"isPartOfComposite\": false\r\n                },\r\n                {\r\n                    \"name\": \"\",\r\n                    \"id\": \"09ce4312-885d-4ca8-b3e3-b0ef27d85473\",\r\n                    \"path\": \"<Gamepad>/leftTrigger\",\r\n                    \"interactions\": \"\",\r\n                    \"processors\": \"\",\r\n                    \"groups\": \"Gamepad\",\r\n                    \"action\": \"Zoom\",\r\n                    \"isComposite\": false,\r\n                    \"isPartOfComposite\": false\r\n                },\r\n                {\r\n                    \"name\": \"\",\r\n                    \"id\": \"ed418e85-49bb-4fd1-ba32-9f82c2f0d663\",\r\n                    \"path\": \"<Keyboard>/ctrl\",\r\n                    \"interactions\": \"\",\r\n                    \"processors\": \"\",\r\n                    \"groups\": \"\",\r\n                    \"action\": \"Crouch\",\r\n                    \"isComposite\": false,\r\n                    \"isPartOfComposite\": false\r\n                },\r\n                {\r\n                    \"name\": \"\",\r\n                    \"id\": \"cfa380b5-2bf5-45c3-a37b-1f50b8ad9306\",\r\n                    \"path\": \"<Gamepad>/buttonEast\",\r\n                    \"interactions\": \"\",\r\n                    \"processors\": \"\",\r\n                    \"groups\": \"Gamepad\",\r\n                    \"action\": \"Crouch\",\r\n                    \"isComposite\": false,\r\n                    \"isPartOfComposite\": false\r\n                },\r\n                {\r\n                    \"name\": \"\",\r\n                    \"id\": \"3a978059-e218-49d7-8b2a-7747ac611899\",\r\n                    \"path\": \"<Mouse>/rightButton\",\r\n                    \"interactions\": \"\",\r\n                    \"processors\": \"\",\r\n                    \"groups\": \"Keyboard&Mouse\",\r\n                    \"action\": \"RightClick\",\r\n                    \"isComposite\": false,\r\n                    \"isPartOfComposite\": false\r\n             [...string is too long...]");
		this.m_Player = this.asset.FindActionMap("Player", true);
		this.m_Player_Move = this.m_Player.FindAction("Move", true);
		this.m_Player_LeftClick = this.m_Player.FindAction("LeftClick", true);
		this.m_Player_RightClick = this.m_Player.FindAction("RightClick", true);
		this.m_Player_FireHold = this.m_Player.FindAction("FireHold", true);
		this.m_Player_Jump = this.m_Player.FindAction("Jump", true);
		this.m_Player_MoveUp = this.m_Player.FindAction("MoveUp", true);
		this.m_Player_Run = this.m_Player.FindAction("Run", true);
		this.m_Player_MouseX = this.m_Player.FindAction("MouseX", true);
		this.m_Player_MouseY = this.m_Player.FindAction("MouseY", true);
		this.m_Player_Zoom = this.m_Player.FindAction("Zoom", true);
		this.m_Player_Crouch = this.m_Player.FindAction("Crouch", true);
		this.m_Player_Interact = this.m_Player.FindAction("Interact", true);
		this.m_Player_Drop = this.m_Player.FindAction("Drop", true);
		this.m_Player_Reload = this.m_Player.FindAction("Reload", true);
		this.m_Player_LeanRight = this.m_Player.FindAction("LeanRight", true);
		this.m_Player_LeanLeft = this.m_Player.FindAction("LeanLeft", true);
		this.m_Player_ChangeWeapon = this.m_Player.FindAction("ChangeWeapon", true);
		this.m_Player_VoiceChat = this.m_Player.FindAction("VoiceChat", true);
		this.m_Player_Any = this.m_Player.FindAction("Any", true);
		this.m_Player_AnyGamepad = this.m_Player.FindAction("AnyGamepad", true);
		this.m_UI = this.asset.FindActionMap("UI", true);
		this.m_UI_Navigate = this.m_UI.FindAction("Navigate", true);
		this.m_UI_Submit = this.m_UI.FindAction("Submit", true);
		this.m_UI_Cancel = this.m_UI.FindAction("Cancel", true);
		this.m_UI_Point = this.m_UI.FindAction("Point", true);
		this.m_UI_Click = this.m_UI.FindAction("Click", true);
		this.m_UI_ScrollWheel = this.m_UI.FindAction("ScrollWheel", true);
		this.m_UI_MiddleClick = this.m_UI.FindAction("MiddleClick", true);
		this.m_UI_RightClick = this.m_UI.FindAction("RightClick", true);
		this.m_UI_TrackedDevicePosition = this.m_UI.FindAction("TrackedDevicePosition", true);
		this.m_UI_TrackedDeviceOrientation = this.m_UI.FindAction("TrackedDeviceOrientation", true);
	}

	// Token: 0x0600006F RID: 111 RVA: 0x00004506 File Offset: 0x00002706
	public void Dispose()
	{
		global::UnityEngine.Object.Destroy(this.asset);
	}

	// Token: 0x17000008 RID: 8
	// (get) Token: 0x06000070 RID: 112 RVA: 0x00004513 File Offset: 0x00002713
	// (set) Token: 0x06000071 RID: 113 RVA: 0x00004520 File Offset: 0x00002720
	public InputBinding? bindingMask
	{
		get
		{
			return this.asset.bindingMask;
		}
		set
		{
			this.asset.bindingMask = value;
		}
	}

	// Token: 0x17000009 RID: 9
	// (get) Token: 0x06000072 RID: 114 RVA: 0x0000452E File Offset: 0x0000272E
	// (set) Token: 0x06000073 RID: 115 RVA: 0x0000453B File Offset: 0x0000273B
	public ReadOnlyArray<InputDevice>? devices
	{
		get
		{
			return this.asset.devices;
		}
		set
		{
			this.asset.devices = value;
		}
	}

	// Token: 0x1700000A RID: 10
	// (get) Token: 0x06000074 RID: 116 RVA: 0x00004549 File Offset: 0x00002749
	public ReadOnlyArray<InputControlScheme> controlSchemes
	{
		get
		{
			return this.asset.controlSchemes;
		}
	}

	// Token: 0x06000075 RID: 117 RVA: 0x00004556 File Offset: 0x00002756
	public bool Contains(InputAction action)
	{
		return this.asset.Contains(action);
	}

	// Token: 0x06000076 RID: 118 RVA: 0x00004564 File Offset: 0x00002764
	public IEnumerator<InputAction> GetEnumerator()
	{
		return this.asset.GetEnumerator();
	}

	// Token: 0x06000077 RID: 119 RVA: 0x00004571 File Offset: 0x00002771
	IEnumerator IEnumerable.GetEnumerator()
	{
		return this.GetEnumerator();
	}

	// Token: 0x06000078 RID: 120 RVA: 0x00004579 File Offset: 0x00002779
	public void Enable()
	{
		this.asset.Enable();
	}

	// Token: 0x06000079 RID: 121 RVA: 0x00004586 File Offset: 0x00002786
	public void Disable()
	{
		this.asset.Disable();
	}

	// Token: 0x1700000B RID: 11
	// (get) Token: 0x0600007A RID: 122 RVA: 0x00004593 File Offset: 0x00002793
	public IEnumerable<InputBinding> bindings
	{
		get
		{
			return this.asset.bindings;
		}
	}

	// Token: 0x0600007B RID: 123 RVA: 0x000045A0 File Offset: 0x000027A0
	public InputAction FindAction(string actionNameOrId, bool throwIfNotFound = false)
	{
		return this.asset.FindAction(actionNameOrId, throwIfNotFound);
	}

	// Token: 0x0600007C RID: 124 RVA: 0x000045AF File Offset: 0x000027AF
	public int FindBinding(InputBinding bindingMask, out InputAction action)
	{
		return this.asset.FindBinding(bindingMask, out action);
	}

	// Token: 0x1700000C RID: 12
	// (get) Token: 0x0600007D RID: 125 RVA: 0x000045BE File Offset: 0x000027BE
	public PlayerControls.PlayerActions Player
	{
		get
		{
			return new PlayerControls.PlayerActions(this);
		}
	}

	// Token: 0x1700000D RID: 13
	// (get) Token: 0x0600007E RID: 126 RVA: 0x000045C6 File Offset: 0x000027C6
	public PlayerControls.UIActions UI
	{
		get
		{
			return new PlayerControls.UIActions(this);
		}
	}

	// Token: 0x1700000E RID: 14
	// (get) Token: 0x0600007F RID: 127 RVA: 0x000045D0 File Offset: 0x000027D0
	public InputControlScheme KeyboardMouseScheme
	{
		get
		{
			if (this.m_KeyboardMouseSchemeIndex == -1)
			{
				this.m_KeyboardMouseSchemeIndex = this.asset.FindControlSchemeIndex("Keyboard&Mouse");
			}
			return this.asset.controlSchemes[this.m_KeyboardMouseSchemeIndex];
		}
	}

	// Token: 0x1700000F RID: 15
	// (get) Token: 0x06000080 RID: 128 RVA: 0x00004618 File Offset: 0x00002818
	public InputControlScheme GamepadScheme
	{
		get
		{
			if (this.m_GamepadSchemeIndex == -1)
			{
				this.m_GamepadSchemeIndex = this.asset.FindControlSchemeIndex("Gamepad");
			}
			return this.asset.controlSchemes[this.m_GamepadSchemeIndex];
		}
	}

	// Token: 0x17000010 RID: 16
	// (get) Token: 0x06000081 RID: 129 RVA: 0x00004660 File Offset: 0x00002860
	public InputControlScheme TouchScheme
	{
		get
		{
			if (this.m_TouchSchemeIndex == -1)
			{
				this.m_TouchSchemeIndex = this.asset.FindControlSchemeIndex("Touch");
			}
			return this.asset.controlSchemes[this.m_TouchSchemeIndex];
		}
	}

	// Token: 0x17000011 RID: 17
	// (get) Token: 0x06000082 RID: 130 RVA: 0x000046A8 File Offset: 0x000028A8
	public InputControlScheme JoystickScheme
	{
		get
		{
			if (this.m_JoystickSchemeIndex == -1)
			{
				this.m_JoystickSchemeIndex = this.asset.FindControlSchemeIndex("Joystick");
			}
			return this.asset.controlSchemes[this.m_JoystickSchemeIndex];
		}
	}

	// Token: 0x17000012 RID: 18
	// (get) Token: 0x06000083 RID: 131 RVA: 0x000046F0 File Offset: 0x000028F0
	public InputControlScheme XRScheme
	{
		get
		{
			if (this.m_XRSchemeIndex == -1)
			{
				this.m_XRSchemeIndex = this.asset.FindControlSchemeIndex("XR");
			}
			return this.asset.controlSchemes[this.m_XRSchemeIndex];
		}
	}

	// Token: 0x0400007B RID: 123
	private readonly InputActionMap m_Player;

	// Token: 0x0400007C RID: 124
	private PlayerControls.IPlayerActions m_PlayerActionsCallbackInterface;

	// Token: 0x0400007D RID: 125
	private readonly InputAction m_Player_Move;

	// Token: 0x0400007E RID: 126
	private readonly InputAction m_Player_LeftClick;

	// Token: 0x0400007F RID: 127
	private readonly InputAction m_Player_RightClick;

	// Token: 0x04000080 RID: 128
	private readonly InputAction m_Player_FireHold;

	// Token: 0x04000081 RID: 129
	private readonly InputAction m_Player_Jump;

	// Token: 0x04000082 RID: 130
	private readonly InputAction m_Player_MoveUp;

	// Token: 0x04000083 RID: 131
	private readonly InputAction m_Player_Run;

	// Token: 0x04000084 RID: 132
	private readonly InputAction m_Player_MouseX;

	// Token: 0x04000085 RID: 133
	private readonly InputAction m_Player_MouseY;

	// Token: 0x04000086 RID: 134
	private readonly InputAction m_Player_Zoom;

	// Token: 0x04000087 RID: 135
	private readonly InputAction m_Player_Crouch;

	// Token: 0x04000088 RID: 136
	private readonly InputAction m_Player_Interact;

	// Token: 0x04000089 RID: 137
	private readonly InputAction m_Player_Drop;

	// Token: 0x0400008A RID: 138
	private readonly InputAction m_Player_Reload;

	// Token: 0x0400008B RID: 139
	private readonly InputAction m_Player_LeanRight;

	// Token: 0x0400008C RID: 140
	private readonly InputAction m_Player_LeanLeft;

	// Token: 0x0400008D RID: 141
	private readonly InputAction m_Player_ChangeWeapon;

	// Token: 0x0400008E RID: 142
	private readonly InputAction m_Player_VoiceChat;

	// Token: 0x0400008F RID: 143
	private readonly InputAction m_Player_Any;

	// Token: 0x04000090 RID: 144
	private readonly InputAction m_Player_AnyGamepad;

	// Token: 0x04000091 RID: 145
	private readonly InputActionMap m_UI;

	// Token: 0x04000092 RID: 146
	private PlayerControls.IUIActions m_UIActionsCallbackInterface;

	// Token: 0x04000093 RID: 147
	private readonly InputAction m_UI_Navigate;

	// Token: 0x04000094 RID: 148
	private readonly InputAction m_UI_Submit;

	// Token: 0x04000095 RID: 149
	private readonly InputAction m_UI_Cancel;

	// Token: 0x04000096 RID: 150
	private readonly InputAction m_UI_Point;

	// Token: 0x04000097 RID: 151
	private readonly InputAction m_UI_Click;

	// Token: 0x04000098 RID: 152
	private readonly InputAction m_UI_ScrollWheel;

	// Token: 0x04000099 RID: 153
	private readonly InputAction m_UI_MiddleClick;

	// Token: 0x0400009A RID: 154
	private readonly InputAction m_UI_RightClick;

	// Token: 0x0400009B RID: 155
	private readonly InputAction m_UI_TrackedDevicePosition;

	// Token: 0x0400009C RID: 156
	private readonly InputAction m_UI_TrackedDeviceOrientation;

	// Token: 0x0400009D RID: 157
	private int m_KeyboardMouseSchemeIndex = -1;

	// Token: 0x0400009E RID: 158
	private int m_GamepadSchemeIndex = -1;

	// Token: 0x0400009F RID: 159
	private int m_TouchSchemeIndex = -1;

	// Token: 0x040000A0 RID: 160
	private int m_JoystickSchemeIndex = -1;

	// Token: 0x040000A1 RID: 161
	private int m_XRSchemeIndex = -1;

	// Token: 0x02000012 RID: 18
	public struct PlayerActions
	{
		// Token: 0x06000084 RID: 132 RVA: 0x00004735 File Offset: 0x00002935
		public PlayerActions(PlayerControls wrapper)
		{
			this.m_Wrapper = wrapper;
		}

		// Token: 0x17000013 RID: 19
		// (get) Token: 0x06000085 RID: 133 RVA: 0x0000473E File Offset: 0x0000293E
		public InputAction Move
		{
			get
			{
				return this.m_Wrapper.m_Player_Move;
			}
		}

		// Token: 0x17000014 RID: 20
		// (get) Token: 0x06000086 RID: 134 RVA: 0x0000474B File Offset: 0x0000294B
		public InputAction LeftClick
		{
			get
			{
				return this.m_Wrapper.m_Player_LeftClick;
			}
		}

		// Token: 0x17000015 RID: 21
		// (get) Token: 0x06000087 RID: 135 RVA: 0x00004758 File Offset: 0x00002958
		public InputAction RightClick
		{
			get
			{
				return this.m_Wrapper.m_Player_RightClick;
			}
		}

		// Token: 0x17000016 RID: 22
		// (get) Token: 0x06000088 RID: 136 RVA: 0x00004765 File Offset: 0x00002965
		public InputAction FireHold
		{
			get
			{
				return this.m_Wrapper.m_Player_FireHold;
			}
		}

		// Token: 0x17000017 RID: 23
		// (get) Token: 0x06000089 RID: 137 RVA: 0x00004772 File Offset: 0x00002972
		public InputAction Jump
		{
			get
			{
				return this.m_Wrapper.m_Player_Jump;
			}
		}

		// Token: 0x17000018 RID: 24
		// (get) Token: 0x0600008A RID: 138 RVA: 0x0000477F File Offset: 0x0000297F
		public InputAction MoveUp
		{
			get
			{
				return this.m_Wrapper.m_Player_MoveUp;
			}
		}

		// Token: 0x17000019 RID: 25
		// (get) Token: 0x0600008B RID: 139 RVA: 0x0000478C File Offset: 0x0000298C
		public InputAction Run
		{
			get
			{
				return this.m_Wrapper.m_Player_Run;
			}
		}

		// Token: 0x1700001A RID: 26
		// (get) Token: 0x0600008C RID: 140 RVA: 0x00004799 File Offset: 0x00002999
		public InputAction MouseX
		{
			get
			{
				return this.m_Wrapper.m_Player_MouseX;
			}
		}

		// Token: 0x1700001B RID: 27
		// (get) Token: 0x0600008D RID: 141 RVA: 0x000047A6 File Offset: 0x000029A6
		public InputAction MouseY
		{
			get
			{
				return this.m_Wrapper.m_Player_MouseY;
			}
		}

		// Token: 0x1700001C RID: 28
		// (get) Token: 0x0600008E RID: 142 RVA: 0x000047B3 File Offset: 0x000029B3
		public InputAction Zoom
		{
			get
			{
				return this.m_Wrapper.m_Player_Zoom;
			}
		}

		// Token: 0x1700001D RID: 29
		// (get) Token: 0x0600008F RID: 143 RVA: 0x000047C0 File Offset: 0x000029C0
		public InputAction Crouch
		{
			get
			{
				return this.m_Wrapper.m_Player_Crouch;
			}
		}

		// Token: 0x1700001E RID: 30
		// (get) Token: 0x06000090 RID: 144 RVA: 0x000047CD File Offset: 0x000029CD
		public InputAction Interact
		{
			get
			{
				return this.m_Wrapper.m_Player_Interact;
			}
		}

		// Token: 0x1700001F RID: 31
		// (get) Token: 0x06000091 RID: 145 RVA: 0x000047DA File Offset: 0x000029DA
		public InputAction Drop
		{
			get
			{
				return this.m_Wrapper.m_Player_Drop;
			}
		}

		// Token: 0x17000020 RID: 32
		// (get) Token: 0x06000092 RID: 146 RVA: 0x000047E7 File Offset: 0x000029E7
		public InputAction Reload
		{
			get
			{
				return this.m_Wrapper.m_Player_Reload;
			}
		}

		// Token: 0x17000021 RID: 33
		// (get) Token: 0x06000093 RID: 147 RVA: 0x000047F4 File Offset: 0x000029F4
		public InputAction LeanRight
		{
			get
			{
				return this.m_Wrapper.m_Player_LeanRight;
			}
		}

		// Token: 0x17000022 RID: 34
		// (get) Token: 0x06000094 RID: 148 RVA: 0x00004801 File Offset: 0x00002A01
		public InputAction LeanLeft
		{
			get
			{
				return this.m_Wrapper.m_Player_LeanLeft;
			}
		}

		// Token: 0x17000023 RID: 35
		// (get) Token: 0x06000095 RID: 149 RVA: 0x0000480E File Offset: 0x00002A0E
		public InputAction ChangeWeapon
		{
			get
			{
				return this.m_Wrapper.m_Player_ChangeWeapon;
			}
		}

		// Token: 0x17000024 RID: 36
		// (get) Token: 0x06000096 RID: 150 RVA: 0x0000481B File Offset: 0x00002A1B
		public InputAction VoiceChat
		{
			get
			{
				return this.m_Wrapper.m_Player_VoiceChat;
			}
		}

		// Token: 0x17000025 RID: 37
		// (get) Token: 0x06000097 RID: 151 RVA: 0x00004828 File Offset: 0x00002A28
		public InputAction Any
		{
			get
			{
				return this.m_Wrapper.m_Player_Any;
			}
		}

		// Token: 0x17000026 RID: 38
		// (get) Token: 0x06000098 RID: 152 RVA: 0x00004835 File Offset: 0x00002A35
		public InputAction AnyGamepad
		{
			get
			{
				return this.m_Wrapper.m_Player_AnyGamepad;
			}
		}

		// Token: 0x06000099 RID: 153 RVA: 0x00004842 File Offset: 0x00002A42
		public InputActionMap Get()
		{
			return this.m_Wrapper.m_Player;
		}

		// Token: 0x0600009A RID: 154 RVA: 0x0000484F File Offset: 0x00002A4F
		public void Enable()
		{
			this.Get().Enable();
		}

		// Token: 0x0600009B RID: 155 RVA: 0x0000485C File Offset: 0x00002A5C
		public void Disable()
		{
			this.Get().Disable();
		}

		// Token: 0x17000027 RID: 39
		// (get) Token: 0x0600009C RID: 156 RVA: 0x00004869 File Offset: 0x00002A69
		public bool enabled
		{
			get
			{
				return this.Get().enabled;
			}
		}

		// Token: 0x0600009D RID: 157 RVA: 0x00004876 File Offset: 0x00002A76
		public static implicit operator InputActionMap(PlayerControls.PlayerActions set)
		{
			return set.Get();
		}

		// Token: 0x0600009E RID: 158 RVA: 0x00004880 File Offset: 0x00002A80
		public void SetCallbacks(PlayerControls.IPlayerActions instance)
		{
			if (this.m_Wrapper.m_PlayerActionsCallbackInterface != null)
			{
				this.Move.started -= this.m_Wrapper.m_PlayerActionsCallbackInterface.OnMove;
				this.Move.performed -= this.m_Wrapper.m_PlayerActionsCallbackInterface.OnMove;
				this.Move.canceled -= this.m_Wrapper.m_PlayerActionsCallbackInterface.OnMove;
				this.LeftClick.started -= this.m_Wrapper.m_PlayerActionsCallbackInterface.OnLeftClick;
				this.LeftClick.performed -= this.m_Wrapper.m_PlayerActionsCallbackInterface.OnLeftClick;
				this.LeftClick.canceled -= this.m_Wrapper.m_PlayerActionsCallbackInterface.OnLeftClick;
				this.RightClick.started -= this.m_Wrapper.m_PlayerActionsCallbackInterface.OnRightClick;
				this.RightClick.performed -= this.m_Wrapper.m_PlayerActionsCallbackInterface.OnRightClick;
				this.RightClick.canceled -= this.m_Wrapper.m_PlayerActionsCallbackInterface.OnRightClick;
				this.FireHold.started -= this.m_Wrapper.m_PlayerActionsCallbackInterface.OnFireHold;
				this.FireHold.performed -= this.m_Wrapper.m_PlayerActionsCallbackInterface.OnFireHold;
				this.FireHold.canceled -= this.m_Wrapper.m_PlayerActionsCallbackInterface.OnFireHold;
				this.Jump.started -= this.m_Wrapper.m_PlayerActionsCallbackInterface.OnJump;
				this.Jump.performed -= this.m_Wrapper.m_PlayerActionsCallbackInterface.OnJump;
				this.Jump.canceled -= this.m_Wrapper.m_PlayerActionsCallbackInterface.OnJump;
				this.MoveUp.started -= this.m_Wrapper.m_PlayerActionsCallbackInterface.OnMoveUp;
				this.MoveUp.performed -= this.m_Wrapper.m_PlayerActionsCallbackInterface.OnMoveUp;
				this.MoveUp.canceled -= this.m_Wrapper.m_PlayerActionsCallbackInterface.OnMoveUp;
				this.Run.started -= this.m_Wrapper.m_PlayerActionsCallbackInterface.OnRun;
				this.Run.performed -= this.m_Wrapper.m_PlayerActionsCallbackInterface.OnRun;
				this.Run.canceled -= this.m_Wrapper.m_PlayerActionsCallbackInterface.OnRun;
				this.MouseX.started -= this.m_Wrapper.m_PlayerActionsCallbackInterface.OnMouseX;
				this.MouseX.performed -= this.m_Wrapper.m_PlayerActionsCallbackInterface.OnMouseX;
				this.MouseX.canceled -= this.m_Wrapper.m_PlayerActionsCallbackInterface.OnMouseX;
				this.MouseY.started -= this.m_Wrapper.m_PlayerActionsCallbackInterface.OnMouseY;
				this.MouseY.performed -= this.m_Wrapper.m_PlayerActionsCallbackInterface.OnMouseY;
				this.MouseY.canceled -= this.m_Wrapper.m_PlayerActionsCallbackInterface.OnMouseY;
				this.Zoom.started -= this.m_Wrapper.m_PlayerActionsCallbackInterface.OnZoom;
				this.Zoom.performed -= this.m_Wrapper.m_PlayerActionsCallbackInterface.OnZoom;
				this.Zoom.canceled -= this.m_Wrapper.m_PlayerActionsCallbackInterface.OnZoom;
				this.Crouch.started -= this.m_Wrapper.m_PlayerActionsCallbackInterface.OnCrouch;
				this.Crouch.performed -= this.m_Wrapper.m_PlayerActionsCallbackInterface.OnCrouch;
				this.Crouch.canceled -= this.m_Wrapper.m_PlayerActionsCallbackInterface.OnCrouch;
				this.Interact.started -= this.m_Wrapper.m_PlayerActionsCallbackInterface.OnInteract;
				this.Interact.performed -= this.m_Wrapper.m_PlayerActionsCallbackInterface.OnInteract;
				this.Interact.canceled -= this.m_Wrapper.m_PlayerActionsCallbackInterface.OnInteract;
				this.Drop.started -= this.m_Wrapper.m_PlayerActionsCallbackInterface.OnDrop;
				this.Drop.performed -= this.m_Wrapper.m_PlayerActionsCallbackInterface.OnDrop;
				this.Drop.canceled -= this.m_Wrapper.m_PlayerActionsCallbackInterface.OnDrop;
				this.Reload.started -= this.m_Wrapper.m_PlayerActionsCallbackInterface.OnReload;
				this.Reload.performed -= this.m_Wrapper.m_PlayerActionsCallbackInterface.OnReload;
				this.Reload.canceled -= this.m_Wrapper.m_PlayerActionsCallbackInterface.OnReload;
				this.LeanRight.started -= this.m_Wrapper.m_PlayerActionsCallbackInterface.OnLeanRight;
				this.LeanRight.performed -= this.m_Wrapper.m_PlayerActionsCallbackInterface.OnLeanRight;
				this.LeanRight.canceled -= this.m_Wrapper.m_PlayerActionsCallbackInterface.OnLeanRight;
				this.LeanLeft.started -= this.m_Wrapper.m_PlayerActionsCallbackInterface.OnLeanLeft;
				this.LeanLeft.performed -= this.m_Wrapper.m_PlayerActionsCallbackInterface.OnLeanLeft;
				this.LeanLeft.canceled -= this.m_Wrapper.m_PlayerActionsCallbackInterface.OnLeanLeft;
				this.ChangeWeapon.started -= this.m_Wrapper.m_PlayerActionsCallbackInterface.OnChangeWeapon;
				this.ChangeWeapon.performed -= this.m_Wrapper.m_PlayerActionsCallbackInterface.OnChangeWeapon;
				this.ChangeWeapon.canceled -= this.m_Wrapper.m_PlayerActionsCallbackInterface.OnChangeWeapon;
				this.VoiceChat.started -= this.m_Wrapper.m_PlayerActionsCallbackInterface.OnVoiceChat;
				this.VoiceChat.performed -= this.m_Wrapper.m_PlayerActionsCallbackInterface.OnVoiceChat;
				this.VoiceChat.canceled -= this.m_Wrapper.m_PlayerActionsCallbackInterface.OnVoiceChat;
				this.Any.started -= this.m_Wrapper.m_PlayerActionsCallbackInterface.OnAny;
				this.Any.performed -= this.m_Wrapper.m_PlayerActionsCallbackInterface.OnAny;
				this.Any.canceled -= this.m_Wrapper.m_PlayerActionsCallbackInterface.OnAny;
				this.AnyGamepad.started -= this.m_Wrapper.m_PlayerActionsCallbackInterface.OnAnyGamepad;
				this.AnyGamepad.performed -= this.m_Wrapper.m_PlayerActionsCallbackInterface.OnAnyGamepad;
				this.AnyGamepad.canceled -= this.m_Wrapper.m_PlayerActionsCallbackInterface.OnAnyGamepad;
			}
			this.m_Wrapper.m_PlayerActionsCallbackInterface = instance;
			if (instance != null)
			{
				this.Move.started += instance.OnMove;
				this.Move.performed += instance.OnMove;
				this.Move.canceled += instance.OnMove;
				this.LeftClick.started += instance.OnLeftClick;
				this.LeftClick.performed += instance.OnLeftClick;
				this.LeftClick.canceled += instance.OnLeftClick;
				this.RightClick.started += instance.OnRightClick;
				this.RightClick.performed += instance.OnRightClick;
				this.RightClick.canceled += instance.OnRightClick;
				this.FireHold.started += instance.OnFireHold;
				this.FireHold.performed += instance.OnFireHold;
				this.FireHold.canceled += instance.OnFireHold;
				this.Jump.started += instance.OnJump;
				this.Jump.performed += instance.OnJump;
				this.Jump.canceled += instance.OnJump;
				this.MoveUp.started += instance.OnMoveUp;
				this.MoveUp.performed += instance.OnMoveUp;
				this.MoveUp.canceled += instance.OnMoveUp;
				this.Run.started += instance.OnRun;
				this.Run.performed += instance.OnRun;
				this.Run.canceled += instance.OnRun;
				this.MouseX.started += instance.OnMouseX;
				this.MouseX.performed += instance.OnMouseX;
				this.MouseX.canceled += instance.OnMouseX;
				this.MouseY.started += instance.OnMouseY;
				this.MouseY.performed += instance.OnMouseY;
				this.MouseY.canceled += instance.OnMouseY;
				this.Zoom.started += instance.OnZoom;
				this.Zoom.performed += instance.OnZoom;
				this.Zoom.canceled += instance.OnZoom;
				this.Crouch.started += instance.OnCrouch;
				this.Crouch.performed += instance.OnCrouch;
				this.Crouch.canceled += instance.OnCrouch;
				this.Interact.started += instance.OnInteract;
				this.Interact.performed += instance.OnInteract;
				this.Interact.canceled += instance.OnInteract;
				this.Drop.started += instance.OnDrop;
				this.Drop.performed += instance.OnDrop;
				this.Drop.canceled += instance.OnDrop;
				this.Reload.started += instance.OnReload;
				this.Reload.performed += instance.OnReload;
				this.Reload.canceled += instance.OnReload;
				this.LeanRight.started += instance.OnLeanRight;
				this.LeanRight.performed += instance.OnLeanRight;
				this.LeanRight.canceled += instance.OnLeanRight;
				this.LeanLeft.started += instance.OnLeanLeft;
				this.LeanLeft.performed += instance.OnLeanLeft;
				this.LeanLeft.canceled += instance.OnLeanLeft;
				this.ChangeWeapon.started += instance.OnChangeWeapon;
				this.ChangeWeapon.performed += instance.OnChangeWeapon;
				this.ChangeWeapon.canceled += instance.OnChangeWeapon;
				this.VoiceChat.started += instance.OnVoiceChat;
				this.VoiceChat.performed += instance.OnVoiceChat;
				this.VoiceChat.canceled += instance.OnVoiceChat;
				this.Any.started += instance.OnAny;
				this.Any.performed += instance.OnAny;
				this.Any.canceled += instance.OnAny;
				this.AnyGamepad.started += instance.OnAnyGamepad;
				this.AnyGamepad.performed += instance.OnAnyGamepad;
				this.AnyGamepad.canceled += instance.OnAnyGamepad;
			}
		}

		// Token: 0x040000A2 RID: 162
		private PlayerControls m_Wrapper;
	}

	// Token: 0x02000013 RID: 19
	public struct UIActions
	{
		// Token: 0x0600009F RID: 159 RVA: 0x00005647 File Offset: 0x00003847
		public UIActions(PlayerControls wrapper)
		{
			this.m_Wrapper = wrapper;
		}

		// Token: 0x17000028 RID: 40
		// (get) Token: 0x060000A0 RID: 160 RVA: 0x00005650 File Offset: 0x00003850
		public InputAction Navigate
		{
			get
			{
				return this.m_Wrapper.m_UI_Navigate;
			}
		}

		// Token: 0x17000029 RID: 41
		// (get) Token: 0x060000A1 RID: 161 RVA: 0x0000565D File Offset: 0x0000385D
		public InputAction Submit
		{
			get
			{
				return this.m_Wrapper.m_UI_Submit;
			}
		}

		// Token: 0x1700002A RID: 42
		// (get) Token: 0x060000A2 RID: 162 RVA: 0x0000566A File Offset: 0x0000386A
		public InputAction Cancel
		{
			get
			{
				return this.m_Wrapper.m_UI_Cancel;
			}
		}

		// Token: 0x1700002B RID: 43
		// (get) Token: 0x060000A3 RID: 163 RVA: 0x00005677 File Offset: 0x00003877
		public InputAction Point
		{
			get
			{
				return this.m_Wrapper.m_UI_Point;
			}
		}

		// Token: 0x1700002C RID: 44
		// (get) Token: 0x060000A4 RID: 164 RVA: 0x00005684 File Offset: 0x00003884
		public InputAction Click
		{
			get
			{
				return this.m_Wrapper.m_UI_Click;
			}
		}

		// Token: 0x1700002D RID: 45
		// (get) Token: 0x060000A5 RID: 165 RVA: 0x00005691 File Offset: 0x00003891
		public InputAction ScrollWheel
		{
			get
			{
				return this.m_Wrapper.m_UI_ScrollWheel;
			}
		}

		// Token: 0x1700002E RID: 46
		// (get) Token: 0x060000A6 RID: 166 RVA: 0x0000569E File Offset: 0x0000389E
		public InputAction MiddleClick
		{
			get
			{
				return this.m_Wrapper.m_UI_MiddleClick;
			}
		}

		// Token: 0x1700002F RID: 47
		// (get) Token: 0x060000A7 RID: 167 RVA: 0x000056AB File Offset: 0x000038AB
		public InputAction RightClick
		{
			get
			{
				return this.m_Wrapper.m_UI_RightClick;
			}
		}

		// Token: 0x17000030 RID: 48
		// (get) Token: 0x060000A8 RID: 168 RVA: 0x000056B8 File Offset: 0x000038B8
		public InputAction TrackedDevicePosition
		{
			get
			{
				return this.m_Wrapper.m_UI_TrackedDevicePosition;
			}
		}

		// Token: 0x17000031 RID: 49
		// (get) Token: 0x060000A9 RID: 169 RVA: 0x000056C5 File Offset: 0x000038C5
		public InputAction TrackedDeviceOrientation
		{
			get
			{
				return this.m_Wrapper.m_UI_TrackedDeviceOrientation;
			}
		}

		// Token: 0x060000AA RID: 170 RVA: 0x000056D2 File Offset: 0x000038D2
		public InputActionMap Get()
		{
			return this.m_Wrapper.m_UI;
		}

		// Token: 0x060000AB RID: 171 RVA: 0x000056DF File Offset: 0x000038DF
		public void Enable()
		{
			this.Get().Enable();
		}

		// Token: 0x060000AC RID: 172 RVA: 0x000056EC File Offset: 0x000038EC
		public void Disable()
		{
			this.Get().Disable();
		}

		// Token: 0x17000032 RID: 50
		// (get) Token: 0x060000AD RID: 173 RVA: 0x000056F9 File Offset: 0x000038F9
		public bool enabled
		{
			get
			{
				return this.Get().enabled;
			}
		}

		// Token: 0x060000AE RID: 174 RVA: 0x00005706 File Offset: 0x00003906
		public static implicit operator InputActionMap(PlayerControls.UIActions set)
		{
			return set.Get();
		}

		// Token: 0x060000AF RID: 175 RVA: 0x00005710 File Offset: 0x00003910
		public void SetCallbacks(PlayerControls.IUIActions instance)
		{
			if (this.m_Wrapper.m_UIActionsCallbackInterface != null)
			{
				this.Navigate.started -= this.m_Wrapper.m_UIActionsCallbackInterface.OnNavigate;
				this.Navigate.performed -= this.m_Wrapper.m_UIActionsCallbackInterface.OnNavigate;
				this.Navigate.canceled -= this.m_Wrapper.m_UIActionsCallbackInterface.OnNavigate;
				this.Submit.started -= this.m_Wrapper.m_UIActionsCallbackInterface.OnSubmit;
				this.Submit.performed -= this.m_Wrapper.m_UIActionsCallbackInterface.OnSubmit;
				this.Submit.canceled -= this.m_Wrapper.m_UIActionsCallbackInterface.OnSubmit;
				this.Cancel.started -= this.m_Wrapper.m_UIActionsCallbackInterface.OnCancel;
				this.Cancel.performed -= this.m_Wrapper.m_UIActionsCallbackInterface.OnCancel;
				this.Cancel.canceled -= this.m_Wrapper.m_UIActionsCallbackInterface.OnCancel;
				this.Point.started -= this.m_Wrapper.m_UIActionsCallbackInterface.OnPoint;
				this.Point.performed -= this.m_Wrapper.m_UIActionsCallbackInterface.OnPoint;
				this.Point.canceled -= this.m_Wrapper.m_UIActionsCallbackInterface.OnPoint;
				this.Click.started -= this.m_Wrapper.m_UIActionsCallbackInterface.OnClick;
				this.Click.performed -= this.m_Wrapper.m_UIActionsCallbackInterface.OnClick;
				this.Click.canceled -= this.m_Wrapper.m_UIActionsCallbackInterface.OnClick;
				this.ScrollWheel.started -= this.m_Wrapper.m_UIActionsCallbackInterface.OnScrollWheel;
				this.ScrollWheel.performed -= this.m_Wrapper.m_UIActionsCallbackInterface.OnScrollWheel;
				this.ScrollWheel.canceled -= this.m_Wrapper.m_UIActionsCallbackInterface.OnScrollWheel;
				this.MiddleClick.started -= this.m_Wrapper.m_UIActionsCallbackInterface.OnMiddleClick;
				this.MiddleClick.performed -= this.m_Wrapper.m_UIActionsCallbackInterface.OnMiddleClick;
				this.MiddleClick.canceled -= this.m_Wrapper.m_UIActionsCallbackInterface.OnMiddleClick;
				this.RightClick.started -= this.m_Wrapper.m_UIActionsCallbackInterface.OnRightClick;
				this.RightClick.performed -= this.m_Wrapper.m_UIActionsCallbackInterface.OnRightClick;
				this.RightClick.canceled -= this.m_Wrapper.m_UIActionsCallbackInterface.OnRightClick;
				this.TrackedDevicePosition.started -= this.m_Wrapper.m_UIActionsCallbackInterface.OnTrackedDevicePosition;
				this.TrackedDevicePosition.performed -= this.m_Wrapper.m_UIActionsCallbackInterface.OnTrackedDevicePosition;
				this.TrackedDevicePosition.canceled -= this.m_Wrapper.m_UIActionsCallbackInterface.OnTrackedDevicePosition;
				this.TrackedDeviceOrientation.started -= this.m_Wrapper.m_UIActionsCallbackInterface.OnTrackedDeviceOrientation;
				this.TrackedDeviceOrientation.performed -= this.m_Wrapper.m_UIActionsCallbackInterface.OnTrackedDeviceOrientation;
				this.TrackedDeviceOrientation.canceled -= this.m_Wrapper.m_UIActionsCallbackInterface.OnTrackedDeviceOrientation;
			}
			this.m_Wrapper.m_UIActionsCallbackInterface = instance;
			if (instance != null)
			{
				this.Navigate.started += instance.OnNavigate;
				this.Navigate.performed += instance.OnNavigate;
				this.Navigate.canceled += instance.OnNavigate;
				this.Submit.started += instance.OnSubmit;
				this.Submit.performed += instance.OnSubmit;
				this.Submit.canceled += instance.OnSubmit;
				this.Cancel.started += instance.OnCancel;
				this.Cancel.performed += instance.OnCancel;
				this.Cancel.canceled += instance.OnCancel;
				this.Point.started += instance.OnPoint;
				this.Point.performed += instance.OnPoint;
				this.Point.canceled += instance.OnPoint;
				this.Click.started += instance.OnClick;
				this.Click.performed += instance.OnClick;
				this.Click.canceled += instance.OnClick;
				this.ScrollWheel.started += instance.OnScrollWheel;
				this.ScrollWheel.performed += instance.OnScrollWheel;
				this.ScrollWheel.canceled += instance.OnScrollWheel;
				this.MiddleClick.started += instance.OnMiddleClick;
				this.MiddleClick.performed += instance.OnMiddleClick;
				this.MiddleClick.canceled += instance.OnMiddleClick;
				this.RightClick.started += instance.OnRightClick;
				this.RightClick.performed += instance.OnRightClick;
				this.RightClick.canceled += instance.OnRightClick;
				this.TrackedDevicePosition.started += instance.OnTrackedDevicePosition;
				this.TrackedDevicePosition.performed += instance.OnTrackedDevicePosition;
				this.TrackedDevicePosition.canceled += instance.OnTrackedDevicePosition;
				this.TrackedDeviceOrientation.started += instance.OnTrackedDeviceOrientation;
				this.TrackedDeviceOrientation.performed += instance.OnTrackedDeviceOrientation;
				this.TrackedDeviceOrientation.canceled += instance.OnTrackedDeviceOrientation;
			}
		}

		// Token: 0x040000A3 RID: 163
		private PlayerControls m_Wrapper;
	}

	// Token: 0x02000014 RID: 20
	public interface IPlayerActions
	{
		// Token: 0x060000B0 RID: 176
		void OnMove(InputAction.CallbackContext context);

		// Token: 0x060000B1 RID: 177
		void OnLeftClick(InputAction.CallbackContext context);

		// Token: 0x060000B2 RID: 178
		void OnRightClick(InputAction.CallbackContext context);

		// Token: 0x060000B3 RID: 179
		void OnFireHold(InputAction.CallbackContext context);

		// Token: 0x060000B4 RID: 180
		void OnJump(InputAction.CallbackContext context);

		// Token: 0x060000B5 RID: 181
		void OnMoveUp(InputAction.CallbackContext context);

		// Token: 0x060000B6 RID: 182
		void OnRun(InputAction.CallbackContext context);

		// Token: 0x060000B7 RID: 183
		void OnMouseX(InputAction.CallbackContext context);

		// Token: 0x060000B8 RID: 184
		void OnMouseY(InputAction.CallbackContext context);

		// Token: 0x060000B9 RID: 185
		void OnZoom(InputAction.CallbackContext context);

		// Token: 0x060000BA RID: 186
		void OnCrouch(InputAction.CallbackContext context);

		// Token: 0x060000BB RID: 187
		void OnInteract(InputAction.CallbackContext context);

		// Token: 0x060000BC RID: 188
		void OnDrop(InputAction.CallbackContext context);

		// Token: 0x060000BD RID: 189
		void OnReload(InputAction.CallbackContext context);

		// Token: 0x060000BE RID: 190
		void OnLeanRight(InputAction.CallbackContext context);

		// Token: 0x060000BF RID: 191
		void OnLeanLeft(InputAction.CallbackContext context);

		// Token: 0x060000C0 RID: 192
		void OnChangeWeapon(InputAction.CallbackContext context);

		// Token: 0x060000C1 RID: 193
		void OnVoiceChat(InputAction.CallbackContext context);

		// Token: 0x060000C2 RID: 194
		void OnAny(InputAction.CallbackContext context);

		// Token: 0x060000C3 RID: 195
		void OnAnyGamepad(InputAction.CallbackContext context);
	}

	// Token: 0x02000015 RID: 21
	public interface IUIActions
	{
		// Token: 0x060000C4 RID: 196
		void OnNavigate(InputAction.CallbackContext context);

		// Token: 0x060000C5 RID: 197
		void OnSubmit(InputAction.CallbackContext context);

		// Token: 0x060000C6 RID: 198
		void OnCancel(InputAction.CallbackContext context);

		// Token: 0x060000C7 RID: 199
		void OnPoint(InputAction.CallbackContext context);

		// Token: 0x060000C8 RID: 200
		void OnClick(InputAction.CallbackContext context);

		// Token: 0x060000C9 RID: 201
		void OnScrollWheel(InputAction.CallbackContext context);

		// Token: 0x060000CA RID: 202
		void OnMiddleClick(InputAction.CallbackContext context);

		// Token: 0x060000CB RID: 203
		void OnRightClick(InputAction.CallbackContext context);

		// Token: 0x060000CC RID: 204
		void OnTrackedDevicePosition(InputAction.CallbackContext context);

		// Token: 0x060000CD RID: 205
		void OnTrackedDeviceOrientation(InputAction.CallbackContext context);
	}
}

﻿using System;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

// Token: 0x02000123 RID: 291
public class ChangeTextValue : MonoBehaviour
{
	// Token: 0x06000E27 RID: 3623 RVA: 0x0005E757 File Offset: 0x0005C957
	private void Awake()
	{
		this.textMesh = base.transform.GetComponentInChildren<TextMeshProUGUI>();
		this._slider = base.transform.GetComponentInChildren<Slider>();
	}

	// Token: 0x06000E28 RID: 3624 RVA: 0x0005E77C File Offset: 0x0005C97C
	private void Start()
	{
		if (Mathf.Round(this._slider.value * this.factor2) * this.factor % 2f == 0f || Mathf.Round(this._slider.value * this.factor2) * this.factor % 2f == 1f)
		{
			this.textMesh.text = (Mathf.Round(this._slider.value * this.factor2) * this.factor).ToString() + ".0";
			return;
		}
		this.textMesh.text = (Mathf.Round(this._slider.value * this.factor2) * this.factor).ToString(this.correct);
	}

	// Token: 0x06000E29 RID: 3625 RVA: 0x0005E854 File Offset: 0x0005CA54
	public void ChangeIntValue(Slider value)
	{
		if (Mathf.Round(value.value * this.factor2) * this.factor % 2f == 0f || Mathf.Round(value.value * this.factor2) * this.factor % 2f == 1f)
		{
			this.textMesh.text = (Mathf.Round(value.value * this.factor2) * this.factor).ToString() + ".0";
			return;
		}
		this.textMesh.text = (Mathf.Round(value.value * this.factor2) * this.factor).ToString(this.correct);
	}

	// Token: 0x04000C94 RID: 3220
	private TextMeshProUGUI textMesh;

	// Token: 0x04000C95 RID: 3221
	private Slider _slider;

	// Token: 0x04000C96 RID: 3222
	[SerializeField]
	private float factor = 0.01f;

	// Token: 0x04000C97 RID: 3223
	[SerializeField]
	private float factor2 = 100f;

	// Token: 0x04000C98 RID: 3224
	[SerializeField]
	private string correct = "F2";
}

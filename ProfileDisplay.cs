﻿using System;
using Steamworks;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

// Token: 0x020000EF RID: 239
public class ProfileDisplay : MonoBehaviour
{
	// Token: 0x06000C82 RID: 3202 RVA: 0x00055D78 File Offset: 0x00053F78
	private void Start()
	{
		this.PlayerSteamID = (ulong)SteamUser.GetSteamID();
		this.PlayerName = SteamFriends.GetPersonaName().ToString();
		this.ImageLoaded = Callback<AvatarImageLoaded_t>.Create(new Callback<AvatarImageLoaded_t>.DispatchDelegate(this.OnImageLoaded));
		this.SetPlayerValues();
	}

	// Token: 0x06000C83 RID: 3203 RVA: 0x00055DB8 File Offset: 0x00053FB8
	private void GetPlayerIcon()
	{
		int largeFriendAvatar = SteamFriends.GetLargeFriendAvatar((CSteamID)this.PlayerSteamID);
		if (largeFriendAvatar == -1)
		{
			return;
		}
		this.PlayerIcon.texture = this.GetSteamImageAsTexture(largeFriendAvatar);
	}

	// Token: 0x06000C84 RID: 3204 RVA: 0x00055DED File Offset: 0x00053FED
	public void SetPlayerValues()
	{
		this.PlayerNameText.text = this.PlayerName;
		if (!this.AvatarReceived)
		{
			this.GetPlayerIcon();
		}
	}

	// Token: 0x06000C85 RID: 3205 RVA: 0x00055E0E File Offset: 0x0005400E
	private void OnImageLoaded(AvatarImageLoaded_t callback)
	{
		if (callback.m_steamID.m_SteamID == this.PlayerSteamID)
		{
			this.PlayerIcon.texture = this.GetSteamImageAsTexture(callback.m_iImage);
			return;
		}
	}

	// Token: 0x06000C86 RID: 3206 RVA: 0x00055E3C File Offset: 0x0005403C
	private Texture2D GetSteamImageAsTexture(int iImage)
	{
		Texture2D texture2D = null;
		uint num;
		uint num2;
		if (SteamUtils.GetImageSize(iImage, out num, out num2))
		{
			byte[] array = new byte[num * num2 * 4U];
			if (SteamUtils.GetImageRGBA(iImage, array, (int)(num * num2 * 4U)))
			{
				texture2D = new Texture2D((int)num, (int)num2, TextureFormat.RGBA32, false, true);
				texture2D.LoadRawTextureData(array);
				texture2D.Apply();
			}
		}
		this.AvatarReceived = true;
		return texture2D;
	}

	// Token: 0x04000AC0 RID: 2752
	public string PlayerName;

	// Token: 0x04000AC1 RID: 2753
	public ulong PlayerSteamID;

	// Token: 0x04000AC2 RID: 2754
	public bool AvatarReceived;

	// Token: 0x04000AC3 RID: 2755
	public TextMeshProUGUI PlayerNameText;

	// Token: 0x04000AC4 RID: 2756
	public RawImage PlayerIcon;

	// Token: 0x04000AC5 RID: 2757
	protected Callback<AvatarImageLoaded_t> ImageLoaded;
}

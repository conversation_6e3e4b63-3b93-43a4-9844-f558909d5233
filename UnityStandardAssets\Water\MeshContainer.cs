﻿using System;
using UnityEngine;

namespace UnityStandardAssets.Water
{
	// Token: 0x0200019B RID: 411
	public class MeshContainer
	{
		// Token: 0x06001040 RID: 4160 RVA: 0x00069781 File Offset: 0x00067981
		public MeshContainer(Mesh m)
		{
			this.mesh = m;
			this.vertices = m.vertices;
			this.normals = m.normals;
		}

		// Token: 0x06001041 RID: 4161 RVA: 0x000697A8 File Offset: 0x000679A8
		public void Update()
		{
			this.mesh.vertices = this.vertices;
			this.mesh.normals = this.normals;
		}

		// Token: 0x04000F04 RID: 3844
		public Mesh mesh;

		// Token: 0x04000F05 RID: 3845
		public Vector3[] vertices;

		// Token: 0x04000F06 RID: 3846
		public Vector3[] normals;
	}
}

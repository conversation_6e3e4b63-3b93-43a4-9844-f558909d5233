﻿using System;
using UnityEngine;
using UnitySA.Utility;

namespace UnitySA.Characters.FirstPerson
{
	// Token: 0x02000176 RID: 374
	[RequireComponent(typeof(CharacterController))]
	[RequireComponent(typeof(AudioSource))]
	public class FPCtrl : MonoBehaviour
	{
		// Token: 0x06000F89 RID: 3977 RVA: 0x0006621C File Offset: 0x0006441C
		private void Start()
		{
			this.m_CharacterController = base.GetComponent<CharacterController>();
			this.m_Camera = Camera.main;
			this.m_OriginalCameraPosition = this.m_Camera.transform.localPosition;
			this.m_FovKick.Setup(this.m_Camera);
			this.m_HeadBob.Setup(this.m_Camera, this.m_StepInterval);
			this.m_StepCycle = 0f;
			this.m_NextStep = this.m_StepCycle / 2f;
			this.m_Jumping = false;
			this.m_MouseLook.Init(base.transform, this.m_Camera.transform);
		}

		// Token: 0x06000F8A RID: 3978 RVA: 0x000662C0 File Offset: 0x000644C0
		private void Update()
		{
			this.RotateView();
			if (!this.m_Jump)
			{
				this.m_Jump = Input.GetButtonDown("Jump");
			}
			if (!this.m_PreviouslyGrounded && this.m_CharacterController.isGrounded)
			{
				base.StartCoroutine(this.m_JumpBob.DoBobCycle());
				this.m_MoveDir.y = 0f;
				this.m_Jumping = false;
			}
			if (!this.m_CharacterController.isGrounded && !this.m_Jumping && this.m_PreviouslyGrounded)
			{
				this.m_MoveDir.y = 0f;
			}
			this.m_PreviouslyGrounded = this.m_CharacterController.isGrounded;
		}

		// Token: 0x06000F8B RID: 3979 RVA: 0x00066368 File Offset: 0x00064568
		private void FixedUpdate()
		{
			float num;
			this.GetInput(out num);
			Vector3 vector = base.transform.forward * this.m_Input.y + base.transform.right * this.m_Input.x;
			RaycastHit raycastHit;
			Physics.SphereCast(base.transform.position, this.m_CharacterController.radius, Vector3.down, out raycastHit, this.m_CharacterController.height / 2f, -1, QueryTriggerInteraction.Ignore);
			vector = Vector3.ProjectOnPlane(vector, raycastHit.normal).normalized;
			this.m_MoveDir.x = vector.x * num;
			this.m_MoveDir.z = vector.z * num;
			if (this.m_CharacterController.isGrounded)
			{
				this.m_MoveDir.y = -this.m_StickToGroundForce;
				if (this.m_Jump)
				{
					this.m_MoveDir.y = this.m_JumpSpeed;
					this.m_Jump = false;
					this.m_Jumping = true;
				}
			}
			else
			{
				this.m_MoveDir += Physics.gravity * this.m_GravityMultiplier * Time.fixedDeltaTime;
			}
			this.m_CollisionFlags = this.m_CharacterController.Move(this.m_MoveDir * Time.fixedDeltaTime);
			this.ProgressStepCycle(num);
			this.UpdateCameraPosition(num);
			this.m_MouseLook.UpdateCursorLock();
		}

		// Token: 0x06000F8C RID: 3980 RVA: 0x000664D8 File Offset: 0x000646D8
		private void ProgressStepCycle(float speed)
		{
			if (this.m_CharacterController.velocity.sqrMagnitude > 0f && (this.m_Input.x != 0f || this.m_Input.y != 0f))
			{
				this.m_StepCycle += (this.m_CharacterController.velocity.magnitude + speed * (this.m_IsWalking ? 1f : this.m_RunstepLenghten)) * Time.fixedDeltaTime;
			}
			if (this.m_StepCycle <= this.m_NextStep)
			{
				return;
			}
			this.m_NextStep = this.m_StepCycle + this.m_StepInterval;
		}

		// Token: 0x06000F8D RID: 3981 RVA: 0x00066584 File Offset: 0x00064784
		private void UpdateCameraPosition(float speed)
		{
			if (!this.m_UseHeadBob)
			{
				return;
			}
			Vector3 vector;
			if (this.m_CharacterController.velocity.magnitude > 0f && this.m_CharacterController.isGrounded)
			{
				this.m_Camera.transform.localPosition = this.m_HeadBob.DoHeadBob(this.m_CharacterController.velocity.magnitude + speed * (this.m_IsWalking ? 1f : this.m_RunstepLenghten));
				vector = this.m_Camera.transform.localPosition;
				vector.y = this.m_Camera.transform.localPosition.y - this.m_JumpBob.Offset();
			}
			else
			{
				vector = this.m_Camera.transform.localPosition;
				vector.y = this.m_OriginalCameraPosition.y - this.m_JumpBob.Offset();
			}
			this.m_Camera.transform.localPosition = vector;
		}

		// Token: 0x06000F8E RID: 3982 RVA: 0x00066688 File Offset: 0x00064888
		private void GetInput(out float speed)
		{
			float axis = Input.GetAxis("Horizontal");
			float axis2 = Input.GetAxis("Vertical");
			bool isWalking = this.m_IsWalking;
			this.m_IsWalking = !Input.GetKey(KeyCode.LeftShift);
			speed = (this.m_IsWalking ? this.m_WalkSpeed : this.m_RunSpeed);
			this.m_Input = new Vector2(axis, axis2);
			if (this.m_Input.sqrMagnitude > 1f)
			{
				this.m_Input.Normalize();
			}
			if (this.m_IsWalking != isWalking && this.m_UseFovKick && this.m_CharacterController.velocity.sqrMagnitude > 0f)
			{
				base.StopAllCoroutines();
				base.StartCoroutine((!this.m_IsWalking) ? this.m_FovKick.FOVKickUp() : this.m_FovKick.FOVKickDown());
			}
		}

		// Token: 0x06000F8F RID: 3983 RVA: 0x0006675F File Offset: 0x0006495F
		private void RotateView()
		{
			this.m_MouseLook.LookRotation(base.transform, this.m_Camera.transform);
		}

		// Token: 0x06000F90 RID: 3984 RVA: 0x00066780 File Offset: 0x00064980
		private void OnControllerColliderHit(ControllerColliderHit hit)
		{
			Rigidbody attachedRigidbody = hit.collider.attachedRigidbody;
			if (this.m_CollisionFlags == CollisionFlags.Below)
			{
				return;
			}
			if (attachedRigidbody == null || attachedRigidbody.isKinematic)
			{
				return;
			}
			attachedRigidbody.AddForceAtPosition(this.m_CharacterController.velocity * 0.1f, hit.point, ForceMode.Impulse);
		}

		// Token: 0x04000E59 RID: 3673
		[SerializeField]
		private bool m_IsWalking;

		// Token: 0x04000E5A RID: 3674
		[SerializeField]
		private float m_WalkSpeed;

		// Token: 0x04000E5B RID: 3675
		[SerializeField]
		private float m_RunSpeed;

		// Token: 0x04000E5C RID: 3676
		[SerializeField]
		[Range(0f, 1f)]
		private float m_RunstepLenghten;

		// Token: 0x04000E5D RID: 3677
		[SerializeField]
		private float m_JumpSpeed;

		// Token: 0x04000E5E RID: 3678
		[SerializeField]
		private float m_StickToGroundForce;

		// Token: 0x04000E5F RID: 3679
		[SerializeField]
		private float m_GravityMultiplier;

		// Token: 0x04000E60 RID: 3680
		[SerializeField]
		private MLook m_MouseLook;

		// Token: 0x04000E61 RID: 3681
		[SerializeField]
		private bool m_UseFovKick;

		// Token: 0x04000E62 RID: 3682
		[SerializeField]
		private FOVZoom m_FovKick = new FOVZoom();

		// Token: 0x04000E63 RID: 3683
		[SerializeField]
		private bool m_UseHeadBob;

		// Token: 0x04000E64 RID: 3684
		[SerializeField]
		private CurveCtrlBob m_HeadBob = new CurveCtrlBob();

		// Token: 0x04000E65 RID: 3685
		[SerializeField]
		private LerpCtrlBob m_JumpBob = new LerpCtrlBob();

		// Token: 0x04000E66 RID: 3686
		[SerializeField]
		private float m_StepInterval;

		// Token: 0x04000E67 RID: 3687
		private Camera m_Camera;

		// Token: 0x04000E68 RID: 3688
		private bool m_Jump;

		// Token: 0x04000E69 RID: 3689
		private float m_YRotation;

		// Token: 0x04000E6A RID: 3690
		private Vector2 m_Input;

		// Token: 0x04000E6B RID: 3691
		private Vector3 m_MoveDir = Vector3.zero;

		// Token: 0x04000E6C RID: 3692
		private CharacterController m_CharacterController;

		// Token: 0x04000E6D RID: 3693
		private CollisionFlags m_CollisionFlags;

		// Token: 0x04000E6E RID: 3694
		private bool m_PreviouslyGrounded;

		// Token: 0x04000E6F RID: 3695
		private Vector3 m_OriginalCameraPosition;

		// Token: 0x04000E70 RID: 3696
		private float m_StepCycle;

		// Token: 0x04000E71 RID: 3697
		private float m_NextStep;

		// Token: 0x04000E72 RID: 3698
		private bool m_Jumping;
	}
}

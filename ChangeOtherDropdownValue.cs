﻿using System;
using TMPro;
using UnityEngine;

// Token: 0x02000122 RID: 290
public class ChangeOtherDropdownValue : MonoBehaviour
{
	// Token: 0x06000E24 RID: 3620 RVA: 0x0005E71A File Offset: 0x0005C91A
	private void Start()
	{
		if (base.GetComponent<TMP_Dropdown>() != null)
		{
			this.self = base.GetComponent<TMP_Dropdown>();
		}
	}

	// Token: 0x06000E25 RID: 3621 RVA: 0x0005E736 File Offset: 0x0005C936
	public void ChangeDropdownValue(TMP_Dropdown dropdown)
	{
		if (!base.gameObject.activeInHierarchy)
		{
			return;
		}
		dropdown.value = this.self.value;
	}

	// Token: 0x04000C93 RID: 3219
	private TMP_Dropdown self;
}

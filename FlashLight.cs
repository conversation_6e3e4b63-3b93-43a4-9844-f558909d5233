﻿using System;
using FishNet;
using FishNet.Connection;
using FishNet.Managing;
using FishNet.Object;
using FishNet.Object.Delegating;
using FishNet.Serializing;
using FishNet.Transporting;
using UnityEngine;

// Token: 0x02000090 RID: 144
public class FlashLight : Weapon
{
	// Token: 0x06000736 RID: 1846 RVA: 0x00030578 File Offset: 0x0002E778
	private void Update()
	{
		base.WeaponUpdate();
		if (this.fireTimer > 0f)
		{
			this.fireTimer -= Time.deltaTime;
		}
		if (base.gameObject.layer == 7)
		{
			return;
		}
		if (!this.onePressShoot)
		{
			if ((this.invertFire ? this.fire2 : this.fire1).ReadValue<float>() > 0.1f && this.inRightHand && (this.reloadWeapon || base.SyncAccessor_currentAmmo > 0))
			{
				this.Fire();
			}
			if ((this.invertFire ? this.fire1 : this.fire2).ReadValue<float>() > 0.1f && this.inLeftHand && (this.reloadWeapon || base.SyncAccessor_currentAmmo > 0))
			{
				this.Fire();
			}
			if ((this.invertFire ? this.fire2 : this.fire1).ReadValue<float>() > 0.1f && this.inRightHand && this.akAnim && base.SyncAccessor_currentAmmo > 0)
			{
				this.camAnimScript.rotateBack = false;
			}
			else if ((this.invertFire ? this.fire1 : this.fire2).ReadValue<float>() > 0.1f && this.inLeftHand && this.akAnim && base.SyncAccessor_currentAmmo > 0)
			{
				this.camAnimScript.rotateBack = false;
			}
			else
			{
				this.camAnimScript.rotateBack = true;
			}
			if (!this.reloadWeapon && base.SyncAccessor_currentAmmo <= 0)
			{
				if ((this.invertFire ? this.fire2 : this.fire1).ReadValue<float>() < 0.1f && this.inRightHand)
				{
					this.isClicked = true;
				}
				if ((this.invertFire ? this.fire1 : this.fire2).ReadValue<float>() < 0.1f && this.inLeftHand)
				{
					this.isClicked = true;
				}
			}
		}
		else
		{
			if ((this.invertFire ? this.fire2 : this.fire1).ReadValue<float>() < 0.1f && this.inRightHand)
			{
				this.isClicked = true;
			}
			if ((this.invertFire ? this.fire1 : this.fire2).ReadValue<float>() < 0.1f && this.inLeftHand)
			{
				this.isClicked = true;
			}
			this.camAnimScript.rotateBack = true;
		}
		if (this.isClicked)
		{
			if ((this.invertFire ? this.fire2 : this.fire1).ReadValue<float>() > 0.1f && this.inRightHand)
			{
				this.Fire();
				this.isClicked = false;
			}
			if ((this.invertFire ? this.fire1 : this.fire2).ReadValue<float>() > 0.1f && this.inLeftHand)
			{
				this.Fire();
				this.isClicked = false;
			}
		}
		if (this.shot && this.timeBetweenFire < 0.2f && this.onePressShoot)
		{
			this.Fire();
		}
	}

	// Token: 0x06000737 RID: 1847 RVA: 0x00030870 File Offset: 0x0002EA70
	private void Fire()
	{
		if (PauseManager.Instance.pause || !this.playerController.SyncAccessor_canMove)
		{
			return;
		}
		if (!this.playerController.IsOwner)
		{
			return;
		}
		if (this.fireTimer > 0f)
		{
			this.shot = true;
			return;
		}
		this.shot = false;
		this.fireTimer = this.timeBetweenFire;
		if (!this.reloadWeapon && base.SyncAccessor_currentAmmo <= 0)
		{
			this.audio.PlayOneShot(this.nobulletClip);
			this.noAmmoClicks++;
		}
		if (!this.reloadWeapon && base.SyncAccessor_currentAmmo <= 0)
		{
			return;
		}
		if (this.reloadWeapon && this.isReloading)
		{
			return;
		}
		this.Power();
	}

	// Token: 0x06000738 RID: 1848 RVA: 0x00030926 File Offset: 0x0002EB26
	[ServerRpc(RunLocally = true)]
	private void Power()
	{
		this.RpcWriter___Server_Power_2166136261();
		this.RpcLogic___Power_2166136261();
	}

	// Token: 0x06000739 RID: 1849 RVA: 0x00030934 File Offset: 0x0002EB34
	[ObserversRpc(RunLocally = true, ExcludeOwner = true)]
	private void PowerObservers()
	{
		this.RpcWriter___Observers_PowerObservers_2166136261();
		this.RpcLogic___PowerObservers_2166136261();
	}

	// Token: 0x0600073B RID: 1851 RVA: 0x0003094C File Offset: 0x0002EB4C
	public override void NetworkInitialize___Early()
	{
		if (this.NetworkInitializeEarly_FlashLight_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeEarly_FlashLight_Assembly-CSharp.dll = true;
		base.NetworkInitialize___Early();
		base.RegisterServerRpc(15U, new ServerRpcDelegate(this.RpcReader___Server_Power_2166136261));
		base.RegisterObserversRpc(16U, new ClientRpcDelegate(this.RpcReader___Observers_PowerObservers_2166136261));
	}

	// Token: 0x0600073C RID: 1852 RVA: 0x0003099E File Offset: 0x0002EB9E
	public override void NetworkInitialize__Late()
	{
		if (this.NetworkInitializeLate_FlashLight_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeLate_FlashLight_Assembly-CSharp.dll = true;
		base.NetworkInitialize__Late();
	}

	// Token: 0x0600073D RID: 1853 RVA: 0x000309B7 File Offset: 0x0002EBB7
	public override void NetworkInitializeIfDisabled()
	{
		this.NetworkInitialize___Early();
		this.NetworkInitialize__Late();
	}

	// Token: 0x0600073E RID: 1854 RVA: 0x000309C8 File Offset: 0x0002EBC8
	private void RpcWriter___Server_Power_2166136261()
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		if (!base.IsOwner)
		{
			NetworkManager networkManager2 = base.NetworkManager;
			if (networkManager2 == null)
			{
				networkManager2 = InstanceFinder.NetworkManager;
			}
			if (networkManager2 != null)
			{
				networkManager2.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because you are not the owner of this object. .");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendServerRpc(15U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x0600073F RID: 1855 RVA: 0x00030ABC File Offset: 0x0002ECBC
	private void RpcLogic___Power_2166136261()
	{
		this.PowerObservers();
	}

	// Token: 0x06000740 RID: 1856 RVA: 0x00030AC4 File Offset: 0x0002ECC4
	private void RpcReader___Server_Power_2166136261(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		if (!base.IsServer)
		{
			return;
		}
		if (!base.OwnerMatches(conn))
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___Power_2166136261();
	}

	// Token: 0x06000741 RID: 1857 RVA: 0x00030B04 File Offset: 0x0002ED04
	private void RpcWriter___Observers_PowerObservers_2166136261()
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendObserversRpc(16U, writer, channel, DataOrderType.Default, false, false, true);
		writer.Store();
	}

	// Token: 0x06000742 RID: 1858 RVA: 0x00030BAD File Offset: 0x0002EDAD
	private void RpcLogic___PowerObservers_2166136261()
	{
		this.audio.PlayOneShot(this.fireClip);
		this.light.SetActive(!this.light.activeSelf);
	}

	// Token: 0x06000743 RID: 1859 RVA: 0x00030BDC File Offset: 0x0002EDDC
	private void RpcReader___Observers_PowerObservers_2166136261(PooledReader PooledReader0, Channel channel)
	{
		if (!base.IsClient)
		{
			return;
		}
		if (base.IsHost)
		{
			return;
		}
		this.RpcLogic___PowerObservers_2166136261();
	}

	// Token: 0x06000744 RID: 1860 RVA: 0x00030C06 File Offset: 0x0002EE06
	public override void Awake()
	{
		this.NetworkInitialize___Early();
		base.Awake();
		this.NetworkInitialize__Late();
	}

	// Token: 0x06000745 RID: 1861 RVA: 0x000023D6 File Offset: 0x000005D6
	public override void Awake___UserLogic()
	{
	}

	// Token: 0x040006AD RID: 1709
	[SerializeField]
	private float reloadTime;

	// Token: 0x040006AE RID: 1710
	[SerializeField]
	private AudioClip reloadClip;

	// Token: 0x040006AF RID: 1711
	private float fireTimer;

	// Token: 0x040006B0 RID: 1712
	private bool touched;

	// Token: 0x040006B1 RID: 1713
	private Vector3 spread;

	// Token: 0x040006B2 RID: 1714
	[SerializeField]
	private GameObject light;

	// Token: 0x040006B3 RID: 1715
	private bool isOn;

	// Token: 0x040006B4 RID: 1716
	private bool NetworkInitializeEarly_FlashLight_Assembly-CSharp.dll;

	// Token: 0x040006B5 RID: 1717
	private bool NetworkInitializeLate_FlashLight_Assembly-CSharp.dll;
}

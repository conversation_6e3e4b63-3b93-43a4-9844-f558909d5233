﻿using System;
using FishNet;
using TMPro;
using UnityEngine;

// Token: 0x02000050 RID: 80
public class FirstToWinsText : MonoBehaviour
{
	// Token: 0x060003C2 RID: 962 RVA: 0x0001BA60 File Offset: 0x00019C60
	private void Awake()
	{
		if (!this.onEndRoundScreen)
		{
			this.buttonScript = base.GetComponent<ButtonSizeTween>();
		}
	}

	// Token: 0x060003C3 RID: 963 RVA: 0x0001BA76 File Offset: 0x00019C76
	private void Start()
	{
		this.pauseManager = PauseManager.Instance;
	}

	// Token: 0x060003C4 RID: 964 RVA: 0x0001BA84 File Offset: 0x00019C84
	private void Update()
	{
		if (!this.onEndRoundScreen)
		{
			if (SceneMotor.Instance == null)
			{
				return;
			}
			if (this.sceneMotor == null)
			{
				this.sceneMotor = SceneMotor.Instance;
			}
			if (InstanceFinder.NetworkManager.IsServer)
			{
				base.transform.localScale = Vector3.zero;
			}
			else
			{
				base.transform.localScale = Vector3.one;
			}
			this.text.text = string.Format("Win {0} Rounds", this.sceneMotor.SyncAccessor_roundAmount);
			this.buttonScript.customText = string.Format("First Player who wins {0} Rounds wins the Match", this.sceneMotor.SyncAccessor_roundAmount);
			return;
		}
		else
		{
			if (!this.pauseManager.onEndRoundScreen)
			{
				base.transform.localScale = Vector3.zero;
				return;
			}
			base.transform.localScale = Vector3.one;
			if (SceneMotor.Instance == null)
			{
				return;
			}
			if (this.sceneMotor == null)
			{
				this.sceneMotor = SceneMotor.Instance;
			}
			this.text.text = string.Format("First To {0}", this.sceneMotor.SyncAccessor_roundAmount);
			return;
		}
	}

	// Token: 0x04000417 RID: 1047
	[SerializeField]
	private bool onEndRoundScreen;

	// Token: 0x04000418 RID: 1048
	[SerializeField]
	private TextMeshProUGUI text;

	// Token: 0x04000419 RID: 1049
	private SceneMotor sceneMotor;

	// Token: 0x0400041A RID: 1050
	private ButtonSizeTween buttonScript;

	// Token: 0x0400041B RID: 1051
	private PauseManager pauseManager;
}

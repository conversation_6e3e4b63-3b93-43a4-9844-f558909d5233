﻿using System;
using UnityEngine;

// Token: 0x02000135 RID: 309
public class SetActiveFromRef : MonoBehaviour
{
	// Token: 0x06000E7B RID: 3707 RVA: 0x0005FB55 File Offset: 0x0005DD55
	private void Update()
	{
		base.transform.localScale = (this.reference.activeSelf ? this.openScale : Vector3.zero);
	}

	// Token: 0x04000D06 RID: 3334
	[SerializeField]
	private GameObject reference;

	// Token: 0x04000D07 RID: 3335
	[SerializeField]
	private Vector3 openScale;
}

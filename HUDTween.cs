﻿using System;
using DG.Tweening;
using DG.Tweening.Core;
using DG.Tweening.Plugins.Options;
using UnityEngine;

// Token: 0x02000026 RID: 38
public class HUDTween : MonoBehaviour
{
	// Token: 0x060001B4 RID: 436 RVA: 0x00010477 File Offset: 0x0000E677
	private void Start()
	{
		this.clientScript = base.GetComponentInParent<PlayerValues>().SyncAccessor_playerClient;
	}

	// Token: 0x060001B5 RID: 437 RVA: 0x0001048C File Offset: 0x0000E68C
	private void Update()
	{
		if (this.clientScript.nonSteamworksTransport)
		{
			this.hudUp.DOLocalMove(Vector3.zero, this.tweenTime, false).SetEase(this.ease);
			this.hudDown.DOLocalMove(Vector3.zero, this.tweenTime, false).SetEase(this.ease);
		}
		if (Input.GetKeyDown(KeyCode.O) && Input.GetKey(KeyCode.LeftControl) && Application.isEditor)
		{
			this.hudUp.gameObject.SetActive(!this.hudUp.gameObject.activeSelf);
			this.hudDown.gameObject.SetActive(!this.hudDown.gameObject.activeSelf);
		}
		if (this.trigger)
		{
			return;
		}
		if (!this.trigger && PauseManager.Instance.onStartRoundScreen)
		{
			this.trigger = true;
			this.hudUp.DOLocalMove(Vector3.zero, this.tweenTime, false).SetEase(this.ease);
			this.hudDown.DOLocalMove(Vector3.zero, this.tweenTime, false).SetEase(this.ease);
		}
	}

	// Token: 0x060001B6 RID: 438 RVA: 0x000105B8 File Offset: 0x0000E7B8
	public void MoveUp()
	{
		this.hudUp.DOLocalMove(Vector3.zero, this.tweenTime, false).SetEase(this.ease);
		this.hudDown.DOLocalMove(Vector3.zero, this.tweenTime, false).SetEase(this.ease);
	}

	// Token: 0x04000264 RID: 612
	[SerializeField]
	private Transform hudUp;

	// Token: 0x04000265 RID: 613
	[SerializeField]
	private Transform hudDown;

	// Token: 0x04000266 RID: 614
	[SerializeField]
	private float tweenTime = 0.4f;

	// Token: 0x04000267 RID: 615
	[SerializeField]
	private Ease ease;

	// Token: 0x04000268 RID: 616
	private bool trigger;

	// Token: 0x04000269 RID: 617
	private ClientInstance clientScript;
}

﻿using System;
using UnityEngine;
using UnityEngine.Rendering.PostProcessing;
using UnityEngine.SceneManagement;

// Token: 0x02000042 RID: 66
public class BabbdiPostProcess : MonoBehaviour
{
	// Token: 0x06000331 RID: 817 RVA: 0x00019C98 File Offset: 0x00017E98
	private void Start()
	{
		if (SceneManager.GetActiveScene().name != "Babbdi")
		{
			return;
		}
		this.colorfulFog.enabled = true;
		this.weaponCam.enabled = false;
		this.volume.enabled = false;
	}

	// Token: 0x040003B3 RID: 947
	[SerializeField]
	private ColorfulFog colorfulFog;

	// Token: 0x040003B4 RID: 948
	[SerializeField]
	private PostProcessVolume volume;

	// Token: 0x040003B5 RID: 949
	[SerializeField]
	private PostProcessVolume weaponCam;
}

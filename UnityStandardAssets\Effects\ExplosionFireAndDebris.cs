﻿using System;
using System.Collections;
using UnityEngine;

namespace UnityStandardAssets.Effects
{
	// Token: 0x0200018E RID: 398
	public class ExplosionFireAndDebris : MonoBehaviour
	{
		// Token: 0x06001018 RID: 4120 RVA: 0x00068EC5 File Offset: 0x000670C5
		private IEnumerator Start()
		{
			float multiplier = base.GetComponent<ParticleSystemMultiplier>().multiplier;
			int num = 0;
			while ((float)num < (float)this.numDebrisPieces * multiplier)
			{
				Transform transform = this.debrisPrefabs[global::UnityEngine.Random.Range(0, this.debrisPrefabs.Length)];
				Vector3 vector = base.transform.position + global::UnityEngine.Random.insideUnitSphere * 3f * multiplier;
				Quaternion rotation = global::UnityEngine.Random.rotation;
				global::UnityEngine.Object.Instantiate<Transform>(transform, vector, rotation);
				num++;
			}
			yield return null;
			float num2 = 10f * multiplier;
			foreach (Collider collider in Physics.OverlapSphere(base.transform.position, num2))
			{
				if (this.numFires > 0)
				{
					Ray ray = new Ray(base.transform.position, collider.transform.position - base.transform.position);
					RaycastHit raycastHit;
					if (collider.Raycast(ray, out raycastHit, num2))
					{
						this.AddFire(collider.transform, raycastHit.point, raycastHit.normal);
						this.numFires--;
					}
				}
			}
			float num3 = 0f;
			while (this.numFires > 0 && num3 < num2)
			{
				RaycastHit raycastHit2;
				if (Physics.Raycast(new Ray(base.transform.position + Vector3.up, global::UnityEngine.Random.onUnitSphere), out raycastHit2, num3))
				{
					this.AddFire(null, raycastHit2.point, raycastHit2.normal);
					this.numFires--;
				}
				num3 += num2 * 0.1f;
			}
			yield break;
		}

		// Token: 0x06001019 RID: 4121 RVA: 0x00068ED4 File Offset: 0x000670D4
		private void AddFire(Transform t, Vector3 pos, Vector3 normal)
		{
			pos += normal * 0.5f;
			global::UnityEngine.Object.Instantiate<Transform>(this.firePrefab, pos, Quaternion.identity).parent = t;
		}

		// Token: 0x04000EE7 RID: 3815
		public Transform[] debrisPrefabs;

		// Token: 0x04000EE8 RID: 3816
		public Transform firePrefab;

		// Token: 0x04000EE9 RID: 3817
		public int numDebrisPieces;

		// Token: 0x04000EEA RID: 3818
		public int numFires;
	}
}

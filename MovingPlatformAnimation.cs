﻿using System;
using FishNet;
using UnityEngine;

// Token: 0x02000060 RID: 96
public class MovingPlatformAnimation : MonoBehaviour
{
	// Token: 0x0600041D RID: 1053 RVA: 0x0001D31E File Offset: 0x0001B51E
	private void Start()
	{
		this.animation = base.GetComponent<Animation>();
	}

	// Token: 0x0600041E RID: 1054 RVA: 0x0001D32C File Offset: 0x0001B52C
	private void Update()
	{
		if (InstanceFinder.NetworkManager.IsServer)
		{
			this.animation.enabled = true;
		}
	}

	// Token: 0x0400046E RID: 1134
	private Animation animation;
}

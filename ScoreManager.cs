﻿using System;
using System.Collections.Generic;
using FishNet;
using FishNet.Connection;
using FishNet.Managing;
using FishNet.Object;
using FishNet.Object.Delegating;
using FishNet.Object.Synchronizing;
using FishNet.Serializing;
using FishNet.Transporting;
using TMPro;
using UnityEngine;

// Token: 0x0200006F RID: 111
public class ScoreManager : NetworkBehaviour
{
	// Token: 0x060004C6 RID: 1222 RVA: 0x00020578 File Offset: 0x0001E778
	public virtual void Awake()
	{
		this.NetworkInitialize___Early();
		this.Awake___UserLogic();
		this.NetworkInitialize__Late();
	}

	// Token: 0x060004C7 RID: 1223 RVA: 0x0002058C File Offset: 0x0001E78C
	public int GetTeamId(int playerId)
	{
		int num;
		if (this.PlayerIdToTeamId.TryGetValue(playerId, out num))
		{
			return num;
		}
		this.SetTeamId(playerId, playerId);
		return playerId;
	}

	// Token: 0x060004C8 RID: 1224 RVA: 0x000205B4 File Offset: 0x0001E7B4
	public List<int> GetPlayerIdsForTeam(int teamId)
	{
		List<int> list;
		if (this.TeamIdToPlayerIds.TryGetValue(teamId, out list))
		{
			return list;
		}
		return new List<int>();
	}

	// Token: 0x060004C9 RID: 1225 RVA: 0x000205D8 File Offset: 0x0001E7D8
	public void SetTeamId(int playerId, int teamId)
	{
		int num;
		if (this.PlayerIdToTeamId.TryGetValue(playerId, out num))
		{
			if (num == teamId)
			{
				return;
			}
			List<int> list;
			if (this.TeamIdToPlayerIds.TryGetValue(num, out list))
			{
				List<int> list2 = new List<int>(list);
				list2.Remove(playerId);
				if (list2.Count == 0)
				{
					this.TeamIdToPlayerIds.Remove(num);
				}
				else
				{
					this.TeamIdToPlayerIds[num] = list2;
				}
			}
		}
		List<int> list3;
		if (this.TeamIdToPlayerIds.TryGetValue(teamId, out list3))
		{
			List<int> list4 = new List<int>(list3) { playerId };
			this.TeamIdToPlayerIds[teamId] = list4;
		}
		else
		{
			List<int> list5 = new List<int> { playerId };
			this.TeamIdToPlayerIds[teamId] = list5;
		}
		this.PlayerIdToTeamId[playerId] = teamId;
	}

	// Token: 0x060004CA RID: 1226 RVA: 0x00020695 File Offset: 0x0001E895
	[ServerRpc(RequireOwnership = false)]
	public void SetTeamIdServer(int playerId, int teamId)
	{
		this.RpcWriter___Server_SetTeamIdServer_1692629761(playerId, teamId);
	}

	// Token: 0x060004CB RID: 1227 RVA: 0x000206A5 File Offset: 0x0001E8A5
	[ObserversRpc]
	public void UpdateDropdown(int playerId, int teamId)
	{
		this.RpcWriter___Observers_UpdateDropdown_1692629761(playerId, teamId);
	}

	// Token: 0x060004CC RID: 1228 RVA: 0x000206B5 File Offset: 0x0001E8B5
	public int GetPoints(int teamId)
	{
		return this.Points.GetValueOrDefault(teamId, 0);
	}

	// Token: 0x060004CD RID: 1229 RVA: 0x000206C4 File Offset: 0x0001E8C4
	public void AddPoints(int teamId, int score = 1)
	{
		if (!this.Points.TryAdd(teamId, score))
		{
			SyncDictionary<int, int> points = this.Points;
			points[teamId] += score;
		}
	}

	// Token: 0x060004CE RID: 1230 RVA: 0x000206F9 File Offset: 0x0001E8F9
	public void ResetScores()
	{
		this.Points.Clear();
	}

	// Token: 0x060004CF RID: 1231 RVA: 0x00020708 File Offset: 0x0001E908
	public bool CheckForRoundWin(out int winningTeamId)
	{
		winningTeamId = -1;
		int num = 0;
		foreach (int num2 in this.TeamIdToPlayerIds.Keys)
		{
			int roundScore = this.GetRoundScore(num2);
			if (roundScore > num)
			{
				winningTeamId = num2;
				num = roundScore;
			}
			else if (roundScore == num)
			{
				winningTeamId = -1;
			}
		}
		return winningTeamId != -1 && num >= this.RoundScoreRequiredToWin;
	}

	// Token: 0x060004D0 RID: 1232 RVA: 0x0002078C File Offset: 0x0001E98C
	public Dictionary<int, int> GetRoundScoreDictionary()
	{
		return this.RoundScore.GetCollection(false);
	}

	// Token: 0x060004D1 RID: 1233 RVA: 0x0002079A File Offset: 0x0001E99A
	public int GetRoundScore(int playerId)
	{
		return this.RoundScore.GetValueOrDefault(playerId, 0);
	}

	// Token: 0x060004D2 RID: 1234 RVA: 0x000207AC File Offset: 0x0001E9AC
	public void AddRoundScore(int playerId, int score = 1)
	{
		if (!this.RoundScore.TryAdd(playerId, score))
		{
			SyncDictionary<int, int> roundScore = this.RoundScore;
			roundScore[playerId] += score;
		}
	}

	// Token: 0x060004D3 RID: 1235 RVA: 0x000207E1 File Offset: 0x0001E9E1
	public void ResetRound()
	{
		this.RoundScore.Clear();
		this.SetRoundIndex(0);
	}

	// Token: 0x060004D4 RID: 1236 RVA: 0x000207F5 File Offset: 0x0001E9F5
	[ServerRpc(RequireOwnership = false, RunLocally = true)]
	public void SetRoundIndex(int n)
	{
		this.RpcWriter___Server_SetRoundIndex_3316948804(n);
		this.RpcLogic___SetRoundIndex_3316948804(n);
	}

	// Token: 0x060004D6 RID: 1238 RVA: 0x00020848 File Offset: 0x0001EA48
	public virtual void NetworkInitialize___Early()
	{
		if (this.NetworkInitializeEarly_ScoreManager_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeEarly_ScoreManager_Assembly-CSharp.dll = true;
		this.syncVar___TakeIndex = new SyncVar<int>(this, 4U, WritePermission.ServerOnly, ReadPermission.Observers, -1f, Channel.Reliable, this.TakeIndex);
		this.RoundScore.InitializeInstance(this, 3U, WritePermission.ServerOnly, ReadPermission.Observers, -1f, Channel.Reliable, true);
		this.Points.InitializeInstance(this, 2U, WritePermission.ServerOnly, ReadPermission.Observers, -1f, Channel.Reliable, true);
		this.TeamIdToPlayerIds.InitializeInstance(this, 1U, WritePermission.ServerOnly, ReadPermission.Observers, -1f, Channel.Reliable, true);
		this.PlayerIdToTeamId.InitializeInstance(this, 0U, WritePermission.ServerOnly, ReadPermission.Observers, -1f, Channel.Reliable, true);
		base.RegisterServerRpc(0U, new ServerRpcDelegate(this.RpcReader___Server_SetTeamIdServer_1692629761));
		base.RegisterObserversRpc(1U, new ClientRpcDelegate(this.RpcReader___Observers_UpdateDropdown_1692629761));
		base.RegisterServerRpc(2U, new ServerRpcDelegate(this.RpcReader___Server_SetRoundIndex_3316948804));
		base.RegisterSyncVarRead(new SyncVarReadDelegate(this.ReadSyncVar___ScoreManager));
	}

	// Token: 0x060004D7 RID: 1239 RVA: 0x00020980 File Offset: 0x0001EB80
	public virtual void NetworkInitialize__Late()
	{
		if (this.NetworkInitializeLate_ScoreManager_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeLate_ScoreManager_Assembly-CSharp.dll = true;
		this.syncVar___TakeIndex.SetRegistered();
		this.RoundScore.SetRegistered();
		this.Points.SetRegistered();
		this.TeamIdToPlayerIds.SetRegistered();
		this.PlayerIdToTeamId.SetRegistered();
	}

	// Token: 0x060004D8 RID: 1240 RVA: 0x000209D5 File Offset: 0x0001EBD5
	public override void NetworkInitializeIfDisabled()
	{
		this.NetworkInitialize___Early();
		this.NetworkInitialize__Late();
	}

	// Token: 0x060004D9 RID: 1241 RVA: 0x000209E4 File Offset: 0x0001EBE4
	private void RpcWriter___Server_SetTeamIdServer_1692629761(int playerId, int teamId)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteInt32(playerId, AutoPackType.Packed);
		writer.WriteInt32(teamId, AutoPackType.Packed);
		base.SendServerRpc(0U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x060004DA RID: 1242 RVA: 0x00020AA2 File Offset: 0x0001ECA2
	public void RpcLogic___SetTeamIdServer_1692629761(int playerId, int teamId)
	{
		this.SetTeamId(playerId, teamId);
		this.UpdateDropdown(playerId, teamId);
	}

	// Token: 0x060004DB RID: 1243 RVA: 0x00020AB4 File Offset: 0x0001ECB4
	private void RpcReader___Server_SetTeamIdServer_1692629761(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		int num = PooledReader0.ReadInt32(AutoPackType.Packed);
		int num2 = PooledReader0.ReadInt32(AutoPackType.Packed);
		if (!base.IsServer)
		{
			return;
		}
		this.RpcLogic___SetTeamIdServer_1692629761(num, num2);
	}

	// Token: 0x060004DC RID: 1244 RVA: 0x00020B00 File Offset: 0x0001ED00
	private void RpcWriter___Observers_UpdateDropdown_1692629761(int playerId, int teamId)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteInt32(playerId, AutoPackType.Packed);
		writer.WriteInt32(teamId, AutoPackType.Packed);
		base.SendObserversRpc(1U, writer, channel, DataOrderType.Default, false, false, false);
		writer.Store();
	}

	// Token: 0x060004DD RID: 1245 RVA: 0x00020BCD File Offset: 0x0001EDCD
	public void RpcLogic___UpdateDropdown_1692629761(int playerId, int teamId)
	{
		if (GameObject.Find(string.Format("TeamIdDropdownPlayer{0}", playerId)) != null)
		{
			GameObject.Find(string.Format("TeamIdDropdownPlayer{0}", playerId)).GetComponent<TMP_Dropdown>().value = teamId;
		}
	}

	// Token: 0x060004DE RID: 1246 RVA: 0x00020C0C File Offset: 0x0001EE0C
	private void RpcReader___Observers_UpdateDropdown_1692629761(PooledReader PooledReader0, Channel channel)
	{
		int num = PooledReader0.ReadInt32(AutoPackType.Packed);
		int num2 = PooledReader0.ReadInt32(AutoPackType.Packed);
		if (!base.IsClient)
		{
			return;
		}
		this.RpcLogic___UpdateDropdown_1692629761(num, num2);
	}

	// Token: 0x060004DF RID: 1247 RVA: 0x00020C58 File Offset: 0x0001EE58
	private void RpcWriter___Server_SetRoundIndex_3316948804(int n)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteInt32(n, AutoPackType.Packed);
		base.SendServerRpc(2U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x060004E0 RID: 1248 RVA: 0x00020D04 File Offset: 0x0001EF04
	public void RpcLogic___SetRoundIndex_3316948804(int n)
	{
		this.sync___set_value_TakeIndex(n, true);
	}

	// Token: 0x060004E1 RID: 1249 RVA: 0x00020D10 File Offset: 0x0001EF10
	private void RpcReader___Server_SetRoundIndex_3316948804(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		int num = PooledReader0.ReadInt32(AutoPackType.Packed);
		if (!base.IsServer)
		{
			return;
		}
		if (conn.IsLocalClient)
		{
			return;
		}
		this.RpcLogic___SetRoundIndex_3316948804(num);
	}

	// Token: 0x17000071 RID: 113
	// (get) Token: 0x060004E2 RID: 1250 RVA: 0x00020D53 File Offset: 0x0001EF53
	// (set) Token: 0x060004E3 RID: 1251 RVA: 0x00020D5B File Offset: 0x0001EF5B
	public int SyncAccessor_TakeIndex
	{
		get
		{
			return this.TakeIndex;
		}
		set
		{
			if (value || !base.IsServer)
			{
				this.TakeIndex = value;
			}
			this.syncVar___TakeIndex.SetValue(value, value);
		}
	}

	// Token: 0x060004E4 RID: 1252 RVA: 0x00020D90 File Offset: 0x0001EF90
	public virtual bool ReadSyncVar___ScoreManager(PooledReader PooledReader0, uint UInt321, bool Boolean2)
	{
		if (UInt321 != 4U)
		{
			return false;
		}
		if (PooledReader0 == null)
		{
			this.sync___set_value_TakeIndex(this.syncVar___TakeIndex.GetValue(true), true);
			return true;
		}
		int num = PooledReader0.ReadInt32(AutoPackType.Packed);
		this.sync___set_value_TakeIndex(num, Boolean2);
		return true;
	}

	// Token: 0x060004E5 RID: 1253 RVA: 0x00020DE7 File Offset: 0x0001EFE7
	public virtual void Awake___UserLogic()
	{
		if (!ScoreManager.Instance)
		{
			ScoreManager.Instance = this;
			return;
		}
		global::UnityEngine.Object.Destroy(base.gameObject);
	}

	// Token: 0x040004DA RID: 1242
	public static ScoreManager Instance;

	// Token: 0x040004DB RID: 1243
	[SyncObject(WritePermissions = WritePermission.ServerOnly)]
	public readonly SyncDictionary<int, int> PlayerIdToTeamId = new SyncDictionary<int, int>();

	// Token: 0x040004DC RID: 1244
	[SyncObject(WritePermissions = WritePermission.ServerOnly)]
	public readonly SyncDictionary<int, List<int>> TeamIdToPlayerIds = new SyncDictionary<int, List<int>>();

	// Token: 0x040004DD RID: 1245
	[SyncObject(WritePermissions = WritePermission.ServerOnly)]
	public readonly SyncDictionary<int, int> Points = new SyncDictionary<int, int>();

	// Token: 0x040004DE RID: 1246
	public int RoundScoreRequiredToWin = 5;

	// Token: 0x040004DF RID: 1247
	[SyncObject(WritePermissions = WritePermission.ServerOnly)]
	public readonly SyncDictionary<int, int> RoundScore = new SyncDictionary<int, int>();

	// Token: 0x040004E0 RID: 1248
	[SyncVar]
	public int TakeIndex;

	// Token: 0x040004E1 RID: 1249
	public SyncVar<int> syncVar___TakeIndex;

	// Token: 0x040004E2 RID: 1250
	private bool NetworkInitializeEarly_ScoreManager_Assembly-CSharp.dll;

	// Token: 0x040004E3 RID: 1251
	private bool NetworkInitializeLate_ScoreManager_Assembly-CSharp.dll;
}

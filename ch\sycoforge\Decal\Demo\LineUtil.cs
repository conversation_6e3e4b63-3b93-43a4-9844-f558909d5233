﻿using System;
using System.Collections.Generic;
using UnityEngine;

namespace ch.sycoforge.Decal.Demo
{
	// Token: 0x02000182 RID: 386
	public static class LineUtil
	{
		// Token: 0x06000FC3 RID: 4035 RVA: 0x000675D8 File Offset: 0x000657D8
		public static void DrawPath(float thickness, Material material, List<Vector3> path)
		{
			if (path == null || (path != null && path.Count < 2))
			{
				return;
			}
			if (thickness <= Mathf.Epsilon)
			{
				GL.Begin(1);
			}
			else
			{
				GL.Begin(7);
			}
			material.SetPass(0);
			GL.Color(Color.blue);
			Vector3 vector = path[0];
			for (int i = 1; i < path.Count; i++)
			{
				Vector3 vector2 = path[i];
				LineUtil.DrawLine(thickness, vector, vector2);
				vector = vector2;
			}
			GL.End();
		}

		// Token: 0x06000FC4 RID: 4036 RVA: 0x00067650 File Offset: 0x00065850
		private static void DrawLine(float thickness, Vector3 start, Vector3 end)
		{
			if (thickness <= Mathf.Epsilon)
			{
				GL.Vertex(start);
				GL.Vertex(end);
				return;
			}
			Camera main = Camera.main;
			Vector3 normalized = (end - start).normalized;
			Vector3 vector = Vector3.Cross((start - main.transform.position).normalized, normalized) * (thickness / 2f);
			GL.Vertex(start - vector);
			GL.Vertex(start + vector);
			GL.Vertex(end + vector);
			GL.Vertex(end - vector);
		}
	}
}

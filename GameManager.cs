﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using FishNet;
using FishNet.Connection;
using FishNet.Managing;
using FishNet.Object;
using FishNet.Object.Delegating;
using FishNet.Object.Synchronizing;
using FishNet.Serializing;
using FishNet.Serializing.Generated;
using FishNet.Transporting;
using UnityEngine;
using UnityEngine.SceneManagement;

// Token: 0x020000CB RID: 203
public class GameManager : NetworkBehaviour
{
	// Token: 0x06000B3D RID: 2877 RVA: 0x0004F6AB File Offset: 0x0004D8AB
	public virtual void Awake()
	{
		this.NetworkInitialize___Early();
		this.Awake___UserLogic();
		this.NetworkInitialize__Late();
	}

	// Token: 0x06000B3E RID: 2878 RVA: 0x0004F6BF File Offset: 0x0004D8BF
	private void OnSceneLoaded(Scene scene, LoadSceneMode mode)
	{
		if (scene.name == this.MainMenuSceneName)
		{
			this.ResetGame();
		}
	}

	// Token: 0x06000B3F RID: 2879 RVA: 0x0004F6DC File Offset: 0x0004D8DC
	public void ResetGame()
	{
		ScoreManager.Instance.ResetScores();
		ScoreManager.Instance.ResetRound();
		if (this.waitForDrawCoroutine != null)
		{
			base.StopCoroutine(this.waitForDrawCoroutine);
		}
		this.waitForDrawCoroutine = null;
		this.alivePlayers.Clear();
		this.recentDeaths.Clear();
		this.timeTillStart = 4.2949673E+09f;
		this.sync___set_value_roundWasWon(false, true);
	}

	// Token: 0x06000B40 RID: 2880 RVA: 0x0004F744 File Offset: 0x0004D944
	[ServerRpc(RequireOwnership = false)]
	public void PlayerDied(int playerId)
	{
		this.RpcWriter___Server_PlayerDied_3316948804(playerId);
	}

	// Token: 0x06000B41 RID: 2881 RVA: 0x0004F75C File Offset: 0x0004D95C
	private void Start()
	{
		if (base.IsServer)
		{
			if (SpawnerManager.Instance != null && RNGWeaponButton.toggle != null)
			{
				SpawnerManager.Instance.sync___set_value_randomiseWeapons(RNGWeaponButton.toggle.isOn, true);
			}
			this.sync___set_value_EnemyOutlinesEnabled(SteamLobby.Instance.enemyOutlineToggle.isOn, true);
			this.sync___set_value_FriendlyFireEnabled(SteamLobby.Instance.friendlyFireToggle.isOn, true);
		}
	}

	// Token: 0x06000B42 RID: 2882 RVA: 0x0004F7CC File Offset: 0x0004D9CC
	public void StartGame()
	{
		this.ResetGame();
		if (base.IsServer)
		{
			if (SpawnerManager.Instance != null && RNGWeaponButton.toggle != null)
			{
				SpawnerManager.Instance.sync___set_value_randomiseWeapons(RNGWeaponButton.toggle.isOn, true);
			}
			this.sync___set_value_EnemyOutlinesEnabled(SteamLobby.Instance.enemyOutlineToggle.isOn, true);
			this.sync___set_value_FriendlyFireEnabled(SteamLobby.Instance.friendlyFireToggle.isOn, true);
		}
	}

	// Token: 0x06000B43 RID: 2883 RVA: 0x0004F844 File Offset: 0x0004DA44
	public void Update()
	{
		if (base.IsOwner && Time.time - this.rttLastUpdated > 0.1f)
		{
			this.AddRoundTripTime(Time.time, (float)base.TimeManager.RoundTripTime / 1000f);
			this.rttLastUpdated = Time.time;
		}
		if (!base.IsServer)
		{
			return;
		}
		if (PauseManager.Instance.inMainMenu || PauseManager.Instance.inVictoryMenu)
		{
			return;
		}
		this.recentDeaths.RemoveAll((Death death) => Time.time - death.TimeOfDeath > 1f);
	}

	// Token: 0x06000B44 RID: 2884 RVA: 0x0004F8E1 File Offset: 0x0004DAE1
	private IEnumerator WaitForDraw()
	{
		Debug.Log("Waiting for draw...");
		float elapsedTime = 0f;
		while (this.GetAliveTeams().Length != 0 && elapsedTime < 1f)
		{
			yield return null;
			elapsedTime += Time.deltaTime;
		}
		int[] aliveTeams = this.GetAliveTeams();
		if (aliveTeams.Length > 1)
		{
			Debug.Log("A player came back to life?? This should never happen!");
			this.waitForDrawCoroutine = null;
			yield break;
		}
		if (SceneMotor.Instance.testMap)
		{
			this.ProgressToNextTake();
			this.waitForDrawCoroutine = null;
			yield break;
		}
		Debug.Log(string.Format("aliveTeams length: {0}", aliveTeams.Length));
		if (aliveTeams.Length == 1)
		{
			int num = aliveTeams[0];
			ScoreManager.Instance.AddRoundScore(num, 1);
			List<int> playerIdsForTeam = ScoreManager.Instance.GetPlayerIdsForTeam(num);
			StringBuilder stringBuilder = new StringBuilder();
			for (int i = 0; i < playerIdsForTeam.Count; i++)
			{
				int num2 = playerIdsForTeam[i];
				if (ClientInstance.playerInstances.ContainsKey(num2))
				{
					stringBuilder.Append(string.Concat(new string[]
					{
						"<color=#",
						PauseManager.Instance.selfNameLogColor,
						">",
						ClientInstance.playerInstances[num2].PlayerName,
						"</color>"
					}));
					if (i < playerIdsForTeam.Count - 2)
					{
						stringBuilder.Append(", ");
					}
					else if (i == playerIdsForTeam.Count - 2)
					{
						stringBuilder.Append(" and ");
					}
				}
			}
			stringBuilder.Append(" won the take");
			PauseManager.Instance.WriteLog(stringBuilder.ToString());
		}
		else
		{
			int[] array = this.recentDeaths.Select((Death death) => death.PlayerId).ToArray<int>();
			List<int> list = new List<int>();
			foreach (int num3 in array)
			{
				int teamId = ScoreManager.Instance.GetTeamId(num3);
				if (!list.Contains(teamId))
				{
					ScoreManager.Instance.AddRoundScore(teamId, 1);
					list.Add(teamId);
				}
			}
			PauseManager.Instance.WriteLog("A draw happened, players: " + string.Join(", ", array.Select((int id) => string.Concat(new string[]
			{
				"<color=#",
				PauseManager.Instance.selfNameLogColor,
				">",
				ClientInstance.playerInstances[id].PlayerName,
				"</color>"
			}))) + " have scored a point for their teams.");
		}
		int winningTeamId;
		bool isRoundWon = ScoreManager.Instance.CheckForRoundWin(out winningTeamId);
		this.UpdateMatchPointsHUD(isRoundWon ? winningTeamId : (-1), ScoreManager.Instance.GetRoundScoreDictionary());
		yield return new WaitForSeconds(1f - elapsedTime);
		if (isRoundWon)
		{
			this.RoundWon(winningTeamId);
			List<int> playerIdsForTeam2 = ScoreManager.Instance.GetPlayerIdsForTeam(winningTeamId);
			StringBuilder stringBuilder2 = new StringBuilder();
			for (int k = 0; k < playerIdsForTeam2.Count; k++)
			{
				int num4 = playerIdsForTeam2[k];
				if (ClientInstance.playerInstances.ContainsKey(num4))
				{
					stringBuilder2.Append(string.Concat(new string[]
					{
						"<color=#",
						PauseManager.Instance.selfNameLogColor,
						">",
						ClientInstance.playerInstances[num4].PlayerName,
						"</color>"
					}));
					if (k < playerIdsForTeam2.Count - 2)
					{
						stringBuilder2.Append(", ");
					}
					else if (k == playerIdsForTeam2.Count - 2)
					{
						stringBuilder2.Append(" and ");
					}
				}
			}
			stringBuilder2.Append(" won the round");
			PauseManager.Instance.WriteLog(stringBuilder2.ToString());
			RoundManager.Instance.CmdEndRound(winningTeamId);
			yield return new WaitForSeconds(4f);
			SceneMotor.Instance.ChangeNetworkScene();
		}
		else
		{
			yield return new WaitForSeconds(2f);
			this.ProgressToNextTake();
		}
		this.alivePlayers.Clear();
		this.waitForDrawCoroutine = null;
		yield break;
	}

	// Token: 0x06000B45 RID: 2885 RVA: 0x0004F8F0 File Offset: 0x0004DAF0
	[ObserversRpc(RunLocally = true)]
	private void UpdateMatchPointsHUD(int winningTeamId, Dictionary<int, int> roundScores)
	{
		this.RpcWriter___Observers_UpdateMatchPointsHUD_1259646723(winningTeamId, roundScores);
		this.RpcLogic___UpdateMatchPointsHUD_1259646723(winningTeamId, roundScores);
	}

	// Token: 0x06000B46 RID: 2886 RVA: 0x0004F90E File Offset: 0x0004DB0E
	private void RoundWon(int winningTeamId)
	{
		ScoreManager.Instance.ResetRound();
		ScoreManager.Instance.AddPoints(winningTeamId, 1);
	}

	// Token: 0x06000B47 RID: 2887 RVA: 0x0004F926 File Offset: 0x0004DB26
	public void ProgressToNextTake()
	{
		ScoreManager.Instance.SetRoundIndex(ScoreManager.Instance.SyncAccessor_TakeIndex + 1);
		this.ObserversRoundSpawn();
	}

	// Token: 0x06000B48 RID: 2888 RVA: 0x0004F944 File Offset: 0x0004DB44
	private int[] GetAliveTeams()
	{
		List<int> list = new List<int>();
		foreach (int num in this.alivePlayers)
		{
			int teamId = ScoreManager.Instance.GetTeamId(num);
			if (!list.Contains(teamId))
			{
				list.Add(teamId);
			}
		}
		return list.ToArray();
	}

	// Token: 0x06000B49 RID: 2889 RVA: 0x0004F9B8 File Offset: 0x0004DBB8
	public int[] GetConnectedTeams()
	{
		List<int> list = new List<int>();
		foreach (ClientInstance clientInstance in ClientInstance.playerInstances.Values)
		{
			int teamId = ScoreManager.Instance.GetTeamId(clientInstance.PlayerId);
			if (!list.Contains(teamId))
			{
				list.Add(teamId);
			}
		}
		return list.ToArray();
	}

	// Token: 0x06000B4A RID: 2890 RVA: 0x0004FA38 File Offset: 0x0004DC38
	[ObserversRpc]
	public void ObserversRoundSpawn()
	{
		this.RpcWriter___Observers_ObserversRoundSpawn_2166136261();
	}

	// Token: 0x06000B4B RID: 2891 RVA: 0x0004FA40 File Offset: 0x0004DC40
	public void CmdKickPlayer(NetworkConnection conn)
	{
		this.KickPlayerTarget(conn);
		conn.Disconnect(false);
	}

	// Token: 0x06000B4C RID: 2892 RVA: 0x0004FA50 File Offset: 0x0004DC50
	[TargetRpc]
	private void KickPlayerTarget(NetworkConnection conn)
	{
		this.RpcWriter___Target_KickPlayerTarget_328543758(conn);
	}

	// Token: 0x06000B4D RID: 2893 RVA: 0x0004FA5C File Offset: 0x0004DC5C
	[ObserversRpc(RunLocally = true)]
	public void SetStartTime(float serverTimeTillStart)
	{
		this.RpcWriter___Observers_SetStartTime_431000436(serverTimeTillStart);
		this.RpcLogic___SetStartTime_431000436(serverTimeTillStart);
	}

	// Token: 0x06000B4E RID: 2894 RVA: 0x0004FA80 File Offset: 0x0004DC80
	public void ResetTeamIds()
	{
		for (int i = 0; i < 4; i++)
		{
			if (ClientInstance.playerInstances.ContainsKey(i))
			{
				ScoreManager.Instance.SetTeamId(ClientInstance.playerInstances[i].PlayerId, ClientInstance.playerInstances[i].PlayerId);
			}
		}
	}

	// Token: 0x06000B4F RID: 2895 RVA: 0x0004FAD0 File Offset: 0x0004DCD0
	[ServerRpc(RequireOwnership = false)]
	public void ScrambleTeams()
	{
		this.RpcWriter___Server_ScrambleTeams_2166136261();
	}

	// Token: 0x06000B50 RID: 2896 RVA: 0x0004FAE3 File Offset: 0x0004DCE3
	private void AddRoundTripTime(float time, float roundTripTime)
	{
		this.RoundTripTimes.Add(new GameManager.RoundTripTime(time, roundTripTime));
		this.RoundTripTimes.RemoveAll((GameManager.RoundTripTime rtt) => Time.time - rtt.Time > 10f);
	}

	// Token: 0x06000B51 RID: 2897 RVA: 0x0004FB24 File Offset: 0x0004DD24
	private float GetAverageRoundTripTime()
	{
		this.RoundTripTimes.RemoveAll((GameManager.RoundTripTime rtt) => Time.time - rtt.Time > 10f);
		if (this.RoundTripTimes.Count == 0)
		{
			return 0f;
		}
		float num = 0f;
		foreach (GameManager.RoundTripTime roundTripTime in this.RoundTripTimes)
		{
			num += roundTripTime.RTT;
		}
		return num / (float)this.RoundTripTimes.Count;
	}

	// Token: 0x06000B53 RID: 2899 RVA: 0x0004FC28 File Offset: 0x0004DE28
	public virtual void NetworkInitialize___Early()
	{
		if (this.NetworkInitializeEarly_GameManager_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeEarly_GameManager_Assembly-CSharp.dll = true;
		this.syncVar___FriendlyFireEnabled = new SyncVar<bool>(this, 3U, WritePermission.ServerOnly, ReadPermission.Observers, -1f, Channel.Reliable, this.FriendlyFireEnabled);
		this.syncVar___EnemyOutlinesEnabled = new SyncVar<bool>(this, 2U, WritePermission.ServerOnly, ReadPermission.Observers, -1f, Channel.Reliable, this.EnemyOutlinesEnabled);
		this.syncVar___playingTeams = new SyncVar<bool>(this, 1U, WritePermission.ServerOnly, ReadPermission.Observers, -1f, Channel.Reliable, this.playingTeams);
		this.syncVar___roundWasWon = new SyncVar<bool>(this, 0U, WritePermission.ServerOnly, ReadPermission.Observers, -1f, Channel.Reliable, this.roundWasWon);
		base.RegisterServerRpc(0U, new ServerRpcDelegate(this.RpcReader___Server_PlayerDied_3316948804));
		base.RegisterObserversRpc(1U, new ClientRpcDelegate(this.RpcReader___Observers_UpdateMatchPointsHUD_1259646723));
		base.RegisterObserversRpc(2U, new ClientRpcDelegate(this.RpcReader___Observers_ObserversRoundSpawn_2166136261));
		base.RegisterTargetRpc(3U, new ClientRpcDelegate(this.RpcReader___Target_KickPlayerTarget_328543758));
		base.RegisterObserversRpc(4U, new ClientRpcDelegate(this.RpcReader___Observers_SetStartTime_431000436));
		base.RegisterServerRpc(5U, new ServerRpcDelegate(this.RpcReader___Server_ScrambleTeams_2166136261));
		base.RegisterSyncVarRead(new SyncVarReadDelegate(this.ReadSyncVar___GameManager));
	}

	// Token: 0x06000B54 RID: 2900 RVA: 0x0004FD8E File Offset: 0x0004DF8E
	public virtual void NetworkInitialize__Late()
	{
		if (this.NetworkInitializeLate_GameManager_Assembly-CSharp.dll)
		{
			return;
		}
		this.NetworkInitializeLate_GameManager_Assembly-CSharp.dll = true;
		this.syncVar___FriendlyFireEnabled.SetRegistered();
		this.syncVar___EnemyOutlinesEnabled.SetRegistered();
		this.syncVar___playingTeams.SetRegistered();
		this.syncVar___roundWasWon.SetRegistered();
	}

	// Token: 0x06000B55 RID: 2901 RVA: 0x0004FDCD File Offset: 0x0004DFCD
	public override void NetworkInitializeIfDisabled()
	{
		this.NetworkInitialize___Early();
		this.NetworkInitialize__Late();
	}

	// Token: 0x06000B56 RID: 2902 RVA: 0x0004FDDC File Offset: 0x0004DFDC
	private void RpcWriter___Server_PlayerDied_3316948804(int playerId)
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteInt32(playerId, AutoPackType.Packed);
		base.SendServerRpc(0U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000B57 RID: 2903 RVA: 0x0004FE88 File Offset: 0x0004E088
	public void RpcLogic___PlayerDied_3316948804(int playerId)
	{
		if (PauseManager.Instance.inMainMenu || PauseManager.Instance.inVictoryMenu)
		{
			return;
		}
		if (this.alivePlayers.Contains(playerId))
		{
			this.alivePlayers.Remove(playerId);
		}
		this.recentDeaths.Add(new Death
		{
			PlayerId = playerId,
			TimeOfDeath = Time.time
		});
		int[] aliveTeams = this.GetAliveTeams();
		Debug.Log("Alive teams: " + string.Join<int>(", ", aliveTeams));
		if (aliveTeams.Length <= 1 && this.waitForDrawCoroutine == null)
		{
			this.waitForDrawCoroutine = base.StartCoroutine(this.WaitForDraw());
		}
		Debug.Log("player " + playerId.ToString() + " died. Remaining players: " + this.alivePlayers.Count.ToString());
	}

	// Token: 0x06000B58 RID: 2904 RVA: 0x0004FF60 File Offset: 0x0004E160
	private void RpcReader___Server_PlayerDied_3316948804(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		int num = PooledReader0.ReadInt32(AutoPackType.Packed);
		if (!base.IsServer)
		{
			return;
		}
		this.RpcLogic___PlayerDied_3316948804(num);
	}

	// Token: 0x06000B59 RID: 2905 RVA: 0x0004FF98 File Offset: 0x0004E198
	private void RpcWriter___Observers_UpdateMatchPointsHUD_1259646723(int winningTeamId, Dictionary<int, int> roundScores)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteInt32(winningTeamId, AutoPackType.Packed);
		writer.Write___System.Collections.Generic.Dictionary`2<System.Int32,System.Int32>FishNet.Serializing.Generated(roundScores);
		base.SendObserversRpc(1U, writer, channel, DataOrderType.Default, false, false, false);
		writer.Store();
	}

	// Token: 0x06000B5A RID: 2906 RVA: 0x00050060 File Offset: 0x0004E260
	private void RpcLogic___UpdateMatchPointsHUD_1259646723(int winningTeamId, Dictionary<int, int> roundScores)
	{
		ClientInstance instance = ClientInstance.Instance;
		if (instance == null)
		{
			return;
		}
		FirstPersonController player = instance.PlayerSpawner.player;
		if (player == null)
		{
			return;
		}
		player.matchPoitnsHUD.UpdateVisuals(winningTeamId, roundScores);
	}

	// Token: 0x06000B5B RID: 2907 RVA: 0x00050088 File Offset: 0x0004E288
	private void RpcReader___Observers_UpdateMatchPointsHUD_1259646723(PooledReader PooledReader0, Channel channel)
	{
		int num = PooledReader0.ReadInt32(AutoPackType.Packed);
		Dictionary<int, int> dictionary = global::FishNet.Serializing.Generated.GeneratedReaders___Internal.Read___System.Collections.Generic.Dictionary`2<System.Int32,System.Int32>FishNet.Serializing.Generateds(PooledReader0);
		if (!base.IsClient)
		{
			return;
		}
		if (base.IsHost)
		{
			return;
		}
		this.RpcLogic___UpdateMatchPointsHUD_1259646723(num, dictionary);
	}

	// Token: 0x06000B5C RID: 2908 RVA: 0x000500DC File Offset: 0x0004E2DC
	private void RpcWriter___Observers_ObserversRoundSpawn_2166136261()
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendObserversRpc(2U, writer, channel, DataOrderType.Default, false, false, false);
		writer.Store();
	}

	// Token: 0x06000B5D RID: 2909 RVA: 0x00050185 File Offset: 0x0004E385
	public void RpcLogic___ObserversRoundSpawn_2166136261()
	{
		ClientInstance.Instance.PlayerSpawner.RoundSpawn();
	}

	// Token: 0x06000B5E RID: 2910 RVA: 0x00050198 File Offset: 0x0004E398
	private void RpcReader___Observers_ObserversRoundSpawn_2166136261(PooledReader PooledReader0, Channel channel)
	{
		if (!base.IsClient)
		{
			return;
		}
		this.RpcLogic___ObserversRoundSpawn_2166136261();
	}

	// Token: 0x06000B5F RID: 2911 RVA: 0x000501B8 File Offset: 0x0004E3B8
	private void RpcWriter___Target_KickPlayerTarget_328543758(NetworkConnection conn)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendTargetRpc(3U, writer, channel, DataOrderType.Default, conn, false, true);
		writer.Store();
	}

	// Token: 0x06000B60 RID: 2912 RVA: 0x00050260 File Offset: 0x0004E460
	private void RpcLogic___KickPlayerTarget_328543758(NetworkConnection conn)
	{
		LobbyController.Instance.LocalPlayerController.KickSelf();
	}

	// Token: 0x06000B61 RID: 2913 RVA: 0x00050274 File Offset: 0x0004E474
	private void RpcReader___Target_KickPlayerTarget_328543758(PooledReader PooledReader0, Channel channel)
	{
		if (!base.IsClient)
		{
			return;
		}
		this.RpcLogic___KickPlayerTarget_328543758(base.LocalConnection);
	}

	// Token: 0x06000B62 RID: 2914 RVA: 0x0005029C File Offset: 0x0004E49C
	private void RpcWriter___Observers_SetStartTime_431000436(float serverTimeTillStart)
	{
		if (!base.IsServer)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because server is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		writer.WriteSingle(serverTimeTillStart, AutoPackType.Unpacked);
		base.SendObserversRpc(4U, writer, channel, DataOrderType.Default, false, false, false);
		writer.Store();
	}

	// Token: 0x06000B63 RID: 2915 RVA: 0x00050358 File Offset: 0x0004E558
	public void RpcLogic___SetStartTime_431000436(float serverTimeTillStart)
	{
		float num = (base.IsServer ? 0f : this.GetAverageRoundTripTime());
		this.timeTillStart = serverTimeTillStart - num;
		this.hasSetStartTime = true;
	}

	// Token: 0x06000B64 RID: 2916 RVA: 0x0005038C File Offset: 0x0004E58C
	private void RpcReader___Observers_SetStartTime_431000436(PooledReader PooledReader0, Channel channel)
	{
		float num = PooledReader0.ReadSingle(AutoPackType.Unpacked);
		if (!base.IsClient)
		{
			return;
		}
		if (base.IsHost)
		{
			return;
		}
		this.RpcLogic___SetStartTime_431000436(num);
	}

	// Token: 0x06000B65 RID: 2917 RVA: 0x000503CC File Offset: 0x0004E5CC
	private void RpcWriter___Server_ScrambleTeams_2166136261()
	{
		if (!base.IsClient)
		{
			NetworkManager networkManager = base.NetworkManager;
			if (networkManager == null)
			{
				networkManager = InstanceFinder.NetworkManager;
			}
			if (networkManager != null)
			{
				networkManager.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			else
			{
				Debug.LogWarning("Cannot complete action because client is not active. This may also occur if the object is not yet initialized or if it does not contain a NetworkObject component. This message may be disabled by setting the Logging field in your attribute to LoggingType.Off.");
			}
			return;
		}
		Channel channel = Channel.Reliable;
		PooledWriter writer = WriterPool.GetWriter();
		base.SendServerRpc(5U, writer, channel, DataOrderType.Default);
		writer.Store();
	}

	// Token: 0x06000B66 RID: 2918 RVA: 0x00050468 File Offset: 0x0004E668
	public void RpcLogic___ScrambleTeams_2166136261()
	{
		int[] connectedTeams = this.GetConnectedTeams();
		if (connectedTeams.Length < 2)
		{
			return;
		}
		Dictionary<int, int> dictionary = new Dictionary<int, int>();
		foreach (int num in connectedTeams)
		{
			foreach (int num2 in ScoreManager.Instance.TeamIdToPlayerIds[num])
			{
				if (ClientInstance.playerInstances.ContainsKey(num2))
				{
					dictionary.TryAdd(num, 0);
					Dictionary<int, int> dictionary2 = dictionary;
					int num3 = num;
					int num4 = dictionary2[num3];
					dictionary2[num3] = num4 + 1;
				}
			}
		}
		int[] array2 = ClientInstance.playerInstances.Keys.OrderBy((int x) => global::UnityEngine.Random.value).ToArray<int>();
		int num5 = 0;
		foreach (int num6 in connectedTeams)
		{
			int num7 = dictionary[num6];
			for (int j = 0; j < num7; j++)
			{
				ScoreManager.Instance.SetTeamId(array2[num5], num6);
				num5++;
			}
		}
	}

	// Token: 0x06000B67 RID: 2919 RVA: 0x000505A8 File Offset: 0x0004E7A8
	private void RpcReader___Server_ScrambleTeams_2166136261(PooledReader PooledReader0, Channel channel, NetworkConnection conn)
	{
		if (!base.IsServer)
		{
			return;
		}
		this.RpcLogic___ScrambleTeams_2166136261();
	}

	// Token: 0x170000A9 RID: 169
	// (get) Token: 0x06000B68 RID: 2920 RVA: 0x000505C8 File Offset: 0x0004E7C8
	// (set) Token: 0x06000B69 RID: 2921 RVA: 0x000505D0 File Offset: 0x0004E7D0
	public bool SyncAccessor_roundWasWon
	{
		get
		{
			return this.roundWasWon;
		}
		set
		{
			if (value || !base.IsServer)
			{
				this.roundWasWon = value;
			}
			this.syncVar___roundWasWon.SetValue(value, value);
		}
	}

	// Token: 0x06000B6A RID: 2922 RVA: 0x00050608 File Offset: 0x0004E808
	public virtual bool ReadSyncVar___GameManager(PooledReader PooledReader0, uint UInt321, bool Boolean2)
	{
		if (UInt321 == 3U)
		{
			if (PooledReader0 == null)
			{
				this.sync___set_value_FriendlyFireEnabled(this.syncVar___FriendlyFireEnabled.GetValue(true), true);
				return true;
			}
			bool flag = PooledReader0.ReadBoolean();
			this.sync___set_value_FriendlyFireEnabled(flag, Boolean2);
			return true;
		}
		else if (UInt321 == 2U)
		{
			if (PooledReader0 == null)
			{
				this.sync___set_value_EnemyOutlinesEnabled(this.syncVar___EnemyOutlinesEnabled.GetValue(true), true);
				return true;
			}
			bool flag2 = PooledReader0.ReadBoolean();
			this.sync___set_value_EnemyOutlinesEnabled(flag2, Boolean2);
			return true;
		}
		else if (UInt321 == 1U)
		{
			if (PooledReader0 == null)
			{
				this.sync___set_value_playingTeams(this.syncVar___playingTeams.GetValue(true), true);
				return true;
			}
			bool flag3 = PooledReader0.ReadBoolean();
			this.sync___set_value_playingTeams(flag3, Boolean2);
			return true;
		}
		else
		{
			if (UInt321 != 0U)
			{
				return false;
			}
			if (PooledReader0 == null)
			{
				this.sync___set_value_roundWasWon(this.syncVar___roundWasWon.GetValue(true), true);
				return true;
			}
			bool flag4 = PooledReader0.ReadBoolean();
			this.sync___set_value_roundWasWon(flag4, Boolean2);
			return true;
		}
	}

	// Token: 0x170000AA RID: 170
	// (get) Token: 0x06000B6B RID: 2923 RVA: 0x00050726 File Offset: 0x0004E926
	// (set) Token: 0x06000B6C RID: 2924 RVA: 0x0005072E File Offset: 0x0004E92E
	public bool SyncAccessor_playingTeams
	{
		get
		{
			return this.playingTeams;
		}
		set
		{
			if (value || !base.IsServer)
			{
				this.playingTeams = value;
			}
			this.syncVar___playingTeams.SetValue(value, value);
		}
	}

	// Token: 0x170000AB RID: 171
	// (get) Token: 0x06000B6D RID: 2925 RVA: 0x00050763 File Offset: 0x0004E963
	// (set) Token: 0x06000B6E RID: 2926 RVA: 0x0005076B File Offset: 0x0004E96B
	public bool SyncAccessor_EnemyOutlinesEnabled
	{
		get
		{
			return this.EnemyOutlinesEnabled;
		}
		set
		{
			if (value || !base.IsServer)
			{
				this.EnemyOutlinesEnabled = value;
			}
			this.syncVar___EnemyOutlinesEnabled.SetValue(value, value);
		}
	}

	// Token: 0x170000AC RID: 172
	// (get) Token: 0x06000B6F RID: 2927 RVA: 0x000507A0 File Offset: 0x0004E9A0
	// (set) Token: 0x06000B70 RID: 2928 RVA: 0x000507A8 File Offset: 0x0004E9A8
	public bool SyncAccessor_FriendlyFireEnabled
	{
		get
		{
			return this.FriendlyFireEnabled;
		}
		set
		{
			if (value || !base.IsServer)
			{
				this.FriendlyFireEnabled = value;
			}
			this.syncVar___FriendlyFireEnabled.SetValue(value, value);
		}
	}

	// Token: 0x06000B71 RID: 2929 RVA: 0x000507DD File Offset: 0x0004E9DD
	public virtual void Awake___UserLogic()
	{
		if (!GameManager.Instance)
		{
			GameManager.Instance = this;
		}
		else
		{
			global::UnityEngine.Object.Destroy(this);
		}
		global::UnityEngine.SceneManagement.SceneManager.sceneLoaded += this.OnSceneLoaded;
	}

	// Token: 0x0400097D RID: 2429
	public static GameManager Instance;

	// Token: 0x0400097E RID: 2430
	public string MainMenuSceneName = "MainMenu";

	// Token: 0x0400097F RID: 2431
	public string VictorySceneName = "VictoryScene";

	// Token: 0x04000980 RID: 2432
	public readonly List<int> alivePlayers = new List<int>();

	// Token: 0x04000981 RID: 2433
	private readonly List<Death> recentDeaths = new List<Death>();

	// Token: 0x04000982 RID: 2434
	[SyncVar]
	public bool roundWasWon;

	// Token: 0x04000983 RID: 2435
	private float rttLastUpdated;

	// Token: 0x04000984 RID: 2436
	private Coroutine waitForDrawCoroutine;

	// Token: 0x04000985 RID: 2437
	public float timeTillStart = float.MinValue;

	// Token: 0x04000986 RID: 2438
	public bool hasSetStartTime;

	// Token: 0x04000987 RID: 2439
	public int numberOfReadyPlayers;

	// Token: 0x04000988 RID: 2440
	[SyncVar]
	public bool playingTeams;

	// Token: 0x04000989 RID: 2441
	[SyncVar]
	public bool EnemyOutlinesEnabled;

	// Token: 0x0400098A RID: 2442
	[SyncVar]
	public bool FriendlyFireEnabled = true;

	// Token: 0x0400098B RID: 2443
	private readonly List<GameManager.RoundTripTime> RoundTripTimes = new List<GameManager.RoundTripTime>();

	// Token: 0x0400098C RID: 2444
	public SyncVar<bool> syncVar___roundWasWon;

	// Token: 0x0400098D RID: 2445
	public SyncVar<bool> syncVar___playingTeams;

	// Token: 0x0400098E RID: 2446
	public SyncVar<bool> syncVar___EnemyOutlinesEnabled;

	// Token: 0x0400098F RID: 2447
	public SyncVar<bool> syncVar___FriendlyFireEnabled;

	// Token: 0x04000990 RID: 2448
	private bool NetworkInitializeEarly_GameManager_Assembly-CSharp.dll;

	// Token: 0x04000991 RID: 2449
	private bool NetworkInitializeLate_GameManager_Assembly-CSharp.dll;

	// Token: 0x020000CC RID: 204
	private struct RoundTripTime
	{
		// Token: 0x06000B72 RID: 2930 RVA: 0x0005080A File Offset: 0x0004EA0A
		public RoundTripTime(float time, float roundTripTime)
		{
			this.Time = time;
			this.RTT = roundTripTime;
		}

		// Token: 0x04000992 RID: 2450
		public readonly float Time;

		// Token: 0x04000993 RID: 2451
		public readonly float RTT;
	}
}

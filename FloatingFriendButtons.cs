﻿using System;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

// Token: 0x020000C9 RID: 201
public class FloatingFriendButtons : MonoBehaviour
{
	// Token: 0x06000B36 RID: 2870 RVA: 0x0004F52C File Offset: 0x0004D72C
	private void Start()
	{
		this.pauseManager = PauseManager.Instance;
	}

	// Token: 0x06000B37 RID: 2871 RVA: 0x0004F53C File Offset: 0x0004D73C
	private void Update()
	{
		if (!this.pauseManager.inMainMenu && this.display.activeSelf)
		{
			this.display.SetActive(false);
		}
		if (this.display.activeSelf && (Vector3.Distance(base.transform.position, Input.mousePosition) > 200f || Input.mouseScrollDelta != Vector2.zero))
		{
			this.display.SetActive(false);
		}
	}

	// Token: 0x06000B38 RID: 2872 RVA: 0x0004F5B8 File Offset: 0x0004D7B8
	public void Spawn(TMP_InputField text)
	{
		base.transform.position = text.transform.position + Vector3.up * this.offsetY;
		this.display.SetActive(true);
		this.currentEntry = text.text;
		this.joinButton.interactable = SteamLobby.Instance.CanJoinFriend(this.currentEntry);
		this.inviteButton.interactable = SteamLobby.Instance.CanInviteFriend(this.currentEntry);
	}

	// Token: 0x06000B39 RID: 2873 RVA: 0x0004F63E File Offset: 0x0004D83E
	public void OpenFriendChat()
	{
		this.display.SetActive(false);
		SteamLobby.Instance.OpenFriendChat(this.currentEntry);
	}

	// Token: 0x06000B3A RID: 2874 RVA: 0x0004F65C File Offset: 0x0004D85C
	public void JoinFriendLobby()
	{
		this.display.SetActive(false);
		SteamLobby.Instance.JoinFriendLobby(this.currentEntry);
	}

	// Token: 0x06000B3B RID: 2875 RVA: 0x0004F67A File Offset: 0x0004D87A
	public void InviteFriendToLobby()
	{
		this.display.SetActive(false);
		SteamLobby.Instance.InviteFriendToLobby(this.currentEntry);
	}

	// Token: 0x04000975 RID: 2421
	[SerializeField]
	private float offsetY = 18f;

	// Token: 0x04000976 RID: 2422
	[SerializeField]
	private Button joinButton;

	// Token: 0x04000977 RID: 2423
	[SerializeField]
	private Button inviteButton;

	// Token: 0x04000978 RID: 2424
	public string currentEntry;

	// Token: 0x04000979 RID: 2425
	public GameObject display;

	// Token: 0x0400097A RID: 2426
	private PauseManager pauseManager;
}

﻿using System;
using System.Collections;
using UnityEngine;

// Token: 0x020000B4 RID: 180
public class DisableAfterTime : MonoBehaviour
{
	// Token: 0x06000A3B RID: 2619 RVA: 0x0004B12D File Offset: 0x0004932D
	private void Start()
	{
		base.StartCoroutine(this.DisableTimer());
	}

	// Token: 0x06000A3C RID: 2620 RVA: 0x0004B13C File Offset: 0x0004933C
	private IEnumerator DisableTimer()
	{
		yield return new WaitForSeconds(this.time);
		base.transform.localScale = Vector3.one;
		base.gameObject.SetActive(false);
		yield break;
	}

	// Token: 0x06000A3D RID: 2621 RVA: 0x000023D6 File Offset: 0x000005D6
	private void Update()
	{
	}

	// Token: 0x0400090B RID: 2315
	[SerializeField]
	private float time;
}

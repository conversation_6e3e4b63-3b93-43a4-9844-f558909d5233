﻿using System;
using TMPro;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

// Token: 0x020000EC RID: 236
public class PlaylistInstance : MonoBehaviour
{
	// Token: 0x06000C75 RID: 3189 RVA: 0x00055BF9 File Offset: 0x00053DF9
	private void Start()
	{
		this.text.text = this.name;
		this.nameInputField = base.GetComponentInChildren<TMP_InputField>();
	}

	// Token: 0x06000C76 RID: 3190 RVA: 0x00055C18 File Offset: 0x00053E18
	public void DeletePlaylist()
	{
		global::UnityEngine.Object.Destroy(base.gameObject);
		MapsManager.Instance.RemovePlaylist(this.index);
	}

	// Token: 0x06000C77 RID: 3191 RVA: 0x00055C35 File Offset: 0x00053E35
	public void OpenPlaylist()
	{
		MapsManager.Instance.PopulateMapsFromPlayList(this.index);
		MapsManager.Instance.currentPlaylistText.text = this.name;
		MapsManager.Instance.selectedPlaylistIndex = this.index;
	}

	// Token: 0x06000C78 RID: 3192 RVA: 0x00055C6C File Offset: 0x00053E6C
	public void DuplicatePlaylist()
	{
		MapsManager.Instance.DupePlaylist(this.index);
	}

	// Token: 0x06000C79 RID: 3193 RVA: 0x00055C7E File Offset: 0x00053E7E
	public void RenamePlaylist()
	{
		if (EventSystem.current.currentSelectedGameObject == this.nameInputField)
		{
			EventSystem.current.SetSelectedGameObject(null);
			return;
		}
		this.nameInputField.Select();
	}

	// Token: 0x06000C7A RID: 3194 RVA: 0x00055CAE File Offset: 0x00053EAE
	public void UpdateName(TMP_InputField field)
	{
		if (field.text != "")
		{
			this.name = field.text;
			MapsManager.Instance.Playlists[this.index].Name = this.name;
		}
	}

	// Token: 0x06000C7B RID: 3195 RVA: 0x00055CEE File Offset: 0x00053EEE
	public void OnSelect(TMP_InputField input)
	{
		this.temptext = this.name;
		this.text.text = "";
	}

	// Token: 0x06000C7C RID: 3196 RVA: 0x00055D0C File Offset: 0x00053F0C
	public void OnDeselect(TMP_InputField input)
	{
		this.text.text = this.temptext;
	}

	// Token: 0x04000AB3 RID: 2739
	public new string name;

	// Token: 0x04000AB4 RID: 2740
	public int index;

	// Token: 0x04000AB5 RID: 2741
	[SerializeField]
	private TextMeshProUGUI text;

	// Token: 0x04000AB6 RID: 2742
	[SerializeField]
	private Color selectedColor;

	// Token: 0x04000AB7 RID: 2743
	[SerializeField]
	private Color deselectedColor;

	// Token: 0x04000AB8 RID: 2744
	[SerializeField]
	private TMP_InputField nameInputField;

	// Token: 0x04000AB9 RID: 2745
	public Button button;

	// Token: 0x04000ABA RID: 2746
	private string temptext;
}
